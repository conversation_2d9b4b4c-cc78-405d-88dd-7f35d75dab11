<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATA DE INICIAÇÃO DE PROJETO — MODELO OFICIAL (STAGE‑GATE 0)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .card {
            @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-6 md:p-8 mb-8;
        }
        .section-title {
            @apply text-2xl font-bold text-gray-900 mb-6 pb-2 border-b-2 border-blue-600;
        }
        .subsection-title {
            @apply text-lg font-semibold text-gray-800 mb-3 mt-6;
        }
        .score-card {
            @apply bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6;
        }
        .score-high { @apply bg-green-100 text-green-800 border-green-300; }
        .score-medium { @apply bg-yellow-100 text-yellow-800 border-yellow-300; }
        .score-low { @apply bg-red-100 text-red-800 border-red-300; }
        
        /* Estilos para funcionalidade de edição */
        .edit-btn {
            @apply cursor-pointer p-1 rounded-full hover:bg-gray-200 transition-colors ml-4 flex-shrink-0;
        }
        .section-header {
            @apply flex justify-between items-center;
        }
        [contenteditable="true"] {
            outline: 2px dashed #3b82f6;
            background-color: #eff6ff;
            padding: 4px;
            border-radius: 4px;
        }
        #save-html-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .input-field {
            @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
        }
        .checkbox-custom {
            @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500;
        }
        
        /* Regras de paginação para PDF */
        @media print {
            body { font-size: 12pt; line-height: 1.4; background: white !important; color: black !important; }
            .container { max-width: none !important; padding: 0 !important; margin: 0 !important; }
            .card { box-shadow: none !important; border: 1px solid #e2e8f0 !important; page-break-inside: avoid; margin-bottom: 20pt !important; }
            .page-break-before { page-break-before: always !important; }
            .no-page-break { page-break-inside: avoid !important; }
            h1, h2, h3, h4 { page-break-after: avoid !important; page-break-inside: avoid !important; }
            table { page-break-inside: avoid !important; }
            button, .interactive-element { display: none !important; }
            .edit-ui { display: none !important; }
            @page { margin: 2cm 1.5cm; size: A4; }
            @page :first { margin-top: 3cm; }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 sm:p-6 md:p-8 max-w-7xl relative">
        <div class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-blue-600 to-blue-800 hidden md:block"></div>

        <header class="mb-12 text-center editable-section">
            <div class="section-header justify-center items-center">
                <h1 class="text-4xl md:text-5xl font-extrabold text-blue-700 editable-content">ATA DE INICIAÇÃO DE PROJETO — MODELO OFICIAL (STAGE‑GATE 0)</h1>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <p class="text-xl text-gray-600 mt-2 editable-content">Finalidade: registrar a decisão de <strong>ingresso</strong> de um projeto no funil da empresa e organizar seu <strong>início formal</strong></p>
        </header>

        <section class="card bg-gradient-to-r from-blue-50 to-indigo-50 editable-section">
            <div class="section-header">
                <h2 class="section-title text-3xl font-extrabold text-blue-800 mb-8">1) Identificação do Projeto</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="grid md:grid-cols-2 gap-8 editable-content">
                <div class="space-y-4">
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Projeto:</label>
                        <input type="text" class="input-field" placeholder="Nome do projeto">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Código interno:</label>
                        <input type="text" class="input-field" placeholder="Ex: INOV-2025-001">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Solicitante / Dono da Ideia:</label>
                        <input type="text" class="input-field" placeholder="Nome completo">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Product Owner (PO):</label>
                        <input type="text" class="input-field" placeholder="Nome completo">
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Tech Lead (TL):</label>
                        <input type="text" class="input-field" placeholder="Nome completo">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Sponsor (diretoria):</label>
                        <input type="text" class="input-field" placeholder="Nome completo">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Data desta ata:</label>
                        <input type="date" class="input-field">
                    </div>
                    <div class="flex flex-col">
                        <label class="font-medium text-gray-700 mb-1">Versão:</label>
                        <input type="text" class="input-field" placeholder="v1.0" value="v1.0">
                    </div>
                </div>
            </div>
        </section>

        <section class="card editable-section">
            <div class="section-header">
                <h2 class="section-title">2) Resumo Executivo</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-6 editable-content">
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Problema/Dor (o que resolver):</label>
                    <textarea class="input-field" rows="3" placeholder="Descreva o problema que o projeto resolve..."></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Proposta de Solução (como resolver):</label>
                    <textarea class="input-field" rows="3" placeholder="Descreva a solução proposta..."></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Público/ICP (Ideal Customer Profile):</label>
                    <textarea class="input-field" rows="2" placeholder="Descreva o público-alvo..."></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Prova/Tração existente (se houver):</label>
                    <textarea class="input-field" rows="2" placeholder="Evidências de mercado, testes, etc..."></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Por que agora? (timing/urgência):</label>
                    <textarea class="input-field" rows="2" placeholder="Justifique o timing..."></textarea>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <label class="font-semibold text-blue-800 mb-2">Recomendação preliminar do comitê:</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="recomendacao" value="GO" class="mr-2">
                            <span>GO</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="recomendacao" value="DERISCAR" class="mr-2">
                            <span>Deriscar e Reavaliar</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="recomendacao" value="CONGELAR" class="mr-2">
                            <span>Congelar</span>
                        </label>
                    </div>
                </div>
            </div>
        </section>

        <section class="card editable-section">
            <div class="section-header">
                <h2 class="section-title">3) Alinhamento Estratégico</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-4 editable-content">
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Tese/OKR impactado:</label>
                    <input type="text" class="input-field" placeholder="Qual OKR ou tese estratégica este projeto impacta?">
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Sinergia com portfólio/ativos:</label>
                    <textarea class="input-field" rows="2" placeholder="Como este projeto se conecta com outros ativos/portfólio da empresa?"></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Uso de IA/Automação:</label>
                    <textarea class="input-field" rows="2" placeholder="Descreva o uso de IA e automação no projeto..."></textarea>
                </div>
            </div>
        </section>

        <section class="card editable-section">
            <div class="section-header">
                <h2 class="section-title">4) Escopo Inicial (MVP)</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-6 editable-content">
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Objetivo do MVP (resultado mensurável):</label>
                    <textarea class="input-field" rows="2" placeholder="Qual resultado mensurável o MVP deve entregar?"></textarea>
                </div>
                <div>
                    <label class="font-medium text-gray-700 mb-3">Funcionalidades-chave (core):</label>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" class="checkbox-custom" id="f1">
                            <input type="text" class="input-field flex-1" placeholder="F1 - Descreva a funcionalidade 1">
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" class="checkbox-custom" id="f2">
                            <input type="text" class="input-field flex-1" placeholder="F2 - Descreva a funcionalidade 2">
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" class="checkbox-custom" id="f3">
                            <input type="text" class="input-field flex-1" placeholder="F3 - Descreva a funcionalidade 3">
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Fora de escopo (neste ciclo):</label>
                    <textarea class="input-field" rows="2" placeholder="O que NÃO será incluído no MVP?"></textarea>
                </div>
                <div class="flex flex-col">
                    <label class="font-medium text-gray-700 mb-1">Entregáveis do ciclo:</label>
                    <textarea class="input-field" rows="2" placeholder="Liste os entregáveis específicos deste ciclo..."></textarea>
                </div>
            </div>
        </section>

        <button id="save-html-btn" class="edit-ui bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-transform transform hover:scale-105">
            Salvar e Baixar Novo HTML
        </button>

    </div>

    <script id="edit-script">
        document.addEventListener('DOMContentLoaded', function () {
            const gearIconSVG = `<svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`;
            const saveIconSVG = `<svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" /></svg>`;
            
            const headerGearIconSVG = gearIconSVG.replace('text-gray-600', 'text-white').replace('h-6 w-6', 'h-8 w-8');
            const headerSaveIconSVG = saveIconSVG.replace('text-gray-600', 'text-white').replace('h-6 w-6', 'h-8 w-8');

            document.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const section = this.closest('.editable-section');
                    if (!section) return;

                    const content = section.querySelector('.editable-content');
                    if (!content) return;
                    
                    const isEditing = content.isContentEditable;
                    const isHeader = section.tagName.toLowerCase() === 'header';

                    if (!isEditing) {
                        content.contentEditable = true;
                        content.focus();
                        this.innerHTML = isHeader ? headerSaveIconSVG : saveIconSVG;
                        this.title = "Salvar alterações";
                    } else {
                        content.contentEditable = false;
                        this.innerHTML = isHeader ? headerGearIconSVG : gearIconSVG;
                        this.title = "Editar este bloco";
                    }
                });
            });

            document.getElementById('save-html-btn').addEventListener('click', function () {
                const docClone = document.documentElement.cloneNode(true);
                docClone.querySelectorAll('.edit-ui').forEach(el => el.remove());
                docClone.querySelector('#edit-script').remove();
                docClone.querySelectorAll('[contenteditable="true"]').forEach(el => {
                    el.removeAttribute('contenteditable');
                });
                const cleanHtml = "<!DOCTYPE html>\n" + docClone.outerHTML;
                const blob = new Blob([cleanHtml], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'ATA_Iniciacao_Projeto_' + new Date().toISOString().slice(0,10) + '.html';
                a.click();
                URL.revokeObjectURL(url);
            });
        });
    </script>

</body>
</html>

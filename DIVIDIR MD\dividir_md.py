#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para dividir um arquivo .md em duas partes preservando o conteúdo

Autor: Pedro Vitor
Data: 2025
Descrição: Script simples que pega um arquivo .md e o divide em 2 arquivos,
          preservando todo o conteúdo original de forma proporcional.
"""

import os
import sys

def dividir_arquivo_md(arquivo_entrada):
    """
    Divide um arquivo .md em duas partes aproximadamente iguais
    
    Args:
        arquivo_entrada (str): Caminho para o arquivo .md original
        
    Returns:
        tuple: (arquivo_parte1, arquivo_parte2) - nomes dos arquivos criados
    """
    
    # Verificar se o arquivo existe
    if not os.path.exists(arquivo_entrada):
        print(f"❌ Erro: Arquivo '{arquivo_entrada}' não encontrado!")
        return None, None
    
    # Verificar se é um arquivo .md
    if not arquivo_entrada.lower().endswith('.md'):
        print(f"⚠️ Aviso: Arquivo '{arquivo_entrada}' não é um .md, mas continuando...")
    
    try:
        # Ler todo o conteúdo do arquivo
        print(f"📖 Lendo arquivo: {arquivo_entrada}")
        with open(arquivo_entrada, 'r', encoding='utf-8') as arquivo:
            linhas = arquivo.readlines()
        
        total_linhas = len(linhas)
        print(f"📊 Total de linhas encontradas: {total_linhas:,}")
        
        # Calcular ponto de divisão (aproximadamente na metade)
        ponto_divisao = total_linhas // 2
        print(f"✂️ Dividindo no meio: linha {ponto_divisao:,}")
        
        # Criar nomes dos arquivos de saída
        nome_base = os.path.splitext(arquivo_entrada)[0]
        extensao = os.path.splitext(arquivo_entrada)[1]
        
        arquivo_parte1 = f"{nome_base}_PARTE1{extensao}"
        arquivo_parte2 = f"{nome_base}_PARTE2{extensao}"
        
        # Criar primeira parte (linhas 0 até ponto_divisao)
        print(f"💾 Criando primeira parte: {arquivo_parte1}")
        with open(arquivo_parte1, 'w', encoding='utf-8') as arquivo:
            arquivo.writelines(linhas[:ponto_divisao])
        
        # Criar segunda parte (linhas ponto_divisao até o final)
        print(f"💾 Criando segunda parte: {arquivo_parte2}")
        with open(arquivo_parte2, 'w', encoding='utf-8') as arquivo:
            arquivo.writelines(linhas[ponto_divisao:])
        
        # Calcular estatísticas
        linhas_parte1 = ponto_divisao
        linhas_parte2 = total_linhas - ponto_divisao
        
        print(f"\n✅ DIVISÃO CONCLUÍDA COM SUCESSO!")
        print(f"📄 Arquivo original: {total_linhas:,} linhas")
        print(f"📄 Parte 1: {linhas_parte1:,} linhas ({linhas_parte1/total_linhas*100:.1f}%)")
        print(f"📄 Parte 2: {linhas_parte2:,} linhas ({linhas_parte2/total_linhas*100:.1f}%)")
        print(f"📁 Arquivos criados:")
        print(f"   • {arquivo_parte1}")
        print(f"   • {arquivo_parte2}")
        
        return arquivo_parte1, arquivo_parte2
        
    except Exception as e:
        print(f"❌ Erro ao processar arquivo: {str(e)}")
        return None, None

def main():
    """
    Função principal do script
    """
    print("🔧 SCRIPT DIVISOR DE ARQUIVOS .MD")
    print("=" * 40)
    
    # Verificar se foi fornecido um arquivo como argumento
    if len(sys.argv) > 1:
        arquivo_entrada = sys.argv[1]
    else:
        # Solicitar nome do arquivo ao usuário
        arquivo_entrada = input("📁 Digite o nome do arquivo .md para dividir: ").strip()
        
        # Se não fornecido, usar o arquivo padrão
        if not arquivo_entrada:
            arquivo_entrada = "compilado scripts.md"
            print(f"📋 Usando arquivo padrão: {arquivo_entrada}")
    
    # Executar a divisão
    parte1, parte2 = dividir_arquivo_md(arquivo_entrada)
    
    if parte1 and parte2:
        print(f"\n🎉 Processo finalizado! Os arquivos foram divididos com sucesso.")
    else:
        print(f"\n💥 Falha na execução. Verifique o arquivo e tente novamente.")

if __name__ == "__main__":
    main()

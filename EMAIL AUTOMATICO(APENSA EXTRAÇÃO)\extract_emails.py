#!/usr/bin/env python3
"""
Script para extrair todos os emails de um arquivo Excel
Autor: Assistente IA
Data: 2024
"""

import pandas as pd
import re
import os
from pathlib import Path

def extract_emails_from_excel(file_path):
    """
    Extrai todos os emails de um arquivo Excel
    
    Args:
        file_path (str): Caminho para o arquivo Excel
        
    Returns:
        list: Lista de emails únicos encontrados
    """
    try:
        # Verifica se o arquivo existe
        if not os.path.exists(file_path):
            print(f"Erro: Arquivo não encontrado: {file_path}")
            return []
        
        # Lê o arquivo Excel
        print(f"Lendo arquivo Excel: {file_path}")
        excel_file = pd.ExcelFile(file_path)
        
        # Lista para armazenar todos os emails encontrados
        all_emails = []
        
        # Padrão regex para emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        # Processa cada planilha no arquivo
        for sheet_name in excel_file.sheet_names:
            print(f"Processando planilha: {sheet_name}")
            
            try:
                # Lê a planilha
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # Converte todas as colunas para string para facilitar a busca
                for column in df.columns:
                    # Busca emails em cada coluna
                    for value in df[column].astype(str):
                        if pd.notna(value) and value != 'nan':
                            # Encontra todos os emails no valor
                            emails = re.findall(email_pattern, str(value))
                            all_emails.extend(emails)
                            
            except Exception as e:
                print(f"Erro ao processar planilha {sheet_name}: {e}")
                continue
        
        # Remove duplicatas e ordena
        unique_emails = sorted(list(set(all_emails)))
        
        print(f"\nTotal de emails únicos encontrados: {len(unique_emails)}")
        
        return unique_emails
        
    except Exception as e:
        print(f"Erro ao processar arquivo Excel: {e}")
        return []

def save_emails_to_file(emails, output_file="emails_extraidos.txt"):
    """
    Salva a lista de emails em um arquivo de texto
    
    Args:
        emails (list): Lista de emails
        output_file (str): Nome do arquivo de saída
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Emails extraídos do arquivo Excel:\n")
            f.write("=" * 50 + "\n\n")
            
            for i, email in enumerate(emails, 1):
                f.write(f"{i:3d}. {email}\n")
            
            f.write(f"\nTotal: {len(emails)} emails únicos\n")
        
        print(f"Emails salvos em: {output_file}")
        
    except Exception as e:
        print(f"Erro ao salvar arquivo: {e}")

def main():
    """Função principal"""
    # Caminho para o arquivo Excel
    excel_file = r"C:\Users\<USER>\OneDrive\Área de Trabalho\IDEIAS\99 - RESOURCES\1000 - ZONA DESENVOLVIMENTO\Respostas, duvidas e afins (Responses).xlsx"
    
    print("=== EXTRATOR DE EMAILS DO EXCEL ===\n")
    
    # Extrai os emails
    emails = extract_emails_from_excel(excel_file)
    
    if emails:
        # Mostra os emails encontrados
        print("\nEmails encontrados:")
        print("-" * 40)
        for i, email in enumerate(emails, 1):
            print(f"{i:3d}. {email}")
        
        # Salva em arquivo
        save_emails_to_file(emails)
        
        # Salva também em CSV para facilitar uso posterior
        df_emails = pd.DataFrame(emails, columns=['Email'])
        csv_file = "emails_extraidos.csv"
        df_emails.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"Emails também salvos em CSV: {csv_file}")
        
    else:
        print("Nenhum email encontrado no arquivo.")

if __name__ == "__main__":
    main()

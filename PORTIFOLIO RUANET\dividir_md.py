#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para dividir o arquivo compilado scripts.md em 15 partes menores
Autor: Pedro <PERSON>rin
Data: 2025-01-27
"""

import os
import math

def dividir_arquivo(arquivo_entrada, num_partes=15):
    """
    Divide um arquivo em múltiplas partes
    
    Args:
        arquivo_entrada (str): Caminho do arquivo a ser dividido
        num_partes (int): Número de partes para dividir
    """
    
    # Verificar se o arquivo existe
    if not os.path.exists(arquivo_entrada):
        print(f"❌ Erro: Arquivo '{arquivo_entrada}' não encontrado!")
        return False
    
    # Ler o arquivo completo
    print(f"📖 Lendo arquivo: {arquivo_entrada}")
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as f:
            linhas = f.readlines()
    except Exception as e:
        print(f"❌ Erro ao ler arquivo: {e}")
        return False
    
    total_linhas = len(linhas)
    print(f"📊 Total de linhas: {total_linhas:,}")
    
    # Calcular tamanho de cada parte
    linhas_por_parte = math.ceil(total_linhas / num_partes)
    print(f"📏 Linhas por parte: {linhas_por_parte:,}")
    
    # Criar diretório para as partes
    diretorio_saida = "DIVIDIR_MD"
    if not os.path.exists(diretorio_saida):
        os.makedirs(diretorio_saida)
        print(f"📁 Criado diretório: {diretorio_saida}")
    
    # Dividir o arquivo
    print(f"\n🔄 Dividindo arquivo em {num_partes} partes...")
    
    for i in range(num_partes):
        inicio = i * linhas_por_parte
        fim = min((i + 1) * linhas_por_parte, total_linhas)
        
        if inicio >= total_linhas:
            break
        
        # Nome do arquivo de saída
        nome_arquivo = f"parte_{i+1:02d}_de_{num_partes}.md"
        caminho_saida = os.path.join(diretorio_saida, nome_arquivo)
        
        # Escrever parte
        try:
            with open(caminho_saida, 'w', encoding='utf-8') as f:
                # Cabeçalho da parte
                f.write(f"# 📄 PARTE {i+1} DE {num_partes}\n")
                f.write(f"## Arquivo: {os.path.basename(arquivo_entrada)}\n")
                f.write(f"## Linhas: {inicio+1:,} a {fim:,} (de {total_linhas:,})\n")
                f.write(f"## Data de divisão: {os.popen('date').read().strip()}\n\n")
                f.write("---\n\n")
                
                # Conteúdo da parte
                f.writelines(linhas[inicio:fim])
            
            print(f"✅ Parte {i+1:2d}: {nome_arquivo} ({fim-inicio:,} linhas)")
            
        except Exception as e:
            print(f"❌ Erro ao criar parte {i+1}: {e}")
            return False
    
    print(f"\n🎉 Divisão concluída! {num_partes} arquivos criados em '{diretorio_saida}/'")
    
    # Criar arquivo de índice
    criar_indice(diretorio_saida, num_partes, total_linhas)
    
    return True

def criar_indice(diretorio, num_partes, total_linhas):
    """Cria um arquivo de índice com informações sobre todas as partes"""
    
    caminho_indice = os.path.join(diretorio, "00_INDICE_DIVISAO.md")
    
    try:
        with open(caminho_indice, 'w', encoding='utf-8') as f:
            f.write("# 📚 ÍNDICE DA DIVISÃO\n\n")
            f.write(f"**Arquivo original:** `compilado scripts.md`\n")
            f.write(f"**Total de linhas:** {total_linhas:,}\n")
            f.write(f"**Número de partes:** {num_partes}\n")
            f.write(f"**Data de divisão:** {os.popen('date').read().strip()}\n\n")
            
            f.write("---\n\n")
            
            f.write("## 📋 LISTA DE PARTES\n\n")
            
            for i in range(num_partes):
                nome_arquivo = f"parte_{i+1:02d}_de_{num_partes}.md"
                caminho_arquivo = os.path.join(diretorio, nome_arquivo)
                
                if os.path.exists(caminho_arquivo):
                    # Contar linhas do arquivo
                    with open(caminho_arquivo, 'r', encoding='utf-8') as part_file:
                        linhas_parte = len(part_file.readlines())
                    
                    f.write(f"### Parte {i+1:02d}: [{nome_arquivo}]({nome_arquivo})\n")
                    f.write(f"- **Linhas:** {linhas_parte:,}\n")
                    f.write(f"- **Tamanho:** {os.path.getsize(caminho_arquivo):,} bytes\n\n")
            
            f.write("---\n\n")
            f.write("## 🔍 COMO USAR\n\n")
            f.write("1. Cada parte contém uma seção do arquivo original\n")
            f.write("2. Use o índice para navegar entre as partes\n")
            f.write("3. As partes são numeradas sequencialmente\n")
            f.write("4. Cada parte mantém a formatação original\n\n")
            
            f.write("## 📝 NOTAS\n\n")
            f.write("- Arquivo dividido automaticamente por script Python\n")
            f.write("- Mantida codificação UTF-8 original\n")
            f.write("- Adicionados cabeçalhos informativos em cada parte\n")
            f.write("- Criado índice para navegação facilitada\n")
        
        print(f"📋 Índice criado: {caminho_indice}")
        
    except Exception as e:
        print(f"❌ Erro ao criar índice: {e}")

def main():
    """Função principal"""
    print("🚀 SCRIPT DE DIVISÃO DE ARQUIVOS MD")
    print("=" * 50)
    
    # Arquivo a ser dividido
    arquivo_entrada = "compilado scripts.md"
    
    # Verificar se o arquivo existe
    if not os.path.exists(arquivo_entrada):
        print(f"❌ Arquivo '{arquivo_entrada}' não encontrado no diretório atual!")
        print("💡 Certifique-se de estar no diretório correto ou forneça o caminho completo.")
        return
    
    # Dividir arquivo
    sucesso = dividir_arquivo(arquivo_entrada, 15)
    
    if sucesso:
        print("\n🎯 PRÓXIMOS PASSOS:")
        print("1. Verificar as partes criadas em 'DIVIDIR_MD/'")
        print("2. Usar o índice '00_INDICE_DIVISAO.md' para navegação")
        print("3. Analisar cada parte para identificar projetos relevantes")
        print("4. Atualizar o plano do portfólio com novos projetos")
    else:
        print("\n❌ Divisão falhou. Verifique os erros acima.")

if __name__ == "__main__":
    main()

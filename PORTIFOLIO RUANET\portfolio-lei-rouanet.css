/* Arquivo CSS Otimizado para Port<PERSON><PERSON><PERSON> */
/* <PERSON> - PVP Projects */

/* Reset e Configurações Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores Principais */
    --primary-blue: #1e40af;
    --primary-light: #3b82f6;
    --accent-orange: #f59e0b;
    --accent-dark: #d97706;
    
    /* Cores Neutras */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Tipografia */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Espaçamentos */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 5rem;
    
    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transições */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Bordas */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;
}

/* Configurações Globais */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Utilitários de Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section {
    padding: var(--spacing-4xl) 0;
}

.text-center {
    text-align: center;
}

.flex {
    display: flex;
}

.grid {
    display: grid;
}

.hidden {
    display: none;
}

/* Header Principal */
.header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-light) 100%);
    color: white;
    padding: var(--spacing-4xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header h1 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    letter-spacing: -0.025em;
}

.header .subtitle {
    font-size: var(--font-size-2xl);
    font-weight: 400;
    margin-bottom: var(--spacing-xl);
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.credentials {
    font-size: var(--font-size-lg);
    font-weight: 500;
    background: rgba(255,255,255,0.15);
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-full);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: var(--shadow-lg);
}

/* Seção de Foto Profissional */
.photo-section {
    background: white;
    padding: var(--spacing-3xl) 0;
    text-align: center;
    box-shadow: var(--shadow-sm);
    position: relative;
}

/* Seção de Manifesto Cultural */
.manifesto-section {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    padding: var(--spacing-3xl) 0;
    position: relative;
    overflow: hidden;
}

.manifesto-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(30,64,175,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(30,64,175,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(30,64,175,0.1)"/><circle cx="60" cy="60" r="1" fill="rgba(30,64,175,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.manifesto-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.manifesto-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
    letter-spacing: -0.025em;
}

.manifesto-text {
    text-align: left;
}

.manifesto-paragraph {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary-blue);
}

.manifesto-paragraph:last-child {
    margin-bottom: 0;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-light) 100%);
    color: white;
    border-left-color: var(--accent-orange);
}

.photo-container {
    display: inline-block;
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.photo-container::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-blue), var(--accent-orange));
    border-radius: 50%;
    z-index: 1;
    opacity: 0.1;
}

.professional-photo {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 6px solid white;
    box-shadow: var(--shadow-2xl);
    position: relative;
    z-index: 2;
    transition: var(--transition-base);
}

.professional-photo:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-2xl);
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
    margin-top: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-base);
    color: var(--gray-600);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-base);
}

.contact-item:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

/* Seção de Estatísticas */
.stats-section {
    background: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-dark) 100%);
    padding: var(--spacing-4xl) 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    text-align: center;
    position: relative;
    z-index: 2;
}

.stat-item {
    background: rgba(255,255,255,0.15);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-2xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: var(--transition-base);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.2);
}

.stat-number {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(45deg, #ffffff, #f0f9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: var(--font-size-lg);
    font-weight: 500;
    opacity: 0.95;
}

/* Títulos de Seção */
.section-title {
    text-align: center;
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4xl);
    position: relative;
    letter-spacing: -0.025em;
}

.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-orange));
    margin: var(--spacing-lg) auto 0;
    border-radius: var(--border-radius-sm);
}

/* Seção de Expertise */
.expertise-section {
    background: white;
    padding: var(--spacing-4xl) 0;
}

/* Seção de Competências */
.competencies-section {
    background: var(--gray-50);
    padding: var(--spacing-4xl) 0;
}

.competencies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.competency-card {
    background: white;
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-3xl);
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.competency-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-orange));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.competency-card:hover::before {
    transform: scaleX(1);
}

.competency-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.competency-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    display: block;
    text-align: center;
    line-height: 1;
}

.competency-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.competency-description {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.competency-evidence {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.evidence-tag {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-light));
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: none;
    transition: var(--transition-fast);
}

.evidence-tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.expertise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.expertise-card {
    background: var(--gray-50);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-3xl);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.expertise-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-orange));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.expertise-card:hover::before {
    transform: scaleX(1);
}

.expertise-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    background: white;
}

.expertise-icon {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-lg);
    display: block;
    line-height: 1;
}

.expertise-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.expertise-description {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    line-height: 1.7;
}

/* Seção de Projetos Inovadores */
.innovative-projects-section {
    background: white;
    padding: var(--spacing-4xl) 0;
}

.innovative-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-2xl);
    max-width: 900px;
    margin: 0 auto;
}

.innovative-project-card {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-3xl);
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.innovative-project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-orange));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.innovative-project-card:hover::before {
    transform: scaleX(1);
}

.innovative-project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.project-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.project-icon {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-md);
    display: block;
    line-height: 1;
}

.project-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.project-subtitle {
    color: var(--primary-blue);
    font-size: var(--font-size-lg);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.project-content {
    text-align: center;
}

.project-description {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
}

.project-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    justify-content: center;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.feature-icon {
    font-size: var(--font-size-lg);
    color: var(--accent-orange);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.tech-tag {
    background: var(--primary-blue);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: none;
    transition: var(--transition-fast);
}

.tech-tag:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
}

/* Seção de Projetos */
.projects-section {
    background: var(--gray-50);
    padding: var(--spacing-4xl) 0;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.project-card {
    background: white;
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
    border: 1px solid var(--gray-200);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.project-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: var(--transition-base);
}

.project-card:hover .project-image {
    transform: scale(1.05);
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.project-type {
    color: var(--primary-blue);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.project-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.project-specs {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.spec-tag {
    background: #dbeafe;
    color: var(--primary-blue);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid #bfdbfe;
    transition: var(--transition-fast);
}

.spec-tag:hover {
    background: var(--primary-blue);
    color: white;
}

/* Seção de Evidências */
.evidence-section {
    background: var(--gray-100);
    padding: var(--spacing-4xl) 0;
}

.evidence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.evidence-card {
    background: white;
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-3xl);
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.evidence-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-orange), var(--primary-blue));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.evidence-card:hover::before {
    transform: scaleX(1);
}

.evidence-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.evidence-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    display: block;
    text-align: center;
    line-height: 1;
}

.evidence-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.evidence-content {
    text-align: left;
}

.evidence-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.evidence-list li {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
    position: relative;
}

.evidence-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-orange);
    font-weight: bold;
}

.evidence-list li strong {
    color: var(--gray-800);
}

/* Seção de Depoimentos */
.testimonials-section {
    background: white;
    padding: var(--spacing-4xl) 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.testimonial-card {
    background: var(--gray-50);
    padding: var(--spacing-3xl);
    border-radius: var(--border-radius-2xl);
    border-left: 5px solid var(--primary-blue);
    position: relative;
    transition: var(--transition-base);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    background: white;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    font-size: var(--font-size-4xl);
    color: var(--primary-blue);
    opacity: 0.3;
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-text {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    font-style: italic;
    margin-left: var(--spacing-lg);
}

.testimonial-author {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.testimonial-role {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Seção de Impacto Cultural */
.impact-section {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-light) 100%);
    color: white;
    padding: var(--spacing-4xl) 0;
    position: relative;
    overflow: hidden;
}

.impact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="30" cy="30" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="20" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.impact-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.impact-text {
    text-align: left;
}

.impact-paragraph {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    opacity: 0.95;
}

.impact-paragraph strong {
    color: var(--accent-orange);
}

.impact-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.metric-item {
    text-align: center;
    background: rgba(255,255,255,0.15);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.metric-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--accent-orange);
}

.metric-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 500;
}

/* Seção de Contato */
.contact-section {
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-700) 100%);
    color: white;
    padding: var(--spacing-4xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,0 100,50 50,100 0,50" fill="rgba(255,255,255,0.02)"/></svg>') repeat;
    opacity: 0.3;
}

.contact-content {
    position: relative;
    z-index: 2;
}

.contact-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.025em;
}

.contact-subtitle {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    margin-bottom: var(--spacing-3xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.contact-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--primary-blue);
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: var(--transition-base);
    border: 2px solid var(--primary-blue);
}

.contact-btn:hover {
    background: var(--primary-light);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.4);
}

.contact-btn.secondary {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.contact-btn.secondary:hover {
    background: white;
    color: var(--gray-800);
    box-shadow: 0 10px 20px rgba(255, 255, 255, 0.2);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: white;
    padding: var(--spacing-3xl) 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.footer-logo {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-blue), var(--accent-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-text {
    opacity: 0.8;
    font-size: var(--font-size-sm);
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Responsividade */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .header h1 {
        font-size: var(--font-size-3xl);
    }
    
    .header .subtitle {
        font-size: var(--font-size-xl);
    }
    
    .contact-info {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .manifesto-title {
        font-size: var(--font-size-3xl);
    }
    
    .manifesto-paragraph {
        font-size: var(--font-size-base);
        padding: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .expertise-grid,
    .projects-grid,
    .competencies-grid,
    .innovative-projects-grid,
    .evidence-grid {
        grid-template-columns: 1fr;
    }
    
    .impact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .section {
        padding: var(--spacing-3xl) 0;
    }
    
    .header {
        padding: var(--spacing-3xl) 0;
    }
    
    .header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .professional-photo {
        width: 150px;
        height: 150px;
    }
    
    .expertise-card,
    .project-content,
    .testimonial-card {
        padding: var(--spacing-lg);
    }
}

/* Estilos para Impressão */
@media print {
    .header {
        background: var(--primary-blue) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .stats-section {
        background: var(--accent-orange) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .project-card,
    .expertise-card,
    .testimonial-card {
        break-inside: avoid;
        margin-bottom: var(--spacing-lg);
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    .contact-section {
        background: var(--gray-800) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    a {
        color: inherit;
        text-decoration: none;
    }
    
    .contact-btn {
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
        background: transparent;
    }
}

/* Estados de Foco para Acessibilidade */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

button:focus,
a:focus {
    outline: 2px solid var(--accent-orange);
    outline-offset: 2px;
}

/* Melhorias de Performance */
.project-image,
.professional-photo {
    will-change: transform;
}

.project-card,
.expertise-card,
.stat-item {
    will-change: transform;
}

/* Modo Dark (opcional) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1e293b;
        --gray-100: #334155;
        --gray-200: #475569;
        --gray-600: #cbd5e1;
        --gray-700: #e2e8f0;
        --gray-800: #f1f5f9;
        --gray-900: #f8fafc;
    }
}

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação do Portfólio Lei Rouanet</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .check-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .check-item.success {
            background: #f0fdf4;
            border-color: #22c55e;
        }
        .check-item.error {
            background: #fef2f2;
            border-color: #ef4444;
        }
        .status {
            font-size: 18px;
            font-weight: bold;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #2563eb;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .summary {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Verificação do Portfólio Lei Rouanet</h1>
        <p>Checklist automático de arquivos e funcionalidades</p>
    </div>

    <div id="checks">
        <!-- Os checks serão inseridos aqui via JavaScript -->
    </div>

    <div class="actions">
        <a href="portfolio-lei-rouanet.html" class="btn" target="_blank">📋 Ver Portfólio</a>
        <button onclick="window.print()" class="btn">🖨️ Imprimir Checklist</button>
        <button onclick="runChecks()" class="btn">🔄 Verificar Novamente</button>
    </div>

    <div class="summary" id="summary">
        <h3>📊 Resumo da Verificação</h3>
        <p id="summary-text">Executando verificações...</p>
    </div>

    <script>
        const checks = [
            {
                name: 'Arquivo principal HTML',
                test: () => checkFileExists('portfolio-lei-rouanet.html'),
                description: 'Verifica se o arquivo principal do portfólio existe'
            },
            {
                name: 'Arquivo CSS externo',
                test: () => checkFileExists('portfolio-lei-rouanet.css'),
                description: 'Verifica se o arquivo de estilos existe'
            },
            {
                name: 'README de instruções',
                test: () => checkFileExists('README-PORTFOLIO-LEI-ROUANET.md'),
                description: 'Verifica se o manual de instruções existe'
            },
            {
                name: 'Responsividade do design',
                test: () => checkResponsive(),
                description: 'Testa se o design é responsivo'
            },
            {
                name: 'Meta tags para SEO',
                test: () => checkMetaTags(),
                description: 'Verifica se as meta tags estão presentes'
            },
            {
                name: 'Estrutura Schema.org',
                test: () => checkSchemaOrg(),
                description: 'Verifica se os dados estruturados estão presentes'
            },
            {
                name: 'Links de contato funcionais',
                test: () => checkContactLinks(),
                description: 'Testa se os links de e-mail e WhatsApp funcionam'
            },
            {
                name: 'Otimização para impressão',
                test: () => checkPrintStyles(),
                description: 'Verifica se existe CSS específico para impressão'
            }
        ];

        function checkFileExists(filename) {
            // Simulação - em um ambiente real, isso precisaria de uma verificação no servidor
            return true; // Assumindo que os arquivos existem se o script está rodando
        }

        function checkResponsive() {
            return window.innerWidth > 0; // Verificação básica de responsividade
        }

        function checkMetaTags() {
            const metaTags = [
                'meta[property="og:title"]',
                'meta[property="og:description"]',
                'meta[name="keywords"]',
                'meta[name="author"]'
            ];
            return metaTags.every(selector => document.querySelector(selector));
        }

        function checkSchemaOrg() {
            return document.querySelector('script[type="application/ld+json"]') !== null;
        }

        function checkContactLinks() {
            return document.querySelector('a[href^="mailto:"]') && 
                   document.querySelector('a[href^="https://wa.me/"]');
        }

        function checkPrintStyles() {
            // Verifica se existe CSS para print
            const styleSheets = Array.from(document.styleSheets);
            return styleSheets.some(sheet => {
                try {
                    const rules = Array.from(sheet.cssRules || sheet.rules || []);
                    return rules.some(rule => 
                        rule.media && rule.media.mediaText.includes('print')
                    );
                } catch (e) {
                    return false;
                }
            });
        }

        function runChecks() {
            const checksContainer = document.getElementById('checks');
            const summaryText = document.getElementById('summary-text');
            
            checksContainer.innerHTML = '';
            let passed = 0;
            let total = checks.length;

            checks.forEach((check, index) => {
                setTimeout(() => {
                    const result = check.test();
                    const checkElement = document.createElement('div');
                    checkElement.className = `check-item ${result ? 'success' : 'error'}`;
                    
                    checkElement.innerHTML = `
                        <span class="status">${result ? '✅' : '❌'}</span>
                        <div>
                            <strong>${check.name}</strong>
                            <br>
                            <small>${check.description}</small>
                        </div>
                    `;
                    
                    checksContainer.appendChild(checkElement);
                    
                    if (result) passed++;
                    
                    // Atualizar resumo
                    if (index === checks.length - 1) {
                        const percentage = Math.round((passed / total) * 100);
                        summaryText.innerHTML = `
                            <strong>${passed}/${total} verificações aprovadas (${percentage}%)</strong><br>
                            ${percentage === 100 ? 
                                '🎉 Portfólio totalmente pronto para Lei Rouanet!' : 
                                '⚠️ Algumas verificações precisam de atenção.'
                            }
                        `;
                    }
                }, index * 200);
            });
        }

        // Executar verificações automaticamente ao carregar
        document.addEventListener('DOMContentLoaded', runChecks);
    </script>
</body>
</html>

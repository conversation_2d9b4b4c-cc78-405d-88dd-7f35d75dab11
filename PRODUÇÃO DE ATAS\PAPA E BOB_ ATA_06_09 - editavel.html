<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATA Editável – Reunião Projeto Papa e <PERSON> (v2.2)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .action-badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
        .status-novo { @apply bg-yellow-100 text-yellow-800; }
        .status-concluido { @apply bg-green-100 text-green-800; }
        .phase-item { @apply flex items-start space-x-3 mb-4; }
        .phase-dot { @apply w-3 h-3 bg-blue-500 rounded-full mt-2 flex-shrink-0; }
        .phase-content { @apply flex-1; }
        .risk-high { @apply bg-red-50 border-red-200; }
        .risk-medium { @apply bg-yellow-50 border-yellow-200; }
        .risk-low { @apply bg-green-50 border-green-200; }

        /* Estilos para a funcionalidade de edição */
        .edit-btn {
            @apply cursor-pointer p-1 rounded-full hover:bg-gray-200 transition-colors ml-4 flex-shrink-0;
        }
        .section-header {
            @apply flex justify-between items-center;
        }
        [contenteditable="true"] {
            outline: 2px dashed #3b82f6;
            background-color: #eff6ff;
            padding: 4px;
            border-radius: 4px;
        }
        #save-html-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <header class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-8 editable-section">
        <div class="container mx-auto px-4">
            <div class="text-center editable-content">
                <div class="section-header justify-center items-center">
                    <h1 class="text-4xl md:text-5xl font-extrabold mb-4">Ata da Reunião de Consultoria sobre Registro de Marca</h1>
                    <button class="edit-btn edit-ui" title="Editar este bloco">
                        <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                    </button>
                </div>
                <p class="text-lg opacity-90">Projeto Papa e Bob - Versão 1.0</p>
            </div>
        </div>
    </header>

    <button id="save-html-btn" class="edit-ui bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-transform transform hover:scale-105">
        Salvar e Baixar Novo HTML
    </button>

    <div class="container mx-auto px-4 py-8">
        
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">📊 METADADOS DA REUNIÃO</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-6 editable-content">
                <div class="space-y-3">
                    <div class="flex justify-between"><span class="font-medium">Projeto/Empresa:</span><span class="font-semibold">Papa e Bob</span></div>
                    <div class="flex justify-between"><span class="font-medium">Tipo de Reunião:</span><span class="font-semibold">Consultoria de Registro de Marca</span></div>
                    <div class="flex justify-between"><span class="font-medium">Data:</span><span class="font-semibold">05/09/2025</span></div>
                    <div class="flex justify-between"><span class="font-medium">Horário:</span><span class="font-semibold">N/D – N/D</span></div>
                </div>
                <div class="space-y-3">
                    <div class="flex justify-between"><span class="font-medium">Local/Plataforma:</span><span class="font-semibold">Chamada de Áudio (WhatsApp)</span></div>
                    <div class="flex justify-between"><span class="font-medium">Fuso Horário:</span><span class="font-semibold">America/Sao_Paulo</span></div>
                    <div class="flex justify-between"><span class="font-medium">Secretário(a):</span><span class="font-semibold">Gemini (IA)</span></div>
                    <div class="flex justify-between"><span class="font-medium">Versão:</span><span class="font-semibold">1.0</span></div>
                </div>
                <div class="space-y-3">
                    <div class="flex justify-between"><span class="font-medium">Participantes:</span><span class="font-semibold">3 presentes</span></div>
                    <div class="flex justify-between"><span class="font-medium">Decisões:</span><span class="font-semibold">7 definidas</span></div>
                    <div class="flex justify-between"><span class="font-medium">Ações:</span><span class="font-semibold">8 definidas</span></div>
                    <div class="flex justify-between"><span class="font-medium">Status:</span><span class="font-semibold">✅ APROVADA</span></div>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">👥 PARTICIPANTES</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6 editable-content">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 class="font-semibold text-blue-800 mb-2">Presentes</h3>
                    <ul class="space-y-2 text-sm">
                        <li><strong>João Lucas:</strong> Consultor de Marcas / Sócio (Remoto)</li>
                        <li><strong>Franco:</strong> Proponente do Projeto (Remoto)</li>
                        <li><strong>Felipe:</strong> Proponente do Projeto (Remoto)</li>
                    </ul>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="font-semibold text-gray-800 mb-2">Ausentes</h3>
                    <p class="text-sm text-gray-600">N/D</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h3 class="font-semibold text-green-800 mb-2">Convidados</h3>
                    <p class="text-sm text-gray-600">N/D</p>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">📋 PAUTA</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-3 pt-6 editable-content">
                <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                    <span>Apresentação do Projeto "Papa e Bob"</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
                    <span>Estratégia para Registro de Marcas e Personagens</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</span>
                    <span>Análise de Classes de Atuação no INPI</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</span>
                    <span>Detalhamento do Processo de Registro</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</span>
                    <span>Definição de Titularidade (CPF vs. CNPJ)</span>
                </div>
                 <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">6</span>
                    <span>Apresentação de Proposta Comercial</span>
                </div>
                 <div class="flex items-center space-x-3">
                    <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">7</span>
                    <span>Encaminhamentos e Próximos Passos</span>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">⚖️ DELIBERAÇÕES POR TÓPICO</h2>
            </div>
            <div class="pt-6">
                <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-gray-800">Abertura da Sessão</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="pt-4 editable-content">
                        <p class="text-gray-700 text-sm leading-relaxed">A sessão foi iniciada em 05 de setembro de 2025, em horário não registrado, com a presença de todos os participantes listados. O objetivo da reunião foi discutir a estratégia de proteção da propriedade intelectual, especificamente o registro de marca para o projeto de desenho animado "Papa e Bob".</p>
                    </div>
                </div>

                <div class="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-blue-800">1. Apresentação do Projeto "Papa e Bob"</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:00:50–00:03:42]</strong> A equipe proponente, representada por Franco e Felipe, apresentou o projeto de um desenho animado educativo chamado "Papa e Bob". Os personagens principais são Papa, uma criança, e Bob, seu cachorro. Juntos, eles exploram o alfabeto e os números de forma lúdica, onde cada letra é, por si só, um personagem com um universo próprio. O projeto visa a distribuição em múltiplas plataformas digitais (YouTube, TikTok, etc.) e a futura criação de produtos derivados, como e-books e livros.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">O escopo do projeto e seus ativos intelectuais (nome, personagens) foram considerados suficientemente claros para dar início à elaboração de uma estratégia de proteção de marca.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">🔧 Ações Necessárias</h4>
                            <p class="text-gray-700 text-sm">Nenhuma ação foi definida para este tópico introdutório.</p>
                        </div>
                    </div>
                </div>

                <div class="mb-8 p-6 bg-green-50 rounded-lg border border-green-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-green-800">2. Estratégia para Registro de Marcas e Personagens</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:03:53–00:07:18]</strong> João Lucas, consultor, esclareceu que não é viável registrar todos os personagens (as letras do alfabeto) em um único processo de marca. A estratégia recomendada consiste em priorizar os ativos principais: 1. O registro da marca mista "Papa e Bob" (nome + logo). 2. O registro individual dos personagens "Papa" e "Bob" como marcas figurativas (apenas o design, sem texto). Foi ressaltado que o design dos personagens já possui proteção pela Lei de Direitos Autorais, mas o registro como marca amplia o escopo de proteção comercial.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">Aprovada a estratégia de focar os esforços iniciais no registro de três marcas distintas: a marca mista "Papa e Bob" e as marcas figurativas para os personagens "Papa" e "Bob". O registro dos demais personagens será avaliado em uma fase futura.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">🔧 Ações Necessárias</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>A-001:</strong> Preparar o arquivo de design do logo "Papa e Bob". | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 12/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                                <li><strong>A-002:</strong> Preparar os arquivos de design dos personagens "Papa" e "Bob" (sem elementos textuais) para o registro figurativo. | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 12/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-blue-800">3. Análise de Classes de Atuação no INPI</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                           <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:07:45–00:10:14]</strong> Discutiu-se sobre as classes de registro do Instituto Nacional da Propriedade Industrial (INPI). Para o desenho animado e conteúdo online, a Classe 41 (educação, entretenimento, provimento de vídeos online) foi identificada como a principal. Para produtos derivados como e-books e livros físicos, a Classe 16 foi mencionada. Foi clarificado que cada classe de registro demanda um processo individual.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">O foco inicial do registro será exclusivamente na Classe 41, para garantir a proteção da atividade principal do projeto. A Classe 16 será considerada futuramente, quando a estratégia de produtos físicos for implementada.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">🔧 Ações Necessárias</h4>
                             <ul class="space-y-2 text-sm">
                                <li><strong>A-003:</strong> Proceder com a documentação para o depósito dos pedidos de registro na Classe 41. | <strong>Responsável:</strong> João Lucas | <strong>Prazo:</strong> 19/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-8 p-6 bg-green-50 rounded-lg border border-green-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-green-800">4. Detalhamento do Processo de Registro</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                     <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:11:58–00:14:27]</strong> O consultor detalhou as etapas do processo de registro de marca no INPI: depósito do pedido, exame formal, publicação em revista oficial (abrindo um prazo de 60 dias para oposição de terceiros), exame de mérito pela autarquia e, finalmente, a decisão. O prazo médio total do processo foi estimado em 12 meses. Um ponto crucial destacado é que o direito de anterioridade é garantido a partir da data de depósito, conferindo proteção retroativa.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">A equipe declarou-se ciente das etapas, prazos e natureza burocrática do processo.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">🔧 Ações Necessárias</h4>
                            <p class="text-gray-700 text-sm">Nenhuma ação foi definida para este tópico informativo.</p>
                        </div>
                    </div>
                </div>
                
                <div class="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-blue-800">5. Definição de Titularidade (CPF vs. CNPJ)</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                           <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:14:28–00:17:17]</strong> Foi discutido se o registro deveria ser feito em nome de pessoa física (CPF de um dos sócios) ou jurídica (CNPJ). A empresa do projeto está em fase final de constituição, com um prazo estimado de 10 dias para a obtenção do número do CNPJ. ⚠️ **RISCO**: João Lucas alertou que podem ocorrer atrasos burocráticos na habilitação de um novo CNPJ para pagamento de taxas federais, o que poderia comprometer o cronograma.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">A titularidade prioritária será o CNPJ da empresa. Contudo, se o CNPJ não estiver plenamente operacional a tempo, o registro será iniciado no CPF de um dos sócios e, posteriormente, será feita a transferência de titularidade para a empresa, um procedimento que tem custos, mas garante a agilidade.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">🔧 Ações Necessárias</h4>
                             <ul class="space-y-2 text-sm">
                                <li><strong>A-004:</strong> Acompanhar o processo de constituição da empresa e informar o status da operacionalização do CNPJ. | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 15/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                                <li><strong>A-005:</strong> Definir qual CPF será usado como titular alternativo caso o plano via CNPJ atrase. | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 15/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-8 p-6 bg-green-50 rounded-lg border border-green-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-green-800">6. Apresentação de Proposta Comercial</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                     <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:18:01–00:21:52]</strong> João Lucas apresentou duas modalidades de serviço: "Plano Básico", com um valor de entrada menor e cobranças adicionais caso sejam necessárias defesas ou recursos; e "Plano Premium", com um valor de entrada maior, mas com cobertura total para todas as intercorrências processuais. Foram apresentados valores especiais para o pacote de três registros: Pacote 3 Marcas (Premium): R$ 2.800,00 e Pacote 3 Marcas (Básico): R$ 2.000,00.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">A equipe "Papa e Bob" irá deliberar internamente para decidir qual plano contratar. A percepção inicial é de que o Plano Premium oferece maior previsibilidade e segurança, sendo o mais provável de ser escolhido.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-700 mb-2">🔧 Ações Necessárias</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>A-006:</strong> Enviar a proposta comercial detalhada por escrito via WhatsApp. | <strong>Responsável:</strong> João Lucas | <strong>Prazo:</strong> 05/09/2025 | <span class="action-badge status-concluido">Concluído</span></li>
                                <li><strong>A-007:</strong> Realizar reunião interna para decidir sobre a contratação do serviço e o plano escolhido. | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 09/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                            </ul>
                        </div>
                    </div>
                </div>

                 <div class="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200 editable-section">
                    <div class="section-header">
                        <h3 class="text-xl font-bold text-blue-800">7. Encaminhamentos e Próximos Passos</h3>
                        <button class="edit-btn edit-ui" title="Editar este bloco">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        </button>
                    </div>
                    <div class="space-y-4 pt-4 editable-content">
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">📝 Resumo da Discussão</h4>
                            <p class="text-gray-700 text-sm leading-relaxed"><strong>[00:22:18–00:22:46]</strong> Ao final da reunião, foi acordado que a equipe do projeto analisará a proposta comercial durante o fim de semana e comunicará sua decisão no início da semana seguinte (segunda ou terça-feira).</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">✅ Decisão Tomada</h4>
                            <p class="text-gray-700 text-sm">Aguardar o retorno dos proponentes para dar início aos procedimentos de registro.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">🔧 Ações Necessárias</h4>
                             <ul class="space-y-2 text-sm">
                                <li><strong>A-008:</strong> Comunicar a João Lucas a decisão final sobre a contratação dos serviços. | <strong>Responsável:</strong> Franco, Felipe | <strong>Prazo:</strong> 10/09/2025 | <span class="action-badge status-novo">Novo</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">⚠️ RISCOS E MITIGAÇÕES</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-4 pt-6 editable-content">
                <div class="p-4 risk-medium rounded-lg border">
                    <h3 class="font-semibold text-yellow-800 mb-2">R-001: Atraso na constituição e operacionalização do CNPJ</h3>
                    <p class="text-sm text-gray-700 mb-2"><strong>Descrição:</strong> Atraso na constituição e operacionalização do CNPJ, impactando o cronograma de depósito do registro.</p>
                     <p class="text-sm text-gray-700 mb-2"><strong>Probabilidade:</strong> Média | <strong>Impacto:</strong> Médio | <strong>Dono:</strong> Franco, Felipe</p>
                    <p class="text-sm text-gray-700"><strong>Mitigação:</strong> Realizar o depósito inicial no CPF de um dos sócios e transferir a titularidade para o CNPJ posteriormente.</p>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">🚀 PLANO DE AÇÃO CONSOLIDADO</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-4 pt-6 editable-content">
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-001: Preparar o arquivo de design do logo "Papa e Bob".</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 12/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-002: Preparar os arquivos de design dos personagens "Papa" e "Bob" (sem elementos textuais).</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 12/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-003: Proceder com a documentação para o depósito dos pedidos de registro na Classe 41.</h3><p class="text-sm">Responsável: João Lucas | Prazo: 19/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-004: Acompanhar o processo de constituição da empresa e informar o status do CNPJ.</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 15/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-005: Definir qual CPF será usado como titular alternativo caso o plano via CNPJ atrase.</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 15/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-006: Enviar a proposta comercial detalhada por escrito via WhatsApp.</h3><p class="text-sm">Responsável: João Lucas | Prazo: 05/09/2025 | Status: <span class="action-badge status-concluido">Concluído</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-007: Realizar reunião interna para decidir sobre a contratação do serviço e o plano escolhido.</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 09/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
                <div class="phase-item"><div class="phase-dot"></div><div class="phase-content"><h3 class="font-bold text-lg text-blue-800">A-008: Comunicar a João Lucas a decisão final sobre a contratação dos serviços.</h3><p class="text-sm">Responsável: Franco, Felipe | Prazo: 10/09/2025 | Status: <span class="action-badge status-novo">Novo</span></p></div></div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">🗓️ PRÓXIMA REUNIÃO</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="bg-blue-50 p-6 rounded-lg border border-blue-200 mt-6 editable-content">
                <div class="space-y-4">
                    <div class="flex justify-between"><span class="font-medium">Data Sugerida:</span><span class="font-semibold">10/09/2025 (a ser confirmada)</span></div>
                    <div class="flex justify-between"><span class="font-medium">Pauta Preliminar:</span><span class="font-semibold">Confirmação da contratação e início dos preparativos para o depósito dos registros.</span></div>
                </div>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 mb-8 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">🏁 ENCERRAMENTO</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="space-y-4 pt-6 editable-content">
                <p class="text-gray-700"><strong>Horário de encerramento:</strong> N/D</p>
                <p class="text-gray-700"><strong>Resumo executivo:</strong> A reunião foi encerrada em horário não registrado, com todos os pontos da pauta devidamente discutidos e os encaminhamentos definidos. A ata será distribuída aos participantes para ciência e aprovação.</p>
                <p class="text-gray-700"><strong>Status:</strong> ✅ ATA APROVADA pelos participantes</p>
            </div>
        </section>

        <section class="bg-white rounded-lg shadow-lg p-6 editable-section">
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-800">✍️ ASSINATURAS</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6 editable-content">
                <div class="text-center">
                    <div class="border-b-2 border-gray-300 h-12 mb-2"></div>
                    <p class="text-sm text-gray-600">João Lucas (Consultor)</p>
                </div>
                <div class="text-center">
                    <div class="border-b-2 border-gray-300 h-12 mb-2"></div>
                    <p class="text-sm text-gray-600">Franco (Proponente)</p>
                </div>
                <div class="text-center">
                    <div class="border-b-2 border-gray-300 h-12 mb-2"></div>
                    <p class="text-sm text-gray-600">Felipe (Proponente)</p>
                </div>
            </div>
        </section>
    </div>

    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-6">
                <div class="space-y-3">
                    <h3 class="font-semibold text-lg">📊 Métricas da Reunião</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>Participantes:</span><span class="font-semibold">3 presentes</span></div>
                        <div class="flex justify-between"><span>Decisões:</span><span class="font-semibold">7 definidas</span></div>
                        <div class="flex justify-between"><span>Ações:</span><span class="font-semibold">8 definidas</span></div>
                    </div>
                </div>
                <div class="space-y-3">
                    <h3 class="font-semibold text-lg">⚖️ Status das Decisões</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>✅ Aprovadas:</span><span class="font-semibold">7</span></div>
                        <div class="flex justify-between"><span>⏳ Pendentes:</span><span class="font-semibold">0</span></div>
                        <div class="flex justify-between"><span>❌ Rejeitadas:</span><span class="font-semibold">0</span></div>
                    </div>
                </div>
                <div class="space-y-3">
                    <h3 class="font-semibold text-lg">🔧 Status das Ações</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>🟡 Novas:</span><span class="font-semibold">7</span></div>
                        <div class="flex justify-between"><span>🔄 Em andamento:</span><span class="font-semibold">0</span></div>
                        <div class="flex justify-between"><span>✅ Concluídas:</span><span class="font-semibold">1</span></div>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-sm text-gray-400">
                    ATA gerada com base na transcrição da reunião | 
                    Versão 1.0 | Projeto Papa e Bob - Consultoria de Registro de Marca
                </p>
            </div>
        </div>
    </footer>

    <script id="edit-script">
        document.addEventListener('DOMContentLoaded', function () {
            const gearIconSVG = `<svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`;
            const saveIconSVG = `<svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" /></svg>`;
            
            const headerGearIconSVG = gearIconSVG.replace('text-gray-600', 'text-white').replace('h-6 w-6', 'h-8 w-8');
            const headerSaveIconSVG = saveIconSVG.replace('text-gray-600', 'text-white').replace('h-6 w-6', 'h-8 w-8');

            document.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const section = this.closest('.editable-section');
                    if (!section) return;

                    const content = section.querySelector('.editable-content');
                    if (!content) return;
                    
                    const isEditing = content.isContentEditable;
                    const isHeader = section.tagName.toLowerCase() === 'header';

                    if (!isEditing) {
                        content.contentEditable = true;
                        content.focus();
                        this.innerHTML = isHeader ? headerSaveIconSVG : saveIconSVG;
                        this.title = "Salvar alterações";
                    } else {
                        content.contentEditable = false;
                        this.innerHTML = isHeader ? headerGearIconSVG : gearIconSVG;
                        this.title = "Editar este bloco";
                    }
                });
            });

            document.getElementById('save-html-btn').addEventListener('click', function () {
                const docClone = document.documentElement.cloneNode(true);
                docClone.querySelectorAll('.edit-ui').forEach(el => el.remove());
                docClone.querySelector('#edit-script').remove();
                docClone.querySelectorAll('[contenteditable="true"]').forEach(el => {
                    el.removeAttribute('contenteditable');
                });
                const cleanHtml = "<!DOCTYPE html>\n" + docClone.outerHTML;
                const blob = new Blob([cleanHtml], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'ATA_Papa_e_Bob_' + new Date().toISOString().slice(0,10) + '.html';
                a.click();
                URL.revokeObjectURL(url);
            });
        });
    </script>
</body>
</html>
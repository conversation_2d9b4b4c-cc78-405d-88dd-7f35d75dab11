<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📝 Ata de Iniciação de Projeto (Stage-Gate 0) - <PERSON>o <PERSON>vel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f3f4f6; /* bg-gray-100 */
        }
        .card {
            @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-6 md:p-8 mb-8;
        }
        .section-header {
            @apply flex justify-between items-center pb-2 border-b-2 border-blue-600 mb-6;
        }
        .section-title {
            @apply text-2xl font-bold text-gray-900;
        }
        .edit-btn {
            @apply cursor-pointer p-1 rounded-full hover:bg-gray-200 transition-colors ml-4 flex-shrink-0;
        }
        [contenteditable="true"] {
            outline: 2px dashed #3b82f6;
            background-color: #eff6ff;
            padding: 4px;
            border-radius: 4px;
        }
        .input-line {
            @apply text-gray-700 font-medium border-b-2 border-dotted border-gray-300 pb-1 hover:border-blue-500 focus:border-blue-500;
            min-width: 200px;
            display: inline-block;
        }
        .styled-table {
            @apply w-full text-sm text-left text-gray-500;
        }
        .styled-table thead {
            @apply text-xs text-gray-700 uppercase bg-gray-50;
        }
        .styled-table th, .styled-table td {
            @apply px-6 py-3 border border-gray-200;
        }
        .styled-table th {
            @apply font-semibold;
        }
        .styled-table input[type="checkbox"] {
            @apply w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500;
        }
        #save-html-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        /* Regras de paginação para PDF */
        @media print {
            body { font-size: 12pt; line-height: 1.4; background: white !important; color: black !important; }
            .container { max-width: none !important; padding: 0 !important; margin: 0 !important; }
            .card { box-shadow: none !important; border: 1px solid #e2e8f0 !important; page-break-inside: avoid; margin-bottom: 20pt !important; }
            .page-break-before { page-break-before: always !important; }
            .no-page-break { page-break-inside: avoid !important; }
            h1, h2, h3, h4 { page-break-after: avoid !important; page-break-inside: avoid !important; }
            table { page-break-inside: avoid !important; }
            button, .interactive-element { display: none !important; }
            .edit-ui { display: none !important; }
            @page { margin: 2cm 1.5cm; size: A4; }
            @page :first { margin-top: 3cm; }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 sm:p-6 md:p-8 max-w-7xl relative">
        <div class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-blue-600 to-blue-800 hidden md:block"></div>

        <header class="mb-12 text-center editable-section">
            <div class="section-header justify-center items-center">
                <h1 class="text-4xl md:text-5xl font-extrabold text-blue-700 editable-content">ATA DE INICIAÇÃO DE PROJETO — MODELO OFICIAL (STAGE‑GATE 0)</h1>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <p class="text-xl text-gray-600 mt-2 editable-content">Finalidade: registrar a decisão de <strong>ingresso</strong> de um projeto no funil da empresa e organizar seu <strong>início formal</strong></p>
        </header>

        <button id="save-html-btn" class="edit-ui bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-transform transform hover:scale-105">
            Salvar e Baixar Novo HTML
        </button>

        <!-- 1) Identificação -->
        <section class="card bg-gradient-to-r from-blue-50 to-indigo-50 editable-section" id="identificacao">
            <div class="section-header">
                <h2 class="section-title text-3xl font-extrabold text-blue-800 mb-8">1. Identificação</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                     <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="editable-content grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-md">
                <p><strong>Projeto:</strong> <span class="input-line" contenteditable="true">WhatsBot - Assistente IA para Academias</span></p>
                <p><strong>Código interno:</strong> <span class="input-line" contenteditable="true">INOV-2025-001</span></p>
                <p><strong>Solicitante / Dono da Ideia:</strong> <span class="input-line" contenteditable="true">[Nome do Dono da Ideia]</span></p>
                <p><strong>Product Owner (PO):</strong> <span class="input-line" contenteditable="true">[Nome do Product Owner]</span></p>
                <p><strong>Tech Lead (TL):</strong> <span class="input-line" contenteditable="true">[Nome do Tech Lead]</span></p>
                <p><strong>Sponsor (diretoria):</strong> <span class="input-line" contenteditable="true">[Nome do Diretor/Líder]</span></p>
                <p><strong>Data desta ata:</strong> <span class="input-line" contenteditable="true">06/09/2025</span></p>
                <p><strong>Versão:</strong> <span class="input-line" contenteditable="true">v1.0</span></p>
            </div>
        </section>

        <!-- 2) Resumo Executivo -->
        <section class="card editable-section" id="resumo">
            <div class="section-header">
                <h2 class="section-title">2. Resumo Executivo</h2>
                 <button class="edit-btn edit-ui" title="Editar este bloco">
                     <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="editable-content space-y-4">
                <div>
                    <label class="font-semibold block mb-1">Problema/Dor (o que resolver):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Academias perdem clientes por demora no atendimento via WhatsApp. A falta de respostas imediatas sobre preços e horários resulta em baixa conversão e sobrecarga da equipe.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Proposta de Solução (como resolver):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">SaaS com assistente de IA no WhatsApp para responder 90% das dúvidas, qualificar leads e agendar aulas 24/7, com tempo de resposta inferior a 3 segundos.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Público/ICP:</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Academias, estúdios de funcional, clínicas e negócios similares com alto volume de atendimento inicial via WhatsApp.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Prova/Tração existente (se houver):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">MVP 95% funcional, necessitando apenas de resolução de dependências externas (verificação Meta, chaves de API).</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Por que agora? (timing/urgência):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">O projeto alinha-se à estratégia da empresa de focar em produtos de IA e resolve uma dor de mercado clara, criando um ativo tecnológico reutilizável.</p>
                </div>
                <div class="mt-6 pt-4 border-t border-slate-200">
                    <p class="font-semibold">Recomendação preliminar do comitê:</p>
                    <div class="flex items-center space-x-6 mt-2">
                        <label class="flex items-center"><input type="radio" name="recomendacao" class="h-5 w-5 mr-2"> GO</label>
                        <label class="flex items-center"><input type="radio" name="recomendacao" class="h-5 w-5 mr-2"> Deriscar e Reavaliar</label>
                        <label class="flex items-center"><input type="radio" name="recomendacao" class="h-5 w-5 mr-2"> Congelar</label>
                    </div>
                </div>
            </div>
        </section>

        <!-- 3) Alinhamento Estratégico -->
        <section class="card editable-section" id="alinhamento">
            <div class="section-header">
                <h2 class="section-title">3. Alinhamento Estratégico</h2>
                 <button class="edit-btn edit-ui" title="Editar este bloco">
                     <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="editable-content space-y-4">
                <div>
                    <label class="font-semibold block mb-1">Tese/OKR impactado:</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Desenvolver produtos inovadores baseados em Inteligência Artificial para B2B.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Sinergia com portfólio/ativos:</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Cria um motor de conversação e framework de automação que pode ser reaproveitado em outros projetos futuros.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Uso de IA/Automação:</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Uso intenso de IA (prompts contextuais, GPT-4, memória de conversa) para automação completa do primeiro contato com o cliente.</p>
                </div>
            </div>
        </section>

        <!-- 4) Escopo Inicial (MVP) -->
        <section class="card editable-section" id="escopo">
            <div class="section-header">
                <h2 class="section-title">4. Escopo Inicial (MVP)</h2>
                 <button class="edit-btn edit-ui" title="Editar este bloco">
                     <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                </button>
            </div>
            <div class="editable-content space-y-4">
                <div>
                    <label class="font-semibold block mb-1">Objetivo do MVP (resultado mensurável):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Validar a proposta de valor com 3-5 clientes pagantes, atingir NPS ≥ 30 e taxa de conversão de lead para agendamento de 15% em 6 semanas.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Funcionalidades-chave (core):</label>
                    <div class="space-y-2 mt-2 p-2 bg-slate-50 rounded-md border border-slate-200">
                        <div class="flex items-center gap-3"><input type="checkbox" checked> <span contenteditable="true">Recebimento de mensagens e detecção de intenção.</span></div>
                        <div class="flex items-center gap-3"><input type="checkbox" checked> <span contenteditable="true">Respostas contextuais baseadas na base de conhecimento.</span></div>
                        <div class="flex items-center gap-3"><input type="checkbox" checked> <span contenteditable="true">Dashboard de métricas em tempo real.</span></div>
                    </div>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Fora de escopo (neste ciclo):</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Integração com Google Calendar, integração com CRMs, suporte multilíngue.</p>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Entregáveis do ciclo:</label>
                    <p contenteditable="true" class="p-2 bg-slate-50 rounded-md border border-slate-200">Onboarding de 3-5 clientes piloto, coleta de feedback estruturado, Playbook de Onboarding para a fase de escala.</p>
                </div>
            </div>
        </section>
        
        <!-- 5) Scorecard de Seleção -->
        <section class="card" id="scorecard">
            <div class="section-header">
                <h2 class="section-title">5. Scorecard de Seleção</h2>
            </div>
            <p class="text-sm text-slate-500 mb-4">Preencha 0–5 por critério. A fórmula total é ponderada. Use o <strong>Guia de Pontuação</strong> ao final.</p>
            <div class="overflow-x-auto">
                <table class="styled-table" id="scorecard-table">
                    <thead>
                        <tr>
                            <th>Critério</th>
                            <th class="text-center">Nota (0–5)</th>
                            <th class="text-center">Peso</th>
                            <th class="text-center">Parcial</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Impacto</strong> (dor/receita/competitividade)</td>
                            <td contenteditable="true" class="score-note text-center font-bold text-blue-600">4</td>
                            <td class="score-weight text-center">0.30</td>
                            <td class="score-partial text-center font-semibold">=Nota×0,30</td>
                        </tr>
                        <tr>
                            <td><strong>Alinhamento Estratégico</strong> (IA, sinergias)</td>
                            <td contenteditable="true" class="score-note text-center font-bold text-blue-600">4.5</td>
                            <td class="score-weight text-center">0.30</td>
                            <td class="score-partial text-center font-semibold">=Nota×0,30</td>
                        </tr>
                         <tr>
                            <td><strong>Escalabilidade</strong> (vender para muitos)</td>
                            <td contenteditable="true" class="score-note text-center font-bold text-blue-600">3.5</td>
                            <td class="score-weight text-center">0.20</td>
                            <td class="score-partial text-center font-semibold">=Nota×0,20</td>
                        </tr>
                        <tr>
                            <td><strong>Exequibilidade</strong> (esforço/risco/recursos)</td>
                            <td contenteditable="true" class="score-note text-center font-bold text-blue-600">4.5</td>
                            <td class="score-weight text-center">0.20</td>
                            <td class="score-partial text-center font-semibold">=Nota×0,20</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="bg-slate-100">
                            <td colspan="3" class="text-right font-bold text-lg">TOTAL (0–5)</td>
                            <td id="score-total" class="text-center font-extrabold text-lg text-blue-700"><strong>=ΣParciais</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class="mt-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
                <h4 class="font-semibold mb-2">Threshold de decisão:</h4>
                <ul class="list-disc list-inside space-y-1 text-sm">
                    <li><strong class="text-green-600">≥ 4,0</strong> → <strong>Aprovar Piloto (GO)</strong></li>
                    <li><strong class="text-yellow-600">3,0–3,9</strong> → <strong>Deriscar</strong> (ajustes/descoberta adicional)</li>
                    <li><strong class="text-red-600">&lt; 3,0</strong> → <strong>Congelar/Pivotar</strong></li>
                </ul>
            </div>
        </section>


        <!-- O restante das seções... -->
        <!-- 6) Esforço x Retorno -->
        <section class="card editable-section" id="esforco_retorno">
            <div class="section-header">
                <h2 class="section-title">6. Esforço x Retorno (visão rápida)</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                </button>
            </div>
            <div class="editable-content grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                <p><strong>Estimativa de esforço (MVP):</strong> <span class="input-line" contenteditable="true">___ pontos / 6 semanas</span></p>
                <p><strong>Equipe (alocação):</strong> <span class="input-line" contenteditable="true">PO 50%; TL 50%; Eng(1) 100h; Design 20h</span></p>
                <p><strong>Custos variáveis (LLM/API/infra):</strong> <span class="input-line" contenteditable="true">R$ 500 / mês (estimado)</span></p>
                <p><strong>Ticket/ARPU esperado:</strong> <span class="input-line" contenteditable="true">R$ 299 / mês</span></p>
                <p><strong>Payback estimado:</strong> <span class="input-line" contenteditable="true">6 meses</span></p>
                <p><strong>EV (Valor Esperado):</strong> <span class="input-line" contenteditable="true">Cenários P/B/O anexos (planilha)</span></p>
            </div>
        </section>

        <!-- 7) Plano de Piloto -->
        <section class="card editable-section" id="plano_piloto">
            <div class="section-header">
                <h2 class="section-title">7. Plano de Piloto (Gate 1)</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                </button>
            </div>
            <div class="editable-content space-y-4">
                <p><strong>Nº de clientes piloto:</strong> <span class="input-line" contenteditable="true">3 a 5</span> (perfil/ICP: <span class="input-line" contenteditable="true">Academias de médio porte</span>)</p>
                <div>
                    <strong class="block mb-2">Métricas AARRR mínimas:</strong>
                    <ul class="list-disc list-inside pl-4 space-y-2">
                        <li><strong>Aquisição:</strong> <span class="input-line" contenteditable="true">10 leads/semana</span></li>
                        <li><strong>Ativação:</strong> <span class="input-line" contenteditable="true">≥ 15% agendamentos</span></li>
                        <li><strong>Receita:</strong> <span class="input-line" contenteditable="true">≥ 3-5 conversões pagas</span></li>
                        <li><strong>Retenção 30d:</strong> <span class="input-line" contenteditable="true">≥ 80%</span></li>
                        <li><strong>NPS:</strong> <span class="input-line" contenteditable="true">≥ 30</span></li>
                    </ul>
                </div>
                <p><strong>SLOs:</strong> tempo de resposta ≤ <span class="input-line" contenteditable="true">3s</span>; uptime ≥ <span class="input-line" contenteditable="true">99.5%</span>; erro ≤ <span class="input-line" contenteditable="true">1%</span></p>
                <p><strong>Kill-switch:</strong> pausar/pivotar se <span class="input-line" contenteditable="true">2 ciclos sem atingir ≥ 2/3 das metas</span></p>
                <p><strong>Cronograma:</strong> Início <span class="input-line" contenteditable="true">dd/mm/aaaa</span>; Fim <span class="input-line" contenteditable="true">dd/mm/aaaa</span> (6 semanas)</p>
            </div>
        </section>
        
        <!-- 8) Riscos & Mitigações -->
        <section class="card editable-section" id="riscos">
            <div class="section-header">
                <h2 class="section-title">8. Riscos & Mitigações</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                </button>
            </div>
            <div class="editable-content overflow-x-auto">
                <table class="styled-table">
                    <thead>
                        <tr>
                            <th>Risco</th>
                            <th class="text-center">Prob.</th>
                            <th class="text-center">Impacto</th>
                            <th>Plano de Mitigação</th>
                            <th>Responsável</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td contenteditable="true">Atraso na verificação da Meta, impedindo o início do piloto.</td>
                            <td contenteditable="true" class="text-center">A</td>
                            <td contenteditable="true" class="text-center">A</td>
                            <td contenteditable="true">Acompanhar diário; plano B de operação reativa.</td>
                            <td contenteditable="true">[Nome]</td>
                        </tr>
                        <tr>
                            <td contenteditable="true">Custo da API da OpenAI variar e impactar a margem.</td>
                            <td contenteditable="true" class="text-center">M</td>
                            <td contenteditable="true" class="text-center">M</td>
                            <td contenteditable="true">Monitorar custos, otimizar prompts, buscar alternativas.</td>
                            <td contenteditable="true">[Nome]</td>
                        </tr>
                         <tr>
                            <td contenteditable="true">Baixa adoção ou dificuldade de uso pelos clientes piloto.</td>
                            <td contenteditable="true" class="text-center">B</td>
                            <td contenteditable="true" class="text-center">A</td>
                            <td contenteditable="true">Onboarding assistido e coleta de feedback constante.</td>
                            <td contenteditable="true">[Nome]</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
        
        <!-- 13) Decisão desta Ata -->
        <section class="card editable-section bg-blue-50 border-blue-200" id="decisao">
            <div class="section-header">
                <h2 class="section-title text-blue-800">13. Decisão desta Ata (Stage-Gate 0)</h2>
                <button class="edit-btn edit-ui" title="Editar este bloco">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                </button>
            </div>
            <div class="editable-content space-y-6">
                <p><strong>Resultado do Scorecard:</strong> <span class="input-line font-bold text-xl" id="final-score-display">4.15</span> / 5,00</p>
                <div>
                    <p class="font-semibold">Deliberação do Comitê:</p>
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-6 mt-2">
                        <label class="flex items-center"><input type="radio" name="deliberacao" class="h-5 w-5 mr-2" checked> <span class="font-bold text-green-700">APROVADO – iniciar Piloto</span></label>
                        <label class="flex items-center"><input type="radio" name="deliberacao" class="h-5 w-5 mr-2"> DERISCAR e reavaliar em ___ dias</label>
                        <label class="flex items-center"><input type="radio" name="deliberacao" class="h-5 w-5 mr-2"> CONGELAR/PIVOTAR</label>
                    </div>
                </div>
                <div>
                    <label class="font-semibold block mb-1">Condições/Restrições:</label>
                    <p contenteditable="true" class="p-2 bg-white rounded-md border border-slate-200">Avançar para a fase de Piloto Controlado. Alocar os recursos preliminares para o piloto e autorizar a equipe a iniciar os trabalhos, sob as condições e métricas estabelecidas.</p>
                </div>

                <div class="mt-8 pt-6 border-t border-slate-300">
                    <h3 class="text-lg font-semibold mb-4">Assinaturas</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div>
                            <p class="border-b-2 border-slate-400 h-10"></p>
                            <p class="text-sm text-center mt-2"><strong>Sponsor:</strong> [Nome]</p>
                        </div>
                        <div>
                            <p class="border-b-2 border-slate-400 h-10"></p>
                            <p class="text-sm text-center mt-2"><strong>PO:</strong> [Nome]</p>
                        </div>
                        <div>
                            <p class="border-b-2 border-slate-400 h-10"></p>
                            <p class="text-sm text-center mt-2"><strong>TL:</strong> [Nome]</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 14) Guia de Pontuação -->
        <section class="card bg-slate-100" id="guia">
            <div class="section-header">
                <h2 class="section-title">14. Guia de Pontuação (apoio ao item 5)</h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div>
                    <h4 class="font-bold mb-2">Impacto</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>0:</strong> nicho ínfimo, sem preço validado</li>
                        <li><strong>3:</strong> mercado claro; dor latente; hipótese de preço</li>
                        <li><strong>5:</strong> grande mercado; forte dor; vantagem competitiva e preço validado</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-2">Alinhamento Estratégico</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>0:</strong> fora da tese</li>
                        <li><strong>3:</strong> usa IA de forma útil; sinergias moderadas</li>
                        <li><strong>5:</strong> core da tese; reuso alto; acelera OKRs</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-2">Escalabilidade</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>0:</strong> serviço artesanal</li>
                        <li><strong>3:</strong> produto com playbook; canais a validar</li>
                        <li><strong>5:</strong> SaaS/PLG com canais repetíveis e baixo custo marginal</li>
                    </ul>
                </div>
                 <div>
                    <h4 class="font-bold mb-2">Exequibilidade</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>0:</strong> alto risco/alto esforço; dependências críticas</li>
                        <li><strong>3:</strong> viável com 1–2 riscos gerenciáveis</li>
                        <li><strong>5:</strong> baixo risco; esforço residual; time/capacidade disponíveis</li>
                    </ul>
                </div>
            </div>
        </section>

    </div>

    <footer class="text-center mt-12 py-8 border-t-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div class="max-w-4xl mx-auto">
            <h3 class="text-lg font-bold text-blue-800 mb-2">📄 ATA OFICIAL</h3>
            <p class="text-sm text-gray-600 mb-4">Registro formal da Iniciação de Projeto - Stage-Gate 0</p>
            
            <div class="bg-blue-100 p-4 rounded-lg border border-blue-300">
                <p class="text-sm text-blue-800">
                    <strong>🔒 Confidencialidade:</strong> Este documento contém informações confidenciais. 
                    Distribuição restrita aos participantes autorizados.
                </p>
            </div>
            
            <div class="mt-6 text-xs text-gray-500">
                <p>Documento gerado em: <span id="timestamp"></span></p>
            </div>
        </div>
    </footer>

    <script id="edit-script">
        document.addEventListener('DOMContentLoaded', function () {
            // --- Funcionalidade de Edição ---
            const editIconSVG = `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>`;
            const saveIconSVG = `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`;
            
            document.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const section = this.closest('.editable-section');
                    if (!section) return;

                    const content = section.querySelector('.editable-content');
                    if (!content) return;
                    
                    const isEditing = content.isContentEditable;

                    if (!isEditing) {
                        content.contentEditable = true;
                        content.focus();
                        this.innerHTML = saveIconSVG;
                        this.title = "Salvar alterações";
                        content.classList.add('editing');
                    } else {
                        content.contentEditable = false;
                        this.innerHTML = editIconSVG;
                        this.title = "Editar este bloco";
                         content.classList.remove('editing');
                    }
                });
            });

            // --- Funcionalidade para Salvar o HTML ---
            document.getElementById('save-html-btn').addEventListener('click', function () {
                const docClone = document.documentElement.cloneNode(true);
                
                // Remove todos os elementos da UI de edição
                docClone.querySelectorAll('.edit-ui').forEach(el => el.remove());
                
                // Remove o próprio script
                docClone.querySelector('#edit-script').remove();
                
                // Remove os atributos de 'contenteditable'
                docClone.querySelectorAll('[contenteditable="true"]').forEach(el => {
                    el.removeAttribute('contenteditable');
                     el.classList.remove('editing');
                });

                // Gera o nome do arquivo com base no título do projeto e data
                const projectTitleElement = document.querySelector('#identificacao .editable-content p:first-child .input-line');
                const projectTitle = projectTitleElement ? projectTitleElement.textContent.trim().replace(/\s+/g, '_') : 'ATA_INICIO_PROJETO';
                const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
                const filename = `${projectTitle}_${date}.html`;
                
                const cleanHtml = "<!DOCTYPE html>\n" + docClone.outerHTML;
                const blob = new Blob([cleanHtml], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            });

            // --- Funcionalidade de Cálculo do Scorecard ---
            const scorecardTable = document.getElementById('scorecard-table');
            const finalScoreDisplay = document.getElementById('final-score-display');
            
            function calculateScore() {
                let totalScore = 0;
                scorecardTable.querySelectorAll('tbody tr').forEach(row => {
                    const noteCell = row.querySelector('.score-note');
                    const weightCell = row.querySelector('.score-weight');
                    const partialCell = row.querySelector('.score-partial');
                    
                    const note = parseFloat(noteCell.textContent.replace(',', '.')) || 0;
                    const weight = parseFloat(weightCell.textContent) || 0;
                    
                    const partial = note * weight;
                    partialCell.textContent = partial.toFixed(2).replace('.', ',');
                    totalScore += partial;
                });
                
                const totalCell = document.getElementById('score-total');
                const formattedTotal = totalScore.toFixed(2);
                totalCell.textContent = formattedTotal.replace('.', ',');
                if (finalScoreDisplay) {
                     finalScoreDisplay.textContent = formattedTotal.replace('.', ',');
                }
            }
            
            scorecardTable.addEventListener('input', (e) => {
                if (e.target.classList.contains('score-note')) {
                    calculateScore();
                }
            });

            // Calcula o score inicial ao carregar a página
            calculateScore();

            // Atualizar timestamp automaticamente
            document.getElementById('timestamp').textContent = new Date().toLocaleString('pt-BR');
        });
    </script>

</body>
</html>

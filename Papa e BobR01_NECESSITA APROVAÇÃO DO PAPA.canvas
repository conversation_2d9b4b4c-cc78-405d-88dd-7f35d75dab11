{"nodes": [{"id": "groupEstrategia", "type": "group", "x": -6465, "y": -9200, "width": 7145, "height": 1720, "label": "ESTRATÉGIA & ESTRUTURAÇÃO"}, {"id": "0567763ad97f170f", "type": "group", "x": -6288, "y": -4720, "width": 5968, "height": 1920, "label": "PLANEJAMENTO PRO DESENVOLVIMENTO DOS PRODUTOS/SERVIÇOS  DO PROJETO"}, {"id": "groupProducao", "type": "group", "x": -6288, "y": -7360, "width": 4952, "height": 2240, "label": "PILARES PRA DESENVOLVER O PROJETO"}, {"id": "563533bfb793a67b", "type": "group", "x": -5500, "y": -12000, "width": 3635, "height": 2430, "label": "💡 SÓCIOS "}, {"id": "104e14dea488b88a", "type": "group", "x": -1675, "y": -12000, "width": 2355, "height": 2430, "label": "💡 FINANCEIRO "}, {"id": "44723a37a4cde543", "type": "group", "x": -5392, "y": -2240, "width": 3161, "height": 1480, "label": "Fase 1: Criação Base do Projeto "}, {"id": "groupFundacao", "type": "group", "x": -7320, "y": -12000, "width": 1645, "height": 2430, "label": "💡 CONCEITO"}, {"id": "d5b0dd58f4520791", "type": "group", "x": -4943, "y": 4480, "width": 2382, "height": 1260, "label": "Fase 7: Segunda Onda de Lançamento (MARÇO 2026)"}, {"id": "d3059a3f0d00abb6", "type": "group", "x": -4966, "y": 1600, "width": 2382, "height": 1160, "label": "Fase 5: Primeira Onda de Lançamento (JANEIRO 2026 FÉRIAS ESCOLARES)"}, {"id": "0ce79d8a483adcc7", "type": "group", "x": -4379, "y": 3080, "width": 1298, "height": 1160, "label": "Fase 6: Desenvolvimento dos Ebooks, e-commerce e Aplicativo"}, {"id": "groupCentro", "type": "group", "x": -4372, "y": -13460, "width": 1430, "height": 780, "label": "🎯 NÚCLEO: ROADMAP & VISÃO MACRO"}, {"id": "e88d917ea0ee854d", "type": "group", "x": -4251, "y": 200, "width": 890, "height": 520, "label": "Fase 3: <PERSON><PERSON><PERSON> da história"}, {"id": "3af968961d8204f2", "type": "group", "x": -4281, "y": 920, "width": 951, "height": 480, "label": "Fase 4: <PERSON><PERSON><PERSON>"}, {"id": "bd8690fb5551f8a2", "type": "group", "x": -4196, "y": -480, "width": 780, "height": 480, "label": "Fase 2: <PERSON><PERSON><PERSON><PERSON> pra captar investidor"}, {"id": "d5245cf1a93f297e", "type": "group", "x": -4997, "y": 6800, "width": 2382, "height": 600, "label": "Fase 9: Terceira Onda de Lançamento (JULHO 2026 - FÉRIAS DE INVERNO)"}, {"id": "9b4d2f911922567a", "type": "group", "x": -4833, "y": 5920, "width": 2054, "height": 640, "label": "Fase 8: <PERSON><PERSON><PERSON> dos Números"}, {"id": "22f227900a0f9caf", "type": "group", "x": -4915, "y": 8360, "width": 2164, "height": 600, "label": "Fase 11: <PERSON><PERSON><PERSON> Lançamento (JANEIRO 2027 - FÉRIAS DE VERÃO) "}, {"id": "30569de64ccba295", "type": "group", "x": -4845, "y": 9200, "width": 1860, "height": 640, "label": "Fase 12: Expandir para mercado físico"}, {"id": "acc2c00ecbb4f67f", "type": "group", "x": -4833, "y": 7600, "width": 2000, "height": 560, "label": "Fase 10: <PERSON><PERSON><PERSON> aprendi<PERSON>o <PERSON>ple<PERSON> (alfabeto + matemática)."}, {"id": "741f234cbac79c04", "type": "group", "x": -4855, "y": 10160, "width": 940, "height": 1080, "label": "Fase 13: <PERSON><PERSON><PERSON><PERSON>"}, {"id": "c595e281d54ec2fa", "type": "group", "x": -3773, "y": 10160, "width": 940, "height": 1080, "label": "Fase 14: Filme para cinema e Netflix"}, {"id": "4d959e88d92113d5", "type": "text", "text": "###  8.1: Desenvolver história/músicas dos números\n\n**Objetivo:** → <PERSON><PERSON><PERSON> mesma lógica das letras (de 0 a 9 ou até 20)\n\n🎯 Meta: nova série dentro da franquia", "x": -4039, "y": 6000, "width": 455, "height": 410}, {"id": "3f69493367d94ce9", "type": "text", "text": "###  9.1: C<PERSON>r estratégias e postagens para lançamentos\n\n**Objetivo:** → \n", "x": -4738, "y": 6880, "width": 440, "height": 410}, {"id": "c21373b7467e3404", "type": "text", "text": "###  9.2: Lançamento da temporada dos (números)\n\n**Objetivo:** → Postagens, vídeo piloto no <PERSON>.\n\n", "x": -4238, "y": 6880, "width": 420, "height": 410}, {"id": "2a186a80e0bd1cb9", "type": "text", "text": "###  9.3: Vendas/divulgação (números)\n\n**Objetivo:** → temporada ser reconhecido pelo maior numero de pessoas para gerar branding\n\n", "x": -3768, "y": 6880, "width": 420, "height": 410}, {"id": "80f6d84fcc017ea9", "type": "text", "text": "### 📈 9.4: ANÁLISE DE MÉTRICAS & ITERAÇÃO\n\n**Processo:**\n- As reuniões quinzenais analisarão métricas de desempenho dos canais.\n- **KPIs do YouTube:** Views, CTR (Click-Through Rate), Retenção de audiência.\n- **KPIs de Negócio:** Custos, ROI por canal.\n\n**Ação:**\n- Ajustar estratégias com base nos dados (ex: mudar estilo de thumbnail, duração dos vídeos, temas).", "x": -3313, "y": 6880, "width": 460, "height": 400}, {"id": "67713963b9b9b736", "type": "text", "text": "###  11.2: Lançamento do projeto (números)\n\n**Objetivo:** → Postagens, vídeo piloto no <PERSON>.\n\n", "x": -4096, "y": 8440, "width": 420, "height": 410}, {"id": "7fee1fbd49684d0a", "type": "text", "text": "###  11.3: Vendas/divulgação (números)\n\n**Objetivo:** → temporada ser reconhecido pelo maior numero de pessoas para gerar branding\n\n", "x": -3626, "y": 8440, "width": 420, "height": 410}, {"id": "ec2ed858be97e358", "type": "text", "text": "###  11.1: C<PERSON>r estratégias e postagens para lançamentos\n\n**Objetivo:** → \n", "x": -4596, "y": 8440, "width": 440, "height": 410}, {"id": "778ce8002dc114cf", "type": "text", "text": "###  10.1: Desenvolver história juntando os números e as letras\n\n**Objetivo:** → Episódios e músicas com desafios, aventuras e aprendizado cruzado.\n\n🎯 Meta: nova série dentro da franquia", "x": -4028, "y": 7680, "width": 455, "height": 410}, {"id": "312733674ad6fdf7", "type": "text", "text": "###  12.1: <PERSON><PERSON><PERSON><PERSON>\n\n**Objetivo:** → \n", "x": -4325, "y": 9290, "width": 400, "height": 410}, {"id": "84a5862a7ac4111b", "type": "text", "text": "###  12.3: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n\n**Objetivo:** → \n", "x": -3885, "y": 9290, "width": 400, "height": 410}, {"id": "bc2e0895858330c1", "type": "text", "text": "###  12.4: <PERSON><PERSON><PERSON>\n\n**Objetivo:** → \n", "x": -3445, "y": 9290, "width": 400, "height": 410}, {"id": "580e8bf972551aec", "type": "text", "text": "###  12.1: <PERSON><PERSON> impress<PERSON>\n\n**Objetivo:** → Transformar os principais ebooks vendidos no e-commerce em livros físicos. \n", "x": -4785, "y": 9290, "width": 400, "height": 410}, {"id": "fc5cdb0c51d92f0c", "type": "text", "text": "Desenvolver tudo em inglês: Tradução e adaptação cultural das letras, vozes, app e site.", "x": -4618, "y": 10250, "width": 380, "height": 410}, {"id": "378c70c872b2f863", "type": "text", "text": "## 🎬 COMO FUNCIONA A INDÚSTRIA DO CINEMA\n\n### 1. **Desenvolvimento**\n\nVocê cria o projeto: roteiro, personagens, storyboard, orçamento, plano de produção.\n\n### 2. **Financiamento**\n\nVocê precisa levantar recursos para produzir o filme. Isso pode vir de:\n\n- Leis de incentivo (ex: Ancine, Rouanet, ProAC)\n    \n- Patrocínios e investidores privados\n    \n- Produtoras que apostam no projeto\n    \n- Crowdfunding (financiamento coletivo)\n    \n\n### 3. **Produção**\n\nÉ a gravação do filme propriamente dito. Inclui:\n\n- Pré-produção (casting, locações, ensaios)\n    \n- Filmagem\n    \n- Pós-produção (edição, som, efeitos visuais)\n    \n\n### 4. **Distribuição**\n\nDepois de pronto, o filme precisa de **um distribuidor** para colocá-lo nas salas de cinema ou plataformas de streaming. Aqui você escolhe o \"caminho\" do filme:\n\n- **Festivais**: Uma porta de entrada para reconhecimento e distribuição.\n    \n- **Distribuidoras**: Empresas que negociam com cinemas e streamings.\n    \n- **Distribuição independente**: Você mesmo negocia com plataformas, cinemas locais ou usa plataformas como Amazon Prime Video Direct ou Vimeo OTT.\n    \n\n---\n\n## 🍿 COMO COLOCAR UM FILME NOS CINEMAS\n\n### Caminho tradicional:\n\n1. **Ter um distribuidor de cinema**: Empresas como Paris Filmes, Downtown, O2 Play, Elo Company etc.\n    \n2. **Negociar com exibidoras**: Como Cinemark, Kinoplex, Cinesystem, etc.\n    \n3. **Pagar pela cópia digital (DCP)**: Formato usado nas salas de cinema.\n    \n4. **Investir em divulgação**: Para atrair público e manter o filme em cartaz.\n    \n\n> ❗ Importante: a maioria dos cinemas só aceita filmes com um **distribuidor credenciado**.\n\n---\n\n## 📺 COMO COLOCAR UM FILME NA NETFLIX\n\n### A Netflix **não aceita envio direto** de filmes por criadores independentes. Você precisa:\n\n1. **Ter um agente ou distribuidor com relação comercial com a Netflix**\n    \n    - Eles são chamados de **“Aggregators”** ou **“Preferred Partners”**.\n        \n    - No Brasil, exemplos incluem Elo Company, O2 Play, Vitrine Filmes.\n        \n2. **Participar de festivais importantes**\n    \n    - Se o filme ganhar destaque, a Netflix pode se interessar.\n        \n3. **Ou licenciar o conteúdo através de distribuidoras**\n    \n    - A Netflix paga **uma licença por tempo determinado** (ex: 2 anos).\n        \n    - O valor depende do tipo de conteúdo, qualidade técnica, público-alvo, etc.\n        \n\n---\n\n## ✅ CAMINHO PRÁTICO PARA UM CRIADOR INDEPENDENTE\n\n1. **Desenvolva um projeto profissional**: roteiro, orçamento, identidade visual, teaser.\n    \n2. **Busque parcerias com produtoras e distribuidoras**.\n    \n3. **Cadastre em editais ou leis de incentivo para captar verba.**\n    \n4. **Participe de festivais** (Gramado, Paulínia, Cannes, Berlim, etc.).\n    \n5. **Se for independente**, avalie lançar em:\n    \n    - **Amazon Prime Video (via Prime Video Direct)**\n        \n    - **YouTube (aluguel ou gratuito com monetização)**\n        \n    - **Vimeo OTT**\n        \n    - **Now, Net ou Globoplay (via agregadores)**\n        \n\n---\n\n## 💡 DICA: Plataformas para distribuição independente\n\n|Plataforma|Tipo de Exibição|Requisitos principais|\n|---|---|---|\n|**Amazon Prime Video Direct**|Aluguel, compra, assinatura|Conta verificada + arquivos técnicos|\n|**Vimeo OTT**|Venda ou assinatura direta|Página própria + pagamento global|\n|**Filmhub**|Distribuição para streamings|Curadoria interna + contrato|\n|**Short of the Week**|Curtas e visibilidade|Seleção editorial|", "x": -3688, "y": 10250, "width": 800, "height": 960}, {"id": "460a765b32d15b42", "type": "text", "text": "###  5.🛒 **ECOMMERCE**\n\n**Objetivo:** Plataforma central de vendas de produtos digitais e físicos.  \n**Etapas:**\n\n1. **Escolha da Plataforma** (Shopify, WooCommerce, etc.).\n    \n2. **Design e Identidade Visual**\n    \n    - Layout infantil, navegação simples.\n        \n3. **Configuração de Produtos**\n    \n    - eBooks, produtos físicos.\n        \n4. **Integração com Pagamentos e Logística**\n    \n    - PIX, cartão, boleto; transportadoras e Correios.\n        \n5. **Estratégia de Marketing**\n    \n    - Google Ads, Meta Ads, SEO.****", "x": -2999, "y": -4587, "width": 560, "height": 1708}, {"id": "c0bf3c05aa5111ea", "type": "text", "text": "###  **4. 📱 APLICATIVO**\n\n**Objetivo**: Criar um aplicativo infantil interativo, seguro e educativo, com **músicas, histórias, jogos, eBooks e vídeo-aulas exclusivas**, gerando receita recorrente via assinatura mensal.\n\n---\n\n### **4.1. Conteúdo do Aplicativo**\n\n**Biblioteca Multimídia**\n\n- **Músicas:** catálogo completo das músicas do Papa & Bob, organizadas por tema (alfabeto, números, animais, objetos, personalidades).\n    \n- **Vídeos Curtos:** clipes musicais, animações e chamadas de ação.\n    \n- **Vídeo-Aulas Educativas:** aulas temáticas gravadas com personagens e apresentadores.\n    \n    - Letras (alfabeto, fonética e pronúncia).\n        \n    - Números (contagem, adição, subtração simples).\n        \n    - Conceitos básicos (cores, formas, profissões, hábitos saudáveis).\n        \n    - Inglês básico para crianças.\n        \n- **Jogos Educativos:** caça-letras, quebra-cabeças, jogos de memória.\n    \n- **Área para Colorir:** páginas interativas com ilustrações do eBook.\n    \n\n---\n\n### **4.2. Modelo de Monetização**\n\n**Assinatura Mensal (Recorrente)**\n\n- Valor sugerido: R$ 14,90 a R$ 19,90/mês (testar via pesquisa).\n    \n- **Plano Gratuito:** acesso limitado nas músicas, vídeos-aulas e  jogos).\n    \n- **Plano Premium:** acesso total a todo conteúdo + novos lançamentos exclusivos.\n    \n- Cobrança via **Google Play Billing** e **Apple In-App Purchase** (para assinaturas automáticas).\n    \n\n---\n\n### **4.3. Recursos e Funcionalidades**\n\n- **Perfis para Crianças:** possibilidade de criar até 3 perfis no mesmo app (com avatares e nomes).\n    \n- **Controle Parental:** área protegida por senha para configurações e pagamentos.\n    \n- **Download Offline:** possibilidade de baixar músicas, vídeos e eBooks para assistir sem internet.\n    \n- **Notificações Amigáveis:** avisar quando sair conteúdo novo.\n    \n- **Gamificação:**\n    \n    - Pontos e medalhas por assistir vídeos, completar jogos e responder quiz.\n        \n    - Personagens reagem às conquistas da criança.\n        \n\n---\n\n### **4.4. Fluxo de Produção**\n\n1. **Planejamento de Conteúdo**\n    \n    - Definir cronograma mensal de lançamentos (músicas, vídeos, aulas).\n        \n    - Exemplo: 2 novas músicas + 1 vídeo-aula + 2 novos jogos por mês.\n        \n2. **Produção das Vídeo-Aulas**\n    \n    - Gravação com apresentador + inserção de animações dos personagens.\n        \n    - Duração: 5 a 8 minutos cada (curta para manter atenção).\n        \n3. **Desenvolvimento do App**\n    \n    - **Fase 1:** Músicas, vídeos e área para colorir.\n        \n    - **Fase 2:** Jogos educativos.\n    -  **Fase 3:**  vídeo-aulas + sistema de assinatura.\n        \n4. **Publicação e Atualizações**\n    \n    - Lançar MVP com 30% do conteúdo pronto e ir atualizando semanalmente.\n        \n\n---\n\n### **4.5. Diferenciais Competitivos**\n\n- Personagens fixos e carismáticos (Papa & Bob).\n    \n- Conteúdo educativo + entretenimento no mesmo lugar.\n    \n- Experiência segura (sem anúncios para assinantes).\n    \n- Lançamentos contínuos para manter assinatura ativa.", "x": -3840, "y": -4587, "width": 715, "height": 1708}, {"id": "ff0263599773f4bc", "type": "text", "text": "###  3. 🎬 **<PERSON><PERSON><PERSON><PERSON> (Instagram, Facebook, TikTok, Shorts, kwai)**\n\n**Objetivo:** <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, cativante e de alto alcance. Divulgar todos produtos/serviços da empresa.\n\n**Etapas:**\n\n1. **Definir Formatos**\n    \n    - <PERSON><PERSON>es musicais (trechos das músicas).\n        \n    - Cenas curtas da história.\n        \n    - Interações de personagens com crianças (chamadas de ação).\n    - \n        \n2. **Calendário de Postagens**\n    \n    - Frequência e temas por semana.\n        \n3. **Produção Visual**\n    \n    - Animação curta (5 a 30 segundos).\n        \n    - Legendagem e efeitos.\n        \n4. **Estratégia de Divulgação**\n    \n    - Hashtags, trends, colaborações e reposts.\n", "x": -4480, "y": -4587, "width": 520, "height": 1708}, {"id": "e5e93c1810a87df2", "type": "text", "text": "\n## 6. **📖 eBooks / Livros**\n\n### Objetivo\n\nProduzir e distribuir materiais digitais e físicos que combinem **educação + diversão**, fortalecendo a marca Papa & Bob e gerando receita própria.\n\n---\n\n## 1. 🎨 **EBOOKS PARA COLORIR (ALFABETO E NÚMEROS)**\n\n📌 **Quantidade sugerida: 2 ebooks**\n\n- **Ebook 1:** _Alfabeto para Colorir_ – 26 páginas (cada letra ilustrada, com Papa & Bob em situações ligadas à letra).\n    \n- **Ebook 2:** _Números para Colorir_ – 10 a 20 páginas (números de 1 a 10 ou até 20, cada um acompanhado de elementos para colorir).\n    \n\n---\n\n## 2. ✏️ **EBOOKS COM EXERCÍCIOS SIMPLES**\n\n📌 **Quantidade sugerida: 3 ebooks**\n\n- **Ebook 3:** _Alfabeto com Exercícios_ – A–Z, incluindo:\n    \n    - Identificação de letra.\n        \n    - Pa<PERSON>ra ilustrada.\n        \n    - Atividade simples (ligar, completar, circular).\n        \n- **Ebook 4:** _Colorir + Escrever_ – Combinação: a criança pinta e depois treina escrever (linha pontilhada para contornar).\n    \n- **Ebook 5:** _Matemática Básica_ – Contagem, somas e subtrações simples, ligar pontos, completar sequências.\n    \n\n---\n\n## 3. 🌍 **EBOOKS DE CONHECIMENTOS GERAIS**\n\n📌 **Quantidade sugerida: 4 ebooks**\n\n- **Ebook 6:** _Animais do Mundo_ – colorir + curiosidades (onde vivem, sons, hábitos).\n    \n- **Ebook 7:** _Frutas e Alimentos Saudáveis_ – aprender nomes, cores e benefícios.\n    \n- **Ebook 8:** _Profissões_ – cada profissão com ilustração, nome e uma atividade simples.\n    \n- **Ebook 9:** _Meio Ambiente e Sustentabilidade_ – atividades de reciclagem, economizar água, cuidar das plantas.\n    \n\n---\n\n## 4. 🌎 **EBOOKS DE IDIOMAS**\n\n📌 **Quantidade sugerida: 3 ebooks**\n\n- **Ebook 10:** _Primeiras Palavras em Inglês_ – objetos e animais ilustrados (básico do vocabulário).\n    \n- **Ebook 11:** _Mini-Dicionário Bilíngue_ (Português–Inglês) – com ícones e emojis (ex.: sol ☀️ – sun).\n    \n- **Ebook 12:** _Jogos de Memória no Ebook_ – páginas duplas para imprimir/recortar pares (palavra + figura).\n    \n\n---\n\n## 5. 📖 **EBOOKS COM HISTÓRIAS**\n\n📌 **Quantidade sugerida: 3 ebooks**\n\n- **Ebook 13:** _Histórias do Alfabeto_ – cada capítulo inspirado em uma letra (não precisa ser todas as 26 letras em um volume, pode ser dividido em blocos: A–I / J–R / S–Z).\n    \n- **Ebook 14:** _Histórias com Números_ – histórias curtas e divertidas com personagens que ensinam contagem.\n    \n- **Ebook 15:** _Histórias de Valores e Emoções_ – amizade, respeito, alegria, tristeza, coragem (cada capítulo com uma emoção).\n    \n\n---\n\n# 📊 **Resumo da Estrutura**\n\n- **Ebooks para colorir:** 2\n    \n- **Ebooks com exercícios simples:** 3\n    \n- **Ebooks de conhecimentos gerais:** 4\n    \n- **Ebooks de idiomas:** 3\n    \n- **Ebooks com histórias:** 3\n    \n\n👉 **Total: 15 ebooks**\n        \n\n---\n\n### **6.2. Formato e Layout**\n\n- **Formato digital:** PDF em A4, compatível com impressão doméstica.\n    \n- **Ilustrações:** vetoriais, traço grosso, fáceis de colorir, com espaços amplos.\n    \n- **Estilo visual:** cores de referência da marca, mantendo consistência de personagens.\n    \n- **Versão física:** impressão em papel offset ou couchê fosco, capa cartonada.\n    \n\n---\n\n### **6.3. Distribuição**\n\n- **Digital gratuito:** capítulos iniciais para gerar leads.\n    \n- **Digital pago:** versão completa via eCommerce e app.\n    \n- **Físico pago:** venda no eCommerce e marketplaces.\n    \n- **Incluso na assinatura do App:** assinantes premium podem baixar todos os livros digitais.\n    \n\n---\n\n### **6.4. Integração com o App**\n\n- Área dedicada dentro do app para **ler e colorir digitalmente**.\n    \n- Opção para **baixar e imprimir**.\n    \n- Versões com **atividades extras** interativas (ligar pontos, caça-palavras, labirintos).", "x": -2160, "y": -4587, "width": 1100, "height": 1067}, {"id": "f2d15c82e91230d2", "type": "text", "text": "### **7. 🎁 PRODTUDO FISICO**\n\n**Objetivo:** Ampliar a marca para o mundo físico.  \n**Etapas:**\n\n1. **Definir Tipos de Produtos**\n    \n    - <PERSON><PERSON> impressos, brinquedos educativos, pel<PERSON><PERSON><PERSON> dos personagens, jogos de tabuleiro.\n        \n2. **Design e Prototipagem**\n    \n    - Criação de mockups e amostras.\n        \n3. **Produção**\n    \n    - Escolha de fornecedores.\n        \n4. **Distribuição e Vendas**\n    \n    - E-commerce, eventos, lojas parceiras.", "x": -2160, "y": -3320, "width": 680, "height": 441}, {"id": "a12b26a3d738abd2", "type": "text", "text": "###  2. 🎵 **MÚSICAS**\n\n## Conteúdos Musicais\n\n- **Temáticas principais**\n    \n    - Alfabeto com animais, objetos, comidas, personalidades e yoga/exercicios.\n        \n    - Músicas específicas para cada letra (em alguns casos, avaliando se vale a pena ter uma música só para ela).\n        \n    - Versões com números e combinações de letras + números.\n\n\n**https://www.mindmeister.com/app/map/3764594265**\n\n## Estratégia de Produção\n\n- Criar **músicas temáticas completas** (alfabeto com animais, objetos, comidas, personalidades).\n    \n- Produzir **versões curtas por letra** quando fizer sentido, mantendo o padrão da estrutura musical.\n    \n- Garan<PERSON>r que todas as músicas tenham **interatividade**, **movimento** e **elementos que incentivem a criança a repetir**.", "x": -5402, "y": -4587, "width": 732, "height": 589}, {"id": "94243467723842f9", "type": "text", "text": "### Elementos Musicais\n- **Padrão de Estrutura Musical***\n    \n    - Introdução → Verso(s) → Refrão → Seção Educativa → Refrão → Encerramento.\n    -\n- **Introdução Animada**\n    \n    - Efeitos divertidos, vozes alegres, frase de chamada (“Vamos brincar?”, “Atenção criançada!”).\n        \n- **Batida Base Simples e Dançante**\n    \n    - BPM 100–120, estilo funk carioca leve ou beat eletrônico infantil.\n        \n    - Instrumentos: sinos, xilofones, pads suaves, sintetizadores redondos.\n        \n- **Voz Clara e Teatral**\n    \n    - Entonação exagerada e amigável, possível uso de backing vocals infantis.\n        \n    - Repetição para facilitar memorização.\n        \n- **Letras Rítmicas e Educativas**\n    \n    - Curtas, com rimas simples e uso de onomatopeias.\n        \n- **Refrão “Grudento”**\n    \n    - <PERSON>ito repetitivo, marcado por palmas ou batida.\n        \n    - Pode incluir coreografias ou chamadas (“Repete comigo!”).\n        \n- **Estrutura Previsível**\n    \n    - Intro → Verso → Refrão → Seção Educativa → Refrão → Encerramento.\n        \n    - Duração ideal: 2 a 3 minutos.\n        \n- **Final Interativo**\n    \n    - Despedida divertida ou convite para outra música (“Agora vamos contar os números!”).\n\n", "x": -5402, "y": -3935, "width": 732, "height": 510}, {"id": "producaoPipeline", "type": "text", "text": "### ⚙️ PIPELINE DE PRODUÇÃO HISTÓRIA\n\nFluxo padrão para cada episódio, otimizado para produção em lote e paralela.\n\n**Roteiro → Design → Voz & Trilha → Animação → Edição & Finalização → Aprovação**\n\n- **Produção Paralela de Trilhas (4.2):** Músicas são finalizadas e sobem para o Spotify enquanto o vídeo está em animação, garantindo disponibilidade simultânea no lançamento.\n- **Produção para Redes Sociais (4.3):** Clipes curtos são extraídos e editados em paralelo para TikTok/Reels.", "x": -6200, "y": -3240, "width": 1530, "height": 361}, {"id": "producaoRoteiro", "type": "text", "text": "### 📝 CRIAÇÃO DOS NOSSOS PRODUTOS/SERVIÇOS\n\n1. <PERSON>IST<PERSON>RI<PERSON> COMPLETA \n2. MÚSICAS\n3. VIDEOS CURTOS (INSTA/FACE, TIKTOK, SHORTS)\n4. APLICATIVO\n5. ECOMMERC<PERSON>\n6. EBOOKS\n7. PRODUT<PERSON> FISICOS\n", "x": -4096, "y": -5880, "width": 565, "height": 586}, {"id": "20c26766a5fe666b", "type": "text", "text": "### 1. 📜 **HISTÓRIA COMPLETA**\n\n\n**Premissa Narrativa:**\nApós uma explosão lúdica (Big Bang), as letras do alfabeto se espalharam. **Papa** (garoto astronauta) e **Bob** (companheiro robô/alien) viajam pelos \"mundos das letras\" para encontrá-las. \n\n- **Temporada 1:** Big Bang, Apresentação do Papa e Bob, E exploração do Mundo das Letras (Alfabeto A-Z)\n  - **Foco:** Apresentação das 26 letras.\n  - **Duração:** 26 episódios.\n\n- **Temporada 2:** O Mundo dos Números\n  - **Foco:** Ensino dos números de 0 a 9.\n  - **Duração:** 10 episódios.\n\n- **Temporada 3:** Integração e Cotidiano\n  - **Foco:** Palavras simples, cores, formas, situações do dia a dia.\n\n**Estrutura Pedagógica:**\n- **Estí<PERSON><PERSON>sensorial:** Foco em repetição e música para memorização.\n- **Personificação:** As letras são personagens com personalidade, criando vínculo afetivo.\n- **Narrativa Contínua:** A busca pelas letras conecta os 26 episódios em uma história maior.\n", "x": -6242, "y": -4587, "width": 764, "height": 589}, {"id": "fundacaoTemporadas", "type": "text", "text": "### 📝 ROTEIRIZAÇÃO EM LOTE \n\nCada episódio é um ciclo fechado, com:\n- Uma aventura emocional (jornada do herói)\n- E elementos virais (estímulo visual e social)\n- Uma música cativante (memória e diversão)\n- Uma conversa com a criança (raciocínio e linguagem)\n- Uma mensagem útil (educação para a vida)\n- Um gancho para o próximo episódio (retenção)\n", "x": -6200, "y": -3920, "width": 680, "height": 510}, {"id": "fundacaoMarca", "type": "text", "text": "### 🎨 IDENTIDADE VISUAL E MARCA\n\n**Liderança:** <PERSON> (Diretor Criativo).\n\n**Componentes:**\n- **Logotipo:** \"<PERSON> & Bob\".\n- **Paleta de Cores:** Primárias e vibrantes.\n- **Tipografia:** Amigável e legível.\n- **Manual de Marca:** Para garantir consistência visual em todos os canais.\n\n", "x": -5122, "y": -2120, "width": 523, "height": 1241}, {"id": "952ed990e45fe104", "type": "text", "text": "### ⚙️ SEO \n\nConjunto de estratégias e técnicas usadas para melhorar a visibilidade de um site nos resultados de pesquisa orgânica dos motores de busca, como o Google==.\nPesquisa de palavras-chave \nPegar referências de desenhos e ver quais palavras chave deles tem mais busca\n", "x": -2872, "y": -2120, "width": 540, "height": 1225}, {"id": "4bbef4c44f28c9c6", "type": "text", "text": "### ✍️ PERSONALINADE DOS PERSONAGENS \n\n**Estilo:** Cartoon, com traços simples, expressivos e acolhedores.\n\n**Protagonistas:**\n- **Papa:** Garoto curioso em traje de astronauta.\n- **Bob:** <PERSON><PERSON><PERSON><PERSON><PERSON> (rob<PERSON> ou ser de outro mundo).\n\n**Letras Animadas:**\n- Cada letra possui olhos, boca e personalidade própria, transformando-as em personagens vivos.\n", "x": -4331, "y": -2120, "width": 550, "height": 490}, {"id": "664901f5259cd0ec", "type": "text", "text": "###  MÚSICA PRINCIPAL (ABERTURA)\n", "x": -3512, "y": -2120, "width": 540, "height": 1225}, {"id": "94a1f7fa9477f41a", "type": "text", "text": "### PSICOLOGIA INFANTIL: Principais Dores de Aprendizado na Infância\n\nEste documento apresenta, com base em práticas da psicologia infantil, as principais dores e dificuldades que crianças entre 3 e 7 anos podem enfrentar no processo de aprendizado. Esses desafios podem ser emocionais, cognitivos, motores ou ambientais, e afetam diretamente o desenvolvimento da leitura, escrita e linguagem.\n1. Dificuldade de atenção e concentração\nCrianças têm naturalmente períodos curtos de atenção e se distraem facilmente. Algumas têm transtornos como TDAH, mas muitas apenas precisam de atividades mais lúdicas e envolventes.\n\nSinais comuns: não finalizam tarefas, esquecem instruções, mudam de atividade com frequência.\n2. Baixa motivação ou falta de interesse\nQuando a criança não entende o propósito do que está aprendendo, ela perde o engajamento. A ausência de vínculo afetivo com o conteúdo e métodos pouco atrativos contribuem para esse desinteresse.\n\nSolução: transformar o aprendizado em brincadeira, história ou desafio pessoal.\n3. Medo de errar / ansiedade de desempenho\nMuitas crianças têm medo de errar e sentem vergonha, o que gera bloqueios cognitivos. Elas evitam tentar para não fracassar.\n\nFrases típicas: 'Não sei!', 'Tá errado?', 'Não quero fazer isso'. Elogiar o esforço é essencial.\n4. Baixa autoestima e insegurança\nA comparação com colegas pode gerar um ciclo de desmotivação. A criança sente que não consegue e passa a tentar menos, o que afeta ainda mais o aprendizado.\n\nImportante: reforçar os avanços, mesmo que pequenos.\n5. Ambiente familiar instável ou pouco estimulante\nBrigas constantes, insegurança emocional ou negligência afetam a concentração. A ausência de estímulo, diálogo e incentivo em casa também prejudica o desenvolvimento.\n\nSolução: criar um ambiente seguro, afetuoso e que valorize o erro como parte do processo.\n6. Dificuldades neurológicas e cognitivas\nCrianças com dislexia, disgrafia, discalculia ou outras condições podem ter dificuldades específicas. Essas condições exigem atenção especializada, mas não indicam falta de inteligência.\n\nSinais: trocas de letras, confusão com sons, rejeição persistente à leitura e escrita.\n7. Desenvolvimento motor atrasado\nA coordenação motora fina é fundamental para escrever e desenhar. Crianças com atraso podem ter dificuldades com lápis, tesoura, colagens, etc.\n\nSolução: incluir atividades motoras (recorte, pintura, massinha).\n8. Excesso de estímulo digital / telas\nExposição intensa a telas passivas reduz o foco e a paciência. A criança se acostuma com estímulos constantes e tem dificuldade em atividades que exigem esforço e construção.\n\nRecomendação: equilíbrio entre tempo de tela e atividades criativas.\n9. Dificuldade de linguagem ou fala\nA criança pode ter vocabulário limitado, dificuldades de pronúncia ou de se expressar. Isso interfere diretamente no aprendizado de leitura e escrita.\n\nSolução: estimular a fala com histórias, conversas, jogos de palavras.\n10. Falta de rotina e previsibilidade\nCrianças precisam de estrutura e rotina para se sentirem seguras. Quando o cotidiano é desorganizado (sono, alimentação, horários), a ansiedade aumenta e o foco diminui.\n\nExemplo: estudar sempre no mesmo horário ajuda o cérebro a entrar em 'modo aprendizado'.\nResumo das Principais Dores de Aprendizado\nCategoria\tDores Comuns\nAtenção & Emoção\tDistração, medo de errar, ansiedade, insegurança\nCognitivo\tDislexia, dificuldade com sons, memória fraca\nMotor & Linguagem\tDificuldade no traçado das letras, fala imprecisa\nAmbiente & Estímulo\tFalta de rotina, brigas familiares, pouco incentivo ou excesso de telas\n", "x": -2861, "y": -7200, "width": 892, "height": 850}, {"id": "producaoStack", "type": "text", "text": "### 🤖  PRINCIPAIS IA's UTILIZADAS NO PROJETO\n\n**Objetivo:** Usar tecnologia para viabilizar produção independente e otimizar tempo.\n\n**Ferramentas de IA:**\n- **Geração de Imagens:**\n  - Leonardo AI (Plano Maestro): Criação de artes conceituais.\n  - ImageFX (Google): Prototipação rápida.\n- **Animação e Vídeo:**\n  - Hedra AI Creator: Animação facial e corporal (lip sync).\n  - Kling AI (Starter Plan): Geração de vídeo a partir de foto + voz.\n  - Veo3 e Flow: Geração de vídeo a partir de foto.\n- **Vozes Sintéticas:**\n  - ElevenLabs: Geração de vozes para personagens e narração.\n- **Apoio Criativo e Gestão:**\n  - ChatGPT Plus (GPT-4): Rascunhos de roteiro, ideias, consultoria técnica.\n- **Áudio:**\n  - Epidemic Sound (Plano Pro): Biblioteca de músicas e efeitos sonoros licenciados.\n  - Suno (Plano Pro): Criação de música. \n\n**Softwares de Produção:**\n- **Edição de Vídeo e Design:**\n  - Adobe Creative Cloud (Equipe): Premiere, After Effects, Photoshop para edição e finalização profissional.\n  - CapCut Pro: Edição rápida para redes sociais.\n  ", "x": -5520, "y": -7200, "width": 800, "height": 850}, {"id": "84751eda9189ce53", "type": "text", "text": "### PILARES CRIATIVOS\n\n\nOs pilares abaixo estão organizados da maior para a menor capacidade de viralização nas redes sociais, sem perder o foco educativo e emocional da série.\n1. Referência a vídeos virais (conteúdo que já funciona)\n🎯 Objetivo: aproveitar o que já é sucesso nas redes para maximizar o apelo visual e emocional.\n📌 Como funciona:\nCada episódio inclui pelo menos uma cena inspirada em elementos virais validados no TikTok, YouTube Kids e Instagram Reels. Esses elementos podem ser:\n- Tombos engraçados e leves (ex: <PERSON> escorregando em uma casca de banana)\n- Pintura satisfatória e ASMR (ex: Papa pintando uma letra com pincel grosso e som suave)\n- Danças virais adaptadas (coreografia com letras ou personagens)\n- Cena relaxante (vento nas folhas, água escorrendo, bolhas flutuando)\n✅ Resultado: aumenta o engajamento visual, aproveita o algoritmo e conecta o conteúdo ao universo que a criança já consome.\n\n2. Música com dança e palavras da letra\n🎯 Objetivo: fixar o aprendizado da letra de forma divertida e física (dança e repetição).\n📌 Como funciona:\nCada episódio possui uma música original com coreografia simples. Exemplo para letra A:\n🎵 'A de Amor, A de Amizade, A de Abelha, A de Alegria!'\nDurante a música, Papa e Bob dançam com gestos temáticos (voando como abelha, fazendo coração com as mãos etc.), convidando a criança a dançar junto.\n✅ Resultado: reforça vocabulário, ritmo, memória e movimento corporal, além de ter grande apelo visual e auditivo para redes.\n\n3. Gancho de continuação (“quero mais”)\n🎯 Objetivo: gerar curiosidade e vontade de assistir o próximo episódio.\n📌 Como funciona:\nCada episódio termina com um gancho emocional ou misterioso, criando expectativa. Exemplos:\n- Bob encontra uma letra desconhecida no chão e pergunta 'O que será isso?'\n- Papa ou Bob caem em um buraco brilhante e o episódio termina escurecendo\n- A Letra do dia revela que tem uma irmã que será a próxima letra\n✅ Resultado: gera retenção, cria hábito de assistir e estimula o compartilhamento entre crianças e pais.\n\n4. Mini Jornada do Herói (Papa & Bob)\n🎯 Objetivo: envolver a criança emocionalmente em uma aventura leve e educativa.\n📌 Como funciona:\nCada episódio segue uma jornada simplificada:\n- Mundo comum: Papa e Bob brincando\n- Chamado para a aventura: algo exige aprender a nova letra\n- Recusa engraçada: 'Mas eu não sei nada com A!'\n- Encontro com a letra-personagem\n- Aventuras no mundo da letra (missões, desafios, aprendizado)\n- Tensão leve (perda, erro, confusão)\n- Retorno com aprendizado e celebração\n- Gancho para o próximo episódio\n✅ Resultado: cria envolvimento emocional, narrativa consistente e identificação com os personagens.\n\n5. Perguntas e interação com a criança\n🎯 Objetivo: estimular o raciocínio e a participação ativa da criança.\n📌 Como funciona:\nDurante o episódio, Papa e Bob fazem perguntas diretas ao público:\n- 'Qual animal começa com a letra A?'\n- 'Você consegue achar algo amarelo por aí?'\nPausas intencionais permitem que a criança pense e responda, incentivando a fala e a escuta ativa.\n✅ Resultado: ativa o cérebro da criança, desenvolve linguagem e cria um vínculo interativo com os personagens.\n\n6. Ensino de habilidades básicas da vida\n🎯 Objetivo: usar o desenho como ferramenta educativa para além do alfabeto.\n📌 Como funciona:\nEm cada episódio há um momento de aprendizado prático. Exemplos:\n- Mostrar como expressar dor: Papa com dor de barriga aponta o local e faz expressão facial\n- Ensinar sinais de trânsito (vermelho: parar; verde: andar)\n- Explicar o tempo (manhã, tarde, noite)\n- Mostrar como pedir ajuda ou identificar emoções\n✅ Resultado: ensina valores e comportamentos úteis para o dia a dia da criança, fortalecendo o lado educativo da série.\n", "x": -4528, "y": -7200, "width": 715, "height": 850}, {"id": "e7c058ca478bace7", "type": "text", "text": "### PROCESSO PEDAGÓGICO de Ensino do Alfabeto – 1º Ano do Ensino Fundamental\n\n\nEste documento descreve, de forma detalhada, como é conduzido o processo de ensino do alfabeto para crianças de 5 a 7 anos no 1º ano do Ensino Fundamental, com base em práticas pedagógicas consagradas, ludicidade e foco na consciência fonológica.\n\n1. Atenção à fase da criança\nA criança não aprende apenas com exposição à letra. Ela aprende quando vê sentido no que está sendo ensinado, quando tem curiosidade e quando consegue aplicar o conhecimento no cotidiano. Por isso, o ensino é sempre lúdico, afetivo e conectado à sua realidade.\n2. Do global ao específico\nIniciamos o processo usando palavras completas, especialmente os nomes das próprias crianças. A partir desses nomes, exploramos as letras iniciais e os sons. Exemplo: com o nome 'ANA', trabalhamos a letra A, seu som, sua grafia e outras palavras iniciadas com A.\n3. Ordem estratégica das letras\nAs letras não são ensinadas na ordem alfabética tradicional. Começamos pelas mais simples e frequentes (A, E, O, M, P, L, T), e deixamos as mais complexas (R, X, G, LH, NH) para etapas posteriores. Isso facilita a formação de sílabas e palavras desde o início.\n4. Trabalho com som, forma e traçado\nCada letra é ensinada com foco em três aspectos: fonema (som), grafema (forma escrita) e traçado (coordenação motora). As crianças são incentivadas a desenhar a letra no ar, no papel e com o dedo, além de associá-la a objetos visuais e sonoros.\n5. Uso de recursos lúdicos e sensoriais\nO ensino é feito com músicas, cartazes, histórias, jogos de memória, dramatizações e pintura. Atividades sensoriais, como colagens e modelagem, tornam o aprendizado concreto e memorável.\n6. Estímulo à descoberta e pensamento\nFazemos perguntas que incentivam a criança a pensar, como: 'Com que letra começa sapato?' ou 'Você conhece alguém com a letra B?'. Essas provocações desenvolvem a consciência fonológica e o raciocínio.\n7. Respeito ao ritmo individual\nNem todas as crianças aprendem no mesmo tempo. Por isso, observamos o progresso individual, oferecemos reforço, repetimos atividades e celebramos cada avanço, por menor que seja.\nResumo do Processo de Ensino do Alfabeto\nAbaixo, uma visão em etapas do processo pedagógico de alfabetização:\nEtapa\tComo fazemos\n8. Palavra significativa\tComeçamos por nomes e objetos do cotidiano\n9. Consciência fonológica\tBrincamos com sons e rimas\n10. Letra por letra\tTrabalhamos som, forma, traçado e contexto de uso\n11. Formação de sílabas\tCom letras já conhecidas (ex: PA – MA – BO)\n12. Construção de palavras\tUnindo sílabas, formando frases simples\n**", "x": -3709, "y": -7200, "width": 710, "height": 850}, {"id": "fundacaoDesign", "type": "text", "text": "### ✍️ DESIGN DE PERSONAGENS \n\n**Estilo:** Cartoon, com traços simples, expressivos e acolhedores.\n\n**Protagonistas:**\n- **Papa:** Garoto curioso em traje de astronauta.\n- **Bob:** <PERSON><PERSON><PERSON><PERSON><PERSON> (r<PERSON><PERSON> ou ser de outro mundo).\n\n**Letras Animadas:**\n- Cada letra possui olhos, boca e personalidade própria, transformando-as em personagens vivos.\n\n**Prototipação:**\n- <PERSON><PERSON> de IA (Leonardo AI, ImageFX) para gerar conceitos e variações rápidas, com finalização manual para garantir estilo único.", "x": -4521, "y": -1479, "width": 419, "height": 600}, {"id": "07d91d5f8a11e7a3", "type": "text", "text": "### ✍️ VOZ DOS PERSONAGENS\n\n", "x": -3962, "y": -1479, "width": 362, "height": 600}, {"id": "c2de983d9c5252d7", "type": "text", "text": "# Estudo de Mercado – Conteúdo Infantil no YouTube Brasil\n\n## 1. Principais Canais Infantis no YouTube Brasil\n\n|   |   |   |   |   |\n|---|---|---|---|---|\n|Canal|Inscritos (estimado)|Estilo|Público-Alvo|Diferencial|\n|Galinha Pintadinha|40M+|Músicas infantis educativas|0-4 anos|Marca global com músicas famosas|\n|Mundo Bita|15M+|Músicas temáticas educativas|2-6 anos|Visual autoral e mensagens educativas criativas|\n|<PERSON>rma da Mônica|15M+|Desenhos animados brasileiros|4-10 anos|Força nostálgica da marca|\n|Luccas Toon|40M+|Aventuras, historinhas e desafios|3-10 anos|Personagem real, narrativas com valores|\n|Gato Galáctico|20M+|Histórias criativas e desenhos|6-12 anos|<PERSON>mor, criatividade e personagens autorais|\n|Ticolicos|4M+|Músicas e aprendizado lúdico|1-5 anos|Conteúdo educativo com ritmo musical|\n|Patrulha Canina BR|-|Aventuras com personagens licenciados|3-8 anos|Franquia internacional consagrada|\n|Canal da Lelê|5M+|Vlogs e rotinas infantis|3-7 anos|Conteúdo protagonizado por criança|\n\n## 2. O que esses canais têm em comum\n\n- Personagens carismáticos e reconhecíveis  \n- Músicas autorais com alta repetição  \n- Cores vibrantes e estímulo visual constante  \n- Linguagem simples e com repetição  \n- Duração curta dos episódios (2 a 6 min)  \n- Frequência de publicação regular  \n- Estrutura fixa e previsível para crianças pequenas\n\n  \n\n## 3. Gap de Mercado Identificado\n\n- Falta de narrativas com continuidade entre letras  \n- Ausência de um mundo interligado e coeso das letras  \n- Pouco foco em emoções e desenvolvimento afetivo  \n- Pouca representação de histórias com arcos emocionais educativos\n\n## 4. Diferenciais Propostos para o Canal \"Papa e Bob no Mundo das Letras\"\n\n- Universo compartilhado entre todas as letras  \n- Foco em narrativa com emoções e valores, não apenas ensino de letras  \n- Personagens com design exclusivo, personalidade e função na história  \n- Estrutura de jornada do herói adaptada para crianças  \n- Expansão para produtos, eBooks, material didático e aplicativos\n\n## 5. Estratégias para Engajar e Viralizar\n\n- Formato em série com letras em sequência (efeito colecionável)  \n- Gancho final: teaser da próxima letra  \n- Repetição estruturada que prende a atenção infantil  \n- Jingles e músicas curtas por letra  \n- Personagens consistentes com arcos emocionais  \n- Shorts e clipes musicais para YouTube, TikTok e Reels  \n- Material extra gratuito (ex: PDF para colorir) para gerar compartilhamento\n\n**", "x": -6305, "y": -8280, "width": 640, "height": 704}, {"id": "producaoPiloto", "type": "text", "text": "###  2.1: Vídeo para captar investidor\n\n**Objetivo:** <PERSON><PERSON>r um vídeo curto (30s - 2min) para validar o conceito e usar como material para captação de investimento.\n\n**Atividades:**\n1.  Roteiro sucinto da primeira interação de Papa e Bob.\n2.  Design final dos personagens.\n3.  Prototipação de vozes e jingle.\n4.  Teste de animação com IA + acabamento manual.", "x": -4080, "y": -400, "width": 550, "height": 320}, {"id": "estrategiaPlataformas", "type": "text", "text": "### 📺  PLATAFORMAS E FORMATOS\n\n**Estratégia Multicanal:**\n\n- **YouTube (Principal):**\n  - Episódios animados completos.\n  -  Criação de playlists por temporada.\n  - Foco em monetização via Adsense.\n  - Formato 16:9.\n\n- **Spotify (e similares):**\n  - Lançamento das músicas originais como faixas de áudio.\n  - Fonte de receita via royalties de streaming (alto potencial de repetição).\n\n- **TikTok:**\n  - Conteúdos curtos e virais (clipes, danças).\n  - Foco em atração de audiência para o YouTube (funil de marketing).\n  - Formato vertical 9:16.\n\n- **Instagram / Facebook:**\n  - Engajamento com a comunidade de pais.\n  - Conteúdos curtos e virais (clipes, danças).\n  - Divulgação de bastidores, novidades e anúncios.\n\n- **Website / App Próprio:**\n  - Hub central com todos os conteúdos.\n  - Futura plataforma de e-commerce e atividades extras.", "x": -6305, "y": -9035, "width": 550, "height": 704}, {"id": "estrategiaJuridico", "type": "text", "text": "### 📜 FORMALIZAÇÃO JURÍDICA\n\n**Objetivo:** Criar a estrutura legal para operar profissionalmente e receber investimentos.\n\n**Estrutura:**\n- **Empresa:** Sociedade Limitada (Ltda) com CNPJ próprio.\n- **Acordo de Sócios:** Formaliza participações, governança e regras de distribuição de lucros.\n- **Regime Tributário:** Simples Nacional (inicialmente).\n- **Remuneração:** Sócios abrem MEI/Simples para emitir NF para a empresa principal, otimizando a carga tributária (6% vs 27.5%).\n- **Controle:** Uso de sistema (ex: Clockify) para registrar horas e justificar pagamentos.\n- **Contabilidade:** Contratação de contador para regularidade fiscal.", "x": -5710, "y": -9035, "width": 550, "height": 465}, {"id": "6d629376fbbd6da7", "type": "text", "text": "###  3.1: <PERSON>envo<PERSON><PERSON> <PERSON><PERSON><PERSON> da história\n\n**Objetivo:** → Criar a introdução da história (bigbang das letras), criar a historia dos personagens Papa e Bob, \n\n", "x": -4069, "y": 300, "width": 550, "height": 320}, {"id": "estrategiaPI", "type": "text", "text": "### 🛡️ PROTEÇÃO DE PROPRIEDADE INTELECTUAL\n\n- **<PERSON><PERSON>:** Registrar \"<PERSON> & Bob\" no INPI.\n- **Direitos Autorais:** Todo material (roteiros, personagens, músicas) pertence à empresa ou possui cessão de direitos formalizada.\n- **Músicas:** Compositor (Izac) registra e licencia para o projeto.", "x": -5070, "y": -9035, "width": 550, "height": 300}, {"id": "crescimentoGovernanca", "type": "text", "text": "### 🏛️ GOVERNANÇA & MELHORIA CONTÍNUA\n\n- **Controle:** Implementar Clockify para registro de horas dos sócios (essencial para prestação de contas a investidores/editais).\n- **An<PERSON><PERSON>e Financeira:** Revisão trimestral de ROI por canal.\n- **Roadmap Tecnológico:** Planejar a adoção de novas IAs, expansão para outros idiomas e melhorias de acessibilidade.", "x": -4456, "y": -9040, "width": 550, "height": 350}, {"id": "fedd7f376cb2a50b", "type": "text", "text": "###  4: Desenvolver história/músicas das letras\n\n**Objetivo:** → <PERSON><PERSON><PERSON> roteiro<PERSON>, letras, áudios e animações de A–Z.\n\n", "x": -4199, "y": 1020, "width": 398, "height": 200}, {"id": "39aaac241dab6a48", "type": "text", "text": "## GS – Vendas e Comercial de Relacionamento\n\n", "x": -2415, "y": -10790, "width": 400, "height": 1080}, {"id": "f1963453d41346aa", "type": "text", "text": "## Franco – Estrategista de Marca\n\n### Criação e Acompanhamento das Redes Sociais\n\n- Planejamento, linha editorial e monitoramento de performance\n    \n- YouTube: conteúdo principal e monetização\n    \n- Instagram: engajamento, bastidores e lançamentos\n    \n- Facebook: distribuição e tráfego pago\n    \n- Google: YouTube Ads, Discovery e aquisição de leads\n    \n\n### Gestão da Empresa\n\n- Planejamento e direção estratégica\n    \n- Criação de estratégias de crescimento, branding e autoridade\n    \n- Planejamento de campanhas, funis e ações de lançamento\n    \n- Posicionamento da marca como referência em educação infantil\n    \n\n### Mídias Sociais e Divulgação\n\n- Planejamento da linha editorial (Instagram, YouTube, Facebook, TikTok)\n    \n- Estratégia de engajamento e crescimento orgânico\n    \n- Monitoramento de métricas e campanhas patrocinadas (Google, Meta)\n    \n\n### Cocriação de Roteiros\n\n- Colaboração com Felipe no desenvolvimento criativo\n    \n- Garantia de coerência entre conteúdo, pedagogia e identidade da marca\n    \n\n### Financeiro Compartilhado\n\n- Realiza as reuniões quinzenais\n    \n- Apresenta métricas de cada plataforma\n    \n\n### Criação e Desenvolvimento do Site e E-commerce\n\n- Escolha e configuração da plataforma (Shopify, WooCommerce, etc.)\n    \n- Criação do layout funcional\n    \n- Integração com checkout transparente, pagamento e frete\n    \n- Configuração de e-mails automáticos\n    \n- Otimização de SEO, carregamento e responsividade\n    \n- Implementação de ferramentas (Pixel Meta, Tag Manager, Analytics)\n    \n- Garantia de segurança digital", "x": -4690, "y": -10790, "width": 561, "height": 1080}, {"id": "0673475ac475118a", "type": "text", "text": "## Felipe – Diretor Criativo e de Produção\n\n### Criação do Logo e Identidade\n\n- Logotipo principal e variações\n    \n- Paleta de cores, tipografia e estilo gráfico\n    \n- Manual da marca para uso consistente em todos os canais e materiais\n    \n\n### Design e Direção de Arte\n\n- Criação visual dos personagens, cenários, objetos e expressões\n    \n- Estilo artístico coerente com a proposta pedagógica e emocional\n    \n- Definição do tom visual dos vídeos, jogos, eBooks e redes sociais\n    \n\n### Cocriação de Roteiros\n\n- Criar histórias envolventes, com ritmo e linguagem adequados para a faixa etária\n    \n- Integrar elementos visuais e pedagógicos nos roteiros\n    \n- Garantir consistência narrativa com a identidade do projeto\n    \n\n### Produção de Vídeos\n\n- Desenvolvimento das cenas animadas, cortes e transições\n    \n- Definição do ritmo visual de acordo com a trilha sonora\n    \n- Direção visual final antes da publicação\n    \n\n### Criação dos eBooks\n\n- Cadernos de pintura com vetores originais dos personagens\n    \n- Atividades de caligrafia, números, letras e lógica visual\n    \n- Exercícios escolares temáticos com estética divertida e funcional para impressão\n    \n\n### Design do App Educativo (em parceria com Pedro Vitor)\n\n- Interface adaptada para crianças (cores, botões grandes, simplicidade visual)\n    \n- Design de telas, menus, ícones e personagens no app\n    \n- Alinhamento com o estilo dos vídeos e dos materiais pedagógicos\n    \n\n### Desenvolvimento do Design do E-commerce\n\n- Design da interface da loja (banners, produtos, ícones, estilo visual)\n    \n- Padronização de imagens de produtos e mockups\n    \n- Garantia de usabilidade e aparência profissional, lúdica e responsiva\n    \n\n### Criação das Postagens para Redes Sociais\n\n- Layouts para Instagram, Facebook, TikTok e YouTube\n    \n- Adaptação do conteúdo para cada formato (feed, reels, stories, thumbnails, etc.)", "x": -5395, "y": -10790, "width": 640, "height": 1080}, {"id": "a63ff87af5bb0589", "type": "text", "text": "**<PERSON> & Bob – No Mundo das Letras**\n\n---\n\n**Introdução da História:**  \n\nA história se inicia com um grande evento cósmico: um \"Big Bang das Letras\", que espalha todas as letras do alfabeto por diferentes mundos do universo. Essas letras se tornam seres vivos, cada uma habitando um planeta próprio com características únicas, onde tudo gira em torno de sua identidade fonética e semântica.\n\nSimultaneamente, somos apresentados à Terra e a um bebê chamado Papa, que desde o nascimento demonstra uma sensibilidade natural à linguagem e aos sons. À medida que cresce, Papa revela-se uma criança curiosa e imaginativa, apaixonada por símbolos, histórias e descobertas.\n\nAo conhecer Bob – um cachorro misterioso que se comunica apenas com ele por meio de uma coleira especial sincronizada com seu cinto tecnológico – Papa é convocado para uma missão: **viajar pelos mundos das letras** para reencontrar cada uma e restaurar a harmonia perdida do alfabeto.\n\n<PERSON><PERSON>, Papa e Bob embarcam em uma jornada de exploração interplanetária onde, a cada episódio, acessam um mundo-letra. Lá, encontram personagens, objetos, desafios e palavras que começam com aquela letra. A narrativa é contínua, ou seja, os episódios não são isolados por letra, mas fazem parte de uma trama maior que evolui à medida que os protagonistas se aproximam de completar a missão do alfabeto.\n\n\n", "x": -6480, "y": -11910, "width": 715, "height": 2200}, {"id": "3a70291ccac80a5d", "type": "text", "text": "## Victor – Comercial e Expansão de Marca\n\n- Atendimento direto com pais, escolas ou empresas\n    \n- Estratégias de divulgação com apoio de Franco\n    \n- Parcerias, colaborações e distribuição\n    \n- Suporte ao cliente", "x": -2915, "y": -10790, "width": 420, "height": 1080}, {"id": "5d63108ce9ddc7a4", "type": "text", "text": "## PV – Programador e Desenvolvedor de Jogos\n\n### Desenvolvimento de Ferramentas\n\n- Aprimoramento das inteligências artificiais para otimização de tempo e processos\n    \n- Desenvolvimento de software que une todas as views e monetização de cada plataforma\n    \n- Desenvolvimento de aplicativo para gestão financeira (despesas gerais, impostos, despesas mensais)\n    \n\n### Desenvolvimento de Jogo Interativo\n\n- Responsável pela criação do game do Papa & Bob\n    \n- Integração com o Universo da Série (personagens e cenários)\n    \n- Programação de funcionalidades educativas", "x": -4036, "y": -10790, "width": 433, "height": 1080}, {"id": "084ea75344cc1a2b", "type": "text", "text": "Gui – Diretor de Áudio e Trilha Sonora\n\n- Criação de músicas originais para personagens e episódios\n    \n- Desenvolvimento de trilhas de fundo, efeitos e sons\n    \n- Participação na criação de vozes e áudio para vídeos e audiobooks", "x": -3520, "y": -10790, "width": 508, "height": 1080}, {"id": "fundacaoConceito", "type": "text", "text": "**<PERSON> & Bob – No Mundo das Letras**\n\n---\n\n**Conceito do Projeto:**  \n“<PERSON> & Bob – No Mundo das Letras” é uma produção educacional original que integra animação, tecnologia, música, interatividade e narrativa emocional para transformar o processo de alfabetização em uma jornada divertida, afetiva e memorável.\n\nO projeto nasce com a missão de apoiar o desenvolvimento linguístico e emocional de crianças na fase da primeira infância, utilizando uma linguagem acessível, personagens cativantes e cenários imersivos. A base pedagógica é construída sobre princípios fundamentais da alfabetização como consciência fonológica, associação entre som e grafema, ampliação de vocabulário e estímulo à curiosidade cognitiva, tudo isso mediado por vínculo emocional e estímulos multissensoriais.\n\n---\n\n**Objetivo Principal:**  \nCriar um ambiente de aprendizado lúdico, afetivo e narrativo que desperte nas crianças o prazer pela linguagem, incentivando a memorização, a pronúncia correta, o reconhecimento das letras e a construção de palavras de forma leve, divertida e emocionalmente envolvente. Ao mesmo tempo, o projeto desenvolve competências como empatia, escuta ativa, resiliência, trabalho em equipe e resolução de problemas.\n\n---\n\n**Público-Alvo:**  \nCrianças na primeira infância (3 a 8 anos), com atenção especial para a fase de alfabetização, considerando também famílias, educadores e instituições de ensino infantil como público secundário e formador.\n\n--- \n\n**Componentes do Projeto:**\n\n1. **Série Animada (YouTube)**\n    \n    - Episódios com 5 a 8 minutos de duração.\n        \n    - Estrutura narrativa contínua com foco na missão de recuperar as letras.\n        \n    - Apresentação de personagens-letras e desafios pedagógicos por episódio.\n        \n    - Conteúdo licenciado para escolas, plataformas e streamings.\n        \n2. **Conteúdos de Redes Sociais (TikTok, Instagram, Facebook, Kwai)**\n    \n    - Clipes curtos com músicas das letras, rimas e interações de Papa e Bob.\n        \n    - Quadros como “Qual é a letra de hoje?”, “Desafio do som”, “Palavra do dia”.\n        \n    - Bastidores da produção, making-ofs e conteúdos educativos para famílias.\n        \n3. **Aplicativo Interativo (Android e iOS)**\n    \n    - Jogos de associação letra-som-imagem.\n        \n    - Navegação pelos mundos das letras em formato de mapa interativo.\n        \n    - Área para colorir os personagens, ouvir as músicas e revisar episódios.\n        \n    - Conteúdo adaptado para escolas e reforço em casa.\n        \n4. **eBooks para Colorir e Aprender**\n    \n    - Materiais impressos ou digitais para reforçar o conteúdo offline.\n        \n    - Atividades com base nas letras, palavras e personagens de cada episódio.\n        \n    - Estímulo motor e visual com orientação pedagógica.\n        \n\n---\n\n**Diferenciais do Projeto:**\n\n- Narrativa contínua e estruturada, que estimula o vínculo emocional e o senso de progressão.\n    \n- Personagens com forte identidade visual e emocional, promovendo identificação imediata.\n    \n- Abordagem multissensorial (visual, auditiva, motora e afetiva).\n    \n- Integração entre entretenimento e aprendizado com base na BNCC (Base Nacional Comum Curricular).\n    \n- Conteúdo acessível e distribuído gratuitamente pelas redes sociais e plataformas educativas.\n    \n- Possibilidade de expansão para outras línguas e temas (números, emoções, cotidiano, etc).\n", "x": -7240, "y": -11910, "width": 715, "height": 2200}, {"id": "2288b7172111d658", "type": "text", "text": "### 5.1: FORMALIZAÇÃO JURÍDICA\n\n**Objetivo:** Criar a estrutura legal para operar profissionalmente e receber investimentos.\n\n**Estrutura:**\n- **Empresa:** Sociedade Limitada (Ltda) com CNPJ próprio.\n- **Acordo de Sócios:** Formaliza participações, governança e regras de distribuição de lucros.\n- **Regime Tributário:** Simples Nacional (inicialmente).\n- **Remuneração:** Sócios abrem MEI/Simples para emitir NF para a empresa principal, otimizando a carga tributária (6% vs 27.5%).\n- **Controle:** Uso de sistema (ex: Clockify) para registrar horas e justificar pagamentos.\n- **Contabilidade:** Contratação de contador para regularidade fiscal.", "x": -4856, "y": 1660, "width": 440, "height": 340}, {"id": "77e1425b4920c950", "type": "text", "text": "###  5.4: Lançamento da temporada das letras\n\n**Objetivo:** → Postagens, vídeo piloto no YouTube.\n\n**Estratégia:**\n- **Campanha de Tração:** Uso de anúncios de baixo orçamento (Meta/Google) para impulsionar o canal e atingir os primeiros 1.000 inscritos, um requisito para a monetização no YouTube.\n", "x": -4268, "y": 1660, "width": 420, "height": 1040}, {"id": "e1ff34fa7284810e", "type": "text", "text": "###  5.5: Vendas/divulgação (letras)\n\n**Objetivo:** → Projeto ser reconhecido pelo maior numero de pessoas para gerar branding\n\n", "x": -3798, "y": 1660, "width": 420, "height": 1040}, {"id": "c336baa8034dd843", "type": "text", "text": "### 📈 5.6: ANÁLISE DE MÉTRICAS & INTERAÇÃO\n\n**Processo:**\n- As reuniões quinzenais analisarão métricas de desempenho dos canais.\n- **KPIs do YouTube:** Views, CTR (Click-Through Rate), Retenção de audiência.\n- **KPIs de Negócio:** Custos, ROI por canal.\n\n**Ação:**\n- Ajustar estratégias com base nos dados (ex: mudar estilo de thumbnail, duração dos vídeos, temas).", "x": -3224, "y": 1700, "width": 550, "height": 400}, {"id": "ab50397b696d477f", "type": "text", "text": "### 💲 5.7: ATIVAÇÃO DA MONETIZAÇÃO\n\n- **<PERSON><PERSON>se (YouTube):** Ativado após atingir 4.000 horas de exibição e 1.000 inscritos. Previsão de receita a partir do 6º-8º mês pós-lançamento.\n- **Streaming Musical:** Receitas recorrentes desde o início.\n", "x": -3224, "y": 2300, "width": 550, "height": 350}, {"id": "94a3a6f9d71b53e7", "type": "text", "text": "###  5.2: PROTEÇÃO DE PROPRIEDADE INTELECTUAL\n\n- **<PERSON><PERSON>:** Registrar \"<PERSON> & Bob\" no INPI.\n- **Direitos Autorais:** Todo material (roteiros, personagens, músicas) pertence à empresa ou possui cessão de direitos formalizada.\n- **Músicas:** Compositor (Izac) registra e licencia para o projeto.", "x": -4856, "y": 2030, "width": 440, "height": 300}, {"id": "7b7955913fb4d577", "type": "text", "text": "###  5.3:  Pré-Lançamento\n\n**Objetivo:** → estrategias, postagens\n", "x": -4856, "y": 2360, "width": 440, "height": 340}, {"id": "b4a22473c55b288f", "type": "text", "text": "### 💰 ESTRATÉGIAS DE MONETIZAÇÃO\n\n**Plano Progressivo:** <PERSON><PERSON> criar valor (conteúdo/público), depois monetizar.\n\n**Fontes de Receita:**\n1.  **YouTube Adsense:** Principal fonte de longo prazo, após atingir critérios do programa.\n    Eestimativa: R$5 a R$25 por mil views. \n    Canal com 100 mil inscritos pode gerar entre R$5.000 a R$15.000/mês.\n\n2.  **Royalties de Streaming Musical:** Receita recorrente via Spotify, etc.\n    Estimativa de US$ 3–5 por 1 000 streams.\n\n3.  **E-commerce Próprio:** \n    Venda de eBooks infantis. Os materiais serão em formato PDF para impressão, com desenhos para colorir, atividades escolares e alfabetização lúdica.\n\n4.  **Parcerias e Patrocínios:**\n    - Licenciamento para escolas.\n    - Patrocínio de episódios/temporadas por empresas.\n    - Venda de direitos para plataformas de streaming maiores.\n\n5.  **TikTok:** \n    Potencial de receita (estimativa: R$0,10 a R$0,50 por mil views), além de TikTok Shop e presentes ao vivo.\n\n6. **Instagram:** \n    Monetização indireta via parcerias com marcas e vendas via link para produtos. Potencial para assinantes.\n\n7.  **Aplicativo de Jogos Educativos:** \n    Jogos interativos baseados nas letras do alfabeto, números e atividades pedagógicas. \n    A monetização ocorrerá por meio de diferentes estratégias combinadas:    \n    Assinatura mensal/anual para liberar todos os conteúdos sem anúncios; Compras dentro do app (in-app purchases), como fases extras, personagens ou atividades especiais;\n    Anúncios apropriados para o público infantil, com uso de vídeos recompensados e formatos seguros;\n\nPromoção de produtos próprios, como os eBooks para impressão;\n\nPossibilidade de licenciamento de personagens e parcerias com escolas e plataformas educacionais.\n\n8. **Produtos Físicos:** Expansão para cadernos de pintar, roupas, estojos, mochilas e outros itens licenciados. Estes produtos têm o potencial de triplicar o faturamento com margens maiores e escalar a marca para além do digital.\n9. **Licenciamento e Parcerias Estratégicas**: Com uma base de fãs sólida, o licenciamento da marca para outros produtos e mídias, além de parcerias, será uma fonte de receita recorrente.\n10. **Expansão Internacional:** Potencial para lançar o canal em outros idiomas (inglês, espanhol, francês).", "x": -1595, "y": -11877, "width": 660, "height": 1210}, {"id": "0c74033ddc9106ae", "type": "text", "text": "📊 Orçamento Mensal \n(Cenário Enxuto –  Planos básicos de cada plataforma)\n\n1. Criação & Conteúdo\n- ElevenLabs (vozes Papa e Bob) – R$99\n- Suno AI (criação de músicas) – R$150\n- Freepik AI (imagens consistentes do Papa e Bob) – R$180\n- Hedra AI (animação labial e edição de vídeo) – R$150\n\n2. Tecnologia & Desenvolvimento\n- Cursor (desenvolvimento do app) – R$240\n\n3. Gestão & Operacional\n- Contabilidade – R$350\n\n🔢 Total Geral (média câmbio atual)\n👉 R$ 1.169 por mês\n\n**📌 Excluído:** Psicóloga/Pedagoga (R$ 2.250) - serão contratadas conforme necessidade", "x": -120, "y": -11342, "width": 540, "height": 805}, {"id": "1700f29547c8fdbb", "type": "text", "text": "📊 Orçamento Mensal \n(Cenário Profissional – Melhor Plano de cada plataforma)\n\n1. Criação & Conteúdo\n- ChatGPT (roteiros, textos, prompts) – R$100\n- ElevenLabs (vozes <PERSON> e Bob) – R$99\n- Suno AI (criação de músicas) – R$150\n- Freepik AI (imagens consistentes do Papa e Bob) – R$180\n- ImageFX (Google) – Incluso no Google AI Ultra\n- Kling AI / Veo 3 (vídeos curtos e clipes animados) – Incluso no Google AI Ultra\n- Hedra AI (animação labial e edição de vídeo) – R$150\n\n2. Design & Identidade Visual\n- CorelDRAW Graphics Suite (edição vetorial e design) – R$155\n- Adobe Creative Cloud (Photoshop) – R$224\n- CapCut (edição de vídeos curtos) – R$35\n\n3. Tecnologia & Desenvolvimento\n- Google AI Ultra (pacote com Veo 3 + ImageFX + Gemini) – R$609\n- Cursor (desenvolvimento do app) – R$240\n\n4. Gestão & Operacional\n- Contabilidade – R$350\n\n🔢 Total Geral (média câmbio atual)\n👉 R$ 2.142 por mês\n\n**📌 Excluído:** Psicóloga/Pedagoga (R$ 2.250) - serão contratadas conforme necessidade", "x": -800, "y": -11342, "width": 540, "height": 840}, {"id": "fundacaoEquipe", "type": "text", "text": "### 🧑‍🤝‍🧑 EQUIPE, PAPÉIS & SOCIEDADE\n\n**Sócios e Participações:**\n- **<PERSON>:** 45% (Diretor Criativo/Produção)\n- **<PERSON>:** 35% (Estrategista de Marca)\n- **PV:** 7% (Desenvolvedor)\n- **<PERSON>:** 5% (Comercial)\n- **<PERSON><PERSON>:** 5% (Diretor de Áudio)\n- **GS:** 2% (Comercial de Relacionamento)\n- **Caixa:** 1% \n\nComissão sobre vendas, contratos e investimentos:  \n10% acrescentados à sua respectiva porcentagem de participação\n\n**Governança:**\n- **Co-CEOs:** <PERSON> (visão artística) & <PERSON> (visão de negócios).\n- **Reuniões:** Quinzenais para alinhamento estratégico, financeiro e de progresso.\n- **Funções:** Delineadas em arquivo interno para cobrir todas as frentes (criativa, técnica, marketing, comercial).", "x": -4555, "y": -11857, "width": 590, "height": 647}, {"id": "3df759c94d8d32be", "type": "text", "text": "##  CEOs - <PERSON> \n\n### Visão Estratégica do Projeto\n\n- Educação infantil com criatividade, afeto e inovação\n    \n\n### Criação e Gestão do E-commerce\n\n- Organização dos produtos\n    \n- Plataformas e meios de pagamento\n    \n- Estratégias de conversão, frete e suporte\n    \n- Monitoramento de indicadores (visualizações, receita, CAC, ROI, feedback)\n    \n\n### Tomada de Decisão\n\n- Todas as decisões importantes são discutidas e validadas por ambos\n    \n\n### Direção Criativa e Estratégica Unificada\n\n- Equilíbrio entre criação artística e marketing\n    \n\n### Gestão de Pessoas e Comunicação Interna\n\n- Coordenação do time e fluxo de trabalho\n    \n\n### Expansão e Parcerias\n\n- Licenciamentos, colaborações e novos formatos\n    \n\n### Análise de Indicadores e Resultados\n\n- Métricas de YouTube, Spotify, vendas e alcance\n    \n\n### Supervisão de Qualidade Geral\n\n- Excelência visual, sonora, pedagógica e comercial do projeto", "x": -3807, "y": -11877, "width": 667, "height": 687}, {"id": "b6d5b516e1527193", "type": "text", "text": "###  6.1: <PERSON><PERSON><PERSON> e<PERSON><PERSON>s\n\n\n", "x": -4266, "y": 3170, "width": 455, "height": 340}, {"id": "b104e2b51bcd83d6", "type": "text", "text": "###  6.2: Criar e-commerce\n\n\n", "x": -3699, "y": 3170, "width": 455, "height": 340}, {"id": "producaoOrcamento", "type": "text", "text": "### 📊 ORÇAMENTO E RECURSOS\n\n\n**Orçamento Total do Projeto (Temporada 1):**\n- **Estimativa (Edital):** R$ 96.400,00.\n- **Inclui:**\n  - Ferramentas de IA e Software.\n  - Mão de obra (roteiristas, ilustradores, animadores - pode ser o trabalho dos sócios).\n  - Contabilidade e custos administrativos.\n\n**Recursos-Chave:**\n- **Tecnologia:** A stack de IA e software.\n- **Humanos:** Tempo e expertise dos sócios.\n- **Financeiro:** Capital próprio inicial + captação externa.", "x": -440, "y": -11920, "width": 540, "height": 437}, {"id": "centralTimeline", "type": "text", "text": "### 🗺️ ROADMAP VISUAL DO PROJETO\n\nEste nó central conecta todas as fases do projeto, representando a **timeline macro** e a jornada integrada desde a concepção até a escala.\n\n**Fluxo Principal:**\nCONCEITO, SÓCIOS, FINANCEIRO\nESTRATÉGIAS & ESTRUTURAÇÃO\nPILARES PRA CRIAR O PROJETO \nPLANEJAMENTO PRO DESENVOLVIMENTO DOS PRODUTOS/SERVIÇOS DO PROJETO\nFase 1: <PERSON><PERSON>r Introdução da história\nFase 2: Criação Base do Projeto\nFase 3: Vídeo pra captar investidor\nFase 4: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> das Letras\nFase 5: Primeira Onda de Lançamento (JANEIRO 2026 - FÉRIAS ESCOLARES)\nFase 6: Desenvolvimento dos Ebooks, e-commerce e Aplicativo\nFase 7: Segunda Onda de Lançamento (MARÇO 2026 - INICIO DAS AULAS)\nFase 8: <PERSON><PERSON><PERSON><PERSON><PERSON> dos Números\nFase 9: Terceira Onda de Lançamento (JULHO 2026 - FÉRIAS DE INVERNO)\nFase 10: <PERSON><PERSON><PERSON> aprendizado completo (alfabeto + matemática).\nFase 11: Quarta Onda de Lançamento (JANEIRO 2027 - FÉRIAS DE VERÃO)\nFase 12: Expandir para mercado físico\nFase 13: Expansão Mundial (FAZER TUDO EM INGLES)\nFase 14: Filme para cinema e Netflix", "x": -4078, "y": -13380, "width": 780, "height": 635}, {"id": "fe1be537e2f349c8", "type": "text", "text": "###  6.4: APP & E-COMMERCE INTEGRADO\n\n**MVP 1 (Conteúdo):**\n- Catálogo de vídeos.\n- Playlist de músicas.\n- Seção com atividades para imprimir (PDFs).\n\n**MVP 2 (Comércio):**\n- Loja in-app para venda de produtos físicos e digitais.\n- **Tecnologia:** Shopify Headless ou similar, com gateway de pagamento (Pix/Cartão).", "x": -3699, "y": 3720, "width": 455, "height": 420}, {"id": "b9f9bea857c7b17a", "type": "text", "text": "###  6.3: <PERSON><PERSON><PERSON> app\n\n**Back<PERSON> Inicial (MVP):**\n- Mini-game para arrastar letras e formar palavras.\n- Feedback sonoro com as vozes dos personagens.\n- **Tecnologia:** Unity ou Godot.\n\n**Dependências:** Assets visuais e de áudio já criados para a série.\n\n**⏱️ Estimativa de Horas:**\n- **MVP 1 (Conteúdo):** ~120h (2 meses)\n- **MVP 2 (Comércio):** ~180h (3 meses)\n- **Total MVP:** ~300h\n\n**📱 Capacidade de Desenvolvimento:**\n- **PV:** 3h/dia × 5 dias × 12 meses = 180h/ano\n- **Foco:** 60% do tempo dedicado ao app = 108h/ano", "x": -4266, "y": 3760, "width": 455, "height": 340}, {"id": "0599515f519f77e5", "type": "text", "text": "###  7.1:  Pré-Lançamento\n\n**Objetivo:** → estrategias, postagens\n", "x": -4833, "y": 4580, "width": 440, "height": 1040}, {"id": "29f807e19abc968b", "type": "text", "text": "###  7.2: Lançamento do ecommerce e app (letras)\n\n**Objetivo:** → Venda ativa de produtos, download de app, coleta de feedback.", "x": -4312, "y": 4580, "width": 420, "height": 1040}, {"id": "48ee6864a5003aeb", "type": "text", "text": "###  7.3: Vendas/divulgação (ecommerce e app)\n\n**Objetivo:** → App ser reconhecido pelo maior numero de pessoas para gerar branding\n\nComeçar vender os Ebooks.\n\n", "x": -3812, "y": 4580, "width": 420, "height": 1040}, {"id": "6aa0b45e6b63b913", "type": "text", "text": "### 📈 7.4: ANÁLISE DE MÉTRICAS & INTERAÇÃO\n\n**Processo:**\n- As reuniões quinzenais analisarão métricas de desempenho dos canais.\n- **KPIs do YouTube:** Views, CTR (Click-Through Rate), Retenção de audiência.\n- **KPIs de Negócio:** Custos, ROI por canal.\n\n**Ação:**\n- Ajustar estratégias com base nos dados (ex: mudar estilo de thumbnail, duração dos vídeos, temas).", "x": -3232, "y": 4580, "width": 550, "height": 1040}, {"id": "1e48650cbc03ec5b", "type": "text", "text": "###  INFLUENCERS (PROFESSORA E PSICOLÓGA)\n\nObjetivo: Humanizar os perfis das redes sociais da empresa com alguém referencia da área de pedagogia e psicologia infantil, assim consequentemente vamos passar mais credibilidade e valor.\n\nAtividade: Encontrar uma professora ou psicóloga influencer para ser a garota propaganda da empresa ", "x": -5070, "y": -8681, "width": 550, "height": 350}, {"id": "f69544539296ea83", "type": "text", "text": "# 📊 CAPACIDADE DE PRODUÇÃO ATUALIZADA – 2025\n\n## 🟡 Distribuição das 4.320 Horas/Ano (Sócios)\n\n| Etapa                            | % do Tempo | Horas/Ano | Aplicação |\n| -------------------------------- | ----------- | --------- | --------- |\n| Pesquisa pedagógica e roteiros   | 25%         | 1.080h    | Desenvolvimento de conteúdo |\n| Criação visual e design          | 30%         | 1.296h    | Personagens, cenários, identidade |\n| Produção de áudio e música       | 20%         | 864h      | Trilhas, vozes, efeitos sonoros |\n| Animação e edição                | 20%         | 864h      | Vídeos, sincronização, finalização |\n| Gestão e coordenação             | 5%          | 216h      | Reuniões, planejamento, qualidade |\n\n**🔢 Total Anual:** **4.320 horas** (base: 6h/dia × 5 dias × 12 meses)\n\n---\n\n## 🔵 Capacidade Mensal por Ferramenta (Valores Atualizados)\n\n| Ferramenta | Créditos/Mês | Episódios 5min | Custo/Mês | Status |\n|------------|--------------|----------------|-----------|---------|\n| Veo-3 Pro  | 1.000        | ~4 episódios   | R$ 100    | Atual |\n| Veo-3 Plus | 7.500        | ~30 episódios  | R$ 600    | Recomendado |\n| Veo-3 Ultra| 12.500       | ~50 episódios  | R$ 1.209  | Escalável |\n| Edra AI    | 1.250        | ~5 episódios   | R$ 320    | Alternativa |\n\n**📌 Nota:** Capacidade baseada em 75 takes/episódio (20 em Qualidade Máxima + 55 em Normal)\n\n---\n\n## 💰 Orçamento Mensal Atualizado (Cenário Profissional)\n\n| Categoria | Ferramenta | Custo/Mês | Total |\n|-----------|------------|-----------|-------|\n| **Vídeo** | Veo-3 Plus | R$ 600    | R$ 600 |\n| **Imagem** | Freepik     | R$ 180    | R$ 180 |\n| **Design** | Adobe Suite | R$ 224    | R$ 224 |\n| **IA Geral**| ChatGPT     | R$ 100    | R$ 100 |\n| **Dev**   | Cursor      | R$ 1.200  | R$ 1.200 |\n| **Voz**   | ElevenLabs  | R$ 99     | R$ 99 |\n| **Contab.**| Contabilidade| R$ 350    | R$ 350 |\n\n**🔢 Total Mensal:** **R$ 2.953**\n\n**📌 Excluído:** Psicóloga/Pedagoga (R$ 2.250) - serão contratadas conforme necessidade", "x": -3813, "y": -9033, "width": 660, "height": 1313}, {"id": "estrategiaInvestimento", "type": "text", "text": "### 💸 CAPTAÇÃO DE INVESTIMENTO\n\n**Objetivo:** Acelerar o desenvolvimento e cobrir custos iniciais.\n\n**Frentes de Captação:**\n\n1.  **Patrocínio via Lei de Incentivo:**\n    - Modelo: Empresas abatem do IRPJ.\n    - Ação: Abordar contatos e empresas que já utilizam o mecanismo.\n\n2.  **Investidor-Anjo / Parceria Privada:**\n    - Modelo: Injeção de capital em troca de participação societária (equity).\n    - Ação: Preparar pitch deck e abordar investidores em potencial da rede de contatos.\n\n**Ação Imediata:** \"O passo número 1 é ter material para conseguir ganhar investidor\" -> Produzir um piloto/teaser.", "x": -655, "y": -8968, "width": 550, "height": 472}, {"id": "estrategiaDecisao", "type": "text", "text": "### ⚖️ PONTO DE DECISÃO: CENÁRIOS DE INVESTIMENTO\n\nEste nó representa a principal bifurcação no cronograma do projeto, dependendo do sucesso na captação de recursos.\n\nO que muda não é *o que* fazer, mas a **velocidade** e **quem arca com os custos**.", "x": -655, "y": -8376, "width": 550, "height": 350}, {"id": "estrategiaCenarioCom", "type": "text", "text": "#### ✅ CENÁRIO COM APORTE\n\n- **<PERSON><PERSON>.**\n- Equipe com maior dedicação (pró-labore).\n- Contratação de ajuda externa.\n- Cronograma encurtado.\n- Risco financeiro pessoal reduzido.", "x": -1135, "y": -8086, "width": 300, "height": 370}, {"id": "estrategiaCenarioSem", "type": "text", "text": "#### ❌ CENÁRIO SEM APORTE\n\n- **<PERSON><PERSON> (Bootstrapped).**\n- <PERSON><PERSON> ma<PERSON> lento (horas livres).\n- Foco em validar o produto com mínimo de recursos.\n- Mantém 100% da propriedade e liberdade.", "x": 120, "y": -8076, "width": 300, "height": 380}, {"id": "b9d8fdff1a8f4577", "type": "text", "text": "# 📊 Estimativa de Horas por Ebook\n\n---\n\n## 1. 🎨 **Ebook para Colorir – Alfabeto (28–30 páginas)**\n\n- Ilustrações: ~18h (média de 40 min por página, com variação de complexidade).\n    \n- Diagramação/layout: ~6h\n    \n- Revisão pedagógica: ~3h\n    \n- Edi<PERSON> final/exportação: ~2h  \n    **Total estimado:** **29h**\n    \n\n---\n\n## 2. 🔢 **Ebook para Colorir – Números (12–15 páginas)**\n\n- Ilustrações: ~7h (40 min cada, mas menos páginas que o alfabeto).\n    \n- Diagramação/layout: ~3h\n    \n- Revisão pedagógica: ~1,5h\n    \n- Edição final/exportação: ~1h  \n    **Total estimado:** **12,5h**\n    \n\n---\n\n## 3. ✏️ **Ebook de Letras com Exercícios (28–30 páginas)**\n\n- Ilustrações + exercícios: ~20h (porque além do desenho tem a criação da atividade).\n    \n- Diagramação/layout: ~7h\n    \n- Revis<PERSON> pedagógica: ~4h\n    \n- Edição final/exportação: ~2h  \n    **Total estimado:** **33h**\n    \n\n---\n\n## 4. 🖍️ **Ebook Colorir + Escrever (28–30 páginas)**\n\n- Ilustrações + linhas pontilhadas: ~16h (mais simples que o de exercícios).\n    \n- Diagramação/layout: ~6h\n    \n- Revisão pedagógica: ~3h\n    \n- Edição final/exportação: ~2h  \n    **Total estimado:** **27h**\n    \n\n---\n\n## 5. ➕ **Ebook de Matemática Básica (22–25 páginas)**\n\n- Criação dos exercícios + ilustrações: ~14h\n    \n- Diagramação/layout: ~5h\n    \n- Revisão pedagógica: ~3h\n    \n- Edição final/exportação: ~2h  \n    **Total estimado:** **24h**\n    \n\n---\n\n# 📌 Resumo Geral (5 ebooks iniciais)\n\n|Ebook|Páginas (aprox.)|Horas (aprox.)|\n|---|---|---|\n|Alfabeto para colorir|28–30|29h|\n|Números para colorir|12–15|12,5h|\n|Alfabeto com exercícios|28–30|33h|\n|Colorir + escrever|28–30|27h|\n|Matemática básica|22–25|24h|\n|**Total geral**|~120–130|**125,5h**|\n\n---\n\n👉 Ou seja, só a primeira fase (5 ebooks) já soma **cerca de 125 horas de trabalho**.  \nIsso dá, por exemplo:\n\n- **3h/dia → 42 dias (1 mês e meio)**\n    \n- **5h/dia → 25 dias (1 mês)**\n    \n- **8h/dia → 16 dias (pouco mais de 2 semanas)**", "x": -3075, "y": -9033, "width": 655, "height": 1313}], "edges": [{"id": "edge2", "fromNode": "groupCentro", "fromSide": "bottom", "toNode": "groupFundacao", "toSide": "top"}, {"id": "edge15", "fromNode": "estrategiaInvestimento", "fromSide": "bottom", "toNode": "estrategiaDecisao", "toSide": "top"}, {"id": "edge16", "fromNode": "estrategiaDecisao", "fromSide": "left", "toNode": "estrategiaCenarioCom", "toSide": "right", "label": "✅ Com Aporte"}, {"id": "edge17", "fromNode": "estrategiaDecisao", "fromSide": "right", "toNode": "estrategiaCenarioSem", "toSide": "left", "label": "❌ Sem Aporte"}, {"id": "adddfbd8f9a043d5", "fromNode": "7b7955913fb4d577", "fromSide": "right", "toNode": "77e1425b4920c950", "toSide": "left"}, {"id": "9b3f6dae0e40b15b", "fromNode": "77e1425b4920c950", "fromSide": "right", "toNode": "e1ff34fa7284810e", "toSide": "left"}, {"id": "343971dfccef3bc9", "fromNode": "b6d5b516e1527193", "fromSide": "right", "toNode": "b104e2b51bcd83d6", "toSide": "left"}, {"id": "c67d5817a2aa90b8", "fromNode": "c21373b7467e3404", "fromSide": "right", "toNode": "2a186a80e0bd1cb9", "toSide": "left"}, {"id": "2a39e244d5f69781", "fromNode": "3f69493367d94ce9", "fromSide": "right", "toNode": "c21373b7467e3404", "toSide": "left"}, {"id": "fa98ae321d5ea5e7", "fromNode": "67713963b9b9b736", "fromSide": "right", "toNode": "7fee1fbd49684d0a", "toSide": "left"}, {"id": "08648f91282816c7", "fromNode": "ec2ed858be97e358", "fromSide": "right", "toNode": "67713963b9b9b736", "toSide": "left"}, {"id": "c8922e6f57d888b5", "fromNode": "b104e2b51bcd83d6", "fromSide": "bottom", "toNode": "fe1be537e2f349c8", "toSide": "top"}, {"id": "46dc3a567bf177c8", "fromNode": "b9f9bea857c7b17a", "fromSide": "right", "toNode": "fe1be537e2f349c8", "toSide": "left"}, {"id": "8e692a6330809d50", "fromNode": "fe1be537e2f349c8", "fromSide": "top", "toNode": "b104e2b51bcd83d6", "toSide": "bottom"}, {"id": "748fbb5e325dc6f1", "fromNode": "producaoStack", "fromSide": "right", "toNode": "84751eda9189ce53", "toSide": "left"}, {"id": "821f952cdb660472", "fromNode": "84751eda9189ce53", "fromSide": "right", "toNode": "e7c058ca478bace7", "toSide": "left"}, {"id": "38666ee3194a47e6", "fromNode": "e7c058ca478bace7", "fromSide": "right", "toNode": "94a1f7fa9477f41a", "toSide": "left"}, {"id": "84f80a385664a1ac", "fromNode": "groupEstrategia", "fromSide": "bottom", "toNode": "groupProducao", "toSide": "top"}, {"id": "8911554b232f0616", "fromNode": "84751eda9189ce53", "fromSide": "bottom", "toNode": "producaoRoteiro", "toSide": "top"}, {"id": "93a08d887a2ad0c7", "fromNode": "e7c058ca478bace7", "fromSide": "bottom", "toNode": "producaoRoteiro", "toSide": "top"}, {"id": "425f8287d8f994d4", "fromNode": "94a1f7fa9477f41a", "fromSide": "bottom", "toNode": "producaoRoteiro", "toSide": "top"}, {"id": "f006c03020759270", "fromNode": "groupCentro", "fromSide": "bottom", "toNode": "563533bfb793a67b", "toSide": "top"}, {"id": "3dfbc912b77c12f2", "fromNode": "groupCentro", "fromSide": "bottom", "toNode": "104e14dea488b88a", "toSide": "top"}, {"id": "4ff666b96cb3a300", "fromNode": "groupFundacao", "fromSide": "bottom", "toNode": "groupEstrategia", "toSide": "top"}, {"id": "7c542fb2ff5e3993", "fromNode": "563533bfb793a67b", "fromSide": "bottom", "toNode": "groupEstrategia", "toSide": "top"}, {"id": "203fc41103a2b703", "fromNode": "estrategiaInvestimento", "fromSide": "top", "toNode": "104e14dea488b88a", "toSide": "bottom"}, {"id": "5f5d175abe34dd4a", "fromNode": "producaoStack", "fromSide": "bottom", "toNode": "producaoRoteiro", "toSide": "top"}, {"id": "fa69b4e74df53a4d", "fromNode": "20c26766a5fe666b", "fromSide": "bottom", "toNode": "fundacaoTemporadas", "toSide": "top"}, {"id": "8f895a38b1842119", "fromNode": "fundacaoTemporadas", "fromSide": "bottom", "toNode": "producaoPipeline", "toSide": "top"}, {"id": "167f9273c7649aee", "fromNode": "a12b26a3d738abd2", "fromSide": "right", "toNode": "ff0263599773f4bc", "toSide": "left"}, {"id": "e85bd19899745c0b", "fromNode": "producaoPipeline", "fromSide": "right", "toNode": "ff0263599773f4bc", "toSide": "left"}, {"id": "480dc61734634b7d", "fromNode": "producaoRoteiro", "fromSide": "bottom", "toNode": "0567763ad97f170f", "toSide": "top"}, {"id": "e937766670edf4b4", "fromNode": "c336baa8034dd843", "fromSide": "bottom", "toNode": "ab50397b696d477f", "toSide": "top"}, {"id": "fc05116f2dab3a7e", "fromNode": "e1ff34fa7284810e", "fromSide": "right", "toNode": "c336baa8034dd843", "toSide": "left"}, {"id": "e120d16e807afdb3", "fromNode": "104e14dea488b88a", "fromSide": "bottom", "toNode": "groupEstrategia", "toSide": "top"}, {"id": "829a5972c10d3f67", "fromNode": "fundacaoEquipe", "fromSide": "right", "toNode": "3df759c94d8d32be", "toSide": "left"}, {"id": "f0fed3d354f118c2", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "0673475ac475118a", "toSide": "top"}, {"id": "a39ccd5ceda6ae5a", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "f1963453d41346aa", "toSide": "top"}, {"id": "43e4a887daab99a8", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "5d63108ce9ddc7a4", "toSide": "top"}, {"id": "52dbe560e5df1e55", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "084ea75344cc1a2b", "toSide": "top"}, {"id": "8866eae3647d0a1c", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "3a70291ccac80a5d", "toSide": "top"}, {"id": "386d9a950e99e174", "fromNode": "3df759c94d8d32be", "fromSide": "bottom", "toNode": "39aaac241dab6a48", "toSide": "top"}, {"id": "d91a5b96a048c6dc", "fromNode": "2288b7172111d658", "fromSide": "right", "toNode": "77e1425b4920c950", "toSide": "left"}, {"id": "d626ec7e90c2e2f6", "fromNode": "94a3a6f9d71b53e7", "fromSide": "right", "toNode": "77e1425b4920c950", "toSide": "left"}, {"id": "afc06517fe5ab326", "fromNode": "4bbef4c44f28c9c6", "fromSide": "bottom", "toNode": "fundacaoDesign", "toSide": "top"}, {"id": "512ae78afb7dfc8a", "fromNode": "4bbef4c44f28c9c6", "fromSide": "bottom", "toNode": "07d91d5f8a11e7a3", "toSide": "top"}, {"id": "c43f791af7481267", "fromNode": "0567763ad97f170f", "fromSide": "bottom", "toNode": "44723a37a4cde543", "toSide": "top"}, {"id": "9e80ab951d324a4f", "fromNode": "3af968961d8204f2", "fromSide": "bottom", "toNode": "d3059a3f0d00abb6", "toSide": "top"}, {"id": "99d37c128e3d5a62", "fromNode": "29f807e19abc968b", "fromSide": "right", "toNode": "48ee6864a5003aeb", "toSide": "left"}, {"id": "985af8308a861dbf", "fromNode": "0599515f519f77e5", "fromSide": "right", "toNode": "29f807e19abc968b", "toSide": "left"}, {"id": "5a77a27e4080faf0", "fromNode": "48ee6864a5003aeb", "fromSide": "right", "toNode": "6aa0b45e6b63b913", "toSide": "left"}, {"id": "526290ee248fbad2", "fromNode": "0ce79d8a483adcc7", "fromSide": "bottom", "toNode": "d5b0dd58f4520791", "toSide": "top"}, {"id": "f9c1a5fb2d942371", "fromNode": "d3059a3f0d00abb6", "fromSide": "bottom", "toNode": "0ce79d8a483adcc7", "toSide": "top"}, {"id": "cab83b5535400cbd", "fromNode": "d5b0dd58f4520791", "fromSide": "bottom", "toNode": "9b4d2f911922567a", "toSide": "top"}, {"id": "a6c94dbdc13d237d", "fromNode": "9b4d2f911922567a", "fromSide": "bottom", "toNode": "d5245cf1a93f297e", "toSide": "top"}, {"id": "b34eb4e87bdff19b", "fromNode": "d5245cf1a93f297e", "fromSide": "bottom", "toNode": "acc2c00ecbb4f67f", "toSide": "top"}, {"id": "27969119c636009f", "fromNode": "acc2c00ecbb4f67f", "fromSide": "bottom", "toNode": "22f227900a0f9caf", "toSide": "top"}, {"id": "9bd6c1f1b1296cad", "fromNode": "22f227900a0f9caf", "fromSide": "bottom", "toNode": "30569de64ccba295", "toSide": "top"}, {"id": "3fa76fb480330cdc", "fromNode": "44723a37a4cde543", "fromSide": "bottom", "toNode": "producaoPiloto", "toSide": "top"}, {"id": "6461f78e46d066e4", "fromNode": "producaoPiloto", "fromSide": "bottom", "toNode": "6d629376fbbd6da7", "toSide": "top"}, {"id": "9532349f35453c20", "fromNode": "a12b26a3d738abd2", "fromSide": "bottom", "toNode": "94243467723842f9", "toSide": "top"}, {"id": "fc66cd3ef307fb88", "fromNode": "94243467723842f9", "fromSide": "bottom", "toNode": "producaoPipeline", "toSide": "top"}, {"id": "bd98d1a92951ccad", "fromNode": "ff0263599773f4bc", "fromSide": "right", "toNode": "c0bf3c05aa5111ea", "toSide": "left"}, {"id": "56d7b7868145835e", "fromNode": "c0bf3c05aa5111ea", "fromSide": "left", "toNode": "ff0263599773f4bc", "toSide": "right"}, {"id": "4f89415de41c0f9e", "fromNode": "c0bf3c05aa5111ea", "fromSide": "right", "toNode": "460a765b32d15b42", "toSide": "left"}, {"id": "066078d441f84726", "fromNode": "e5e93c1810a87df2", "fromSide": "left", "toNode": "460a765b32d15b42", "toSide": "right"}, {"id": "4aee3a4f09a9abfb", "fromNode": "e5e93c1810a87df2", "fromSide": "bottom", "toNode": "f2d15c82e91230d2", "toSide": "top"}, {"id": "6e87fa3fbe5e0030", "fromNode": "f2d15c82e91230d2", "fromSide": "left", "toNode": "460a765b32d15b42", "toSide": "right"}, {"id": "0aec0fb45049c47a", "fromNode": "e88d917ea0ee854d", "fromSide": "bottom", "toNode": "3af968961d8204f2", "toSide": "top"}, {"id": "a2f59d5b9d2cd72d", "fromNode": "460a765b32d15b42", "fromSide": "left", "toNode": "c0bf3c05aa5111ea", "toSide": "right"}, {"id": "17748f7f9ae243d4", "fromNode": "producaoOrcamento", "fromSide": "bottom", "toNode": "1700f29547c8fdbb", "toSide": "top"}, {"id": "bd64e73163baf76c", "fromNode": "producaoOrcamento", "fromSide": "bottom", "toNode": "0c74033ddc9106ae", "toSide": "top"}]}
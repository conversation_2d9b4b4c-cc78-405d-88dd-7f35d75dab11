# 📸 Screenshot Capture - Captura de Tela com Mouse

Script Python que permite capturar screenshots usando os botões laterais do mouse.

## 🚀 Como Usar

### 1. Instalação das Dependências

**Opção A - Automática (Windows):**
```bash
# Execute o arquivo .bat
instalar_dependencias.bat
```

**Opção B - Manual:**
```bash
pip install pynput pillow
```

### 2. Executar o Script

```bash
python screenshot_capture.py
```

## 🖱️ Controles

- **Botão Lateral ESQUERDO**: Captura screenshot da tela inteira
- **Botão Lateral DIREITO**: Para o script
- **Ctrl+C**: Força a saída do programa

## 📁 Estrutura de Arquivos

```
screenshots/
├── screenshot_001_20241201_143022.png
├── screenshot_002_20241201_143045.png
├── screenshot_003_20241201_143108.png
└── ...
```

## 🔧 Funcionalidades

- ✅ Captura automática de tela inteira
- ✅ Numeração sequencial das imagens
- ✅ Timestamp no nome do arquivo
- ✅ Pasta automática para organizar screenshots
- ✅ Controle com botões laterais do mouse
- ✅ Feedback visual no terminal
- ✅ Tratamento de erros

## 📋 Requisitos

- Python 3.6+
- Windows/Linux/macOS
- Mouse com botões laterais

## 🛠️ Bibliotecas Utilizadas

- `pynput`: Detecção de cliques do mouse
- `Pillow (PIL)`: Captura e processamento de imagens

## ⚠️ Notas Importantes

1. **Permissões**: Em alguns sistemas, pode ser necessário executar como administrador
2. **Botões Laterais**: Certifique-se de que seu mouse tem botões laterais funcionais
3. **Performance**: O script é otimizado para não impactar o desempenho do sistema
4. **Segurança**: O script não envia dados para nenhum servidor externo

## 🐛 Solução de Problemas

**Erro "ModuleNotFoundError":**
```bash
pip install --upgrade pip
pip install pynput pillow
```

**Botões laterais não funcionam:**
- Verifique se o mouse tem botões laterais
- Teste os botões em outros aplicativos
- Execute como administrador

**Permissão negada:**
- Execute o terminal como administrador
- Verifique as permissões da pasta de destino

# =============================================================================
# CONFIGURAÇÕES DO WEB SCRAPER
# =============================================================================

# Ambiente de execução
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# BANCO DE DADOS
# =============================================================================

# SQLite (para desenvolvimento)
DATABASE_URL=sqlite+aiosqlite:///./data/webscraper.db

# PostgreSQL (para produção)
# DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/webscraper
POSTGRES_USER=webscraper
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=webscraper
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# =============================================================================
# STORAGE
# =============================================================================

# Storage local (desenvolvimento)
STORAGE_TYPE=filesystem
RAW_DATA_PATH=./data/raw
PROCESSED_DATA_PATH=./data/processed

# S3/MinIO (produção)
# STORAGE_TYPE=s3
S3_ENDPOINT_URL=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_BUCKET_NAME=webscraper-data
S3_REGION=us-east-1

# =============================================================================
# CACHE E REDIS
# =============================================================================

REDIS_URL=redis://localhost:6379/0
CACHE_TTL_SECONDS=3600
ENABLE_CACHE=true

# =============================================================================
# CONFIGURAÇÕES DE SCRAPING
# =============================================================================

# Rate limiting
DEFAULT_REQUESTS_PER_SECOND=1.0
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=30
MAX_RETRIES=3
BACKOFF_FACTOR=2.0

# User Agent
USER_AGENT="WebScraper/1.0 (+mailto:<EMAIL>)"

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
BROWSER_POOL_SIZE=3

# =============================================================================
# OBSERVABILIDADE
# =============================================================================

# Logs estruturados
STRUCTURED_LOGGING=true
LOG_FORMAT=json

# Métricas Prometheus
PROMETHEUS_PORT=8000
METRICS_ENABLED=true

# Grafana (opcional)
GRAFANA_URL=http://localhost:3000
GRAFANA_API_KEY=your_grafana_api_key

# =============================================================================
# PREFECT (ORQUESTRAÇÃO)
# =============================================================================

PREFECT_API_URL=http://localhost:4200/api
PREFECT_WORK_POOL=default-agent-pool

# =============================================================================
# CONFIGURAÇÕES DE DOMÍNIO
# =============================================================================

# Domínios permitidos (separados por vírgula)
ALLOWED_DOMAINS=docs.microsoft.com,developer.mozilla.org,example.com

# Respeitar robots.txt
RESPECT_ROBOTS_TXT=true

# =============================================================================
# QUALIDADE E VALIDAÇÃO
# =============================================================================

# Score mínimo de qualidade para aceitar uma página
MIN_QUALITY_SCORE=50

# Tamanho mínimo do conteúdo (em caracteres)
MIN_CONTENT_LENGTH=100

# Número mínimo de palavras
MIN_WORD_COUNT=20

# =============================================================================
# FEATURES OPCIONAIS
# =============================================================================

# RAG e busca semântica
ENABLE_RAG=false
CHROMA_PERSIST_DIRECTORY=./data/chroma
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# API FastAPI
API_HOST=0.0.0.0
API_PORT=8080
API_WORKERS=1

# =============================================================================
# SEGURANÇA
# =============================================================================

# Chave secreta para JWT (se usar API)
SECRET_KEY=your-super-secret-key-change-this-in-production

# CORS (se usar API)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# DESENVOLVIMENTO
# =============================================================================

# Modo de desenvolvimento
DEV_MODE=true
RELOAD_ON_CHANGE=true

# Salvar HTML bruto para debug
SAVE_RAW_HTML=true

# Capturar screenshots em caso de erro
SCREENSHOT_ON_ERROR=true
SCREENSHOT_PATH=./data/screenshots

# Multi-stage Dockerfile para WebScraper Empresarial
# Otimizado para produção com cache layers e segurança

# ================================
# Stage 1: Base Python com dependências do sistema
# ================================
FROM python:3.11-slim as base

# Metadados
LABEL maintainer="WebScraper Team"
LABEL version="3.0.0"
LABEL description="Enterprise Web Scraper with PostgreSQL, S3, Prometheus, and Kubernetes support"

# Variáveis de ambiente para otimização
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    # Dependências básicas
    curl \
    wget \
    git \
    # Dependências para PostgreSQL
    libpq-dev \
    # Dependências para Playwright
    libnss3 \
    libnspr4 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    # Dependências para compilação
    gcc \
    g++ \
    make \
    # Limpeza
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# ================================
# Stage 2: Dependências Python
# ================================
FROM base as python-deps

# Instalar Poetry
RUN pip install poetry==1.7.1

# Copiar arquivos de dependências
WORKDIR /app
COPY pyproject.toml poetry.lock* ./

# Configurar Poetry e instalar dependências
RUN poetry config virtualenvs.create false \
    && poetry install --only=main --no-root \
    && rm -rf $POETRY_CACHE_DIR

# Instalar Playwright browsers
RUN playwright install chromium \
    && playwright install-deps chromium

# ================================
# Stage 3: Aplicação
# ================================
FROM base as app

# Copiar dependências Python do stage anterior
COPY --from=python-deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=python-deps /usr/local/bin /usr/local/bin
COPY --from=python-deps /root/.cache/ms-playwright /root/.cache/ms-playwright

# Criar usuário não-root para segurança
RUN groupadd -r webscraper && useradd -r -g webscraper webscraper

# Criar diretórios necessários
RUN mkdir -p /app/data /app/logs /app/cache \
    && chown -R webscraper:webscraper /app

# Copiar código da aplicação
WORKDIR /app
COPY --chown=webscraper:webscraper . .

# Configurar permissões
RUN chmod +x scripts/*.sh 2>/dev/null || true

# Mudar para usuário não-root
USER webscraper

# Configurações padrão
ENV WEBSCRAPER_ENVIRONMENT=production \
    WEBSCRAPER_LOG_LEVEL=INFO \
    WEBSCRAPER_DATA_DIR=/app/data \
    WEBSCRAPER_CACHE_DIR=/app/cache \
    WEBSCRAPER_METRICS_ENABLED=true \
    WEBSCRAPER_METRICS_PORT=8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expor portas
EXPOSE 8000 8080

# Comando padrão
CMD ["python", "-m", "src.api.main"]

# ================================
# Stage 4: Desenvolvimento (opcional)
# ================================
FROM app as development

USER root

# Instalar dependências de desenvolvimento
RUN pip install \
    pytest \
    pytest-asyncio \
    pytest-cov \
    black \
    isort \
    flake8 \
    mypy \
    pre-commit

# Instalar dependências adicionais para desenvolvimento
COPY requirements-dev.txt* ./
RUN if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi

USER webscraper

# Comando para desenvolvimento
CMD ["python", "-m", "src.api.main", "--reload"]

# ================================
# Stage 5: Produção otimizada
# ================================
FROM app as production

# Otimizações adicionais para produção
ENV PYTHONOPTIMIZE=2

# Remover arquivos desnecessários
USER root
RUN find /usr/local/lib/python3.11/site-packages -name "*.pyc" -delete \
    && find /usr/local/lib/python3.11/site-packages -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

USER webscraper

# Comando otimizado para produção
CMD ["python", "-O", "-m", "src.api.main"]

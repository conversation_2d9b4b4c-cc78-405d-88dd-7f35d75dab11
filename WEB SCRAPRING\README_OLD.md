# 🕷️ WebScraper Complexo

Sistema robusto de web scraping com observabilidade, tolerância a falhas e respeito a requisitos legais.

## 🎯 Visão Geral

Este projeto implementa um pipeline completo de coleta, parsing, validação e versionamento de conteúdos web (estáticos e dinâmicos), com foco em:

- **Robustez**: Rate limiting, retries com backoff, circuit breakers
- **Observabilidade**: Logs estruturados, métricas Prometheus, dashboards
- **Ética**: Respeito a robots.txt, termos de uso e políticas de coleta
- **Escalabilidade**: Arquitetura assíncrona, storage distribuído
- **Qualidade**: Validação de dados, scores de qualidade, testes automatizados

## 🏗️ Arquitetura

```
[Scheduler/Prefect]
      │
      ▼
[Dispatcher]──▶[HTTP Fetcher (httpx, cache, retries)]
      │                         │
      │                         └──▶[Headless (Playwright) p/ dinâmicos]
      ▼
[Parser Layer (selectolax/lxml)]──▶[Normalização/Validação (Pydantic)]
      │
      ▼
[Deduplicação & Versionamento (hash, ETag)]
      │
      ├──▶[Raw Storage (S3/MinIO/Filesystem datado)]
      ├──▶[DB Metadados (PostgreSQL)]
      └──▶[Index Semântico (Chroma/pgvector)]  ← opcional/RAG
      │
      ▼
[Observabilidade (logs, métricas, alertas)]
```

## 🚀 Início Rápido

### Pré-requisitos

- Python 3.11+
- Node.js (para Playwright)
- PostgreSQL (opcional, SQLite por padrão)
- Redis (opcional, para cache)

### Instalação

1. **Clone e configure o ambiente:**
```bash
git clone <repository-url>
cd webscraper
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows

pip install -e ".[dev]"
```

2. **Configure as variáveis de ambiente:**
```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

3. **Instale o Playwright:**
```bash
playwright install
```

4. **Execute as migrações do banco:**
```bash
alembic upgrade head
```

### Uso Básico

```bash
# Executar scraping de um domínio específico
webscraper run --domain docs.microsoft.com

# Executar com configurações personalizadas
webscraper run --config configs/custom.yml

# Executar flow do Prefect
prefect deployment run "webscraper-daily/default"
```

## 📁 Estrutura do Projeto

```
webscraper/
├─ configs/
│   ├─ domains.yml            # Regras por domínio
│   └─ prefect_blocks.json    # Configurações de orquestração
├─ data/
│   ├─ raw/2025-09-07/…       # HTML bruto versionado
│   └─ processed/…            # JSON normalizado
├─ src/
│   ├─ core/                  # Componentes principais
│   │   ├─ http_client.py     # Cliente HTTP com retries
│   │   ├─ browser.py         # Playwright helpers
│   │   ├─ parser_base.py     # Base para parsers
│   │   ├─ normalize.py       # Limpeza e normalização
│   │   ├─ validators.py      # Modelos Pydantic
│   │   └─ storage.py         # Storage abstrato
│   ├─ domains/               # Parsers específicos
│   │   ├─ generic.py         # Parser genérico
│   │   └─ microsoft_docs.py  # Parser para docs Microsoft
│   ├─ crawl/                 # Sistema de crawling
│   │   ├─ sitemap.py         # Descoberta via sitemap
│   │   └─ frontier.py        # Fila de URLs
│   └─ flows/                 # Flows do Prefect
│       ├─ mvp_flow.py        # Flow principal
│       └─ incremental.py     # Coleta incremental
├─ tests/                     # Testes automatizados
├─ docker/                    # Configurações Docker
└─ pyproject.toml            # Configuração do projeto
```

## 🔧 Configuração

### Configurações por Domínio

Edite `configs/domains.yml` para configurar:

- **Rate limiting**: Requisições por segundo, burst limits
- **Seletores**: CSS/XPath para extrair conteúdo
- **Qualidade**: Critérios de validação
- **Escopo**: URLs permitidas/bloqueadas

### Variáveis de Ambiente

Principais configurações no `.env`:

```bash
# Banco de dados
DATABASE_URL=sqlite+aiosqlite:///./data/webscraper.db

# Rate limiting
DEFAULT_REQUESTS_PER_SECOND=1.0
MAX_CONCURRENT_REQUESTS=10

# Observabilidade
LOG_LEVEL=INFO
METRICS_ENABLED=true
```

## 🧪 Testes

```bash
# Executar todos os testes
pytest

# Testes unitários apenas
pytest -m unit

# Testes com cobertura
pytest --cov=src --cov-report=html

# Testes específicos de parser
pytest tests/test_parsers.py -v
```

## 📊 Monitoramento

### Métricas Disponíveis

- `webscraper_requests_total`: Total de requisições por domínio/status
- `webscraper_request_duration_seconds`: Latência das requisições
- `webscraper_pages_processed_total`: Páginas processadas com sucesso
- `webscraper_quality_score`: Score médio de qualidade por domínio

### Dashboards

Acesse `http://localhost:3000` para ver os dashboards do Grafana (se configurado).

## ⚖️ Política de Coleta

### Princípios Éticos

1. **Respeito a robots.txt**: Sempre verificamos e respeitamos as diretrizes
2. **Rate limiting**: Limitamos requisições para não sobrecarregar servidores
3. **Identificação clara**: User-Agent com informações de contato
4. **Dados sensíveis**: Não coletamos PII sem autorização explícita

### Limites por Domínio

| Domínio | RPS | Burst | Observações |
|---------|-----|-------|-------------|
| docs.microsoft.com | 2.0 | 10 | Documentação oficial |
| stackoverflow.com | 0.5 | 3 | Mais conservador |
| github.com | 1.0 | 5 | Repositórios públicos |

### Contato para Remoção

Para solicitar remoção de dados coletados, entre em contato: <EMAIL>

## 🚀 Deployment

### Docker

```bash
# Build da imagem
docker build -f docker/Dockerfile -t webscraper:latest .

# Executar com docker-compose
docker-compose up -d
```

### Kubernetes

```bash
# Deploy no cluster
kubectl apply -f k8s/
```

## 🔄 Roadmap

### Fase 0 - MVP ✅
- [x] HTTP assíncrono com cache
- [x] Parser genérico
- [x] Storage SQLite
- [x] Validação Pydantic

### Fase 1 - Dinâmica & Qualidade 🚧
- [ ] Playwright para páginas dinâmicas
- [ ] Versionamento por hash
- [ ] Testes com VCR
- [ ] Score de qualidade

### Fase 2 - Escala & Observabilidade 📋
- [ ] PostgreSQL + MinIO
- [ ] Métricas Prometheus
- [ ] Coleta incremental (ETag)
- [ ] Alertas automatizados

### Fase 3 - RAG & API 🔮
- [ ] Chunking semântico
- [ ] Índice vetorial
- [ ] API FastAPI
- [ ] Interface web

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

- **Issues**: [GitHub Issues](https://github.com/pedro/webscraper/issues)
- **Email**: <EMAIL>
- **Documentação**: [Wiki do projeto](https://github.com/pedro/webscraper/wiki)

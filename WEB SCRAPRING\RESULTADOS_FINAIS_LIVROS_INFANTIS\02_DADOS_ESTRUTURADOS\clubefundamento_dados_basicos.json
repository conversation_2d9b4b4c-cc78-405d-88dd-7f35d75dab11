{"url": "https://www.clubefundamento.com.br/", "name": "Clube Fundamento", "type": "clube_livros", "framework": "svelte", "scraped_at": "2025-09-12T15:32:15.744448", "status_code": 200, "content_length": 108140, "title": "Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil", "meta_description": "Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.", "meta_keywords": "clube de livros, livros infantis, educação, crianças, leitura", "headings": {"h1": [], "h2": ["Como Funciona?", "Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária.", "PLANO COMPLETO", "PLANO DUPLO", "PLANO SIMPLES", "Kit Clube Expresso.", "O que vem  no kit?", "<PERSON><PERSON><PERSON><PERSON>", "Avaliações dos Pais"], "h3": ["+12 MILHÕES", "BEST-SELLERS", "24 ANOS", "97% DE APROVAÇÃO", "JÁ TENHO LIVROS DA FUNDAMENTO. E AGORA?", "Receba tudo de uma vez só.", "1.  \n\t\t\t\t\t\t\t\tBest-sellers nacionais e internacionais", "2.  \n\t\t\t\t\t\t\t\tPassaporte do Leitor", "3.  \n\t\t\t\t\t\t\t\t<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thais", "<PERSON><PERSON>", "Antonia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sobre", "Redes Sociais"], "h4": ["Como funciona?"], "h5": [], "h6": []}, "links": [{"url": "https://www.clubefundamento.com.br/", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/#planos", "text": "Planos", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://wix.clubefundamento.com.br/amostra", "text": "Amostra", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://wix.clubefundamento.com.br/blog", "text": "Blog", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://assinar.clubefundamento.com.br", "text": "SOU ASSINANTE", "title": "", "classes": ["rounded-full", "bg-[#21A26C]", "px-4", "py-2", "font-bold", "text-white", "transition-colors", "hover:bg-green-700"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "COMPRAR", "title": "", "classes": ["rounded-full", "bg-[#FAE941]", "px-6", "py-2", "font-bold", "text-black", "transition-colors", "hover:bg-yellow-300"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/#planos", "text": "ASSINE JÁ", "title": "", "classes": ["mx-auto", "font-bold", "rounded-full", "bg-[#FAE941]", "px-12", "py-2", "md:py-4", "text-center", "text-base", "text-gray-900", "hover:bg-[#F8D93A]", "md:px-16", "md:text-lg", "lg:px-20"]}, {"url": "https://www.clubefundamento.com.br/amostra", "text": "AMOSTRA", "title": "", "classes": ["mx-auto", "rounded-full", "bg-[#FAE941]", "px-12", "py-2", "text-center", "text-base", "font-bold", "text-gray-900", "hover:bg-[#F8D93A]", "md:px-16", "md:py-4", "md:text-lg", "lg:px-20"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-duplo-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-simples-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://www.lojafundamento.com.br/clube-fundamento/kits-clube-fundamento/", "text": "Compre Aqui", "title": "", "classes": ["inline-block", "rounded-lg", "bg-yellow-400", "px-8", "py-4", "text-lg", "font-bold", "text-black", "shadow-lg", "transition-colors", "duration-200", "hover:bg-yellow-500", "hover:shadow-xl"]}, {"url": "https://www.clubefundamento.com.br/sobre-o-kit", "text": "Mais detalhes sobre o kit", "title": "", "classes": ["transform", "cursor-pointer", "rounded-full", "bg-[#FAE941]", "px-8", "py-4", "text-sm", "font-bold", "tracking-wide", "text-gray-900", "uppercase", "shadow-lg", "transition-colors", "duration-200", "hover:bg-yellow-400", "hover:shadow-xl", "md:text-lg"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "As histórias na hora de dormir ficaram ainda melhores ❤️✨", "title": "", "classes": ["group", "block", "overflow-hidden", "rounded-2xl", "bg-white", "shadow-sm", "transition-all", "duration-300", "hover:shadow-lg"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "Já vou avisando que o livro é ótimo 😍😍", "title": "", "classes": ["group", "block", "overflow-hidden", "rounded-2xl", "bg-white", "shadow-sm", "transition-all", "duration-300", "hover:shadow-lg"]}, {"url": "https://www.clubefundamento.com.br/cdn-cgi/l/email-protection", "text": "[email protected]", "title": "", "classes": ["__cf_email__"]}, {"url": "https://www.reclameaqui.com.br/empresa/editora-fundamento/", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/perguntas-frequentes", "text": "Dúvidas e Perguntas Frequentes", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.lojafundamento.com.br/", "text": "<PERSON><PERSON>", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/sobre", "text": "So<PERSON> Nós", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/blog", "text": "Blog", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/sobre-o-kit", "text": "Mais <PERSON> o <PERSON>", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/atividades", "text": "Atividades Gratuitas", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/44c0be_48170609e9c048b1b0f774178c25867d.pdf", "text": "Contrato", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/7e0657_71745947a22243ada3acfaef410aaa69.pdf", "text": "Termos e Condições de Uso", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/7e0657_0955674f4ea04e5ca4e0543d8784877a.pdf", "text": "Política de Privacidade", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "Instagram", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}, {"url": "https://www.youtube.com/@fundamentoeditora", "text": "YouTube", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}, {"url": "https://www.tiktok.com/@fundamentoeditora", "text": "TikTok", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}], "images": [{"url": "https://www.clubefundamento.com.br/_app/immutable/assets/clubefundamento.uJCGl9c2.avif", "alt": "Clube Fundamento", "title": "", "classes": ["md:h-[30px]", "md:w-[150px]", "lg:h-[64px]", "lg:w-[299px]"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/banner.BFypisbC.webp", "alt": "Banner do Clube Fundamento", "title": "", "classes": ["h-full", "w-full", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/logo.C87zx4WW.avif", "alt": "Editora Fundamento logo", "title": "", "classes": ["mx-auto", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/1.CqNyifm5.avif", "alt": "+12 MILHÕES", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/2.DreehQ1f.avif", "alt": "BEST-SELLERS", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/3.DM_SDhga.avif", "alt": "24 ANOS", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/4.CO8_fAn_.avif", "alt": "97% DE APROVAÇÃO", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/temas-dos-livros.B3PBL6ou.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-28", "w-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/empreendedorismo.DtztLjSB.avif", "alt": "Livros sobre <PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/colaboracao.DHN6uOkb.avif", "alt": "Livros sobre Colaboração", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/disciplina.gJRDGkRR.avif", "alt": "Livros sobre Disciplina", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/amizade.CZfXsw2Q.avif", "alt": "Livros sobre Amizade", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/coragem.DGK8X3cX.avif", "alt": "Livros sobre Coragem", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/familia.DRIZhRJU.avif", "alt": "Livros sobre Família", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/honestidade.DqWq3SWe.avif", "alt": "Livros sobre Honestidade", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/a-casa-na-arvore.ClGk23Cx.avif", "alt": "A Casa na Árvore", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/corajoso.CeYKnM7Q.avif", "alt": "Corajos<PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/tom-gates.Cvdb9tOM.avif", "alt": "<PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/gol.DPfvbjaM.avif", "alt": "Gol", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/diario-de-um-pug.C8I7p4El.avif", "alt": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/meus-sonhos-magicos.BprIPBBv.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-menino-que-criava-dragoes.C1mHF4wL.avif", "alt": "O Menino que Criava Dragões", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-unicornio.BpCZ9FNU.avif", "alt": "O unicórnio que queria vender cupcakes", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/a-princesa-corajosa.BfvsScwP.avif", "alt": "A Princesa <PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/dragon-masters.DMM095tU.avif", "alt": "Dragon Masters", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/nosso-amor.Lgbrxuj4.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/historias-divertidas-da-wayside.CYa6LEWl.avif", "alt": "Histórias Divertidas da Wayside School", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/fada-perola.405C0VtK.avif", "alt": "Fada Pérola", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/amigos-no-parquinho.C-_sDZjL.avif", "alt": "Amigos no Parquinho", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-amuleto.BiWyLgZH.avif", "alt": "O Amuleto", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}], "structure": {"total_elements": 1042, "forms": 0, "buttons": 15, "navigation": 1, "sections": 10, "articles": 0, "title_count": 1, "title_sample": ["Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil"], "description_count": 1, "description_sample": [""], "products_count": 0, "prices_count": 0, "images_count": 1, "images_sample": [""]}, "content_analysis": {"word_count": 1598, "char_count": 9831, "paragraphs": 145, "lists": 1, "book_keywords": {"livro": 32, "história": 6, "criança": 8, "infantil": 0, "leitura": 5, "educativo": 3}, "subscription_keywords": {"assinatura": 0, "mensal": 3, "plano": 5, "clube": 9, "receber": 4}}, "technology_stack": {"frameworks": ["Svelte"], "libraries": [], "analytics": [], "other": [], "server": "cloudflare"}}
{"dentrodahistoria": {"url": "https://www.dentrodahistoria.com.br/clube/", "name": "Clube Dentro da História", "type": "livros_personalizados", "framework": "nuxt", "scraped_at": "2025-09-12T15:37:19.821501", "status_code": 200, "content_length": 69142, "title": "Clube Dentro da História | Assinatura Livros Personalizados", "meta_description": "<PERSON><PERSON> m<PERSON>, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido.", "meta_keywords": "", "headings": {"h1": [], "h2": [], "h3": [], "h4": [], "h5": [], "h6": []}, "links": [], "images": [], "technology_stack": {"frameworks": [], "libraries": [], "analytics": ["Google Tag Manager"], "other": [], "server": "nginx"}, "products": [], "prices": [], "features": [{"keyword": "personalizado", "mentions": 1}, {"keyword": "história", "mentions": 1}, {"keyword": "livro", "mentions": 1}], "subscription_info": {"assinatura": 1}, "target_audience": []}, "clubefundamento": {"url": "https://www.clubefundamento.com.br/", "name": "Clube Fundamento", "type": "clube_livros", "framework": "svelte", "scraped_at": "2025-09-12T15:37:22.148358", "status_code": 200, "content_length": 108140, "title": "Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil", "meta_description": "Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.", "meta_keywords": "clube de livros, livros infantis, educação, crianças, leitura", "headings": {"h1": [], "h2": ["Como Funciona?", "Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária.", "PLANO COMPLETO", "PLANO DUPLO", "PLANO SIMPLES", "Kit Clube Expresso.", "O que vem  no kit?", "<PERSON><PERSON><PERSON><PERSON>", "Avaliações dos Pais"], "h3": ["+12 MILHÕES", "BEST-SELLERS", "24 ANOS", "97% DE APROVAÇÃO", "JÁ TENHO LIVROS DA FUNDAMENTO. E AGORA?", "Receba tudo de uma vez só.", "1.  \n\t\t\t\t\t\t\t\tBest-sellers nacionais e internacionais", "2.  \n\t\t\t\t\t\t\t\tPassaporte do Leitor", "3.  \n\t\t\t\t\t\t\t\t<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thais", "<PERSON><PERSON>", "Antonia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sobre", "Redes Sociais"], "h4": ["Como funciona?"], "h5": [], "h6": []}, "links": [{"url": "https://www.clubefundamento.com.br/", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/#planos", "text": "Planos", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://wix.clubefundamento.com.br/amostra", "text": "Amostra", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://wix.clubefundamento.com.br/blog", "text": "Blog", "title": "", "classes": ["text-white", "transition-colors", "hover:text-yellow-300"]}, {"url": "https://assinar.clubefundamento.com.br", "text": "SOU ASSINANTE", "title": "", "classes": ["rounded-full", "bg-[#21A26C]", "px-4", "py-2", "font-bold", "text-white", "transition-colors", "hover:bg-green-700"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "COMPRAR", "title": "", "classes": ["rounded-full", "bg-[#FAE941]", "px-6", "py-2", "font-bold", "text-black", "transition-colors", "hover:bg-yellow-300"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/#planos", "text": "ASSINE JÁ", "title": "", "classes": ["mx-auto", "font-bold", "rounded-full", "bg-[#FAE941]", "px-12", "py-2", "md:py-4", "text-center", "text-base", "text-gray-900", "hover:bg-[#F8D93A]", "md:px-16", "md:text-lg", "lg:px-20"]}, {"url": "https://www.clubefundamento.com.br/amostra", "text": "AMOSTRA", "title": "", "classes": ["mx-auto", "rounded-full", "bg-[#FAE941]", "px-12", "py-2", "text-center", "text-base", "font-bold", "text-gray-900", "hover:bg-[#F8D93A]", "md:px-16", "md:py-4", "md:text-lg", "lg:px-20"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-duplo-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://assinar.clubefundamento.com.br/checkout/plano-simples-anual", "text": "COMPRAR", "title": "", "classes": ["inline-block", "cursor-pointer", "rounded-full", "border-2", "border-white/80", "bg-yellow-400", "px-8", "py-3", "text-base", "font-bold", "tracking-wide", "text-gray-800", "uppercase", "no-underline", "shadow-lg", "transition-all", "duration-300", "hover:-translate-y-1", "hover:border-white", "hover:bg-yellow-300", "hover:shadow-xl", "active:translate-y-0", "md:px-6", "md:py-2.5", "md:text-sm"]}, {"url": "https://www.lojafundamento.com.br/clube-fundamento/kits-clube-fundamento/", "text": "Compre Aqui", "title": "", "classes": ["inline-block", "rounded-lg", "bg-yellow-400", "px-8", "py-4", "text-lg", "font-bold", "text-black", "shadow-lg", "transition-colors", "duration-200", "hover:bg-yellow-500", "hover:shadow-xl"]}, {"url": "https://www.clubefundamento.com.br/sobre-o-kit", "text": "Mais detalhes sobre o kit", "title": "", "classes": ["transform", "cursor-pointer", "rounded-full", "bg-[#FAE941]", "px-8", "py-4", "text-sm", "font-bold", "tracking-wide", "text-gray-900", "uppercase", "shadow-lg", "transition-colors", "duration-200", "hover:bg-yellow-400", "hover:shadow-xl", "md:text-lg"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "As histórias na hora de dormir ficaram ainda melhores ❤️✨", "title": "", "classes": ["group", "block", "overflow-hidden", "rounded-2xl", "bg-white", "shadow-sm", "transition-all", "duration-300", "hover:shadow-lg"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "Já vou avisando que o livro é ótimo 😍😍", "title": "", "classes": ["group", "block", "overflow-hidden", "rounded-2xl", "bg-white", "shadow-sm", "transition-all", "duration-300", "hover:shadow-lg"]}, {"url": "https://www.clubefundamento.com.br/cdn-cgi/l/email-protection", "text": "[email protected]", "title": "", "classes": ["__cf_email__"]}, {"url": "https://www.reclameaqui.com.br/empresa/editora-fundamento/", "text": "", "title": "", "classes": []}, {"url": "https://www.clubefundamento.com.br/perguntas-frequentes", "text": "Dúvidas e Perguntas Frequentes", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.lojafundamento.com.br/", "text": "<PERSON><PERSON>", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/sobre", "text": "So<PERSON> Nós", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/blog", "text": "Blog", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/sobre-o-kit", "text": "Mais <PERSON> o <PERSON>", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/atividades", "text": "Atividades Gratuitas", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/44c0be_48170609e9c048b1b0f774178c25867d.pdf", "text": "Contrato", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/7e0657_71745947a22243ada3acfaef410aaa69.pdf", "text": "Termos e Condições de Uso", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.clubefundamento.com.br/_files/ugd/7e0657_0955674f4ea04e5ca4e0543d8784877a.pdf", "text": "Política de Privacidade", "title": "", "classes": ["block", "transition-colors", "hover:text-white"]}, {"url": "https://www.instagram.com/fundamentoeditora/", "text": "Instagram", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}, {"url": "https://www.youtube.com/@fundamentoeditora", "text": "YouTube", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}, {"url": "https://www.tiktok.com/@fundamentoeditora", "text": "TikTok", "title": "", "classes": ["block", "text-sm", "text-gray-300", "underline", "transition-colors", "hover:text-white"]}], "images": [{"url": "https://www.clubefundamento.com.br/_app/immutable/assets/clubefundamento.uJCGl9c2.avif", "alt": "Clube Fundamento", "title": "", "classes": ["md:h-[30px]", "md:w-[150px]", "lg:h-[64px]", "lg:w-[299px]"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/banner.BFypisbC.webp", "alt": "Banner do Clube Fundamento", "title": "", "classes": ["h-full", "w-full", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/logo.C87zx4WW.avif", "alt": "Editora Fundamento logo", "title": "", "classes": ["mx-auto", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/1.CqNyifm5.avif", "alt": "+12 MILHÕES", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/2.DreehQ1f.avif", "alt": "BEST-SELLERS", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/3.DM_SDhga.avif", "alt": "24 ANOS", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/4.CO8_fAn_.avif", "alt": "97% DE APROVAÇÃO", "title": "", "classes": ["h-16", "w-16", "object-contain", "lg:h-16", "lg:w-16"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/temas-dos-livros.B3PBL6ou.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-28", "w-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/empreendedorismo.DtztLjSB.avif", "alt": "Livros sobre <PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/colaboracao.DHN6uOkb.avif", "alt": "Livros sobre Colaboração", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/disciplina.gJRDGkRR.avif", "alt": "Livros sobre Disciplina", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/amizade.CZfXsw2Q.avif", "alt": "Livros sobre Amizade", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/coragem.DGK8X3cX.avif", "alt": "Livros sobre Coragem", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/familia.DRIZhRJU.avif", "alt": "Livros sobre Família", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/honestidade.DqWq3SWe.avif", "alt": "Livros sobre Honestidade", "title": "", "classes": ["h-[90%]", "w-[90%]", "rounded-full", "object-contain"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/a-casa-na-arvore.ClGk23Cx.avif", "alt": "A Casa na Árvore", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/corajoso.CeYKnM7Q.avif", "alt": "Corajos<PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/tom-gates.Cvdb9tOM.avif", "alt": "<PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/gol.DPfvbjaM.avif", "alt": "Gol", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/diario-de-um-pug.C8I7p4El.avif", "alt": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/meus-sonhos-magicos.BprIPBBv.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-menino-que-criava-dragoes.C1mHF4wL.avif", "alt": "O Menino que Criava Dragões", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-unicornio.BpCZ9FNU.avif", "alt": "O unicórnio que queria vender cupcakes", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/a-princesa-corajosa.BfvsScwP.avif", "alt": "A Princesa <PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/dragon-masters.DMM095tU.avif", "alt": "Dragon Masters", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/nosso-amor.Lgbrxuj4.avif", "alt": "<PERSON><PERSON>", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/historias-divertidas-da-wayside.CYa6LEWl.avif", "alt": "Histórias Divertidas da Wayside School", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/fada-perola.405C0VtK.avif", "alt": "Fada Pérola", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/amigos-no-parquinho.C-_sDZjL.avif", "alt": "Amigos no Parquinho", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}, {"url": "https://www.clubefundamento.com.br/_app/immutable/assets/o-amuleto.BiWyLgZH.avif", "alt": "O Amuleto", "title": "", "classes": ["h-52", "w-40", "rounded-lg", "object-cover"]}], "technology_stack": {"frameworks": ["Svelte"], "libraries": [], "analytics": [], "other": [], "server": "cloudflare"}, "products": [], "prices": [], "plans": [{"type": "plano completo", "mentions": 1}, {"type": "plano duplo", "mentions": 1}, {"type": "plano simples", "mentions": 1}, {"type": "mensal", "mentions": 3}, {"type": "semestral", "mentions": 3}, {"type": "anual", "mentions": 3}], "book_info": [{"title": "Corajos<PERSON>", "description": "Sucesso com os pequenos"}, {"title": "Totalmente ilustrado", "description": "Coragem e gentileza Idade: 3, 4, 5 e 6 anos  <PERSON>"}, {"title": "Gol", "description": "Best-seller Internacional"}, {"title": "<PERSON><PERSON>", "description": "Best-seller Internacional"}, {"title": "Muito divertido e educativo", "description": "Totalmente ilustrado <PERSON>: 7 anos  Meus Sonhos Mágicos"}, {"title": "Filhos fortes e mais felizes", "description": "Totalmente ilustrado"}, {"title": "Estimula o empreendedorismo", "description": "Fácil de ler e muito divertido"}, {"title": "A Princesa <PERSON>", "description": "Valoriza ações de coragem"}, {"title": "Dragon Masters", "description": "Best-seller New York Times"}, {"title": "<PERSON><PERSON>", "description": "Trabalho em equipe e confiança Idade: 7 anos  Nosso Amor"}, {"title": "Divertidas da Wayside School", "description": "+15 Milhões de Livros Vendidos"}, {"title": "Internacional", "description": "Ilustrações lindas e coloridas"}, {"title": "Amigos no Parquinho", "description": "Inspira gentileza nas crianças"}, {"title": "Totalmente ilustrado", "description": "Fortalece vínculos pais-filhos <PERSON>: 3, 4, 5 e 6 anos  <PERSON>ulet<PERSON>"}, {"title": "New York Times", "description": "Ganhou diversos prêmios"}, {"title": "Totalmente ilustrado", "description": "Fortalece o vínculo familiar Idade: 3-6 an<PERSON>"}, {"title": "Europa", "description": "+5 milhões de livros vendidos"}, {"title": "A Lagarta Muito Impaciente", "description": "<PERSON>u filhos vai dormir melhor"}, {"title": "Totalmente ilustrado", "description": "Fortalece o vínculo familiar Idade: 3-6 an<PERSON>"}, {"title": "Obra de arte da literatura brasileira", "description": "+100 semanas na lista da Época"}, {"title": "Corajos<PERSON>", "description": "Sucesso com os pequenos"}, {"title": "Totalmente ilustrado", "description": "Coragem e gentileza Idade: 3, 4, 5 e 6 anos  <PERSON>"}, {"title": "Gol", "description": "Best-seller Internacional"}, {"title": "<PERSON><PERSON>", "description": "Best-seller Internacional"}, {"title": "Muito divertido e educativo", "description": "Totalmente ilustrado <PERSON>: 7 anos  Meus Sonhos Mágicos"}, {"title": "Filhos fortes e mais felizes", "description": "Totalmente ilustrado"}, {"title": "Estimula o empreendedorismo", "description": "Fácil de ler e muito divertido"}, {"title": "A Princesa <PERSON>", "description": "Valoriza ações de coragem"}, {"title": "Dragon Masters", "description": "Best-seller New York Times"}, {"title": "<PERSON><PERSON>", "description": "Trabalho em equipe e confiança Idade: 7 anos  Nosso Amor"}, {"title": "Divertidas da Wayside School", "description": "+15 Milhões de Livros Vendidos"}, {"title": "Internacional", "description": "Ilustrações lindas e coloridas"}, {"title": "Amigos no Parquinho", "description": "Inspira gentileza nas crianças"}, {"title": "Totalmente ilustrado", "description": "Fortalece vínculos pais-filhos <PERSON>: 3, 4, 5 e 6 anos  <PERSON>ulet<PERSON>"}, {"title": "New York Times", "description": "Ganhou diversos prêmios"}, {"title": "Totalmente ilustrado", "description": "Fortalece o vínculo familiar Idade: 3-6 an<PERSON>"}, {"title": "Europa", "description": "+5 milhões de livros vendidos"}, {"title": "A Lagarta Muito Impaciente", "description": "<PERSON>u filhos vai dormir melhor"}, {"title": "Totalmente ilustrado", "description": "Fortalece o vínculo familiar Idade: 3-6 an<PERSON>"}, {"title": "Obra de arte da literatura brasileira", "description": "+100 semanas na lista da Época"}, {"title": "e criatividade", "description": "8-9 anos"}, {"title": "muito divertido", "description": "8-9 anos"}, {"title": "e determinação", "description": "8-9 anos"}, {"title": "e autoconfiança", "description": "3-6 anos"}, {"title": "v<PERSON><PERSON><PERSON>", "description": "3-6 anos"}, {"title": "v<PERSON><PERSON><PERSON>", "description": "3-6 anos"}, {"title": "e criatividade", "description": "8-9 anos"}, {"title": "muito divertido", "description": "8-9 anos"}, {"title": "e determinação", "description": "8-9 anos"}, {"title": "e autoconfiança", "description": "3-6 anos"}, {"title": "v<PERSON><PERSON><PERSON>", "description": "3-6 anos"}, {"title": "v<PERSON><PERSON><PERSON>", "description": "3-6 anos"}], "statistics": {"livros_vendidos": "12", "anos_mercado": "24", "aprovacao": "97", "avaliacao": "4.8"}}, "meutibi": {"url": "https://meutibi.com.br/", "name": "Tibi <PERSON> <PERSON><PERSON>", "type": "livros_infantis", "framework": "aspnet", "scraped_at": "2025-09-12T15:37:25.136955", "status_code": 200, "content_length": 96728, "title": "Tibi - <PERSON><PERSON>", "meta_description": "Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!", "meta_keywords": "", "headings": {"h1": ["Livros em destaque", "Faça parte da nossa família leitora!", "Livros por idade", "0-3 anos", "3-6 anos", "6+ anos", "Faça parte da nossa Família Leitor<PERSON>"], "h2": ["Lançamento", "A Última Gota", "Best-Seller", "A Menina da Cabeça Quadrada", "Todos os livros", "Coleções", "A Última Gota", "Acreditar", "O Irmãozinho da Jaquinha", "Capaz", "A Menina da Cabeça Quadrada", "Cartas para o Futuro"], "h3": ["<PERSON><PERSON><PERSON>", "Leitores curiosos", "Amo Ler!"], "h4": ["Sobre", "Quem somos", "Blog - <PERSON><PERSON><PERSON> que lê", "Tem alguma dúvida?", "Central de Ajuda", "Termos e Condições", "Trocas e Devoluções", "Siga a Tibi", "Fale com a gente", "<EMAIL>", "(71) 98106-5462", "Contato para empresas (revenda)", "(71) 99606-9038", "Formas de pagamento"], "h5": [], "h6": []}, "links": [{"url": "https://meutibi.com.br/#carouselExampleControls2", "text": "", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselExampleControls2", "text": "", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/", "text": "", "title": "", "classes": ["navbar-brand"]}, {"url": "https://meutibi.com.br/Order/New", "text": "0", "title": "", "classes": ["cart-a-mobile"]}, {"url": "https://meutibi.com.br/Account/Login?returnUrl=%2F", "text": "Acessar conta", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "Todos os livros", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/", "text": "Livros por idade", "title": "", "classes": ["nav-link", "link-idade", "color-link", "onlypc", "size-varela"]}, {"url": "https://meutibi.com.br/Home/PrimeirosLivros", "text": "0-3 anos\r\n                                            \n\r\n                                                Primeiros livros", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/LeitoresCuriosos", "text": "3-6 anos\r\n                                            \n\r\n                                                Leitores curiosos", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/AmoLer", "text": "6+ anos\r\n                                            \n\r\n                                                <PERSON> ler", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/LivrosPorIdade", "text": "Livros por idade", "title": "", "classes": ["nav-link", "color-link", "onlymobile", "size-varela"]}, {"url": "https://meutibi.com.br/Professoras/Lp", "text": "<PERSON><PERSON>", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/pnld/ameninadacabecaquadrada", "text": "PNLD", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/sobreaautora", "text": "Sobre a autora", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://api.whatsapp.com/send?phone=***********&text=&source=&data=&app_absent=", "text": "Contato para empresas (revenda): (71) 99606-9038", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/Account/Login?returnUrl=%2F", "text": "Acesse sua conta", "title": "", "classes": ["onlypc"]}, {"url": "https://meutibi.com.br/Order/New", "text": "0", "title": "", "classes": ["cart-a-pc"]}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/ChorarEComoChover", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Docura", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/#carouselHomeBanner", "text": "Anterior", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselHomeBanner", "text": "Próximo", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/ChorarEComoChover", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/Docura", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/#carouselHomeMobileBanner", "text": "Anterior", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselHomeMobileBanner", "text": "Próximo", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "Lançamento\r\n            \n\r\n                A Última Gota\r\n            \nCOMPRAR\nCOMPRAR", "title": "", "classes": ["banner-lancamento2", "hovercard"]}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "Best-Seller\r\n            \n\r\n                <PERSON> Menina da Cabeça Quadrada\r\n            \nCOMPRAR", "title": "", "classes": ["banner-lancamento", "hovercard"]}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "Todos os livros", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/Colecoes", "text": "Coleções", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "Lançamento\n\n\n\n\n\r\n                   A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentáveis em crianças e adultos.\r\n                \n\n\n\n\n\r\n                                5-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "Lançamento\n\n\n\n\n\r\n                    Acreditar\r\n                \n\r\n                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para entender o que o faz único.\r\n                \n\n\n\n\n\r\n                                2-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/IrmaoDaJaquinha", "text": "Lançamento\n\n\n\n\n\r\n                    O Irmãozinho da Jaquinha\r\n                \n\r\n                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor que cresce junto com a família.\r\n                \n\n\n\n\n\r\n                                2-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Capaz", "text": "Lançamento\n\n\n\n\n\r\n                   Capaz\r\n                \n\r\n                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.\r\n                \n\n\n\n\n\r\n                                5-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AmeninadaCabecaQuadrada", "text": "Best-Seller\n\n\n\n\n\r\n                    A Menina da Cabeça Quadrada\r\n                \n\r\n                    De tanto usar celular, tablet e computador, Cecília acordou\r\n                    com a cabeça quadrada! Uma história para ajudar uma geração\r\n                    superconectada a aproveitar a vida lá fora.\r\n                \n\n\n\n\n\r\n                                2-10 <PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Lua", "text": "Cartas para o Futuro\r\n                \n\r\n                    As crianças podem ter voz ativa nas questões do mundo? Através do poder das cartas, a pequena Lua nos mostra que sim!\r\n                \n\n\n\n\n\r\n                                4-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "CONHEÇA TODOS OS LIVROS", "title": "", "classes": ["conheca-mobile-2"]}, {"url": "https://www.instagram.com/maequele/", "text": "CONHEÇA NOSSA HISTÓRIA", "title": "", "classes": ["botao-mobile"]}, {"url": "https://meutibi.com.br/Home/PrimeirosLivros", "text": "0-3 anos\r\n                                \n\r\n                                    Primeiros Livros", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/LeitoresCuriosos", "text": "3-6 anos\r\n                                \n\r\n                                    Leitores curiosos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AmoLer", "text": "6+ anos\r\n                                \n\r\n                                    <PERSON>!", "title": "", "classes": []}, {"url": "https://meutibi.com.br/#carouselExampleIndicators", "text": "Previous", "title": "", "classes": ["carousel-control-prev", "onlypc"]}, {"url": "https://meutibi.com.br/#carouselExampleIndicators", "text": "Next", "title": "", "classes": ["carousel-control-next", "onlypc"]}, {"url": "https://api.whatsapp.com/send?phone=5511998243336&text=&source=&data=&app_absent=", "text": "", "title": "", "classes": []}, {"url": "https://www.instagram.com/editoratibi/", "text": "Quem somos", "title": "", "classes": ["footer-link"]}], "images": [{"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/logo-tibi.svg", "alt": "", "title": "", "classes": ["img-fluid", "pc-img"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/banner-mobile-a-ultima-gota.png", "alt": "", "title": "", "classes": ["onlymobile"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/livro-sobre-vicio-no-celular-a-menina-da-cabeca-quadrada.webp", "alt": "", "title": "", "classes": ["onlymobile"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-mobile-colecoes.webp", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/presente.svg", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/cartao.svg", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/caminhao.svg", "alt": "", "title": "", "classes": ["img-fluid", "img-icones-home"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/seguro.svg", "alt": "", "title": "", "classes": ["img-fluid", "img-icones-home"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/produtos-a-ultima-gota.png", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/capa_do_livro_acreditar_emilia_nunez.png", "alt": "capaz", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/irmao-jaquinha/produto-IJ-home.webp", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/capaz/produto-capaz.png", "alt": "capaz", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Images/livros/cartas-para-o-futuro/capa-cartas-para-o-futuro-lua-emilia-nunez.jpg", "alt": "cartas-para-o-futuro", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner2mobile.png", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/formasdepagamento2.png", "alt": "", "title": "", "classes": []}], "technology_stack": {"frameworks": ["Next.js"], "libraries": [], "analytics": ["Google Tag Manager"], "other": [], "server": "Microsoft-IIS/10.0"}, "products": [{"title": "Best-Seller", "price": "", "image": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "description": "Best-Seller"}, {"title": "Todos os livros", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "description": "Todos os livros\r\n                            \n\n\n\n\n\n\n\n\n\n\n\n\n\r\n                                Coleções"}, {"title": "Livros por idade\r\n                                ", "price": "", "image": "", "description": "Livros por idade\r\n                                \n\n\n\n\n\n\n\n\n\r\n                                                0-3 anos\r\n                                            \n\r\n                                  "}, {"title": "Todos os livros", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "description": "Todos os livros"}, {"title": "Livros em destaque", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/produtos-a-ultima-gota.png", "description": "Livros em destaque\r\n            \n\n\n\n\n\nLançamento\n\n\n\n\n\r\n                   A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promov"}, {"title": "Livros em destaque", "price": "", "image": "", "description": "Livros em destaque"}, {"title": "A Última Gota", "price": "", "image": "", "description": "A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentávei"}, {"title": "Acreditar", "price": "", "image": "", "description": "Acreditar\r\n                \n\r\n                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para ente"}, {"title": "O Irmãozinho da Jaquinha", "price": "", "image": "", "description": "O Irmãozinho da Jaquinha\r\n                \n\r\n                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor q"}, {"title": "Capaz", "price": "", "image": "", "description": "Capaz\r\n                \n\r\n                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.\r\n          "}, {"title": "Best-Seller", "price": "", "image": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "description": "Best-Seller"}, {"title": "A Menina da Cabeça Quadrada", "price": "", "image": "", "description": "A Menina da Cabeça Quadrada\r\n                \n\r\n                    De tanto usar celular, tablet e computador, Cecília acordou\r\n                    com a cabeça quadrada! Uma história para ajudar uma"}, {"title": "Cartas para o Futuro", "price": "", "image": "", "description": "Cartas para o Futuro\r\n                \n\r\n                    As crianças podem ter voz ativa nas questões do mundo? Através do poder das cartas, a pequena Lua nos mostra que sim!\r\n                \n\n\n\n"}, {"title": "CONHEÇA TODOS OS LIVROS", "price": "", "image": "", "description": "CONHEÇA TODOS OS LIVROS"}, {"title": "CONHEÇA NOSSA HISTÓRIA", "price": "", "image": "", "description": "CONHEÇA NOSSA HISTÓRIA"}, {"title": "Livros por idade", "price": "", "image": "", "description": "Livros por idade\r\n            \n\n\n\n\n\n\n\n\r\n                                    0-3 anos\r\n                                \n\r\n                                    Primeiros Livros\r\n                         "}, {"title": "Livros por idade", "price": "", "image": "", "description": "Livros por idade"}], "prices": [], "categories": [], "age_groups": [{"category": "primeiros livros", "mentions": 2}, {"category": "leitores curiosos", "mentions": 2}, {"category": "amo ler", "mentions": 2}], "book_details": []}, "storyspark": {"url": "https://storyspark.ai/pt", "name": "Story Spark - Criação de Histórias", "type": "criacao_historias", "framework": "nextjs", "scraped_at": "2025-09-12T15:37:28.011648", "status_code": 200, "content_length": 404454, "title": "Story Spark | Criar e Ler Histórias Mágicas para Crianças", "meta_description": "Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura.", "meta_keywords": "", "headings": {"h1": ["Toda história começa com você", "TEM PERGUNTAS?"], "h2": ["Desperte sua imaginação", "Como funciona", "JUNTE-SE A 200,000 OUTROS CONTADORES DE HISTÓRIAS", "<PERSON>riar uma história por", "SUAS HISTÓRIAS BELAMENTE CONTADAS"], "h3": ["Crie seu personagem", "Crie sua história", "Adicione um toque de magia", "Organize seu mundo", "Imprima sua aventura", "Crie sua aventura", "Compartilhe sua aventura", "Imprima sua aventura"], "h4": [], "h5": [], "h6": []}, "links": [{"url": "https://storyspark.ai/pt/", "text": "", "title": "", "classes": ["flex-center", "gap-2", "md:gap-5"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/character-world", "text": "Personagens", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/library", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/pricing", "text": "Preços", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/learn", "text": "Aprender", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["w-3/4", "text-xl"]}, {"url": "https://storyspark.ai/pt/profile", "text": "<PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/character-world", "text": "Personagens", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["flex", "items-center", "gap-2"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Comunidade", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/library", "text": "Biblioteca de Histórias", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/pricing", "text": "Preços", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/", "text": "Aprender", "title": "", "classes": ["flex", "items-center", "gap-2"]}, {"url": "https://storyspark.ai/pt/faq", "text": "FAQ", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/blog", "text": "Blog", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/learn", "text": "Centro de Aprendizado", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/about-us", "text": "So<PERSON> Nós", "title": "", "classes": []}, {"url": "https://facebook.com/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://Instagram.com/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://www.youtube.com/@StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://www.Linkedin.com/company/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/privacy-policy", "text": "Política de Privacidade", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/terms-of-service", "text": "Termos de Serviço", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/create-story", "text": "Crie sua história", "title": "", "classes": ["mt-4"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Ver histórias da comunidade", "title": "", "classes": ["mx-auto", "mt-4"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "criar uma hist<PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/auth?signup=true", "text": "Inscreva-se agora", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/personalized-gifts", "text": "presente especial", "title": "", "classes": ["cursor-pointer", "italic", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/terms-of-service", "text": "aqui", "title": "", "classes": ["cursor-pointer", "italic", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/library", "text": "biblioteca infantil", "title": "", "classes": ["cursor-pointer", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Biblioteca da Comunidade", "title": "", "classes": ["cursor-pointer", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/faq", "text": "Tem mais perguntas?", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/auth", "text": "<PERSON><PERSON> hoje", "title": "", "classes": []}], "images": [{"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&w=256&q=75", "alt": "Story-Spark-Logo", "title": "", "classes": ["mb-5", "h-[44px]", "w-[44px]"]}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Left Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75", "alt": "Portuguese-flag", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Left Spark Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Right Spark Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75", "alt": "Portuguese-flag", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fdemo4.webp&w=3840&q=50", "alt": "", "title": "", "classes": ["object-cover"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook1.png&w=640&q=75", "alt": "Book 1", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook2.png&w=640&q=75", "alt": "Book 2", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook3.png&w=640&q=75", "alt": "Book 3", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook4.png&w=640&q=75", "alt": "Book 4", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook5.png&w=640&q=75", "alt": "Book 5", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-1.png&w=3840&q=75", "alt": "Crie seu personagem", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-2.png&w=3840&q=75", "alt": "Crie sua história", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-3.png&w=3840&q=75", "alt": "Adicione um toque de magia", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-4.png&w=3840&q=75", "alt": "Organize seu mundo", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-5.png&w=3840&q=75", "alt": "Imprima sua aventura", "title": "", "classes": []}, {"url": "https://storyspark.ai/cloud.svg", "alt": "Cloud", "title": "", "classes": ["object-contain"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F1.png&w=384&q=75", "alt": "Community Image 1", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F2.png&w=384&q=75", "alt": "Community Image 2", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F3.png&w=384&q=75", "alt": "Community Image 3", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F4.png&w=384&q=75", "alt": "Community Image 4", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F5.png&w=384&q=75", "alt": "Community Image 5", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F6.png&w=384&q=75", "alt": "Community Image 6", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}], "technology_stack": {"frameworks": ["Next.js", "React"], "libraries": [], "analytics": [], "other": [], "server": "Vercel"}, "features": [], "pricing": [{"plan": "pro", "mentions": 3}], "ai_capabilities": [{"feature": "ia", "mentions": 91}, {"feature": "ai", "mentions": 13}, {"feature": "gera<PERSON>", "mentions": 1}, {"feature": "criar <PERSON><PERSON><PERSON><PERSON><PERSON>", "mentions": 1}], "target_audience": ["Crian<PERSON><PERSON>", "crianças", "crianças", "educadores", "crianças"], "demo_info": []}}
{"url": "https://storyspark.ai/pt", "name": "Story Spark - Criação de Histórias", "type": "criacao_historias", "framework": "nextjs", "scraped_at": "2025-09-12T15:37:28.011648", "status_code": 200, "content_length": 404454, "title": "Story Spark | Criar e Ler Histórias Mágicas para Crianças", "meta_description": "Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura.", "meta_keywords": "", "headings": {"h1": ["Toda história começa com você", "TEM PERGUNTAS?"], "h2": ["Desperte sua imaginação", "Como funciona", "JUNTE-SE A 200,000 OUTROS CONTADORES DE HISTÓRIAS", "<PERSON>riar uma história por", "SUAS HISTÓRIAS BELAMENTE CONTADAS"], "h3": ["Crie seu personagem", "Crie sua história", "Adicione um toque de magia", "Organize seu mundo", "Imprima sua aventura", "Crie sua aventura", "Compartilhe sua aventura", "Imprima sua aventura"], "h4": [], "h5": [], "h6": []}, "links": [{"url": "https://storyspark.ai/pt/", "text": "", "title": "", "classes": ["flex-center", "gap-2", "md:gap-5"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/character-world", "text": "Personagens", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/library", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/pricing", "text": "Preços", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/learn", "text": "Aprender", "title": "", "classes": ["font-semibold", "text-indigo", "underline-offset-8", "hover:underline", "text-sm", "md:text-base", "lg:text-md"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["w-3/4", "text-xl"]}, {"url": "https://storyspark.ai/pt/profile", "text": "<PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/character-world", "text": "Personagens", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/", "text": "<PERSON><PERSON><PERSON>", "title": "", "classes": ["flex", "items-center", "gap-2"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Comunidade", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/library", "text": "Biblioteca de Histórias", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/pricing", "text": "Preços", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/", "text": "Aprender", "title": "", "classes": ["flex", "items-center", "gap-2"]}, {"url": "https://storyspark.ai/pt/faq", "text": "FAQ", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/blog", "text": "Blog", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/learn", "text": "Centro de Aprendizado", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/about-us", "text": "So<PERSON> Nós", "title": "", "classes": []}, {"url": "https://facebook.com/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://Instagram.com/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://www.youtube.com/@StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://www.Linkedin.com/company/StorySparkAI", "text": "", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/privacy-policy", "text": "Política de Privacidade", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/terms-of-service", "text": "Termos de Serviço", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/create-story", "text": "Crie sua história", "title": "", "classes": ["mt-4"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Ver histórias da comunidade", "title": "", "classes": ["mx-auto", "mt-4"]}, {"url": "https://storyspark.ai/pt/create-story", "text": "criar uma hist<PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/auth?signup=true", "text": "Inscreva-se agora", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/personalized-gifts", "text": "presente especial", "title": "", "classes": ["cursor-pointer", "italic", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/terms-of-service", "text": "aqui", "title": "", "classes": ["cursor-pointer", "italic", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/library", "text": "biblioteca infantil", "title": "", "classes": ["cursor-pointer", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/storyteller", "text": "Biblioteca da Comunidade", "title": "", "classes": ["cursor-pointer", "underline", "underline-offset-4"]}, {"url": "https://storyspark.ai/pt/faq", "text": "Tem mais perguntas?", "title": "", "classes": []}, {"url": "https://storyspark.ai/pt/auth", "text": "<PERSON><PERSON> hoje", "title": "", "classes": []}], "images": [{"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&w=256&q=75", "alt": "Story-Spark-Logo", "title": "", "classes": ["mb-5", "h-[44px]", "w-[44px]"]}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Left Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75", "alt": "Portuguese-flag", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Left Spark Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75", "alt": "Right Spark Icon", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75", "alt": "Portuguese-flag", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fdemo4.webp&w=3840&q=50", "alt": "", "title": "", "classes": ["object-cover"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook1.png&w=640&q=75", "alt": "Book 1", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook2.png&w=640&q=75", "alt": "Book 2", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook3.png&w=640&q=75", "alt": "Book 3", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook4.png&w=640&q=75", "alt": "Book 4", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook5.png&w=640&q=75", "alt": "Book 5", "title": "", "classes": ["h-[150px]", "w-[150px]", "rounded-md", "object-cover", "md:h-[200px]", "md:w-[200px]"]}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-1.png&w=3840&q=75", "alt": "Crie seu personagem", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-2.png&w=3840&q=75", "alt": "Crie sua história", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-3.png&w=3840&q=75", "alt": "Adicione um toque de magia", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-4.png&w=3840&q=75", "alt": "Organize seu mundo", "title": "", "classes": []}, {"url": "https://storyspark.ai/_next/image?url=%2Fstep-5.png&w=3840&q=75", "alt": "Imprima sua aventura", "title": "", "classes": []}, {"url": "https://storyspark.ai/cloud.svg", "alt": "Cloud", "title": "", "classes": ["object-contain"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F1.png&w=384&q=75", "alt": "Community Image 1", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F2.png&w=384&q=75", "alt": "Community Image 2", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F3.png&w=384&q=75", "alt": "Community Image 3", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F4.png&w=384&q=75", "alt": "Community Image 4", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F5.png&w=384&q=75", "alt": "Community Image 5", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}, {"url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fcommunity%2F6.png&w=384&q=75", "alt": "Community Image 6", "title": "", "classes": ["h-[100px]", "w-[100px]", "rounded-md", "object-cover", "md:h-[150px]", "md:w-[150px]"]}], "technology_stack": {"frameworks": ["Next.js", "React"], "libraries": [], "analytics": [], "other": [], "server": "Vercel"}, "features": [], "pricing": [{"plan": "pro", "mentions": 3}], "ai_capabilities": [{"feature": "ia", "mentions": 91}, {"feature": "ai", "mentions": 13}, {"feature": "gera<PERSON>", "mentions": 1}, {"feature": "criar <PERSON><PERSON><PERSON><PERSON><PERSON>", "mentions": 1}], "target_audience": ["Crian<PERSON><PERSON>", "crianças", "crianças", "educadores", "crianças"], "demo_info": []}
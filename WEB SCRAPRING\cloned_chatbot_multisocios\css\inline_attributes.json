[{"tag": "div", "style": "font-size:1.5rem", "classes": []}, {"tag": "div", "style": "display:flex;flex-direction:column;gap:0.75rem", "classes": []}, {"tag": "div", "style": "margin-top:1.5rem", "classes": ["card"]}, {"tag": "div", "style": "display:flex;flex-direction:column;gap:0.75rem", "classes": []}, {"tag": "div", "style": "margin-top:1.5rem", "classes": ["card"]}, {"tag": "div", "style": "display:flex;flex-direction:column;gap:0.5rem", "classes": []}, {"tag": "div", "style": "text-align:center;margin-bottom:3rem", "classes": []}, {"tag": "h1", "style": "font-size:3rem;font-weight:700;color:var(--gray-900);margin-bottom:1rem;background:linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text", "classes": []}, {"tag": "p", "style": "font-size:1.25rem;color:var(--gray-600);margin-bottom:2rem;max-width:600px;margin:0 auto 2rem auto", "classes": []}, {"tag": "div", "style": "display:flex;gap:1rem;justify-content:center;flex-wrap:wrap", "classes": []}, {"tag": "a", "style": "font-size:1.125rem;padding:1rem 2rem", "classes": ["button"]}, {"tag": "a", "style": "font-size:1.125rem;padding:1rem 2rem", "classes": ["button", "button-secondary"]}, {"tag": "div", "style": "margin-bottom:3rem", "classes": []}, {"tag": "div", "style": "text-align:center;margin-bottom:2rem", "classes": []}, {"tag": "h2", "style": "font-size:2rem;font-weight:700;color:var(--gray-900);margin-bottom:1rem", "classes": []}, {"tag": "p", "style": "font-size:1.125rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "width:3rem;height:3rem;background:var(--primary-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:1.5rem;margin-bottom:1rem", "classes": []}, {"tag": "h3", "style": "font-size:1.25rem;font-weight:600;color:var(--gray-900);margin-bottom:0.5rem", "classes": []}, {"tag": "p", "style": "color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "width:3rem;height:3rem;background:var(--secondary-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:1.5rem;margin-bottom:1rem", "classes": []}, {"tag": "h3", "style": "font-size:1.25rem;font-weight:600;color:var(--gray-900);margin-bottom:0.5rem", "classes": []}, {"tag": "p", "style": "color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "width:3rem;height:3rem;background:var(--success-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:1.5rem;margin-bottom:1rem", "classes": []}, {"tag": "h3", "style": "font-size:1.25rem;font-weight:600;color:var(--gray-900);margin-bottom:0.5rem", "classes": []}, {"tag": "p", "style": "color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "margin-bottom:3rem", "classes": []}, {"tag": "div", "style": "text-align:center;margin-bottom:2rem", "classes": []}, {"tag": "h2", "style": "font-size:2rem;font-weight:700;color:var(--gray-900);margin-bottom:1rem", "classes": []}, {"tag": "p", "style": "font-size:1.125rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "text-align:center", "classes": []}, {"tag": "div", "style": "width:4rem;height:4rem;background:var(--primary-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:2rem;margin:0 auto 1rem auto", "classes": []}, {"tag": "h3", "style": "font-weight:600;color:var(--gray-900);margin-bottom:0.25rem", "classes": []}, {"tag": "p", "style": "font-size:0.875rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "text-align:center", "classes": []}, {"tag": "div", "style": "width:4rem;height:4rem;background:var(--success-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:2rem;margin:0 auto 1rem auto", "classes": []}, {"tag": "h3", "style": "font-weight:600;color:var(--gray-900);margin-bottom:0.25rem", "classes": []}, {"tag": "p", "style": "font-size:0.875rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "text-align:center", "classes": []}, {"tag": "div", "style": "width:4rem;height:4rem;background:var(--secondary-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:2rem;margin:0 auto 1rem auto", "classes": []}, {"tag": "h3", "style": "font-weight:600;color:var(--gray-900);margin-bottom:0.25rem", "classes": []}, {"tag": "p", "style": "font-size:0.875rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "text-align:center", "classes": []}, {"tag": "div", "style": "width:4rem;height:4rem;background:var(--accent-color);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center;font-size:2rem;margin:0 auto 1rem auto", "classes": []}, {"tag": "h3", "style": "font-weight:600;color:var(--gray-900);margin-bottom:0.25rem", "classes": []}, {"tag": "p", "style": "font-size:0.875rem;color:var(--gray-600)", "classes": []}, {"tag": "div", "style": "background:linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);border-radius:var(--radius-xl);padding:3rem 2rem;text-align:center;color:var(--white);margin-bottom:2rem", "classes": []}, {"tag": "h2", "style": "font-size:2rem;font-weight:700;margin-bottom:1rem", "classes": []}, {"tag": "p", "style": "font-size:1.125rem;opacity:0.9;margin-bottom:2rem", "classes": []}, {"tag": "div", "style": "display:flex;gap:1rem;justify-content:center;flex-wrap:wrap", "classes": []}, {"tag": "a", "style": "background:var(--white);color:var(--primary-color);padding:1rem 2rem;border-radius:var(--radius-md);text-decoration:none;font-weight:600;font-size:1.125rem;transition:all 0.2s ease", "classes": []}, {"tag": "a", "style": "background:transparent;border:2px solid var(--white);color:var(--white);padding:1rem 2rem;border-radius:var(--radius-md);text-decoration:none;font-weight:600;font-size:1.125rem;transition:all 0.2s ease", "classes": []}]
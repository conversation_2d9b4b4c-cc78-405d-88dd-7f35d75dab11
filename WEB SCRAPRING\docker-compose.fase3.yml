# Docker Compose para WebScraper Empresarial - Fase 3
# Stack completo para produção e desenvolvimento

version: '3.8'

services:
  # ================================
  # WebScraper Application
  # ================================
  webscraper:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: webscraper-app
    restart: unless-stopped
    ports:
      - "8000:8000"  # Metrics/Health
      - "8080:8080"  # API
    environment:
      # Database
      - WEBSCRAPER_DATABASE_URL=postgresql+asyncpg://webscraper:webscraper123@postgres:5432/webscraper
      
      # Redis
      - WEBSCRAPER_REDIS_URL=redis://redis:6379/0
      
      # S3/MinIO
      - WEBSCRAPER_S3_ENDPOINT_URL=http://minio:9000
      - WEBSCRAPER_S3_ACCESS_KEY=minioadmin
      - WEBSCRAPER_S3_SECRET_KEY=minioadmin123
      - WEBSCRAPER_S3_BUCKET_NAME=webscraper
      
      # Elasticsearch
      - WEBSCRAPER_ELASTICSEARCH_URL=http://elasticsearch:9200
      
      # Application
      - WEBSCRAPER_ENVIRONMENT=development
      - WEBSCRAPER_DEBUG=true
      - WEBSCRAPER_LOG_LEVEL=DEBUG
      - WEBSCRAPER_METRICS_ENABLED=true
      - WEBSCRAPER_ALERTS_ENABLED=true
      
      # Webhook para testes
      - WEBSCRAPER_WEBHOOK_URL=http://webhook-test:8080/webhook
    volumes:
      - ./src:/app/src:ro
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ================================
  # PostgreSQL Database
  # ================================
  postgres:
    image: postgres:15-alpine
    container_name: webscraper-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=webscraper
      - POSTGRES_USER=webscraper
      - POSTGRES_PASSWORD=webscraper123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U webscraper -d webscraper"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ================================
  # Redis Cache & Queues
  # ================================
  redis:
    image: redis:7-alpine
    container_name: webscraper-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ================================
  # MinIO S3 Storage
  # ================================
  minio:
    image: minio/minio:latest
    container_name: webscraper-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # Elasticsearch Analytics
  # ================================
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: webscraper-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ================================
  # Prometheus Monitoring
  # ================================
  prometheus:
    image: prom/prometheus:latest
    container_name: webscraper-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - webscraper-network
    depends_on:
      - webscraper

  # ================================
  # Grafana Dashboard
  # ================================
  grafana:
    image: grafana/grafana:latest
    container_name: webscraper-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - webscraper-network
    depends_on:
      - prometheus

  # ================================
  # Webhook Test Server
  # ================================
  webhook-test:
    image: mendhak/http-https-echo:latest
    container_name: webscraper-webhook-test
    restart: unless-stopped
    ports:
      - "8888:8080"
    environment:
      - HTTP_PORT=8080
    networks:
      - webscraper-network

  # ================================
  # Redis Commander (Redis UI)
  # ================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: webscraper-redis-ui
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    networks:
      - webscraper-network
    depends_on:
      - redis

  # ================================
  # pgAdmin (PostgreSQL UI)
  # ================================
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: webscraper-pgadmin
    restart: unless-stopped
    ports:
      - "8082:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - webscraper-network
    depends_on:
      - postgres

# ================================
# Networks
# ================================
networks:
  webscraper-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================
# Volumes
# ================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  pgadmin_data:
    driver: local

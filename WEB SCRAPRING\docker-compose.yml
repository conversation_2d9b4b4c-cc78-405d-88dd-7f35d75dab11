version: '3.8'

# =============================================================================
# WebScraper - Docker Compose Configuration
# =============================================================================

services:
  # =============================================================================
  # Aplicação Principal
  # =============================================================================
  webscraper:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: application
    container_name: webscraper-app
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://webscraper:${POSTGRES_PASSWORD:-webscraper123}@postgres:5432/webscraper
      - REDIS_URL=redis://redis:6379/0
      - ENABLE_CACHE=true
      - METRICS_ENABLED=true
      - PROMETHEUS_PORT=8000
      - LOG_LEVEL=INFO
      - PREFECT_API_URL=http://prefect-server:4200/api
    volumes:
      - webscraper_data:/app/data
      - webscraper_logs:/app/logs
      - ./configs:/app/configs:ro
    ports:
      - "8080:8080"  # API
      - "8000:8000"  # Métricas Prometheus
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "./healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # Banco de Dados PostgreSQL
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: webscraper-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=webscraper
      - POSTGRES_USER=webscraper
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-webscraper123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U webscraper -d webscraper"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Cache Redis
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: webscraper-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # =============================================================================
  # Prefect Server (Orquestração)
  # =============================================================================
  prefect-server:
    image: prefecthq/prefect:2.14-python3.11
    container_name: webscraper-prefect-server
    restart: unless-stopped
    command: prefect server start --host 0.0.0.0
    environment:
      - PREFECT_UI_URL=http://localhost:4200
      - PREFECT_API_URL=http://localhost:4200/api
      - PREFECT_SERVER_API_HOST=0.0.0.0
    volumes:
      - prefect_data:/root/.prefect
    ports:
      - "4200:4200"
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4200/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Prefect Agent (Worker)
  # =============================================================================
  prefect-agent:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: application
    container_name: webscraper-prefect-agent
    restart: unless-stopped
    command: prefect agent start --work-queue default
    environment:
      - PREFECT_API_URL=http://prefect-server:4200/api
      - DATABASE_URL=postgresql+asyncpg://webscraper:${POSTGRES_PASSWORD:-webscraper123}@postgres:5432/webscraper
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - webscraper_data:/app/data
      - webscraper_logs:/app/logs
      - ./configs:/app/configs:ro
    depends_on:
      - prefect-server
      - postgres
      - redis
    networks:
      - webscraper-network

  # =============================================================================
  # Prometheus (Métricas)
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: webscraper-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Grafana (Dashboards)
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: webscraper-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MinIO (Storage S3-compatible) - Opcional
  # =============================================================================
  minio:
    image: minio/minio:latest
    container_name: webscraper-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    networks:
      - webscraper-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

# =============================================================================
# Volumes
# =============================================================================
volumes:
  webscraper_data:
    driver: local
  webscraper_logs:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prefect_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local

# =============================================================================
# Networks
# =============================================================================
networks:
  webscraper-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

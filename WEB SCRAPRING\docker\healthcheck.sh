#!/bin/bash

# =============================================================================
# Script de healthcheck para o container WebScraper
# =============================================================================

set -e

# Configurações
TIMEOUT=10
API_PORT=${API_PORT:-8080}
METRICS_PORT=${PROMETHEUS_PORT:-8000}

# Função para verificar se uma porta está respondendo
check_port() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    if timeout $timeout bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Função para verificar HTTP endpoint
check_http() {
    local url=$1
    local timeout=${2:-5}
    
    if curl -f -s --max-time $timeout "$url" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# Verificações básicas
# =============================================================================

echo "Executando healthcheck..."

# 1. Verificar se o processo principal está rodando
if ! pgrep -f "webscraper" >/dev/null; then
    echo "ERRO: Processo webscraper não encontrado"
    exit 1
fi

# 2. Verificar acesso aos diretórios essenciais
if [ ! -r "/app/data" ]; then
    echo "ERRO: Diretório /app/data não acessível"
    exit 1
fi

if [ ! -w "/app/logs" ]; then
    echo "ERRO: Diretório /app/logs não gravável"
    exit 1
fi

# =============================================================================
# Verificações de conectividade
# =============================================================================

# 3. Verificar banco de dados
if [[ "${DATABASE_URL}" == postgresql* ]]; then
    # PostgreSQL
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    
    if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
        if ! check_port "$DB_HOST" "$DB_PORT" 3; then
            echo "ERRO: PostgreSQL não acessível em $DB_HOST:$DB_PORT"
            exit 1
        fi
    fi
elif [[ "${DATABASE_URL}" == sqlite* ]]; then
    # SQLite - verificar se o arquivo existe e é acessível
    DB_FILE=$(echo $DATABASE_URL | sed 's/sqlite.*:\/\/\///')
    if [ ! -f "$DB_FILE" ] && [ ! -w "$(dirname "$DB_FILE")" ]; then
        echo "ERRO: Arquivo SQLite não acessível: $DB_FILE"
        exit 1
    fi
fi

# 4. Verificar Redis (se habilitado)
if [ "${ENABLE_CACHE:-false}" = "true" ] && [ -n "${REDIS_URL}" ]; then
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/redis:\/\/[^:]*:\([0-9]*\).*/\1/p')
    
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        if ! check_port "$REDIS_HOST" "$REDIS_PORT" 3; then
            echo "AVISO: Redis não acessível em $REDIS_HOST:$REDIS_PORT"
            # Não falha o healthcheck, apenas avisa
        fi
    fi
fi

# =============================================================================
# Verificações de serviços
# =============================================================================

# 5. Verificar API (se habilitada)
if [ "${API_ENABLED:-false}" = "true" ]; then
    if check_port "localhost" "$API_PORT" 3; then
        # Verificar endpoint de health da API
        if ! check_http "http://localhost:$API_PORT/health" 3; then
            echo "ERRO: API health endpoint não responde"
            exit 1
        fi
    else
        echo "ERRO: API não está escutando na porta $API_PORT"
        exit 1
    fi
fi

# 6. Verificar métricas Prometheus (se habilitadas)
if [ "${METRICS_ENABLED:-false}" = "true" ]; then
    if check_port "localhost" "$METRICS_PORT" 3; then
        # Verificar endpoint de métricas
        if ! check_http "http://localhost:$METRICS_PORT/metrics" 3; then
            echo "ERRO: Endpoint de métricas não responde"
            exit 1
        fi
    else
        echo "ERRO: Servidor de métricas não está escutando na porta $METRICS_PORT"
        exit 1
    fi
fi

# =============================================================================
# Verificações de recursos
# =============================================================================

# 7. Verificar uso de memória (aviso se > 90%)
if command -v free >/dev/null 2>&1; then
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ "$MEMORY_USAGE" -gt 90 ]; then
        echo "AVISO: Uso de memória alto: ${MEMORY_USAGE}%"
    fi
fi

# 8. Verificar espaço em disco (aviso se < 10% livre)
DISK_USAGE=$(df /app | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 90 ]; then
    echo "AVISO: Espaço em disco baixo: ${DISK_USAGE}% usado"
fi

# =============================================================================
# Verificações específicas do WebScraper
# =============================================================================

# 9. Verificar se há jobs travados (opcional)
# Isso dependeria da implementação específica do sistema de jobs

# 10. Verificar logs recentes para erros críticos
if [ -f "/app/logs/webscraper.log" ]; then
    # Verificar se há erros críticos nos últimos 5 minutos
    RECENT_ERRORS=$(tail -n 100 /app/logs/webscraper.log | grep -c "ERROR\|CRITICAL" || true)
    if [ "$RECENT_ERRORS" -gt 10 ]; then
        echo "AVISO: Muitos erros recentes nos logs: $RECENT_ERRORS"
    fi
fi

# =============================================================================
# Resultado final
# =============================================================================

echo "Healthcheck concluído com sucesso"
exit 0

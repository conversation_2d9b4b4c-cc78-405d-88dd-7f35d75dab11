-- =============================================================================
-- Script de inicialização do banco PostgreSQL para WebScraper
-- =============================================================================

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Configurar timezone
SET timezone = 'UTC';

-- =============================================================================
-- Configurações de performance
-- =============================================================================

-- Configurações para melhor performance com JSONB
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET pg_stat_statements.track = 'all';

-- =============================================================================
-- Funções auxiliares
-- =============================================================================

-- Função para gerar hash SHA256
CREATE OR REPLACE FUNCTION sha256(input text)
RETURNS text AS $$
BEGIN
    RETURN encode(digest(input, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para normalizar URLs
CREATE OR REPLACE FUNCTION normalize_url(url text)
RETURNS text AS $$
BEGIN
    -- Remove fragmentos (#) e alguns parâmetros de tracking
    url := regexp_replace(url, '#.*$', '');
    url := regexp_replace(url, '[?&](utm_[^&]*|fbclid=[^&]*|gclid=[^&]*)', '', 'g');
    url := regexp_replace(url, '\?$', '');
    
    -- Converte para lowercase (exceto path)
    RETURN lower(split_part(url, '/', 1)) || '://' || 
           lower(split_part(split_part(url, '/', 3), '?', 1)) ||
           substring(url from position('/' in substring(url from 9)));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para extrair domínio de uma URL
CREATE OR REPLACE FUNCTION extract_domain(url text)
RETURNS text AS $$
BEGIN
    RETURN lower(split_part(split_part(url, '/', 3), ':', 1));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =============================================================================
-- Comentários e documentação
-- =============================================================================

COMMENT ON DATABASE webscraper IS 'Banco de dados do sistema WebScraper - armazena metadados de páginas coletadas, configurações e métricas';

COMMENT ON FUNCTION sha256(text) IS 'Gera hash SHA256 de um texto - usado para content_hash das páginas';
COMMENT ON FUNCTION normalize_url(text) IS 'Normaliza URLs removendo fragmentos e parâmetros de tracking';
COMMENT ON FUNCTION extract_domain(text) IS 'Extrai o domínio de uma URL';

-- =============================================================================
-- Configurações de logging
-- =============================================================================

-- Log de queries lentas (> 1 segundo)
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET log_statement = 'mod';
ALTER SYSTEM SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';

-- =============================================================================
-- Usuários e permissões adicionais (se necessário)
-- =============================================================================

-- Criar usuário read-only para dashboards/relatórios
-- CREATE USER webscraper_readonly WITH PASSWORD 'readonly_password';
-- GRANT CONNECT ON DATABASE webscraper TO webscraper_readonly;
-- GRANT USAGE ON SCHEMA public TO webscraper_readonly;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO webscraper_readonly;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO webscraper_readonly;

-- =============================================================================
-- Configurações finais
-- =============================================================================

-- Aplicar configurações
SELECT pg_reload_conf();

-- Mostrar informações da inicialização
SELECT 
    'WebScraper Database Initialized' as status,
    current_database() as database,
    current_user as user,
    version() as postgres_version,
    now() as initialized_at;

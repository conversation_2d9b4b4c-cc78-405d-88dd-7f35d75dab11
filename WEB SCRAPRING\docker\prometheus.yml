# =============================================================================
# Configuração do Prometheus para WebScraper
# =============================================================================

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'webscraper-monitor'

# Configuração de alertas (Alertmanager)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Regras de alerta
rule_files:
  # - "alert_rules.yml"

# Configuração de scraping
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # WebScraper application metrics
  - job_name: 'webscraper'
    static_configs:
      - targets: ['webscraper:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # PostgreSQL metrics (se usar postgres_exporter)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis metrics (se usar redis_exporter)  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node/system metrics (se usar node_exporter)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # Prefect metrics (se disponível)
  - job_name: 'prefect'
    static_configs:
      - targets: ['prefect-server:4200']
    scrape_interval: 60s
    metrics_path: /api/metrics
    scrape_timeout: 30s

# Configurações de storage
storage:
  tsdb:
    # Retenção de dados
    retention.time: 30d
    retention.size: 10GB
    
    # Configurações de compactação
    min-block-duration: 2h
    max-block-duration: 25h
    
    # WAL (Write-Ahead Log)
    wal-compression: true

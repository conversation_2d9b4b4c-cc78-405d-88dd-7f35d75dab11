# 📊 Project Analysis Report

**Generated on:** 2025-09-12 15:42:52  
**Root directory:** `C:\Users\<USER>\OneDrive\Área de Trabalho\IDEIAS\99 - RESOURCES\1000 - ZONA DESENVOLVIMENTO\WEB SCRAPRING\enhanced_multi_sites_results`  
**Purpose:** Complete project structure documentation excluding CSV files

## 📈 Project Summary

- **Total files analyzed:** 16
- **📄 Code/Text files:** 16
- **🖼️ Image files:** 0
- **🚫 Excluded files (CSV):** 0
- **⚠️ Large files (> 5MB):** 0
- **📁 Other files:** 0

## 🌳 Project Structure

```
├── clubefundamento 📁
│   ├── clubefundamento_enhanced_analysis.md 📄
│   ├── enhanced_data.json 📄
│   └── html 📁
│       └── original.html 📄
├── combine_project.py 📄
├── compilado sites .md 📄
├── dentrodahistoria 📁
│   ├── dentrodahistoria_enhanced_analysis.md 📄
│   ├── enhanced_data.json 📄
│   └── html 📁
│       └── original.html 📄
├── enhanced_consolidated_data.json 📄
├── enhanced_multi_sites_analysis.md 📄
├── meutibi 📁
│   ├── enhanced_data.json 📄
│   ├── html 📁
│   │   └── original.html 📄
│   └── meutibi_enhanced_analysis.md 📄
└── storyspark 📁
    ├── enhanced_data.json 📄
    ├── html 📁
    │   └── original.html 📄
    └── storyspark_enhanced_analysis.md 📄
```

## 📋 Project Contents

### 📄 Code and Text Files


#### 📁 Directory: clubefundamento

##### 📄 clubefundamento_enhanced_analysis.md
*Path: `clubefundamento\clubefundamento_enhanced_analysis.md`*  
*Size: 2.09 KB*

```md
# 🚀 Análise Avançada - Clube Fundamento

> **Web Scraping Avançado Executado com Sucesso**
> Site: https://www.clubefundamento.com.br/
> Data: 2025-09-12T15:37:22.148358
> Parser: parse_clubefundamento

## 📊 **Resumo Executivo**

O **Clube Fundamento** é uma plataforma de **clube_livros** construída com **svelte**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: svelte
- **Tipo**: clube_livros
- **Status**: 200
- **Tamanho**: 108140 bytes

### **Tecnologias Detectadas**
- **Frameworks**: Svelte
- **Server**: cloudflare


## 🎯 **Informações Básicas**

- **Título**: Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil
- **Descrição**: Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.
- **Palavras-chave**: clube de livros, livros infantis, educação, crianças, leitura

## 📦 **Produtos Encontrados**

Nenhum produto específico encontrado com os seletores utilizados.

## 💰 **Análise de Preços**

Nenhum preço específico encontrado.


## 🔍 **Estrutura de Headings**
- **H2**: 9 elementos
  - Como Funciona?...
  - Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária....
  - PLANO COMPLETO...
- **H3**: 20 elementos
  - +12 MILHÕES...
  - BEST-SELLERS...
  - 24 ANOS...
- **H4**: 1 elementos
  - Como funciona?...


## 🎉 **Conclusão**

A análise avançada do **Clube Fundamento** revelou:

- ✅ **Produtos identificados**: 0
- ✅ **Preços extraídos**: 0
- ✅ **Tecnologia**: svelte bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `clubefundamento/enhanced_data.json` - Dados estruturados avançados
- `clubefundamento/html/original.html` - HTML original
- `clubefundamento_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
```

---

##### 📄 enhanced_data.json
*Path: `clubefundamento\enhanced_data.json`*  
*Size: 26.29 KB*

```json
{
  "url": "https://www.clubefundamento.com.br/",
  "name": "Clube Fundamento",
  "type": "clube_livros",
  "framework": "svelte",
  "scraped_at": "2025-09-12T15:37:22.148358",
  "status_code": 200,
  "content_length": 108140,
  "title": "Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil",
  "meta_description": "Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.",
  "meta_keywords": "clube de livros, livros infantis, educação, crianças, leitura",
  "headings": {
    "h1": [],
    "h2": [
      "Como Funciona?",
      "Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária.",
      "PLANO COMPLETO",
      "PLANO DUPLO",
      "PLANO SIMPLES",
      "Kit Clube Expresso.",
      "O que vem  no kit?",
      "Depoimentos",
      "Avaliações dos Pais"
    ],
    "h3": [
      "+12 MILHÕES",
      "BEST-SELLERS",
      "24 ANOS",
      "97% DE APROVAÇÃO",
      "JÁ TENHO LIVROS DA FUNDAMENTO. E AGORA?",
      "Receba tudo de uma vez só.",
      "1.  \n\t\t\t\t\t\t\t\tBest-sellers nacionais e internacionais",
      "2.  \n\t\t\t\t\t\t\t\tPassaporte do Leitor",
      "3.  \n\t\t\t\t\t\t\t\tBrindes",
      "Vanessa",
      "Márcia Cristina",
      "Sheila",
      "Luciana",
      "Thais",
      "Daiana",
      "Antonia",
      "Cidiane",
      "Arlete",
      "Sobre",
      "Redes Sociais"
    ],
    "h4": [
      "Como funciona?"
    ],
    "h5": [],
    "h6": []
  },
  "links": [
    {
      "url": "https://www.clubefundamento.com.br/",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.clubefundamento.com.br/#planos",
      "text": "Planos",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://wix.clubefundamento.com.br/amostra",
      "text": "Amostra",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://wix.clubefundamento.com.br/blog",
      "text": "Blog",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br",
      "text": "SOU ASSINANTE",
      "title": "",
      "classes": [
        "rounded-full",
        "bg-[#21A26C]",
        "px-4",
        "py-2",
        "font-bold",
        "text-white",
        "transition-colors",
        "hover:bg-green-700"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "rounded-full",
        "bg-[#FAE941]",
        "px-6",
        "py-2",
        "font-bold",
        "text-black",
        "transition-colors",
        "hover:bg-yellow-300"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.clubefundamento.com.br/#planos",
      "text": "ASSINE JÁ",
      "title": "",
      "classes": [
        "mx-auto",
        "font-bold",
        "rounded-full",
        "bg-[#FAE941]",
        "px-12",
        "py-2",
        "md:py-4",
        "text-center",
        "text-base",
        "text-gray-900",
        "hover:bg-[#F8D93A]",
        "md:px-16",
        "md:text-lg",
        "lg:px-20"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/amostra",
      "text": "AMOSTRA",
      "title": "",
      "classes": [
        "mx-auto",
        "rounded-full",
        "bg-[#FAE941]",
        "px-12",
        "py-2",
        "text-center",
        "text-base",
        "font-bold",
        "text-gray-900",
        "hover:bg-[#F8D93A]",
        "md:px-16",
        "md:py-4",
        "md:text-lg",
        "lg:px-20"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-duplo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-simples-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://www.lojafundamento.com.br/clube-fundamento/kits-clube-fundamento/",
      "text": "Compre Aqui",
      "title": "",
      "classes": [
        "inline-block",
        "rounded-lg",
        "bg-yellow-400",
        "px-8",
        "py-4",
        "text-lg",
        "font-bold",
        "text-black",
        "shadow-lg",
        "transition-colors",
        "duration-200",
        "hover:bg-yellow-500",
        "hover:shadow-xl"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/sobre-o-kit",
      "text": "Mais detalhes sobre o kit",
      "title": "",
      "classes": [
        "transform",
        "cursor-pointer",
        "rounded-full",
        "bg-[#FAE941]",
        "px-8",
        "py-4",
        "text-sm",
        "font-bold",
        "tracking-wide",
        "text-gray-900",
        "uppercase",
        "shadow-lg",
        "transition-colors",
        "duration-200",
        "hover:bg-yellow-400",
        "hover:shadow-xl",
        "md:text-lg"
      ]
    },
    {
      "url": "https://www.instagram.com/fundamentoeditora/",
      "text": "As histórias na hora de dormir ficaram ainda melhores ❤️✨",
      "title": "",
      "classes": [
        "group",
        "block",
        "overflow-hidden",
        "rounded-2xl",
        "bg-white",
        "shadow-sm",
        "transition-all",
        "duration-300",
        "hover:shadow-lg"
      ]
    },
    {
      "url": "https://www.instagram.com/fundamentoeditora/",
      "text": "Já vou avisando que o livro é ótimo 😍😍",
      "title": "",
      "classes": [
        "group",
        "block",
        "overflow-hidden",
        "rounded-2xl",
        "bg-white",
        "shadow-sm",
        "transition-all",
        "duration-300",
        "hover:shadow-lg"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/cdn-cgi/l/email-protection",
      "text": "[email protected]",
      "title": "",
      "classes": [
        "__cf_email__"
      ]
    },
    {
      "url": "https://www.reclameaqui.com.br/empresa/editora-fundamento/",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.clubefundamento.com.br/perguntas-frequentes",
      "text": "Dúvidas e Perguntas Frequentes",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.lojafundamento.com.br/",
      "text": "Loja Virtual",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/sobre",
      "text": "Sobre Nós",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/blog",
      "text": "Blog",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/sobre-o-kit",
      "text": "Mais Sobre o Kit",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/atividades",
      "text": "Atividades Gratuitas",
      "title": "",
      "classes": [
        "block",
        "transition-colors",
        "hover:text-white"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/_files/ugd/44c0be_48170609e9c048b1b0f774178c25867d.pdf",
      "text": "Contrato",
      "title": "",
      "

... (Content truncated - file too large)
```

---


#### 📁 Directory: clubefundamento\html

##### 📄 original.html
*Path: `clubefundamento\html\original.html`*  
*Size: 105.61 KB*

```html
<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<link href="./favicon.ico" rel="icon"/>
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<title>Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil</title>
<meta content="Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos." name="description"/>
<meta content="clube de livros, livros infantis, educação, crianças, leitura" name="keywords"/>
<meta content="Clube Fundamento - Clube de Livros Infantis" property="og:title"/>
<meta content="Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos." property="og:description"/>
<meta content="website" property="og:type"/>
<meta content="summary_large_image" name="twitter:card"/>
<!-- Otimizações de performance -->
<meta content="index, follow" name="robots"/>
<link href="https://www.clubefundamento.com.br/" rel="canonical"/>
<link href="https://s3.amazonaws.com" rel="preconnect"/>
<link href="https://s3.amazonaws.com" rel="dns-prefetch"/>
<link href="./_app/immutable/assets/0.DsJ08R8X.css" rel="stylesheet"/>
<link href="./_app/immutable/assets/4.Cm4fRFos.css" rel="stylesheet"/>
<link href="./_app/immutable/entry/start.BlIVssTZ.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/BAWt4-nI.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/BYdHp-zn.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/r_Vhqamn.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/BAfzkbIJ.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/D0iwhpLH.js" rel="modulepreload"/>
<link href="./_app/immutable/entry/app.o0hgn_bP.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/Bzak7iHL.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/B9w7wuOF.js" rel="modulepreload"/>
<link href="./_app/immutable/nodes/0.CFB5IScr.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/Dszi91u7.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/C1cDsA85.js" rel="modulepreload"/>
<link href="./_app/immutable/nodes/4.Ck6Flz1m.js" rel="modulepreload"/>
<link href="./_app/immutable/chunks/C2R3bjLj.js" rel="modulepreload"/><!--[--><script data-account-id="*********" data-widget-id="068bc340-40fc-789b-8000-6374eeb3e8b5" src="https://diffuser-cdn.app-us1.com/whatsapp/widget.cjs.production.min.js">
</script><!-- --><!--]-->
</head>
<body data-sveltekit-preload-data="hover">
<div style="display: contents"><!--[--><!--[--><!-- --><header class="sticky top-0 z-50 bg-[#183652] shadow-sm"><div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8"><div class="flex h-24 items-center justify-between"><div class="flex items-center space-x-3"><a aria-label="Clube Fundamento" href="/"><img alt="Clube Fundamento" class="md:h-[30px] md:w-[150px] lg:h-[64px] lg:w-[299px]" src="/_app/immutable/assets/clubefundamento.uJCGl9c2.avif"/></a></div> <nav class="hidden space-x-8 md:flex"><a class="text-white transition-colors hover:text-yellow-300" href="/#planos">Planos</a> <a class="text-white transition-colors hover:text-yellow-300" href="https://wix.clubefundamento.com.br/amostra">Amostra</a> <a class="text-white transition-colors hover:text-yellow-300" href="https://wix.clubefundamento.com.br/blog">Blog</a></nav> <div class="hidden items-center space-x-4 md:flex"><a class="rounded-full bg-[#21A26C] px-4 py-2 font-bold text-white transition-colors hover:bg-green-700" href="https://assinar.clubefundamento.com.br">SOU ASSINANTE</a> <a class="rounded-full bg-[#FAE941] px-6 py-2 font-bold text-black transition-colors hover:bg-yellow-300" href="https://assinar.clubefundamento.com.br/checkout/plano-completo-anual">COMPRAR</a></div> <button aria-label="Abrir menu" class="p-2 md:hidden"><svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewbox="0 0 24 24"><path d="M4 6h16M4 12h16M4 18h16" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path></svg></button></div> <!--[!--><!--]--></div></header><!-- --> <!--[!--><!-- --><main class="max-w-screen"><div class="h-[200px] w-full md:h-auto lg:h-full"><a href="https://assinar.clubefundamento.com.br/checkout/plano-completo-anual"><img alt="Banner do Clube Fundamento" class="h-full w-full object-cover" decoding="async" fetchpriority="high" loading="eager" src="/_app/immutable/assets/banner.BFypisbC.webp"/></a></div><!-- --> <div class="flex w-full flex-col items-center justify-center"><!--[!--><picture><!--[--><!--]--> <img alt="Editora Fundamento logo" class="mx-auto object-contain" decoding="async" height="50" loading="lazy" src="/_app/immutable/assets/logo.C87zx4WW.avif" width="200"/></picture><!--]--></div> <div class="flex w-full justify-center overflow-hidden px-4 py-6 md:px-10 lg:px-20"><div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 lg:gap-6"><!--[--><div class="flex items-start gap-4 lg:gap-4"><div class="flex-shrink-0"><!--[!--><picture><!--[--><!--]--> <img alt="+12 MILHÕES" class="h-16 w-16 object-contain lg:h-16 lg:w-16" decoding="async" height="64" loading="lazy" src="/_app/immutable/assets/1.CqNyifm5.avif" width="64"/></picture><!--]--></div> <div class="min-w-0 flex-1 pt-1 lg:pt-2"><h3 class="text-lg leading-tight font-semibold">+12 MILHÕES</h3> <p class="text-sm leading-tight text-gray-600">de livros vendidos e crianças alcançadas.</p></div></div><div class="flex items-start gap-4 lg:gap-4"><div class="flex-shrink-0"><!--[!--><picture><!--[--><!--]--> <img alt="BEST-SELLERS" class="h-16 w-16 object-contain lg:h-16 lg:w-16" decoding="async" height="64" loading="lazy" src="/_app/immutable/assets/2.DreehQ1f.avif" width="64"/></picture><!--]--></div> <div class="min-w-0 flex-1 pt-1 lg:pt-2"><h3 class="text-lg leading-tight font-semibold">BEST-SELLERS</h3> <p class="text-sm leading-tight text-gray-600">Livros educativos e divertidos de qualidade.</p></div></div><div class="flex items-start gap-4 lg:gap-4"><div class="flex-shrink-0"><!--[!--><picture><!--[--><!--]--> <img alt="24 ANOS" class="h-16 w-16 object-contain lg:h-16 lg:w-16" decoding="async" height="64" loading="lazy" src="/_app/immutable/assets/3.DM_SDhga.avif" width="64"/></picture><!--]--></div> <div class="min-w-0 flex-1 pt-1 lg:pt-2"><h3 class="text-lg leading-tight font-semibold">24 ANOS</h3> <p class="text-sm leading-tight text-gray-600">Desde 2001 publicando livros.</p></div></div><div class="flex items-start gap-4 lg:gap-4"><div class="flex-shrink-0"><!--[!--><picture><!--[--><!--]--> <img alt="97% DE APROVAÇÃO" class="h-16 w-16 object-contain lg:h-16 lg:w-16" decoding="async" height="64" loading="lazy" src="/_app/immutable/assets/4.CO8_fAn_.avif" width="64"/></picture><!--]--></div> <div class="min-w-0 flex-1 pt-1 lg:pt-2"><h3 class="text-lg leading-tight font-semibold">97% DE APROVAÇÃO</h3> <p class="text-sm leading-tight text-gray-600">4.8 estrelas na Amazon nos últimos 12 meses.</p></div></div><!--]--></div></div><!-- --> <section class="bg-white py-6" id="vantagens"><div class="mb-8 px-4 lg:mb-12"><h2 class="text-center text-4xl lg:text-5xl font-bold mb-4">Como Funciona?</h2> <p class="mx-auto max-w-2xl text-center text-xl">No Clube Fundamento você recebe mensalmente livros de <strong>alta qualidade</strong> para crianças
			de 3 a 14 anos. É mais econômico e mais rápido.</p></div><!-- --> <div class="flex w-full justify-center px-4"></div> <div class="flex w-full justify-center px-4"><a class="mx-auto font-bold rounded-full bg-[#FAE941] px-12 py-2 md:py-4 text-center text-base text-gray-900 hover:bg-[#F8D93A] md:px-16 md:text-lg lg:px-20" href="/#planos">ASSINE JÁ</a></div></section><!-- --> <section class="overflow-hidden bg-white py-4 md:py-20"><div class="mx-auto"><div class="mb-16 text-center"><div class="mb-5 flex flex-col items-center justify-center gap-5 md:flex-row"><!--[!--><picture><!--[--><!--]--> <img alt="Temas dos Livros" class="h-28 w-full object-contain" height="112" src="/_app/immutable/assets/temas-dos-livros.B3PBL6ou.avif" width="672"/></picture><!--]--></div> <p class="text-2xl font-bold text-black md:text-3xl">Seu filho vai aprender valores como:</p></div> <div class="relative w-full"><button aria-label="Anterior" class="custom-prev-btn absolute top-1/2 left-4 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-slate-700 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-slate-600 svelte-4p4cqw"><svg class="h-5 w-5 svelte-4p4cqw" fill="currentColor" viewbox="0 0 20 20"><path clip-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" fill-rule="evenodd"></path></svg></button> <button aria-label="Próximo" class="custom-next-btn absolute top-1/2 right-4 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-slate-700 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-slate-600 svelte-4p4cqw"><svg class="h-5 w-5 svelte-4p4cqw" fill="currentColor" viewbox="0 0 20 20"><path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path></svg></button> <swiper-container autoplay-delay="4000" autoplay-disable-on-interaction="false" breakpoints-1024-slides-per-view="4" breakpoints-1024-space-between="30" breakpoints-1280-slides-per-view="5" breakpoints-1280-space-between="30" breakpoints-1536-slides-per-view="5" breakpoints-1536-space-between="35" breakpoints-320-slides-per-view="1" breakpoints-320-space-between="20" breakpoints-640-slides-per-view="2" breakpoints-640-space-between="25" breakpoints-768-slides-per-view="2.5" breakpoints-768-space-between="30" class="books-swiper" loop="true" navigation="false" pagination="true" pagination-clickable="true" slides-per-view="auto" space-between="30"><!--[--><swiper-slide><div class="flex flex-col items-center px-2 text-center"><div cl

... (Content truncated - file too large)
```

---

##### 📄 combine_project.py
*Path: `combine_project.py`*  
*Size: 13.58 KB*

```py
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to analyze and combine project files into a single markdown document.
Creates a hierarchical view of the project structure and includes contents of readable files.
Useful for project documentation and analysis by LLMs.

Modifications:
- Excludes CSV files from analysis
- Includes images in the markdown output
- Organizes content by type (code, images, data)
"""

import os
import sys
from datetime import datetime
import mimetypes
import base64

class ProjectAnalyzer:
    def __init__(self):
        # Extensions that we consider as readable text files
        self.text_extensions = {
            '.py', '.js', '.html', '.css', '.java', '.c', '.cpp', '.h',
            '.json', '.md', '.txt', '.xml', '.yaml', '.yml', '.ini',
            '.cfg', '.conf', '.jsx', '.tsx', '.ts', '.svg', '.sql',
            '.sh', '.bat', '.ps1', '.env', '.gitignore', '.dockerignore',
            '.rst', '.properties', '.gradle', '.toml', '.lock'
        }
        
        # Image extensions that we want to include
        self.image_extensions = {
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif',
            '.webp', '.svg', '.ico', '.eps'
        }
        
        # Extensions to explicitly exclude (CSV files)
        self.excluded_extensions = {
            '.csv'  # Excluding CSV files as requested
        }
        
        # Files and directories to ignore
        self.ignore_files = {
            '.git', '.idea', '.vscode', '__pycache__', 'node_modules',
            'venv', 'env', '.env', '.DS_Store', 'Thumbs.db'
        }
        
        # Size limits to prevent memory issues
        self.max_file_size_mb = 5  # Maximum file size to read (in MB)
        self.max_total_output_mb = 50  # Maximum total output size (in MB)
        
        # Initialize mimetypes
        mimetypes.init()

    def get_file_size_str(self, file_path):
        """Get human-readable file size."""
        size_bytes = os.path.getsize(file_path)
        
        if size_bytes < 1024:
            return f"{size_bytes} bytes"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.2f} MB"

    def is_excluded_file(self, file_path):
        """Check if file should be excluded (like CSV files)."""
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        return ext in self.excluded_extensions

    def is_text_file(self, file_path):
        """Determine if a file is a text file based on extension and content."""
        # First check if file is excluded
        if self.is_excluded_file(file_path):
            return False
            
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # Check if extension is in our text extensions list
        if ext in self.text_extensions:
            return True
            
        # Try to detect by mime type
        mime_type = mimetypes.guess_type(file_path)[0]
        if mime_type and mime_type.startswith('text/'):
            return True
            
        # Try to read file as text
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1024)  # Try to read first 1KB
                return True
        except:
            return False

    def is_image_file(self, file_path):
        """Check if file is an image."""
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        return ext in self.image_extensions

    def should_ignore(self, path):
        """Check if the path should be ignored."""
        return any(ignore in path for ignore in self.ignore_files)

    def is_file_too_large(self, file_path):
        """Check if file is too large to process."""
        try:
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            return size_mb > self.max_file_size_mb
        except:
            return True

    def generate_tree_structure(self, start_path, output_file):
        """Generate the project tree structure and write to the markdown file."""
        with open(output_file, 'w', encoding='utf-8') as out:
            # Write header
            out.write(f"# 📊 Project Analysis Report\n\n")
            out.write(f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  \n")
            out.write(f"**Root directory:** `{os.path.abspath(start_path)}`  \n")
            out.write(f"**Purpose:** Complete project structure documentation excluding CSV files\n\n")
            
            # Write project summary
            total_files = 0
            text_files = 0
            image_files = 0
            excluded_files = 0
            large_files = 0
            
            for root, dirs, files in os.walk(start_path):
                dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]
                for file in files:
                    file_path = os.path.join(root, file)
                    if self.should_ignore(file_path):
                        continue
                    total_files += 1
                    if self.is_file_too_large(file_path):
                        large_files += 1
                    elif self.is_excluded_file(file_path):
                        excluded_files += 1
                    elif self.is_text_file(file_path):
                        text_files += 1
                    elif self.is_image_file(file_path):
                        image_files += 1
            
            out.write(f"## 📈 Project Summary\n\n")
            out.write(f"- **Total files analyzed:** {total_files}\n")
            out.write(f"- **📄 Code/Text files:** {text_files}\n")
            out.write(f"- **🖼️ Image files:** {image_files}\n")
            out.write(f"- **🚫 Excluded files (CSV):** {excluded_files}\n")
            out.write(f"- **⚠️ Large files (> {self.max_file_size_mb}MB):** {large_files}\n")
            out.write(f"- **📁 Other files:** {total_files - text_files - image_files - excluded_files - large_files}\n\n")
            
            # Write project tree structure
            out.write("## 🌳 Project Structure\n\n")
            out.write("```\n")
            self.write_tree(start_path, out)
            out.write("```\n\n")
            
            # Write file contents
            out.write("## 📋 Project Contents\n\n")
            self.write_file_contents(start_path, out)

    def write_tree(self, start_path, out, prefix=""):
        """Write the tree structure of the project."""
        items = sorted(os.listdir(start_path))
        
        for i, item in enumerate(items):
            path = os.path.join(start_path, item)
            
            if self.should_ignore(path):
                continue
                
            is_last = i == len(items) - 1
            
            # Add file type indicators
            if os.path.isfile(path):
                if self.is_excluded_file(path):
                    item_display = f"{item} (CSV - excluded)"
                elif self.is_image_file(path):
                    item_display = f"{item} 🖼️"
                elif self.is_text_file(path):
                    item_display = f"{item} 📄"
                else:
                    item_display = f"{item} 📁"
            else:
                item_display = f"{item} 📁"
                
            out.write(f"{prefix}{'└── ' if is_last else '├── '}{item_display}\n")
            
            if os.path.isdir(path):
                self.write_tree(path, out, prefix + ('    ' if is_last else '│   '))

    def write_file_contents(self, start_path, out, current_path=""):
        """Write the contents of all readable files and images."""
        # Collect files by type
        text_files = []
        image_files = []
        excluded_files = []
        large_files = []  # Files that are too large to process
        
        for root, dirs, files in os.walk(start_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]
            
            for file in files:
                file_path = os.path.join(root, file)
                
                if self.should_ignore(file_path):
                    continue
                
                # Check file size first
                if self.is_file_too_large(file_path):
                    large_files.append(file_path)
                    continue
                
                if self.is_excluded_file(file_path):
                    excluded_files.append(file_path)
                elif self.is_text_file(file_path):
                    text_files.append(file_path)
                elif self.is_image_file(file_path):
                    image_files.append(file_path)
        
        # Write large files summary
        if large_files:
            out.write(f"### ⚠️ Large Files (> {self.max_file_size_mb}MB)\n\n")
            out.write(f"Total large files skipped: {len(large_files)}\n\n")
            for file_path in sorted(large_files):
                rel_path = os.path.relpath(file_path, start_path)
                file_size = self.get_file_size_str(file_path)
                out.write(f"- `{rel_path}` ({file_size})\n")
            out.write("\n---\n\n")
        
        # Write excluded files summary
        if excluded_files:
            out.write(f"### 🚫 Excluded Files (CSV)\n\n")
            out.write(f"Total excluded files: {len(excluded_files)}\n\n")
            for file_path in sorted(excluded_files):
                rel_path = os.path.relpath(file_path, start_path)
                file_size = self.get_file_size_str(file_path)
                out.write(f"- `{rel_path}` ({file_size})\n")
            out.write("\n---\n\n")
        
        # Write images section
        if image_files:
            out.write(f"### 🖼️ Project Images\n\n")
            for file_path in sorted(image_files):
                rel_path = os.path.relpath(file_path, s

... (Content truncated - file too large)
```

---

##### 📄 compilado sites .md
*Path: `compilado sites .md`*  
*Size: 34.43 KB*

```md
# 📊 Project Analysis Report

**Generated on:** 2025-09-12 15:42:52  
**Root directory:** `C:\Users\<USER>\OneDrive\Área de Trabalho\IDEIAS\99 - RESOURCES\1000 - ZONA DESENVOLVIMENTO\WEB SCRAPRING\enhanced_multi_sites_results`  
**Purpose:** Complete project structure documentation excluding CSV files

## 📈 Project Summary

- **Total files analyzed:** 16
- **📄 Code/Text files:** 16
- **🖼️ Image files:** 0
- **🚫 Excluded files (CSV):** 0
- **⚠️ Large files (> 5MB):** 0
- **📁 Other files:** 0

## 🌳 Project Structure

```
├── clubefundamento 📁
│   ├── clubefundamento_enhanced_analysis.md 📄
│   ├── enhanced_data.json 📄
│   └── html 📁
│       └── original.html 📄
├── combine_project.py 📄
├── compilado sites .md 📄
├── dentrodahistoria 📁
│   ├── dentrodahistoria_enhanced_analysis.md 📄
│   ├── enhanced_data.json 📄
│   └── html 📁
│       └── original.html 📄
├── enhanced_consolidated_data.json 📄
├── enhanced_multi_sites_analysis.md 📄
├── meutibi 📁
│   ├── enhanced_data.json 📄
│   ├── html 📁
│   │   └── original.html 📄
│   └── meutibi_enhanced_analysis.md 📄
└── storyspark 📁
    ├── enhanced_data.json 📄
    ├── html 📁
    │   └── original.html 📄
    └── storyspark_enhanced_analysis.md 📄
```

## 📋 Project Contents

### 📄 Code and Text Files


#### 📁 Directory: clubefundamento

##### 📄 clubefundamento_enhanced_analysis.md
*Path: `clubefundamento\clubefundamento_enhanced_analysis.md`*  
*Size: 2.09 KB*

```md
# 🚀 Análise Avançada - Clube Fundamento

> **Web Scraping Avançado Executado com Sucesso**
> Site: https://www.clubefundamento.com.br/
> Data: 2025-09-12T15:37:22.148358
> Parser: parse_clubefundamento

## 📊 **Resumo Executivo**

O **Clube Fundamento** é uma plataforma de **clube_livros** construída com **svelte**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: svelte
- **Tipo**: clube_livros
- **Status**: 200
- **Tamanho**: 108140 bytes

### **Tecnologias Detectadas**
- **Frameworks**: Svelte
- **Server**: cloudflare


## 🎯 **Informações Básicas**

- **Título**: Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil
- **Descrição**: Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.
- **Palavras-chave**: clube de livros, livros infantis, educação, crianças, leitura

## 📦 **Produtos Encontrados**

Nenhum produto específico encontrado com os seletores utilizados.

## 💰 **Análise de Preços**

Nenhum preço específico encontrado.


## 🔍 **Estrutura de Headings**
- **H2**: 9 elementos
  - Como Funciona?...
  - Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária....
  - PLANO COMPLETO...
- **H3**: 20 elementos
  - +12 MILHÕES...
  - BEST-SELLERS...
  - 24 ANOS...
- **H4**: 1 elementos
  - Como funciona?...


## 🎉 **Conclusão**

A análise avançada do **Clube Fundamento** revelou:

- ✅ **Produtos identificados**: 0
- ✅ **Preços extraídos**: 0
- ✅ **Tecnologia**: svelte bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `clubefundamento/enhanced_data.json` - Dados estruturados avançados
- `clubefundamento/html/original.html` - HTML original
- `clubefundamento_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
```

---

##### 📄 enhanced_data.json
*Path: `clubefundamento\enhanced_data.json`*  
*Size: 26.29 KB*

```json
{
  "url": "https://www.clubefundamento.com.br/",
  "name": "Clube Fundamento",
  "type": "clube_livros",
  "framework": "svelte",
  "scraped_at": "2025-09-12T15:37:22.148358",
  "status_code": 200,
  "content_length": 108140,
  "title": "Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil",
  "meta_description": "Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.",
  "meta_keywords": "clube de livros, livros infantis, educação, crianças, leitura",
  "headings": {
    "h1": [],
    "h2": [
      "Como Funciona?",
      "Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária.",
      "PLANO COMPLETO",
      "PLANO DUPLO",
      "PLANO SIMPLES",
      "Kit Clube Expresso.",
      "O que vem  no kit?",
      "Depoimentos",
      "Avaliações dos Pais"
    ],
    "h3": [
      "+12 MILHÕES",
      "BEST-SELLERS",
      "24 ANOS",
      "97% DE APROVAÇÃO",
      "JÁ TENHO LIVROS DA FUNDAMENTO. E AGORA?",
      "Receba tudo de uma vez só.",
      "1.  \n\t\t\t\t\t\t\t\tBest-sellers nacionais e internacionais",
      "2.  \n\t\t\t\t\t\t\t\tPassaporte do Leitor",
      "3.  \n\t\t\t\t\t\t\t\tBrindes",
      "Vanessa",
      "Márcia Cristina",
      "Sheila",
      "Luciana",
      "Thais",
      "Daiana",
      "Antonia",
      "Cidiane",
      "Arlete",
      "Sobre",
      "Redes Sociais"
    ],
    "h4": [
      "Como funciona?"
    ],
    "h5": [],
    "h6": []
  },
  "links": [
    {
      "url": "https://www.clubefundamento.com.br/",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.clubefundamento.com.br/#planos",
      "text": "Planos",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://wix.clubefundamento.com.br/amostra",
      "text": "Amostra",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://wix.clubefundamento.com.br/blog",
      "text": "Blog",
      "title": "",
      "classes": [
        "text-white",
        "transition-colors",
        "hover:text-yellow-300"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br",
      "text": "SOU ASSINANTE",
      "title": "",
      "classes": [
        "rounded-full",
        "bg-[#21A26C]",
        "px-4",
        "py-2",
        "font-bold",
        "text-white",
        "transition-colors",
        "hover:bg-green-700"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "rounded-full",
        "bg-[#FAE941]",
        "px-6",
        "py-2",
        "font-bold",
        "text-black",
        "transition-colors",
        "hover:bg-yellow-300"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.clubefundamento.com.br/#planos",
      "text": "ASSINE JÁ",
      "title": "",
      "classes": [
        "mx-auto",
        "font-bold",
        "rounded-full",
        "bg-[#FAE941]",
        "px-12",
        "py-2",
        "md:py-4",
        "text-center",
        "text-base",
        "text-gray-900",
        "hover:bg-[#F8D93A]",
        "md:px-16",
        "md:text-lg",
        "lg:px-20"
      ]
    },
    {
      "url": "https://www.clubefundamento.com.br/amostra",
      "text": "AMOSTRA",
      "title": "",
      "classes": [
        "mx-auto",
        "rounded-full",
        "bg-[#FAE941]",
        "px-12",
        "py-2",
        "text-center",
        "text-base",
        "font-bold",
        "text-gray-900",
        "hover:bg-[#F8D93A]",
        "md:px-16",
        "md:py-4",
        "md:text-lg",
        "lg:px-20"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-duplo-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://assinar.clubefundamento.com.br/checkout/plano-simples-anual",
      "text": "COMPRAR",
      "title": "",
      "classes": [
        "inline-block",
        "cursor-pointer",
        "rounded-full",
        "border-2",
        "border-white/80",
        "bg-yellow-400",
        "px-8",
        "py-3",
        "text-base",
        "font-bold",
        "tracking-wide",
        "text-gray-800",
        "uppercase",
        "no-underline",
        "shadow-lg",
        "transition-all",
        "duration-300",
        "hover:-translate-y-1",
        "hover:border-white",
        "hover:bg-yellow-300",
        "hover:shadow-xl",
        "active:translate-y-0",
        "md:px-6",
        "md:py-2.5",
        "md:text-sm"
      ]
    },
    {
      "url": "https://www.lojafundamento.com.br/clube-fundamento/kits-clube-fundamento/",
      "text": "Compre Aqui",
      "title": "

... (Content truncated - file too large)
```

---


#### 📁 Directory: dentrodahistoria

##### 📄 dentrodahistoria_enhanced_analysis.md
*Path: `dentrodahistoria\dentrodahistoria_enhanced_analysis.md`*  
*Size: 1.82 KB*

```md
# 🚀 Análise Avançada - Clube Dentro da História

> **Web Scraping Avançado Executado com Sucesso**
> Site: https://www.dentrodahistoria.com.br/clube/
> Data: 2025-09-12T15:37:19.821501
> Parser: parse_dentrodahistoria

## 📊 **Resumo Executivo**

O **Clube Dentro da História** é uma plataforma de **livros_personalizados** construída com **nuxt**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: nuxt
- **Tipo**: livros_personalizados
- **Status**: 200
- **Tamanho**: 69142 bytes

### **Tecnologias Detectadas**
- **Analytics**: Google Tag Manager
- **Server**: nginx


## 🎯 **Informações Básicas**

- **Título**: Clube Dentro da História | Assinatura Livros Personalizados
- **Descrição**: Todo mês, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido.
- **Palavras-chave**: 

## 📦 **Produtos Encontrados**

Nenhum produto específico encontrado com os seletores utilizados.

## 💰 **Análise de Preços**

Nenhum preço específico encontrado.


## 🔍 **Estrutura de Headings**


## 🎉 **Conclusão**

A análise avançada do **Clube Dentro da História** revelou:

- ✅ **Produtos identificados**: 0
- ✅ **Preços extraídos**: 0
- ✅ **Tecnologia**: nuxt bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `dentrodahistoria/enhanced_data.json` - Dados estruturados avançados
- `dentrodahistoria/html/original.html` - HTML original
- `dentrodahistoria_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
```

---

##### 📄 enhanced_data.json
*Path: `dentrodahistoria\enhanced_data.json`*  
*Size: 1.17 KB*

```json
{
  "url": "https://www.dentrodahistoria.com.br/clube/",
  "name": "Clube Dentro da História",
  "type": "livros_personalizados",
  "framework": "nuxt",
  "scraped_at": "2025-09-12T15:37:19.821501",
  "status_code": 200,
  "content_length": 69142,
  "title": "Clube Dentro da História | Assinatura Livros Personalizados",
  "meta_description": "Todo mês, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido.",
  "meta_keywords": "",
  "headings": {
    "h1": [],
    "h2": [],
    "h3": [],
    "h4": [],
    "h5": [],
    "h6": []
  },
  "links": [],
  "images": [],
  "technology_stack": {
    "frameworks": [],
    "libraries": [],
    "analytics": [
      "Google Tag Manager"
    ],
    "other": [],
    "server": "nginx"
  },
  "products": [],
  "prices": [],
  "features": [
    {
      "keyword": "personalizado",
      "mentions": 1
    },
    {
      "keyword": "história",
      "mentions": 1
    },
    {
      "keyword": "livro",
      "mentions": 1
    }
  ],
  "subscription_info": {
    "assinatura": 1
  },
  "target_audience": []
}
```

---


#### 📁 Directory: dentrodahistoria\html

##### 📄 original.html
*Path: `dentrodahistoria\html\original.html`*  
*Size: 67.61 KB*

```html
<!DOCTYPE html>

<html data-n-head="%7B%22lang%22:%7B%22ssr%22:%22en%22%7D%7D" data-n-head-ssr="" lang="en">
<head>
<meta charset="utf-8" data-n-head="ssr"/><meta content="width=device-width,initial-scale=1" data-n-head="ssr" name="viewport"/><meta content="Todo mês, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido." data-hid="description" data-n-head="ssr" itemprop="description" name="description"/><meta content="Clube Dentro da História | Assinatura Livros Personalizados" data-hid="twitterTitle" data-n-head="ssr" name="twitter:title"/><meta content="Todo mês, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido." data-hid="twitterDescription" data-n-head="ssr" name="twitter:description"/><meta content="Clube Dentro da História | Assinatura Livros Personalizados" data-hid="ogTitle" data-n-head="ssr" property="og:title"/><meta content="https://www.dentrodahistoria.com.br/clube/OG-clube-Hagens.png" data-hid="ogImage" data-n-head="ssr" property="og:image"/><meta content="https://www.dentrodahistoria.com.br/clube/" data-hid="ogUrl" data-n-head="ssr" property="og:url"/><meta content="Todo mês, receba em casa incríveis livros infantis personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Clube de assinatura educativo e muito divertido." data-hid="ogDescription" data-n-head="ssr" property="og:description"/><meta content="Dentro da História" data-hid="ogSiteName" data-n-head="ssr" property="og:site_name"/><meta content="#3d0171" data-n-head="ssr" name="theme-color"/><meta content="#3d0171" data-n-head="ssr" name="apple-mobile-web-app-status-bar-style"/><meta content="1866126726955656" data-hid="fbAdmins" data-n-head="ssr" property="fb:admins"/><title>Clube Dentro da História | Assinatura Livros Personalizados</title><base href="/clube/"/><link data-n-head="ssr" href="/favicon.ico" rel="icon" type="image/x-icon"/><link data-n-head="ssr" href="/favicon.ico" rel="icon" type="image/x-icon"/><script async="" data-n-head="ssr" src="https://www.googletagmanager.com/gtag/js?id=UA-80360102-4"></script><script data-hid="gtm-script" data-n-head="ssr">window._gtm_init||(window._gtm_init=1,function(t,e,n,a,o){t[n]=1==t[n]||"yes"==e[n]||1==e[n]||1==e.msDoNotTrack||t[a]&&t[a][o]&&t[a][o]()?1:0}(window,navigator,"doNotTrack","external","msTrackingProtectionEnabled"),function(a,o,i,g,m){a[m]={},a._gtm_inject=function(t){var e,n;a.doNotTrack||a[m][t]||(a[m][t]=1,a[g]=a[g]||[],a[g].push({"gtm.start":(new Date).getTime(),event:"gtm.js"}),e=o.getElementsByTagName(i)[0],(n=o.createElement(i)).async=!0,n.src="https://www.googletagmanager.com/gtm.js?id="+t,e.parentNode.insertBefore(n,e))},a._gtm_inject("GTM-P2C232T")}(window,document,"script","dataLayer","_gtm_ids"))</script><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><link as="script" href="https://cdn.dentrodahistoria.com.br/clube/home/<USER>" rel="preload"/><style data-vue-ssr-id="2c6794ac:0 2c6794ac:1 2c6794ac:2 49569972:0 c16a4f18:0 c16a4f18:1 c16a4f18:2 1997a3c0:0 1997a3c0:1 1997a3c0:2">@import url(https://fonts.googleapis.com/css?family=Nunito:400,600,700,900&display=swap);@import url(https://fonts.googleapis.com/css?family=Roboto:400,600,700,900&display=swap);:root{--column-width:6.6666666667vw}@media(min-width:0px){:root{--column-width:6.6666666667vw}}@media(min-width:480px){:root{--column-width:2.7777777778vw}}@media(min-width:0px){html{font-size:2.9166666667vw}}@media(min-width:480px){html{font-size:14px}}@media(min-width:1024px){:root{--column-width:2.7777777778vw}}@media(min-width:1920px){:root{--column-width:2.7777777778vw}}@media(min-width:2620px){:root{--column-width:2.7777777778vw}}@media(min-width:1024px){html{font-size:1.5648854962vw}}@media(min-width:2620px){html{font-size:1.56vw}}*,:after,:before,html{box-sizing:border-box}.async-hide{opacity:0!important}*{-webkit-font-smoothing:antialiased}body{background-color:#f8f7fc;background-color:#8e4dd4;font-family:Roboto,sans-serif}img{aspect-ratio:attr(width)/attr(height)}.content{padding-left:11.1111111111vw;padding-right:11.1111111111vw}@media(max-width:479px){.content{padding-left:0;padding-right:0}}.text-center{text-align:center}.nuxt-progress{background-color:#000;height:2px;left:0;opacity:1;position:fixed;right:0;top:0;transition:width .1s,opacity .4s;width:0;z-index:999999}.nuxt-progress.nuxt-progress-notransition{transition:none}.nuxt-progress-failed{background-color:red}[data-v-64ccb031]:root{--column-width:6.6666666667vw}@media(min-width:0px){[data-v-64ccb031]:root{--column-width:6.6666666667vw}}@media(min-width:480px){[data-v-64ccb031]:root{--column-width:2.7777777778vw}}@media(min-width:0px){html[data-v-64ccb031]{font-size:2.9166666667vw}}@media(min-width:480px){html[data-v-64ccb031]{font-size:14px}}@media(min-width:1024px){[data-v-64ccb031]:root{--column-width:2.7777777778vw}}@media(min-width:1920px){[data-v-64ccb031]:root{--column-width:2.7777777778vw}}@media(min-width:2620px){[data-v-64ccb031]:root{--column-width:2.7777777778vw}}@media(min-width:1024px){html[data-v-64ccb031]{font-size:1.5648854962vw}}@media(min-width:2620px){html[data-v-64ccb031]{font-size:1.56vw}}[data-v-64ccb031],[data-v-64ccb031]:after,[data-v-64ccb031]:before,html[data-v-64ccb031]{box-sizing:border-box}.async-hide[data-v-64ccb031]{opacity:0!important}[data-v-64ccb031]{-webkit-font-smoothing:antialiased}body[data-v-64ccb031]{background-color:#f8f7fc;background-color:#8e4dd4;font-family:Roboto,sans-serif}img[data-v-64ccb031]{aspect-ratio:attr(width)/attr(height)}.content[data-v-64ccb031]{padding-left:11.1111111111vw;padding-right:11.1111111111vw}@media(max-width:479px){.content[data-v-64ccb031]{padding-left:0;padding-right:0}}.text-center[data-v-64ccb031]{text-align:center}[data-v-6b4937ac]:root{--column-width:6.6666666667vw}@media(min-width:0px){[data-v-6b4937ac]:root{--column-width:6.6666666667vw}}@media(min-width:480px){[data-v-6b4937ac]:root{--column-width:2.7777777778vw}}@media(min-width:0px){html[data-v-6b4937ac]{font-size:2.9166666667vw}}@media(min-width:480px){html[data-v-6b4937ac]{font-size:14px}}@media(min-width:1024px){[data-v-6b4937ac]:root{--column-width:2.7777777778vw}}@media(min-width:1920px){[data-v-6b4937ac]:root{--column-width:2.7777777778vw}}@media(min-width:2620px){[data-v-6b4937ac]:root{--column-width:2.7777777778vw}}@media(min-width:1024px){html[data-v-6b4937ac]{font-size:1.5648854962vw}}@media(min-width:2620px){html[data-v-6b4937ac]{font-size:1.56vw}}[data-v-6b4937ac],[data-v-6b4937ac]:after,[data-v-6b4937ac]:before,html[data-v-6b4937ac]{box-sizing:border-box}.async-hide[data-v-6b4937ac]{opacity:0!important}[data-v-6b4937ac]{-webkit-font-smoothing:antialiased}body[data-v-6b4937ac]{background-color:#f8f7fc;background-color:#8e4dd4;font-family:Roboto,sans-serif}img[data-v-6b4937ac]{aspect-ratio:attr(width)/attr(height)}.content[data-v-6b4937ac]{padding-left:11.1111111111vw;padding-right:11.1111111111vw}@media(max-width:479px){.content[data-v-6b4937ac]{padding-left:0;padding-right:0}}.text-center[data-v-6b4937ac]{text-align:center}.trigger-modal[data-v-6b4937ac]{display:none}@media(max-width:479px){.trigger-modal[data-v-6b4937ac]{border:1px solid #fff;border-radius:4px;display:block;margin-left:1.4953271028rem;margin-right:1.4953271028rem;margin-top:1.4953271028rem;padding-bottom:.7476635514rem;padding-top:.7476635514rem;text-align:center}.span-trigger-modal[data-v-6b4937ac]{color:#fece02;font-weight:700}.text-trigger-modal[data-v-6b4937ac]{color:#fff;font-family:Roboto;font-size:12px;font-style:normal;font-weight:400;line-height:18px}}.hero-area[data-v-6b4937ac]{background:#923fda;display:flex;padding-top:3.0476190476rem}@media(max-width:479px){.hero-area[data-v-6b4937ac]{flex-direction:column;padding-top:2.9906542056rem;position:relative;width:100vw}.hero-area .rounded-video[data-v-6b4937ac]{border-radius:50%;margin:0 auto 2.9906542056rem;overflow:hidden;width:24.4859813084rem}.hero-area .rounded-video video[data-v-6b4937ac]{border-radius:50%;width:100%}}.hero-area .content-area[data-v-6b4937ac]{width:33.3333333333vw}.hero-area .content-area .heading[data-v-6b4937ac]{line-height:1.25;margin-bottom:.380952381rem}@media(max-width:479px){.hero-area .content-area[data-v-6b4937ac]{width:100%}.hero-area .content-area[data-v-6b4937ac] .heading{color:#fff;font-family:Nunito;font-size:2.6168224299rem;font-style:normal;font-weight:900;margin:0 auto .7476635514rem;width:86.6666666667vw}.hero-area .content-area[data-v-6b4937ac] .paragraph{margin:0 auto;width:86.6666666667vw}}@media(max-width:479px)and (max-width:479px){.hero-area .content-area[data-v-6b4937ac] .paragraph{margin-top:42px}}.hero-area .box-first-step[data-v-6b4937ac]{padding-top:1.9047619048rem;position:relative}@media(max-width:479px){.hero-area .box-first-step[data-v-6b4937ac]{background:#8339c4;border-radius:24px;box-shadow:0 16px 32px rgba(80,16,136,.8);margin-top:3.738317757rem;padding:2.9906542056rem 6.6666666667vw}}.hero-area .box-first-step .input-name-group[data-v-6b4937ac]{position:relative}.hero-area .box-first-

... (Content truncated - file too large)
```

---

##### 📄 enhanced_consolidated_data.json
*Path: `enhanced_consolidated_data.json`*  
*Size: 69.33 KB*

```json
{
  "dentrodahistoria": {
    "url": "https://www.dentrodahistoria.com.br/clube/",
    "name": "Clube Dentro da História",
    "type": "livros_personalizados",
    "framework": "nuxt",
    "scraped_at": "2025-09-12T15:37:19.821501",
    "status_code": 200,
    "content_length": 69142,
    "title": "Clube Dentro da História | Assinatura Livros Personalizados",
    "meta_description": "Todo mês, receba em casa incríveis livros personalizados com seu filho como protagonista ao lado dos seus personagens favoritos. Educativo e muito divertido.",
    "meta_keywords": "",
    "headings": {
      "h1": [],
      "h2": [],
      "h3": [],
      "h4": [],
      "h5": [],
      "h6": []
    },
    "links": [],
    "images": [],
    "technology_stack": {
      "frameworks": [],
      "libraries": [],
      "analytics": [
        "Google Tag Manager"
      ],
      "other": [],
      "server": "nginx"
    },
    "products": [],
    "prices": [],
    "features": [
      {
        "keyword": "personalizado",
        "mentions": 1
      },
      {
        "keyword": "história",
        "mentions": 1
      },
      {
        "keyword": "livro",
        "mentions": 1
      }
    ],
    "subscription_info": {
      "assinatura": 1
    },
    "target_audience": []
  },
  "clubefundamento": {
    "url": "https://www.clubefundamento.com.br/",
    "name": "Clube Fundamento",
    "type": "clube_livros",
    "framework": "svelte",
    "scraped_at": "2025-09-12T15:37:22.148358",
    "status_code": 200,
    "content_length": 108140,
    "title": "Clube Fundamento - O Mais Completo Clube de Livros Infantis do Brasil",
    "meta_description": "Livros educativos e divertidos entregues mensalmente na sua casa. Para crianças de 3 a 14 anos. Mais de 10 milhões de livros vendidos.",
    "meta_keywords": "clube de livros, livros infantis, educação, crianças, leitura",
    "headings": {
      "h1": [],
      "h2": [
        "Como Funciona?",
        "Confira exemplos de livros que seu filho pode receber, de acordo com a faixa etária.",
        "PLANO COMPLETO",
        "PLANO DUPLO",
        "PLANO SIMPLES",
        "Kit Clube Expresso.",
        "O que vem  no kit?",
        "Depoimentos",
        "Avaliações dos Pais"
      ],
      "h3": [
        "+12 MILHÕES",
        "BEST-SELLERS",
        "24 ANOS",
        "97% DE APROVAÇÃO",
        "JÁ TENHO LIVROS DA FUNDAMENTO. E AGORA?",
        "Receba tudo de uma vez só.",
        "1.  \n\t\t\t\t\t\t\t\tBest-sellers nacionais e internacionais",
        "2.  \n\t\t\t\t\t\t\t\tPassaporte do Leitor",
        "3.  \n\t\t\t\t\t\t\t\tBrindes",
        "Vanessa",
        "Márcia Cristina",
        "Sheila",
        "Luciana",
        "Thais",
        "Daiana",
        "Antonia",
        "Cidiane",
        "Arlete",
        "Sobre",
        "Redes Sociais"
      ],
      "h4": [
        "Como funciona?"
      ],
      "h5": [],
      "h6": []
    },
    "links": [
      {
        "url": "https://www.clubefundamento.com.br/",
        "text": "",
        "title": "",
        "classes": []
      },
      {
        "url": "https://www.clubefundamento.com.br/#planos",
        "text": "Planos",
        "title": "",
        "classes": [
          "text-white",
          "transition-colors",
          "hover:text-yellow-300"
        ]
      },
      {
        "url": "https://wix.clubefundamento.com.br/amostra",
        "text": "Amostra",
        "title": "",
        "classes": [
          "text-white",
          "transition-colors",
          "hover:text-yellow-300"
        ]
      },
      {
        "url": "https://wix.clubefundamento.com.br/blog",
        "text": "Blog",
        "title": "",
        "classes": [
          "text-white",
          "transition-colors",
          "hover:text-yellow-300"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br",
        "text": "SOU ASSINANTE",
        "title": "",
        "classes": [
          "rounded-full",
          "bg-[#21A26C]",
          "px-4",
          "py-2",
          "font-bold",
          "text-white",
          "transition-colors",
          "hover:bg-green-700"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
        "text": "COMPRAR",
        "title": "",
        "classes": [
          "rounded-full",
          "bg-[#FAE941]",
          "px-6",
          "py-2",
          "font-bold",
          "text-black",
          "transition-colors",
          "hover:bg-yellow-300"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
        "text": "",
        "title": "",
        "classes": []
      },
      {
        "url": "https://www.clubefundamento.com.br/#planos",
        "text": "ASSINE JÁ",
        "title": "",
        "classes": [
          "mx-auto",
          "font-bold",
          "rounded-full",
          "bg-[#FAE941]",
          "px-12",
          "py-2",
          "md:py-4",
          "text-center",
          "text-base",
          "text-gray-900",
          "hover:bg-[#F8D93A]",
          "md:px-16",
          "md:text-lg",
          "lg:px-20"
        ]
      },
      {
        "url": "https://www.clubefundamento.com.br/amostra",
        "text": "AMOSTRA",
        "title": "",
        "classes": [
          "mx-auto",
          "rounded-full",
          "bg-[#FAE941]",
          "px-12",
          "py-2",
          "text-center",
          "text-base",
          "font-bold",
          "text-gray-900",
          "hover:bg-[#F8D93A]",
          "md:px-16",
          "md:py-4",
          "md:text-lg",
          "lg:px-20"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br/checkout/plano-completo-anual",
        "text": "COMPRAR",
        "title": "",
        "classes": [
          "inline-block",
          "cursor-pointer",
          "rounded-full",
          "border-2",
          "border-white/80",
          "bg-yellow-400",
          "px-8",
          "py-3",
          "text-base",
          "font-bold",
          "tracking-wide",
          "text-gray-800",
          "uppercase",
          "no-underline",
          "shadow-lg",
          "transition-all",
          "duration-300",
          "hover:-translate-y-1",
          "hover:border-white",
          "hover:bg-yellow-300",
          "hover:shadow-xl",
          "active:translate-y-0",
          "md:px-6",
          "md:py-2.5",
          "md:text-sm"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br/checkout/plano-duplo-anual",
        "text": "COMPRAR",
        "title": "",
        "classes": [
          "inline-block",
          "cursor-pointer",
          "rounded-full",
          "border-2",
          "border-white/80",
          "bg-yellow-400",
          "px-8",
          "py-3",
          "text-base",
          "font-bold",
          "tracking-wide",
          "text-gray-800",
          "uppercase",
          "no-underline",
          "shadow-lg",
          "transition-all",
          "duration-300",
          "hover:-translate-y-1",
          "hover:border-white",
          "hover:bg-yellow-300",
          "hover:shadow-xl",
          "active:translate-y-0",
          "md:px-6",
          "md:py-2.5",
          "md:text-sm"
        ]
      },
      {
        "url": "https://assinar.clubefundamento.com.br/checkout/plano-simples-anual",
        "text": "COMPRAR",
        "title": "",
        "classes": [
          "inline-block",
          "cursor-pointer",
          "rounded-full",
          "border-2",
          "border-white/80",
          "bg-yellow-400",
          "px-8",
          "py-3",
          "text-base",
          "font-bold",
          "tracking-wide",
          "text-gray-800",
          "uppercase",
          "no-underline",
          "shadow-lg",
          "transition-all",
          "duration-300",
          "hover:-translate-y-1",
          "hover:border-white",
          "hover:bg-yellow-300",
          "hover:shadow-xl",
          "active:translate-y-0",
          "md:px-6",
          "md:py-2.5",
          "md:text-sm"
        ]
      },
      {
        "url": "https://www.lojafundamento.com.br/clube-fundamento/kits-clube-fundamento/",
        "text": "Compre Aqui",
        "title": "",
        "classes": [
          "inline-block",
          "rounded-lg",
          "bg-yellow-400",
          "px-8",
          "py-4",
          "text-lg",
          "font-bold",
          "text-black",
          "shadow-lg",
          "transition-colors",
          "duration-200",
          "hover:bg-yellow-500",
          "hover:shadow-xl"
        ]
      },
      {
        "url": "https://www.clubefundamento.com.br/sobre-o-kit",
        "text": "Mais detalhes sobre o kit",
        "title": "",
        "classes": [
          "transform",
          "cursor-pointer",
          "rounded-full",
          "bg-[#FAE941]",
          "px-8",
          "py-4",
          "text-sm",
          "font-bold",
          "tracking-wide",
          "text-gray-900",
          "uppercase",
          "shadow-lg",
          "transition-colors",
          "duration-200",
          "hover:bg-yellow-400",
          "hover:shadow-xl",
          "md:text-lg"
        ]
      },
      {
        "url": "https://www.instagram.com/fundamentoeditora/",
        "text": "As histórias na hora de dormir ficaram ainda melhores ❤️✨",
        "title": "",
        "classes": [
          "group",
          "block",
          "overflow-hidden",
          "rounded-2xl",
          "bg-white",
          "shadow-sm",
          "transition-all",
          "duration-300",
          "hover:shadow-lg"
        ]
      },
      {
        "url": "https://www.instagram.com/fundamentoeditora/",
        "text": "Já vou avisando que o livro é ótimo 😍😍",
        "title": "",
        "classes": [
          "group",
          "block",
          "overflow-hidden",
          "rounded-2xl",
          "bg-white",
          "sh

... (Content truncated - file too large)
```

---

##### 📄 enhanced_multi_sites_analysis.md
*Path: `enhanced_multi_sites_analysis.md`*  
*Size: 4.59 KB*

```md
# 🚀 Análise Consolidada Avançada - Multi-Sites Scraper

> **Web Scraping Avançado de Múltiplos Sites Executado com Sucesso**
> Data: 2025-09-12 15:37:30
> Versão: Enhanced Multi-Sites Scraper v2.0

## 📊 **Resumo Executivo**

Este relatório apresenta a análise consolidada avançada de **4 sites** do segmento de livros infantis e criação de histórias, com parsers específicos para cada plataforma:


### 🚀 **Clube Dentro da História**
- **URL**: https://www.dentrodahistoria.com.br/clube/
- **Tipo**: livros_personalizados
- **Framework**: nuxt
- **Parser**: parse_dentrodahistoria
- **Status**: ✅ Sucesso (200)
- **Produtos**: 0 encontrados
- **Preços**: 0 extraídos
- **Conteúdo**: 69142 bytes

### 🚀 **Clube Fundamento**
- **URL**: https://www.clubefundamento.com.br/
- **Tipo**: clube_livros
- **Framework**: svelte
- **Parser**: parse_clubefundamento
- **Status**: ✅ Sucesso (200)
- **Produtos**: 0 encontrados
- **Preços**: 0 extraídos
- **Conteúdo**: 108140 bytes

### 🚀 **Tibi - Livros Infantis**
- **URL**: https://meutibi.com.br/
- **Tipo**: livros_infantis
- **Framework**: aspnet
- **Parser**: parse_meutibi
- **Status**: ✅ Sucesso (200)
- **Produtos**: 17 encontrados
- **Preços**: 0 extraídos
- **Conteúdo**: 96728 bytes

### 🚀 **Story Spark - Criação de Histórias**
- **URL**: https://storyspark.ai/pt
- **Tipo**: criacao_historias
- **Framework**: nextjs
- **Parser**: parse_storyspark
- **Status**: ✅ Sucesso (200)
- **Produtos**: 0 encontrados
- **Preços**: 0 extraídos
- **Conteúdo**: 404454 bytes


## 🏗️ **Comparativo Avançado de Tecnologias**

| Site | Framework | Parser | Produtos | Preços | Tamanho (KB) |
|------|-----------|--------|----------|--------|--------------|
| Clube Dentro da História | nuxt | parse_dentrodahistoria | 0 | 0 | 67.5 |
| Clube Fundamento | svelte | parse_clubefundamento | 0 | 0 | 105.6 |
| Tibi - Livros Infantis | aspnet | parse_meutibi | 17 | 0 | 94.5 |
| Story Spark - Criação de Histórias | nextjs | parse_storyspark | 0 | 0 | 395.0 |


## 🎯 **Insights Avançados**

### **Análise de Frameworks**
- **Nuxt**: 1 site(s)
- **Svelte**: 1 site(s)
- **Aspnet**: 1 site(s)
- **Nextjs**: 1 site(s)


### **Análise de Produtos e Preços**
- **Total de Produtos Extraídos**: 17
- **Total de Preços Encontrados**: 0


### **Análise por Tipo de Negócio**
- **Livros Personalizados**: 1 site(s), 0 produtos, 0 preços
- **Clube Livros**: 1 site(s), 0 produtos, 0 preços
- **Livros Infantis**: 1 site(s), 17 produtos, 0 preços
- **Criacao Historias**: 1 site(s), 0 produtos, 0 preços


## 📁 **Estrutura de Arquivos Gerados**

```
enhanced_multi_sites_results/
├── 📊 enhanced_multi_sites_analysis.md   # Este relatório
├── 📄 enhanced_consolidated_data.json    # Dados consolidados avançados
├── 📁 dentrodahistoria/
│   ├── 📄 enhanced_data.json             # Dados estruturados avançados
│   ├── 📄 dentrodahistoria_enhanced_analysis.md # Relatório individual avançado
│   └── 📁 html/
│       └── 📄 original.html              # HTML original
├── 📁 clubefundamento/
│   ├── 📄 enhanced_data.json             # Dados estruturados avançados
│   ├── 📄 clubefundamento_enhanced_analysis.md # Relatório individual avançado
│   └── 📁 html/
│       └── 📄 original.html              # HTML original
├── 📁 meutibi/
│   ├── 📄 enhanced_data.json             # Dados estruturados avançados
│   ├── 📄 meutibi_enhanced_analysis.md # Relatório individual avançado
│   └── 📁 html/
│       └── 📄 original.html              # HTML original
├── 📁 storyspark/
│   ├── 📄 enhanced_data.json             # Dados estruturados avançados
│   ├── 📄 storyspark_enhanced_analysis.md # Relatório individual avançado
│   └── 📁 html/
│       └── 📄 original.html              # HTML original


## 🎉 **Conclusão**

O scraping avançado de múltiplos sites foi **executado com sucesso**, revelando:

- ✅ **Parsers específicos**: Cada site teve extração customizada
- ✅ **Dados estruturados**: 17 produtos e 0 preços extraídos
- ✅ **Diversidade tecnológica**: 4 frameworks diferentes
- ✅ **Análise detalhada**: Relatórios individuais e consolidado
- ✅ **Segmento focado**: Todos os sites são do nicho de livros infantis

**Total de sites processados**: 4/4

---

**🚀 Enhanced Multi-Sites Scraper - Concluído com Sucesso!**
```

---


#### 📁 Directory: meutibi

##### 📄 enhanced_data.json
*Path: `meutibi\enhanced_data.json`*  
*Size: 22.53 KB*

```json
{
  "url": "https://meutibi.com.br/",
  "name": "Tibi - Livros Infantis",
  "type": "livros_infantis",
  "framework": "aspnet",
  "scraped_at": "2025-09-12T15:37:25.136955",
  "status_code": 200,
  "content_length": 96728,
  "title": "Tibi - Livros infantis fantásticos",
  "meta_description": "Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!",
  "meta_keywords": "",
  "headings": {
    "h1": [
      "Livros em destaque",
      "Faça parte da nossa família leitora!",
      "Livros por idade",
      "0-3 anos",
      "3-6 anos",
      "6+ anos",
      "Faça parte da nossa Família Leitora"
    ],
    "h2": [
      "Lançamento",
      "A Última Gota",
      "Best-Seller",
      "A Menina da Cabeça Quadrada",
      "Todos os livros",
      "Coleções",
      "A Última Gota",
      "Acreditar",
      "O Irmãozinho da Jaquinha",
      "Capaz",
      "A Menina da Cabeça Quadrada",
      "Cartas para o Futuro"
    ],
    "h3": [
      "Primeiros Livros",
      "Leitores curiosos",
      "Amo Ler!"
    ],
    "h4": [
      "Sobre",
      "Quem somos",
      "Blog - Mãe que lê",
      "Tem alguma dúvida?",
      "Central de Ajuda",
      "Termos e Condições",
      "Trocas e Devoluções",
      "Siga a Tibi",
      "Fale com a gente",
      "<EMAIL>",
      "(71) 98106-5462",
      "Contato para empresas (revenda)",
      "(71) 99606-9038",
      "Formas de pagamento"
    ],
    "h5": [],
    "h6": []
  },
  "links": [
    {
      "url": "https://meutibi.com.br/#carouselExampleControls2",
      "text": "",
      "title": "",
      "classes": [
        "carousel-control-prev"
      ]
    },
    {
      "url": "https://meutibi.com.br/#carouselExampleControls2",
      "text": "",
      "title": "",
      "classes": [
        "carousel-control-next"
      ]
    },
    {
      "url": "https://meutibi.com.br/",
      "text": "",
      "title": "",
      "classes": [
        "navbar-brand"
      ]
    },
    {
      "url": "https://meutibi.com.br/Order/New",
      "text": "0",
      "title": "",
      "classes": [
        "cart-a-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/Account/Login?returnUrl=%2F",
      "text": "Acessar conta",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Home/AllBooks",
      "text": "Todos os livros",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/",
      "text": "Livros por idade",
      "title": "",
      "classes": [
        "nav-link",
        "link-idade",
        "color-link",
        "onlypc",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/Home/PrimeirosLivros",
      "text": "0-3 anos\r\n                                            \n\r\n                                                Primeiros livros",
      "title": "",
      "classes": [
        "idade-hover"
      ]
    },
    {
      "url": "https://meutibi.com.br/Home/LeitoresCuriosos",
      "text": "3-6 anos\r\n                                            \n\r\n                                                Leitores curiosos",
      "title": "",
      "classes": [
        "idade-hover"
      ]
    },
    {
      "url": "https://meutibi.com.br/Home/AmoLer",
      "text": "6+ anos\r\n                                            \n\r\n                                                Amo ler",
      "title": "",
      "classes": [
        "idade-hover"
      ]
    },
    {
      "url": "https://meutibi.com.br/Home/LivrosPorIdade",
      "text": "Livros por idade",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "onlymobile",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/Professoras/Lp",
      "text": "Professoras",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/pnld/ameninadacabecaquadrada",
      "text": "PNLD",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/sobreaautora",
      "text": "Sobre a autora",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "size-varela"
      ]
    },
    {
      "url": "https://api.whatsapp.com/send?phone=***********&text=&source=&data=&app_absent=",
      "text": "Contato para empresas (revenda): (71) 99606-9038",
      "title": "",
      "classes": [
        "nav-link",
        "color-link",
        "size-varela"
      ]
    },
    {
      "url": "https://meutibi.com.br/Account/Login?returnUrl=%2F",
      "text": "Acesse sua conta",
      "title": "",
      "classes": [
        "onlypc"
      ]
    },
    {
      "url": "https://meutibi.com.br/Order/New",
      "text": "0",
      "title": "",
      "classes": [
        "cart-a-pc"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/Acreditar",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/AUltimaGota",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/ChorarEComoChover",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/Docura",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/#carouselHomeBanner",
      "text": "Anterior",
      "title": "",
      "classes": [
        "carousel-control-prev"
      ]
    },
    {
      "url": "https://meutibi.com.br/#carouselHomeBanner",
      "text": "Próximo",
      "title": "",
      "classes": [
        "carousel-control-next"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/Acreditar",
      "text": "",
      "title": "",
      "classes": [
        "carousel-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/AUltimaGota",
      "text": "",
      "title": "",
      "classes": [
        "carousel-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/ChorarEComoChover",
      "text": "",
      "title": "",
      "classes": [
        "carousel-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/Docura",
      "text": "",
      "title": "",
      "classes": [
        "carousel-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada",
      "text": "",
      "title": "",
      "classes": [
        "carousel-mobile"
      ]
    },
    {
      "url": "https://meutibi.com.br/#carouselHomeMobileBanner",
      "text": "Anterior",
      "title": "",
      "classes": [
        "carousel-control-prev"
      ]
    },
    {
      "url": "https://meutibi.com.br/#carouselHomeMobileBanner",
      "text": "Próximo",
      "title": "",
      "classes": [
        "carousel-control-next"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/AUltimaGota",
      "text": "Lançamento\r\n            \n\r\n                A Última Gota\r\n            \nCOMPRAR\nCOMPRAR",
      "title": "",
      "classes": [
        "banner-lancamento2",
        "hovercard"
      ]
    },
    {
      "url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada",
      "text": "Best-Seller\r\n            \n\r\n                A Menina da Cabeça Quadrada\r\n            \nCOMPRAR",
      "title": "",
      "classes": [
        "banner-lancamento",
        "hovercard"
      ]
    },
    {
      "url": "https://meutibi.com.br/Home/AllBooks",
      "text": "Todos os livros",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Home/Colecoes",
      "text": "Coleções",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/AUltimaGota",
      "text": "Lançamento\n\n\n\n\n\r\n                   A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentáveis em crianças e adultos.\r\n                \n\n\n\n\n\r\n                                5-10 Anos",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/Acreditar",
      "text": "Lançamento\n\n\n\n\n\r\n                    Acreditar\r\n                \n\r\n                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para entender o que o faz único.\r\n                \n\n\n\n\n\r\n                                2-10 Anos",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/IrmaoDaJaquinha",
      "text": "Lançamento\n\n\n\n\n\r\n                    O Irmãozinho da Jaquinha\r\n                \n\r\n                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor que cresce junto com a família.\r\n                \n\n\n\n\n\r\n                                2-10 Anos",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/Capaz",
      "text": "Lançamento\n\n\n\n\n\r\n                   Capaz\r\n                \n\r\n                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.\r\n                \n\n\n\n\n\r\n                                5-10 Anos",
      "title": "",
      "classes": []
    },
    {
      "url": "https://meutibi.com.br/Books/AmeninadaCabecaQuadrada",
      "text": "Best-Se

... (Content truncated - file too large)
```

---


#### 📁 Directory: meutibi\html

##### 📄 original.html
*Path: `meutibi\html\original.html`*  
*Size: 80.68 KB*

```html

<!DOCTYPE html>

<html>
<head>
<meta charset="utf-8"/>
<meta content="IE=edge" http-equiv="X-UA-Compatible"/>
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<meta content="telephone=no" name="format-detection"/>
<meta content="Tibi - Livros infantis fantásticos" property="og:title"/>
<meta content="Tibi" property="og:site_name"/>
<meta content="Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!" property="og:description"/>
<meta content="https://www.meutibi.com.br/Content/site_novo/img/og-img/logo-tibi.jpg" property="og:image"/>
<meta content="Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!" name="description"/>
<meta content="j0rW0aD37jcuSU9R0DdO5z2drBRet4wRM_Xruc6mgxk" name="google-site-verification">
<meta content="87021e022f2a19ddb2d70874138417cd" name="p:domain_verify">
<!-- Google Tag Manager -->
<script>

        (function (w, d, s, l, i) {

            w[l] = w[l] || []; w[l].push({

                'gtm.start':

                    new Date().getTime(), event: 'gtm.js'

            }); var f = d.getElementsByTagName(s)[0],

                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =

                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);

        })(window, document, 'script', 'dataLayer', 'GTM-K8DL3RJ');</script>
<!-- End Google Tag Manager -->
<link href="/Content/site_novo/css/all.min.css" rel="stylesheet"/>
<link href="/Content/site_novo/css/flaticon.css" rel="stylesheet"/>
<link href="https://unpkg.com/swiper@7/swiper-bundle.min.css" rel="stylesheet"/>
<link href="/Content/site_novo/css/toastify.css" rel="stylesheet" type="text/css"/>
<link href="/Content/site_novo/img/favicon.png" rel="icon" type="image/x-icon">
<title>Tibi - Livros infantis fantásticos</title>
<!-- CSS  -->
<script src="/bundles/jquery?v=FVs3ACwOLIVInrAl5sdzR2jrCDmVOWFbZMY6g6Q0ulE1"></script>
<link href="/bundles/padraocss?v=ixQt-toViimBuy2xe0HVgrQJXCak2lYuGB5PavIgZL81" rel="stylesheet"/>
<script type="text/javascript">

        var appInsights = window.appInsights || function (config) {

            function r(config) { t[config] = function () { var i = arguments; t.queue.push(function () { t[config].apply(t, i) }) } }

            var t = { config: config }, u = document, e = window, o = 'script', s = u.createElement(o), i, f; for (s.src = config.url || '//az416426.vo.msecnd.net/scripts/a/ai.0.js', u.getElementsByTagName(o)[0].parentNode.appendChild(s), t.cookie = u.cookie, t.queue = [], i = ['Event', 'Exception', 'Metric', 'PageView', 'Trace', 'Ajax']; i.length;)r('track' + i.pop()); return r('setAuthenticatedUserContext'), r('clearAuthenticatedUserContext'), config.disableExceptionTracking || (i = 'onerror', r('_' + i), f = e[i], e[i] = function (config, r, u, e, o) { var s = f && f(config, r, u, e, o); return s !== !0 && t['_' + i](config, r, u, e, o), s }), t

        }({

            instrumentationKey: 'c464cafa-3f04-419f-80de-9c79bb8d067d'

        });



        window.appInsights = appInsights;

        appInsights.trackPageView();

    </script>
<!-- Hotjar Tracking Code for www.meutibi.com.br -->
<script>

        (function (h, o, t, j, a, r) {

            h.hj = h.hj || function () { (h.hj.q = h.hj.q || []).push(arguments) };

            h._hjSettings = { hjid: 2383038, hjsv: 6 };

            a = o.getElementsByTagName('head')[0];

            r = o.createElement('script'); r.async = 1;

            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;

            a.appendChild(r);

        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');

    </script>
<style>

        @keyframes loading {

            0% {

                transform: translate(-50%, -50%) rotate(0deg);

            }



            100% {

                transform: translate(-50%, -50%) rotate(360deg);

            }

        }



        .loading-circle {

            color: transparent;

        }



            .loading-circle:after {

                content: "";

                position: absolute;

                top: 50%;

                left: 50%;

                transform: translate(-50%, -50%);

                width: 16px;

                height: 16px;

                border-radius: 50%;

                border: 2px solid #fff;

                border-color: #fff transparent #fff transparent;

                animation: loading 1s linear infinite;

            }



        #ctnModal {

            width: 40%;

            top: 0;

            display: flex;

            flex-direction: column;

            background: white;

            border-radius: 20px;

            position: relative;

        }



        #fade {

            position: fixed;

            top: 0;

            display: none;

            height: 100vh;

            align-items: center;

            justify-content: center;

            width: 100%;

            z-index: 1;

            background: rgba(0, 0, 0, 0.6);

        }



        #close {

            cursor: pointer;

        }



        #tituloModal {

            display: flex;

            justify-content: space-between;

            align-items: center;

            padding: 20px;

            padding-left: 40px;

            color: #88c4c7;

            font-weight: 600;

            border-radius: 20px 20px 0px 0;

            background-color: white;

            width: 100%;

            font-size: 20px;

        }



        .fechar-modal {

            text-align: center;

            background-color: #88c4c7;

            padding: 14px;

            width: 100%;

            font-size: 17px;

            color: white;

            border-radius: 0px 0px 20px 20px;

        }



        .fechar-modal2 {

            text-align: center;

            background-color: transparent;

            padding: 14px;

            width: 100%;

            font-size: 17px;

            color: #88c4c7;

        }



        @media (max-width: 340px) {

            #tituloModal {

                display: flex;

                justify-content: left;

                padding: 15px !important;

                padding-left: 15px !important;

                color: #7daa1b;

                font-weight: 600;

                border-radius: 7px 7px 0px 0;

                background-color: white;

                width: 100%;

                font-size: 20px;

            }



            .modal-mobile-1 {

                padding-top: 10px !important;

                padding-left: 15px !important;

                padding-right: 15px !important;

                padding-bottom: 10px !important;

            }

        }



        @media (max-width: 1100px) {

            #ctnModal {

                width: 95%;

            }

        }



        @media (min-width: 1450px) {

            #ctnModal {

                width: 30%;

            }

        }





        .button-area-das-professoras {

            margin: 6px 0;

            display: inline-block;

            color: #fff !important;

            padding: 5px 15px !important;

            background-color: #7DAA1D !important;

            border-radius: 20px;

        }



        @media (min-width: 992px) {

            .flex-box-professora-menu {

                align-items: center;

            }



            .button-area-das-professoras {

                font-size: 16px;

                margin: 0 0 0 8px;

                padding: 3px 12px !important;

            }

        }



        .cc-btn {

            background-color: #7DAA1D !important;

        }



        .fundo-promocao {

            background: #9f53ad;

        }



        .card-top-livros img {

            width: 100%;

        }



        @media (min-width: 998.98px) {

            .card-top-livros img {

                max-height: 311px;

            }

        }



        .link-books {

            cursor: pointer;

        }



        .fundo-bookweek {

            background-color: black !important;

        }

    </style>
</link></meta></meta></head>
<body>
<span id="activeFadeSpan" style="display:none"></span>
<noscript>
<iframe height="0" src="https://www.googletagmanager.com/ns.html?id=GTM-K8DL3RJ" style="display: none; visibility: hidden" width="0"></iframe>
</noscript>
<style>

    .spinAnimation {

        -webkit-animation: rotate360 4s linear infinite;

        -moz-animation: rotate360 4s linear infinite;

        animation: rotate360 4s linear infinite;

    }



    @-moz-keyframes rotate360 {

        100% {

            -moz-transform: rotate(360deg);

        }

    }



    @-webkit-keyframes rotate360 {

        100% {

            -webkit-transform: rotate(360deg);

        }

    }



    @keyframes rotate360 {

        100% {

            -webkit-transform: rotate(360deg);

            transform: rotate(360deg);

        }

    }



    @media (max-width: 1200px) {

        .livrosporidadePC {

            display: none;

        }

    }

</style>
<!--END CAROUSEL TOPO AREA-->
<div class="sticky-top">
<div class="background-carousel-topo">
<div class="carousel carousel-touch slide" data-ride="carousel" id="carouselExampleControls2">
<div class="alinha-centro">
<div class="carousel-inner container-carousel">
<div class="carousel-item active">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-mover-truck alinha-centro-icon"></i>Frete grátis para 2 ou mais unidades

                            </span>
</div>
<div class="carousel-item">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-credit-card alinha-centro-icon"></i>Pagamento parcelado em até 3 vezes

                            </span>
</div>
<div class="carousel-item">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-shield-1 alinha-centro-icon-ssl"></i>Site Seguro com certificado SSL

     

... (Content truncated - file too large)
```

---


#### 📁 Directory: meutibi

##### 📄 meutibi_enhanced_analysis.md
*Path: `meutibi\meutibi_enhanced_analysis.md`*  
*Size: 2.85 KB*

```md
# 🚀 Análise Avançada - Tibi - Livros Infantis

> **Web Scraping Avançado Executado com Sucesso**
> Site: https://meutibi.com.br/
> Data: 2025-09-12T15:37:25.136955
> Parser: parse_meutibi

## 📊 **Resumo Executivo**

O **Tibi - Livros Infantis** é uma plataforma de **livros_infantis** construída com **aspnet**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: aspnet
- **Tipo**: livros_infantis
- **Status**: 200
- **Tamanho**: 96728 bytes

### **Tecnologias Detectadas**
- **Frameworks**: Next.js
- **Analytics**: Google Tag Manager
- **Server**: Microsoft-IIS/10.0


## 🎯 **Informações Básicas**

- **Título**: Tibi - Livros infantis fantásticos
- **Descrição**: Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!
- **Palavras-chave**: 

## 📦 **Produtos Encontrados**

**Total de Produtos**: 17

### Produto 1
- **Título**: Best-Seller
- **Preço**: 
- **Descrição**: Best-Seller...

### Produto 2
- **Título**: Todos os livros
- **Preço**: 
- **Descrição**: Todos os livros

                            














                                Coleções...

### Produto 3
- **Título**: Livros por idade

                                
- **Preço**: 
- **Descrição**: Livros por idade

                                










                                       ...

### Produto 4
- **Título**: Todos os livros
- **Preço**: 
- **Descrição**: Todos os livros...

### Produto 5
- **Título**: Livros em destaque
- **Preço**: 
- **Descrição**: Livros em destaque

            





Lançamento






                   A Última Gota

           ...

## 💰 **Análise de Preços**

Nenhum preço específico encontrado.


## 🔍 **Estrutura de Headings**
- **H1**: 7 elementos
  - Livros em destaque...
  - Faça parte da nossa família leitora!...
  - Livros por idade...
- **H2**: 12 elementos
  - Lançamento...
  - A Última Gota...
  - Best-Seller...
- **H3**: 3 elementos
  - Primeiros Livros...
  - Leitores curiosos...
  - Amo Ler!...
- **H4**: 14 elementos
  - Sobre...
  - Quem somos...
  - Blog - Mãe que lê...


## 🎉 **Conclusão**

A análise avançada do **Tibi - Livros Infantis** revelou:

- ✅ **Produtos identificados**: 17
- ✅ **Preços extraídos**: 0
- ✅ **Tecnologia**: aspnet bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `meutibi/enhanced_data.json` - Dados estruturados avançados
- `meutibi/html/original.html` - HTML original
- `meutibi_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
```

---


#### 📁 Directory: storyspark

##### 📄 enhanced_data.json
*Path: `storyspark\enhanced_data.json`*  
*Size: 14.41 KB*

```json
{
  "url": "https://storyspark.ai/pt",
  "name": "Story Spark - Criação de Histórias",
  "type": "criacao_historias",
  "framework": "nextjs",
  "scraped_at": "2025-09-12T15:37:28.011648",
  "status_code": 200,
  "content_length": 404454,
  "title": "Story Spark | Criar e Ler Histórias Mágicas para Crianças",
  "meta_description": "Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura.",
  "meta_keywords": "",
  "headings": {
    "h1": [
      "Toda história começa com você",
      "TEM PERGUNTAS?"
    ],
    "h2": [
      "Desperte sua imaginação",
      "Como funciona",
      "JUNTE-SE A 200,000 OUTROS CONTADORES DE HISTÓRIAS",
      "Criar uma história por",
      "SUAS HISTÓRIAS BELAMENTE CONTADAS"
    ],
    "h3": [
      "Crie seu personagem",
      "Crie sua história",
      "Adicione um toque de magia",
      "Organize seu mundo",
      "Imprima sua aventura",
      "Crie sua aventura",
      "Compartilhe sua aventura",
      "Imprima sua aventura"
    ],
    "h4": [],
    "h5": [],
    "h6": []
  },
  "links": [
    {
      "url": "https://storyspark.ai/pt/",
      "text": "",
      "title": "",
      "classes": [
        "flex-center",
        "gap-2",
        "md:gap-5"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/create-story",
      "text": "Criar História",
      "title": "",
      "classes": [
        "font-semibold",
        "text-indigo",
        "underline-offset-8",
        "hover:underline",
        "text-sm",
        "md:text-base",
        "lg:text-md"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/character-world",
      "text": "Personagens",
      "title": "",
      "classes": [
        "font-semibold",
        "text-indigo",
        "underline-offset-8",
        "hover:underline",
        "text-sm",
        "md:text-base",
        "lg:text-md"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/library",
      "text": "Nossas histórias",
      "title": "",
      "classes": [
        "font-semibold",
        "text-indigo",
        "underline-offset-8",
        "hover:underline",
        "text-sm",
        "md:text-base",
        "lg:text-md"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/pricing",
      "text": "Preços",
      "title": "",
      "classes": [
        "font-semibold",
        "text-indigo",
        "underline-offset-8",
        "hover:underline",
        "text-sm",
        "md:text-base",
        "lg:text-md"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/learn",
      "text": "Aprender",
      "title": "",
      "classes": [
        "font-semibold",
        "text-indigo",
        "underline-offset-8",
        "hover:underline",
        "text-sm",
        "md:text-base",
        "lg:text-md"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/create-story",
      "text": "Criar História",
      "title": "",
      "classes": [
        "w-3/4",
        "text-xl"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/profile",
      "text": "Meu Perfil",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/character-world",
      "text": "Personagens",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/",
      "text": "Nossas histórias",
      "title": "",
      "classes": [
        "flex",
        "items-center",
        "gap-2"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/storyteller",
      "text": "Comunidade",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/library",
      "text": "Biblioteca de Histórias",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/pricing",
      "text": "Preços",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/",
      "text": "Aprender",
      "title": "",
      "classes": [
        "flex",
        "items-center",
        "gap-2"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/faq",
      "text": "FAQ",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/blog",
      "text": "Blog",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/learn",
      "text": "Centro de Aprendizado",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/about-us",
      "text": "Sobre Nós",
      "title": "",
      "classes": []
    },
    {
      "url": "https://facebook.com/StorySparkAI",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://Instagram.com/StorySparkAI",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.youtube.com/@StorySparkAI",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://www.Linkedin.com/company/StorySparkAI",
      "text": "",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/privacy-policy",
      "text": "Política de Privacidade",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/terms-of-service",
      "text": "Termos de Serviço",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/create-story",
      "text": "Crie sua história",
      "title": "",
      "classes": [
        "mt-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/storyteller",
      "text": "Ver histórias da comunidade",
      "title": "",
      "classes": [
        "mx-auto",
        "mt-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/create-story",
      "text": "criar uma história",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/auth?signup=true",
      "text": "Inscreva-se agora",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/personalized-gifts",
      "text": "presente especial",
      "title": "",
      "classes": [
        "cursor-pointer",
        "italic",
        "underline",
        "underline-offset-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/terms-of-service",
      "text": "aqui",
      "title": "",
      "classes": [
        "cursor-pointer",
        "italic",
        "underline",
        "underline-offset-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/library",
      "text": "biblioteca infantil",
      "title": "",
      "classes": [
        "cursor-pointer",
        "underline",
        "underline-offset-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/storyteller",
      "text": "Biblioteca da Comunidade",
      "title": "",
      "classes": [
        "cursor-pointer",
        "underline",
        "underline-offset-4"
      ]
    },
    {
      "url": "https://storyspark.ai/pt/faq",
      "text": "Tem mais perguntas?",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/pt/auth",
      "text": "Comece hoje",
      "title": "",
      "classes": []
    }
  ],
  "images": [
    {
      "url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&w=256&q=75",
      "alt": "Story-Spark-Logo",
      "title": "",
      "classes": [
        "mb-5",
        "h-[44px]",
        "w-[44px]"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75",
      "alt": "Left Icon",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75",
      "alt": "Portuguese-flag",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75",
      "alt": "Left Spark Icon",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2FimagesV2%2Fsparks.png&w=48&q=75",
      "alt": "Right Spark Icon",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2Fworldwide.png&w=48&q=75",
      "alt": "Portuguese-flag",
      "title": "",
      "classes": []
    },
    {
      "url": "https://storyspark.ai/_next/image?url=%2Fdemo4.webp&w=3840&q=50",
      "alt": "",
      "title": "",
      "classes": [
        "object-cover"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook1.png&w=640&q=75",
      "alt": "Book 1",
      "title": "",
      "classes": [
        "h-[150px]",
        "w-[150px]",
        "rounded-md",
        "object-cover",
        "md:h-[200px]",
        "md:w-[200px]"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook2.png&w=640&q=75",
      "alt": "Book 2",
      "title": "",
      "classes": [
        "h-[150px]",
        "w-[150px]",
        "rounded-md",
        "object-cover",
        "md:h-[200px]",
        "md:w-[200px]"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook3.png&w=640&q=75",
      "alt": "Book 3",
      "title": "",
      "classes": [
        "h-[150px]",
        "w-[150px]",
        "rounded-md",
        "object-cover",
        "md:h-[200px]",
        "md:w-[200px]"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook4.png&w=640&q=75",
      "alt": "Book 4",
      "title": "",
      "classes": [
        "h-[150px]",
        "w-[150px]",
        "rounded-md",
        "object-cover",
        "md:h-[200px]",
        "md:w-[200px]"
      ]
    },
    {
      "url": "https://storyspark.ai/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-

... (Content truncated - file too large)
```

---


#### 📁 Directory: storyspark\html

##### 📄 original.html
*Path: `storyspark\html\original.html`*  
*Size: 394.96 KB*

```html
<!DOCTYPE html>
<html><head><meta charset="utf-8"/><meta content="width=device-width, initial-scale=1" name="viewport"/><link as="font" crossorigin="" href="/_next/static/media/50ae58c90b3efe37-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/7b89a4fd5e90ede0-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/904be59b21bd51cb-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/95bc6516d0274e02-s.p.otf" rel="preload" type="font/otf"/><link as="font" crossorigin="" href="/_next/static/media/c092d4737c448cf6-s.p.otf" rel="preload" type="font/otf"/><link as="font" crossorigin="" href="/_next/static/media/e93861d9efa137b7-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/fad5e1188f09afdc-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/fb80db0e9f250198-s.p.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="" href="/_next/static/media/fca0f299eeb5ec16-s.p.otf" rel="preload" type="font/otf"/><link as="font" crossorigin="" href="/_next/static/media/ff6024dca1aea6d8-s.p.woff2" rel="preload" type="font/woff2"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&amp;w=96&amp;q=75 1x, /_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&amp;w=256&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=%2FimagesV2%2Fsparks.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2FimagesV2%2Fsparks.png&amp;w=48&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesizes="100vw" imagesrcset="/_next/image?url=%2Fdemo4.webp&amp;w=640&amp;q=50 640w, /_next/image?url=%2Fdemo4.webp&amp;w=750&amp;q=50 750w, /_next/image?url=%2Fdemo4.webp&amp;w=828&amp;q=50 828w, /_next/image?url=%2Fdemo4.webp&amp;w=1080&amp;q=50 1080w, /_next/image?url=%2Fdemo4.webp&amp;w=1200&amp;q=50 1200w, /_next/image?url=%2Fdemo4.webp&amp;w=1920&amp;q=50 1920w, /_next/image?url=%2Fdemo4.webp&amp;w=2048&amp;q=50 2048w, /_next/image?url=%2Fdemo4.webp&amp;w=3840&amp;q=50 3840w" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook1.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook1.png&amp;w=640&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook2.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook2.png&amp;w=640&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook3.png&amp;w=640&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook4.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook4.png&amp;w=640&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" imagesrcset="/_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook5.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Fdoywbbxjwqsrx.cloudfront.net%2Fhome-page%2Fbooks%2Fbook5.png&amp;w=640&amp;q=75 2x" rel="preload"/><link as="image" fetchpriority="high" href="/cloud.svg" rel="preload"/><link data-precedence="next" href="/_next/static/css/2cd46a8e86f37904.css" rel="stylesheet"/><link data-precedence="next" href="/_next/static/css/5ef1c2258875a83b.css" rel="stylesheet"/><link data-precedence="next" href="/_next/static/css/9f7949553827ce3b.css" rel="stylesheet"/><link data-precedence="next" href="/_next/static/css/19a8af15187a8b6f.css" rel="stylesheet"/><link as="script" fetchpriority="low" href="/_next/static/chunks/webpack-fc392a74cd805795.js" rel="preload"/><script async="" src="/_next/static/chunks/fd9d1056-19ffd9d03df797c8.js"></script><script async="" src="/_next/static/chunks/7023-08398b3fe3100e74.js"></script><script async="" src="/_next/static/chunks/main-app-b8d8f654a4c3de8b.js"></script><script async="" src="/_next/static/chunks/8173-7f5dd7d0c0bf6e3a.js"></script><script async="" src="/_next/static/chunks/954-b65e9a2d13384c21.js"></script><script async="" src="/_next/static/chunks/156-463fa05c500fefbb.js"></script><script async="" src="/_next/static/chunks/6351-9df3d4e606edddf4.js"></script><script async="" src="/_next/static/chunks/6013-aab79ab246c1a882.js"></script><script async="" src="/_next/static/chunks/4550-2614e4b0377232f4.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/(app)/(home)/layout-f039cc2a4a5ab73d.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/(app)/(home)/loading-c600cdbd56374962.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/not-found-c41e585c89f55e5e.js"></script><script async="" src="/_next/static/chunks/4480-adba274194082253.js"></script><script async="" src="/_next/static/chunks/2060-3f2d75d03ef3afb5.js"></script><script async="" src="/_next/static/chunks/5644-49b9becb7c9579bd.js"></script><script async="" src="/_next/static/chunks/1292-f4a89e6b61589709.js"></script><script async="" src="/_next/static/chunks/6982-9f6f2714e379bb29.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/(app)/layout-5bc3bea1517364cc.js"></script><script async="" src="/_next/static/chunks/907-a9397d5e29074430.js"></script><script async="" src="/_next/static/chunks/app/layout-92d9b4fb2c6bffd1.js"></script><script async="" src="/_next/static/chunks/6199-25dcfc4f11ce2527.js"></script><script async="" src="/_next/static/chunks/2895-0935b9b8572314e3.js"></script><script async="" src="/_next/static/chunks/3917-14bd4a3b211686a6.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/(app)/(home)/@child/page-7e1baae024ea1b0f.js"></script><script async="" src="/_next/static/chunks/3858-1324c72f52feef6c.js"></script><script async="" src="/_next/static/chunks/714-8b3cd7aea483fa6b.js"></script><script async="" src="/_next/static/chunks/app/%5Blocale%5D/(app)/(home)/@main/page-ed82882eebe06bd8.js"></script><link as="script" href="https://www.googletagmanager.com/gtm.js?id=GTM-MKL9JLVX" rel="preload"/><link as="script" href="https://www.googletagmanager.com/gtag/js?id=G-D25VB3SV7T" rel="preload"/><link as="script" href="//cdn.cookie-script.com/s/5731677cb6b5d7e93c0ad446fdfae962.js" rel="preload"/><title>Story Spark | Criar e Ler Histórias Mágicas para Crianças</title><meta content="Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura." name="description"/><link href="https://storyspark.ai/pt" rel="canonical"/><link href="https://storyspark.ai/pt" hreflang="pt" rel="alternate"/><link href="https://storyspark.ai" hreflang="en" rel="alternate"/><link href="https://storyspark.ai/es" hreflang="es" rel="alternate"/><link href="https://storyspark.ai/fr" hreflang="fr" rel="alternate"/><link href="https://storyspark.ai/ar" hreflang="ar" rel="alternate"/><link href="https://storyspark.ai/tr" hreflang="tr" rel="alternate"/><meta content="Story Spark | Criar e Ler Histórias Mágicas para Crianças" property="og:title"/><meta content="Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura." property="og:description"/><meta content="https://storyspark.ai/imagesV2/Logo1200x630.png" property="og:image"/><meta content="summary_large_image" name="twitter:card"/><meta content="Story Spark | Criar e Ler Histórias Mágicas para Crianças" name="twitter:title"/><meta content="Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura." name="twitter:description"/><meta content="https://storyspark.ai/imagesV2/Logo1200x630.png" name="twitter:image"/><link href="/favicon.ico" rel="icon" sizes="16x16" type="image/x-icon"/><meta name="next-size-adjust"/><script nomodule="" src="/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js"></script></head><body><html class="__variable_069ab3 __variable_dbf593 __variable_fde0d3 __variable_af5ebc __variable_856aec __variable_ddc990 __variable_ce2ec7 __variable_d947b8 __variable_5445ab __variable_baba7c __variable_628a01" lang="pt"><body class="bg-lightGrey font-montserrat"><div class="Toastify"></div><div class="w-full"><div class="sticky top-0 z-50"><div class="relative flex items-center justify-between px-4 py-3 transition-all sm:py-1 lg:px-6 lg:py-0 bg-v2"><div class="mx-auto flex w-full max-w-main items-center justify-between"><div class="flex-start flex-shrink-0"><a class="flex-center gap-2 md:gap-5" href="/pt/"><img alt="Story-Spark-Logo" class="mb-5 h-[44px] w-[44px]" data-nimg="1" decoding="async" fetchpriority="high" height="57" src="/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&amp;w=256&amp;q=75" srcset="/_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&amp;w=96&amp;q=75 1x, /_next/image?url=%2FimagesV2%2FStory-Spark-Logo.png&amp;w=256&amp;q=75 2x" style="color:transparent" width="66"/><svg class="hidden w-[150px] text-indigo transition-all md:block lg:w-[200px]" height="27.177" viewbox="0 0 258.155 27.177" width="258.155" xmlns="http://www.w3.org/2000/svg"><g data-name="Group 411" id="Group_411" transform="translate(-2164.845 -56.553)"><g data-name="Group 410" id="Group_410"><path d="M2173.878,83.73a20.828,20.828,0,0,1-4.837-.586,15.506,15.506,0,0,1-4.159-1.569l1.361-6.161a14.45,14.45,0,0,0,4.1,1.531,19.141,19.141,0,0,0,3.534.359,4.321,4.321,0

... (Content truncated - file too large)
```

---


#### 📁 Directory: storyspark

##### 📄 storyspark_enhanced_analysis.md
*Path: `storyspark\storyspark_enhanced_analysis.md`*  
*Size: 2.10 KB*

```md
# 🚀 Análise Avançada - Story Spark - Criação de Histórias

> **Web Scraping Avançado Executado com Sucesso**
> Site: https://storyspark.ai/pt
> Data: 2025-09-12T15:37:28.011648
> Parser: parse_storyspark

## 📊 **Resumo Executivo**

O **Story Spark - Criação de Histórias** é uma plataforma de **criacao_historias** construída com **nextjs**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: nextjs
- **Tipo**: criacao_historias
- **Status**: 200
- **Tamanho**: 404454 bytes

### **Tecnologias Detectadas**
- **Frameworks**: Next.js, React
- **Server**: Vercel


## 🎯 **Informações Básicas**

- **Título**: Story Spark | Criar e Ler Histórias Mágicas para Crianças
- **Descrição**: Crie histórias divertidas e personalizadas que trazem as aventuras do seu filho à vida e despertam sua paixão pela leitura.
- **Palavras-chave**: 

## 📦 **Produtos Encontrados**

Nenhum produto específico encontrado com os seletores utilizados.

## 💰 **Análise de Preços**

Nenhum preço específico encontrado.


## 🔍 **Estrutura de Headings**
- **H1**: 2 elementos
  - Toda história começa com você...
  - TEM PERGUNTAS?...
- **H2**: 5 elementos
  - Desperte sua imaginação...
  - Como funciona...
  - JUNTE-SE A 200,000 OUTROS CONTADORES DE HISTÓRIAS...
- **H3**: 8 elementos
  - Crie seu personagem...
  - Crie sua história...
  - Adicione um toque de magia...


## 🎉 **Conclusão**

A análise avançada do **Story Spark - Criação de Histórias** revelou:

- ✅ **Produtos identificados**: 0
- ✅ **Preços extraídos**: 0
- ✅ **Tecnologia**: nextjs bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `storyspark/enhanced_data.json` - Dados estruturados avançados
- `storyspark/html/original.html` - HTML original
- `storyspark_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
```

---


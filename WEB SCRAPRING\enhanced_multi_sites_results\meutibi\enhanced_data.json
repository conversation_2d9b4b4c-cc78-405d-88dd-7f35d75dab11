{"url": "https://meutibi.com.br/", "name": "Tibi <PERSON> <PERSON><PERSON>", "type": "livros_infantis", "framework": "aspnet", "scraped_at": "2025-09-12T15:37:25.136955", "status_code": 200, "content_length": 96728, "title": "Tibi - <PERSON><PERSON>", "meta_description": "Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!", "meta_keywords": "", "headings": {"h1": ["Livros em destaque", "Faça parte da nossa família leitora!", "Livros por idade", "0-3 anos", "3-6 anos", "6+ anos", "Faça parte da nossa Família Leitor<PERSON>"], "h2": ["Lançamento", "A Última Gota", "Best-Seller", "A Menina da Cabeça Quadrada", "Todos os livros", "Coleções", "A Última Gota", "Acreditar", "O Irmãozinho da Jaquinha", "Capaz", "A Menina da Cabeça Quadrada", "Cartas para o Futuro"], "h3": ["<PERSON><PERSON><PERSON>", "Leitores curiosos", "Amo Ler!"], "h4": ["Sobre", "Quem somos", "Blog - <PERSON><PERSON><PERSON> que lê", "Tem alguma dúvida?", "Central de Ajuda", "Termos e Condições", "Trocas e Devoluções", "Siga a Tibi", "Fale com a gente", "<EMAIL>", "(71) 98106-5462", "Contato para empresas (revenda)", "(71) 99606-9038", "Formas de pagamento"], "h5": [], "h6": []}, "links": [{"url": "https://meutibi.com.br/#carouselExampleControls2", "text": "", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselExampleControls2", "text": "", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/", "text": "", "title": "", "classes": ["navbar-brand"]}, {"url": "https://meutibi.com.br/Order/New", "text": "0", "title": "", "classes": ["cart-a-mobile"]}, {"url": "https://meutibi.com.br/Account/Login?returnUrl=%2F", "text": "Acessar conta", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "Todos os livros", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/", "text": "Livros por idade", "title": "", "classes": ["nav-link", "link-idade", "color-link", "onlypc", "size-varela"]}, {"url": "https://meutibi.com.br/Home/PrimeirosLivros", "text": "0-3 anos\r\n                                            \n\r\n                                                Primeiros livros", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/LeitoresCuriosos", "text": "3-6 anos\r\n                                            \n\r\n                                                Leitores curiosos", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/AmoLer", "text": "6+ anos\r\n                                            \n\r\n                                                <PERSON> ler", "title": "", "classes": ["idade-hover"]}, {"url": "https://meutibi.com.br/Home/LivrosPorIdade", "text": "Livros por idade", "title": "", "classes": ["nav-link", "color-link", "onlymobile", "size-varela"]}, {"url": "https://meutibi.com.br/Professoras/Lp", "text": "<PERSON><PERSON>", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/pnld/ameninadacabecaquadrada", "text": "PNLD", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/sobreaautora", "text": "Sobre a autora", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://api.whatsapp.com/send?phone=***********&text=&source=&data=&app_absent=", "text": "Contato para empresas (revenda): (71) 99606-9038", "title": "", "classes": ["nav-link", "color-link", "size-varela"]}, {"url": "https://meutibi.com.br/Account/Login?returnUrl=%2F", "text": "Acesse sua conta", "title": "", "classes": ["onlypc"]}, {"url": "https://meutibi.com.br/Order/New", "text": "0", "title": "", "classes": ["cart-a-pc"]}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/ChorarEComoChover", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Docura", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/#carouselHomeBanner", "text": "Anterior", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselHomeBanner", "text": "Próximo", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/ChorarEComoChover", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/Docura", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "", "title": "", "classes": ["carousel-mobile"]}, {"url": "https://meutibi.com.br/#carouselHomeMobileBanner", "text": "Anterior", "title": "", "classes": ["carousel-control-prev"]}, {"url": "https://meutibi.com.br/#carouselHomeMobileBanner", "text": "Próximo", "title": "", "classes": ["carousel-control-next"]}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "Lançamento\r\n            \n\r\n                A Última Gota\r\n            \nCOMPRAR\nCOMPRAR", "title": "", "classes": ["banner-lancamento2", "hovercard"]}, {"url": "https://meutibi.com.br/Books/AMeninaDaCabecaQuadrada", "text": "Best-Seller\r\n            \n\r\n                <PERSON> Menina da Cabeça Quadrada\r\n            \nCOMPRAR", "title": "", "classes": ["banner-lancamento", "hovercard"]}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "Todos os livros", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/Colecoes", "text": "Coleções", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AUltimaGota", "text": "Lançamento\n\n\n\n\n\r\n                   A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentáveis em crianças e adultos.\r\n                \n\n\n\n\n\r\n                                5-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Acreditar", "text": "Lançamento\n\n\n\n\n\r\n                    Acreditar\r\n                \n\r\n                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para entender o que o faz único.\r\n                \n\n\n\n\n\r\n                                2-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/IrmaoDaJaquinha", "text": "Lançamento\n\n\n\n\n\r\n                    O Irmãozinho da Jaquinha\r\n                \n\r\n                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor que cresce junto com a família.\r\n                \n\n\n\n\n\r\n                                2-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Capaz", "text": "Lançamento\n\n\n\n\n\r\n                   Capaz\r\n                \n\r\n                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.\r\n                \n\n\n\n\n\r\n                                5-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/AmeninadaCabecaQuadrada", "text": "Best-Seller\n\n\n\n\n\r\n                    A Menina da Cabeça Quadrada\r\n                \n\r\n                    De tanto usar celular, tablet e computador, Cecília acordou\r\n                    com a cabeça quadrada! Uma história para ajudar uma geração\r\n                    superconectada a aproveitar a vida lá fora.\r\n                \n\n\n\n\n\r\n                                2-10 <PERSON><PERSON>", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Books/Lua", "text": "Cartas para o Futuro\r\n                \n\r\n                    As crianças podem ter voz ativa nas questões do mundo? Através do poder das cartas, a pequena Lua nos mostra que sim!\r\n                \n\n\n\n\n\r\n                                4-10 Anos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AllBooks", "text": "CONHEÇA TODOS OS LIVROS", "title": "", "classes": ["conheca-mobile-2"]}, {"url": "https://www.instagram.com/maequele/", "text": "CONHEÇA NOSSA HISTÓRIA", "title": "", "classes": ["botao-mobile"]}, {"url": "https://meutibi.com.br/Home/PrimeirosLivros", "text": "0-3 anos\r\n                                \n\r\n                                    Primeiros Livros", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/LeitoresCuriosos", "text": "3-6 anos\r\n                                \n\r\n                                    Leitores curiosos", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Home/AmoLer", "text": "6+ anos\r\n                                \n\r\n                                    <PERSON>!", "title": "", "classes": []}, {"url": "https://meutibi.com.br/#carouselExampleIndicators", "text": "Previous", "title": "", "classes": ["carousel-control-prev", "onlypc"]}, {"url": "https://meutibi.com.br/#carouselExampleIndicators", "text": "Next", "title": "", "classes": ["carousel-control-next", "onlypc"]}, {"url": "https://api.whatsapp.com/send?phone=5511998243336&text=&source=&data=&app_absent=", "text": "", "title": "", "classes": []}, {"url": "https://www.instagram.com/editoratibi/", "text": "Quem somos", "title": "", "classes": ["footer-link"]}], "images": [{"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/logo-tibi.svg", "alt": "", "title": "", "classes": ["img-fluid", "pc-img"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/banner-mobile-a-ultima-gota.png", "alt": "", "title": "", "classes": ["onlymobile"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/livro-sobre-vicio-no-celular-a-menina-da-cabeca-quadrada.webp", "alt": "", "title": "", "classes": ["onlymobile"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-mobile-colecoes.webp", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/presente.svg", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/cartao.svg", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/caminhao.svg", "alt": "", "title": "", "classes": ["img-fluid", "img-icones-home"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/seguro.svg", "alt": "", "title": "", "classes": ["img-fluid", "img-icones-home"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/produtos-a-ultima-gota.png", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/capa_do_livro_acreditar_emilia_nunez.png", "alt": "capaz", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/irmao-jaquinha/produto-IJ-home.webp", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/capaz/produto-capaz.png", "alt": "capaz", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "alt": "a-menina-da-cabeca-quadrada", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Images/livros/cartas-para-o-futuro/capa-cartas-para-o-futuro-lua-emilia-nunez.jpg", "alt": "cartas-para-o-futuro", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/banner2mobile.png", "alt": "", "title": "", "classes": ["img-fluid"]}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-left.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/arrow-right.svg", "alt": "", "title": "", "classes": []}, {"url": "https://meutibi.com.br/Content/site_novo/img/formasdepagamento2.png", "alt": "", "title": "", "classes": []}], "technology_stack": {"frameworks": ["Next.js"], "libraries": [], "analytics": ["Google Tag Manager"], "other": [], "server": "Microsoft-IIS/10.0"}, "products": [{"title": "Best-Seller", "price": "", "image": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "description": "Best-Seller"}, {"title": "Todos os livros", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "description": "Todos os livros\r\n                            \n\n\n\n\n\n\n\n\n\n\n\n\n\r\n                                Coleções"}, {"title": "Livros por idade\r\n                                ", "price": "", "image": "", "description": "Livros por idade\r\n                                \n\n\n\n\n\n\n\n\n\r\n                                                0-3 anos\r\n                                            \n\r\n                                  "}, {"title": "Todos os livros", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png", "description": "Todos os livros"}, {"title": "Livros em destaque", "price": "", "image": "https://meutibi.com.br/Content/site_novo/img/a-ultima-gota/produtos-a-ultima-gota.png", "description": "Livros em destaque\r\n            \n\n\n\n\n\nLançamento\n\n\n\n\n\r\n                   A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promov"}, {"title": "Livros em destaque", "price": "", "image": "", "description": "Livros em destaque"}, {"title": "A Última Gota", "price": "", "image": "", "description": "A Última Gota\r\n                \n\r\n                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentávei"}, {"title": "Acreditar", "price": "", "image": "", "description": "Acreditar\r\n                \n\r\n                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para ente"}, {"title": "O Irmãozinho da Jaquinha", "price": "", "image": "", "description": "O Irmãozinho da Jaquinha\r\n                \n\r\n                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor q"}, {"title": "Capaz", "price": "", "image": "", "description": "Capaz\r\n                \n\r\n                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.\r\n          "}, {"title": "Best-Seller", "price": "", "image": "https://meutibi.com.br/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg", "description": "Best-Seller"}, {"title": "A Menina da Cabeça Quadrada", "price": "", "image": "", "description": "A Menina da Cabeça Quadrada\r\n                \n\r\n                    De tanto usar celular, tablet e computador, Cecília acordou\r\n                    com a cabeça quadrada! Uma história para ajudar uma"}, {"title": "Cartas para o Futuro", "price": "", "image": "", "description": "Cartas para o Futuro\r\n                \n\r\n                    As crianças podem ter voz ativa nas questões do mundo? Através do poder das cartas, a pequena Lua nos mostra que sim!\r\n                \n\n\n\n"}, {"title": "CONHEÇA TODOS OS LIVROS", "price": "", "image": "", "description": "CONHEÇA TODOS OS LIVROS"}, {"title": "CONHEÇA NOSSA HISTÓRIA", "price": "", "image": "", "description": "CONHEÇA NOSSA HISTÓRIA"}, {"title": "Livros por idade", "price": "", "image": "", "description": "Livros por idade\r\n            \n\n\n\n\n\n\n\n\r\n                                    0-3 anos\r\n                                \n\r\n                                    Primeiros Livros\r\n                         "}, {"title": "Livros por idade", "price": "", "image": "", "description": "Livros por idade"}], "prices": [], "categories": [], "age_groups": [{"category": "primeiros livros", "mentions": 2}, {"category": "leitores curiosos", "mentions": 2}, {"category": "amo ler", "mentions": 2}], "book_details": []}
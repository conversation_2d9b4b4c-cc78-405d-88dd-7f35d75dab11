
<!DOCTYPE html>

<html>
<head>
<meta charset="utf-8"/>
<meta content="IE=edge" http-equiv="X-UA-Compatible"/>
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<meta content="telephone=no" name="format-detection"/>
<meta content="Tibi - Livros infantis fantásticos" property="og:title"/>
<meta content="Tibi" property="og:site_name"/>
<meta content="Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!" property="og:description"/>
<meta content="https://www.meutibi.com.br/Content/site_novo/img/og-img/logo-tibi.jpg" property="og:image"/>
<meta content="Livros infantis feitos com muito amor para unir as famílias no gosto pela leitura!" name="description"/>
<meta content="j0rW0aD37jcuSU9R0DdO5z2drBRet4wRM_Xruc6mgxk" name="google-site-verification">
<meta content="87021e022f2a19ddb2d70874138417cd" name="p:domain_verify">
<!-- Google Tag Manager -->
<script>

        (function (w, d, s, l, i) {

            w[l] = w[l] || []; w[l].push({

                'gtm.start':

                    new Date().getTime(), event: 'gtm.js'

            }); var f = d.getElementsByTagName(s)[0],

                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =

                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);

        })(window, document, 'script', 'dataLayer', 'GTM-K8DL3RJ');</script>
<!-- End Google Tag Manager -->
<link href="/Content/site_novo/css/all.min.css" rel="stylesheet"/>
<link href="/Content/site_novo/css/flaticon.css" rel="stylesheet"/>
<link href="https://unpkg.com/swiper@7/swiper-bundle.min.css" rel="stylesheet"/>
<link href="/Content/site_novo/css/toastify.css" rel="stylesheet" type="text/css"/>
<link href="/Content/site_novo/img/favicon.png" rel="icon" type="image/x-icon">
<title>Tibi - Livros infantis fantásticos</title>
<!-- CSS  -->
<script src="/bundles/jquery?v=FVs3ACwOLIVInrAl5sdzR2jrCDmVOWFbZMY6g6Q0ulE1"></script>
<link href="/bundles/padraocss?v=ixQt-toViimBuy2xe0HVgrQJXCak2lYuGB5PavIgZL81" rel="stylesheet"/>
<script type="text/javascript">

        var appInsights = window.appInsights || function (config) {

            function r(config) { t[config] = function () { var i = arguments; t.queue.push(function () { t[config].apply(t, i) }) } }

            var t = { config: config }, u = document, e = window, o = 'script', s = u.createElement(o), i, f; for (s.src = config.url || '//az416426.vo.msecnd.net/scripts/a/ai.0.js', u.getElementsByTagName(o)[0].parentNode.appendChild(s), t.cookie = u.cookie, t.queue = [], i = ['Event', 'Exception', 'Metric', 'PageView', 'Trace', 'Ajax']; i.length;)r('track' + i.pop()); return r('setAuthenticatedUserContext'), r('clearAuthenticatedUserContext'), config.disableExceptionTracking || (i = 'onerror', r('_' + i), f = e[i], e[i] = function (config, r, u, e, o) { var s = f && f(config, r, u, e, o); return s !== !0 && t['_' + i](config, r, u, e, o), s }), t

        }({

            instrumentationKey: 'c464cafa-3f04-419f-80de-9c79bb8d067d'

        });



        window.appInsights = appInsights;

        appInsights.trackPageView();

    </script>
<!-- Hotjar Tracking Code for www.meutibi.com.br -->
<script>

        (function (h, o, t, j, a, r) {

            h.hj = h.hj || function () { (h.hj.q = h.hj.q || []).push(arguments) };

            h._hjSettings = { hjid: 2383038, hjsv: 6 };

            a = o.getElementsByTagName('head')[0];

            r = o.createElement('script'); r.async = 1;

            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;

            a.appendChild(r);

        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');

    </script>
<style>

        @keyframes loading {

            0% {

                transform: translate(-50%, -50%) rotate(0deg);

            }



            100% {

                transform: translate(-50%, -50%) rotate(360deg);

            }

        }



        .loading-circle {

            color: transparent;

        }



            .loading-circle:after {

                content: "";

                position: absolute;

                top: 50%;

                left: 50%;

                transform: translate(-50%, -50%);

                width: 16px;

                height: 16px;

                border-radius: 50%;

                border: 2px solid #fff;

                border-color: #fff transparent #fff transparent;

                animation: loading 1s linear infinite;

            }



        #ctnModal {

            width: 40%;

            top: 0;

            display: flex;

            flex-direction: column;

            background: white;

            border-radius: 20px;

            position: relative;

        }



        #fade {

            position: fixed;

            top: 0;

            display: none;

            height: 100vh;

            align-items: center;

            justify-content: center;

            width: 100%;

            z-index: 1;

            background: rgba(0, 0, 0, 0.6);

        }



        #close {

            cursor: pointer;

        }



        #tituloModal {

            display: flex;

            justify-content: space-between;

            align-items: center;

            padding: 20px;

            padding-left: 40px;

            color: #88c4c7;

            font-weight: 600;

            border-radius: 20px 20px 0px 0;

            background-color: white;

            width: 100%;

            font-size: 20px;

        }



        .fechar-modal {

            text-align: center;

            background-color: #88c4c7;

            padding: 14px;

            width: 100%;

            font-size: 17px;

            color: white;

            border-radius: 0px 0px 20px 20px;

        }



        .fechar-modal2 {

            text-align: center;

            background-color: transparent;

            padding: 14px;

            width: 100%;

            font-size: 17px;

            color: #88c4c7;

        }



        @media (max-width: 340px) {

            #tituloModal {

                display: flex;

                justify-content: left;

                padding: 15px !important;

                padding-left: 15px !important;

                color: #7daa1b;

                font-weight: 600;

                border-radius: 7px 7px 0px 0;

                background-color: white;

                width: 100%;

                font-size: 20px;

            }



            .modal-mobile-1 {

                padding-top: 10px !important;

                padding-left: 15px !important;

                padding-right: 15px !important;

                padding-bottom: 10px !important;

            }

        }



        @media (max-width: 1100px) {

            #ctnModal {

                width: 95%;

            }

        }



        @media (min-width: 1450px) {

            #ctnModal {

                width: 30%;

            }

        }





        .button-area-das-professoras {

            margin: 6px 0;

            display: inline-block;

            color: #fff !important;

            padding: 5px 15px !important;

            background-color: #7DAA1D !important;

            border-radius: 20px;

        }



        @media (min-width: 992px) {

            .flex-box-professora-menu {

                align-items: center;

            }



            .button-area-das-professoras {

                font-size: 16px;

                margin: 0 0 0 8px;

                padding: 3px 12px !important;

            }

        }



        .cc-btn {

            background-color: #7DAA1D !important;

        }



        .fundo-promocao {

            background: #9f53ad;

        }



        .card-top-livros img {

            width: 100%;

        }



        @media (min-width: 998.98px) {

            .card-top-livros img {

                max-height: 311px;

            }

        }



        .link-books {

            cursor: pointer;

        }



        .fundo-bookweek {

            background-color: black !important;

        }

    </style>
</link></meta></meta></head>
<body>
<span id="activeFadeSpan" style="display:none"></span>
<noscript>
<iframe height="0" src="https://www.googletagmanager.com/ns.html?id=GTM-K8DL3RJ" style="display: none; visibility: hidden" width="0"></iframe>
</noscript>
<style>

    .spinAnimation {

        -webkit-animation: rotate360 4s linear infinite;

        -moz-animation: rotate360 4s linear infinite;

        animation: rotate360 4s linear infinite;

    }



    @-moz-keyframes rotate360 {

        100% {

            -moz-transform: rotate(360deg);

        }

    }



    @-webkit-keyframes rotate360 {

        100% {

            -webkit-transform: rotate(360deg);

        }

    }



    @keyframes rotate360 {

        100% {

            -webkit-transform: rotate(360deg);

            transform: rotate(360deg);

        }

    }



    @media (max-width: 1200px) {

        .livrosporidadePC {

            display: none;

        }

    }

</style>
<!--END CAROUSEL TOPO AREA-->
<div class="sticky-top">
<div class="background-carousel-topo">
<div class="carousel carousel-touch slide" data-ride="carousel" id="carouselExampleControls2">
<div class="alinha-centro">
<div class="carousel-inner container-carousel">
<div class="carousel-item active">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-mover-truck alinha-centro-icon"></i>Frete grátis para 2 ou mais unidades

                            </span>
</div>
<div class="carousel-item">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-credit-card alinha-centro-icon"></i>Pagamento parcelado em até 3 vezes

                            </span>
</div>
<div class="carousel-item">
<span class="itemcarousel font-jellee">
<i class="font-20-icon flaticon-shield-1 alinha-centro-icon-ssl"></i>Site Seguro com certificado SSL

                            </span>
</div>
<div class="onlypc" style="position:unset;">
<a class="carousel-control-prev" data-slide="prev" href="#carouselExampleControls2" role="button">
<img src="/Content/site_novo/img/arrow-left.svg" style="opacity: 0.5; "/>
<span class="sr-only"></span>
</a>
<a class="carousel-control-next" data-slide="next" href="#carouselExampleControls2" role="button">
<img src="/Content/site_novo/img/arrow-right.svg" style="opacity: 0.5;"/>
<span class="sr-only"></span>
</a>
</div>
</div>
</div>
</div>
</div>
<nav class="navbar navbar-expand-lg navbar-light shadow-sm bg-white" style="transition: all 0.2s ease; z-index:1020 !important;">
<div class="container">
<button aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation" class="navbar-toggler menuquadrado onlymobile" data-target="#navbarText" data-toggle="collapse" style="padding-top:10px; padding-bottom:10px;" type="button">
<svg class="stroke-svg stroke-svg1" height="25" id="icoOpen" width="25">
<path d="M0,5 30,5" stroke-width="2"></path>
<path d="M0,14 30,14" stroke-width="2"></path>
<path d="M0,23 30,23" stroke-width="2"></path>
</svg>
</button>
<a class="navbar-brand" href="/">
<img class="img-fluid pc-img" src="/Content/site_novo/img/logo-tibi.svg"/>
</a>
<a class="cart-a-mobile" href="/Order/New">
<span class="color-link nav-link onlymobile" style="padding-left:0px !important; padding-right:0px !important;">
<i class="flaticon-shopping-cart" style="font-size:22px; position:relative; top:2px;"></i>
<span class="onlymobile align-top" style="font-size:13px;

                  background-color:#7DAA1D; color:white; border-radius:100%;

                  padding: 5px; padding-top:2px; padding-bottom:2px; position:relative; right:10px; bottom:4px;  ">0</span>
</span>
</a>
<button aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation" class="navbar-toggler onlypc" data-target="#navbarText" data-toggle="collapse" type="button">
<span class="navbar-toggler-icon"></span>
</button>
<div class="collapse navbar-collapse padding-menu collapse-teste1" id="navbarText">
<style>

                        .bg-graymobile {

                            background-color: #f7f7f7;

                        }



                        .group-adminLinks2 {

                            -moz-transition: height .4s;

                            -ms-transition: height .4s;

                            -o-transition: height .4s;

                            -webkit-transition: height .4s;

                            transition: height .4s;

                            height: 0;

                            overflow: hidden;

                        }





                        .admin-group-pc {

                            color: rgb(128, 128, 128);

                            padding: 4px 13px;

                            background-color: #f7f7f7;

                            border-radius: 7px;

                            margin: 4px 0;

                            transition: transform .2s linear;

                        }



                            .admin-group-pc:hover {

                                background-color: #e6e5e5;

                                transform: translateX(5px);

                            }



                            .admin-group-pc i {

                                font-size: 13px;

                            }



                        .group-adminLinks2 a:first-child p {

                            margin-top: 8px;

                        }



                        .group-adminLinks2 a:last-of-type p {

                            margin-bottom: 8px;

                        }



                        @media (max-width:998.98px) {

                            a.color-link {

                                padding-left: 16px !important;

                            }



                            span.color-link {

                                padding-left: 16px;

                            }



                            .group-adminLinks {

                                -moz-transition: height .4s;

                                -ms-transition: height .4s;

                                -o-transition: height .4s;

                                -webkit-transition: height .4s;

                                transition: height .4s;

                                height: 0;

                                overflow: hidden;

                            }





                                .group-adminLinks span.color-link {

                                    padding-left: 26px;

                                    padding-top: 15px;

                                    padding-bottom: 15px;

                                }





                                .group-adminLinks span.color-link {

                                    background: #eaeaea;

                                }



                            .nav-mobile-style {

                                padding-bottom: 0 !important;

                            }



                            .leftprofessora {

                                margin-left: 16px !important;

                            }



                            .nav-mobile-style a:focus span {

                                background-color: #e0e0e0;

                            }

                        }

                    </style>
<style>

                        .bookweeklink {

                            color: #fff !important;

                            background: black;

                            padding: 3px 20px;

                            border-radius: 8px;

                        }



                            .bookweeklink:hover {

                                color: #fff !important;

                            }



                        @media(max-width:991.99px) {

                            .bookweeklink {

                                padding: 0.5rem;

                            }

                        }

                    </style>
<style>

                        @media (max-width: 1399px) {

                            .livrosporidadePC2 {

                                display: none;

                            }

                        }

                    </style>
<ul class="navbar-nav mr-auto flex-box-professora-menu">
<li class="nav-item nav-mobile-style" style=" border-bottom:unset !important; padding-top:16px;  ">
<a href="/Account/Login?returnUrl=%2F">
<span class="color-link onlymobile nav-link size-varela bg-graymobile" style="        border-radius: 20px; padding-top: 8px;padding-bottom: 12px;padding-left: 15px;">
<i class="flaticon-user" style="font-size:24px;"></i> <span>Acessar conta</span>
</span>
</a>
</li>
<li class="nav-item nav-mobile-style" style=" border-bottom:unset !important;">
<a class="nav-link color-link size-varela" href="/Home/AllBooks" style="color:rgb(128, 128, 128)">Todos os livros</a>
</li>
<li class="nav-item dropdown livrosporidadePC2">
<a aria-expanded="false" aria-haspopup="true" class="nav-link link-idade color-link onlypc size-varela" href="#" id="navbarDropdown " role="button" style="color:rgb(128, 128, 128)">

                                Livros por idade

                                <i class="fas fa-chevron-down icone-link" style=" font-size:14px;"></i>
</a>
<div aria-labelledby="navbarDropdown" class="dropdown-menu shadow-tibi-2 testedropdown">
<div class="row">
<div class="col-lg-4">
<a class="idade-hover" href="/Home/PrimeirosLivros">
<div class="idade-container">
<div class="imgidade img-1"></div>
</div>
<p style="

                            color: gray;

                            font-family: 'jellee', sans-serif;

                            font-size: 18px;

                            text-align: center;

                            margin-bottom: 3px;

                            margin-top: 15px;

                          ">

                                                0-3 anos

                                            </p>
<p style="

                            color: gray;



                            font-size: 17px;

                            text-align: center;



                            margin-bottom: 0;

                            padding-bottom: 0;

                          ">

                                                Primeiros livros

                                            </p>
</a>
</div>
<div class="col-lg-4">
<a class="idade-hover" href="/Home/LeitoresCuriosos">
<div class="idade-container">
<div class="imgidade img-2"></div>
</div>
<p style="

                            color: gray;

                            font-family: 'jellee', sans-serif;

                            font-size: 18px;

                            text-align: center;

                            margin-bottom: 3px;

                            margin-top: 15px;

                          ">

                                                3-6 anos

                                            </p>
<p style="        color: gray;

        font-size: 17px;

        text-align: center;

        margin-bottom: 0;

        padding-bottom: 0;

">

                                                Leitores curiosos

                                            </p>
</a>
</div>
<div class="col-lg-4">
<a class="idade-hover" href="/Home/AmoLer">
<div class="idade-container">
<div class="imgidade img-3"></div>
</div>
<p style="        color: gray;

        font-family: 'jellee', sans-serif;

        font-size: 18px;

        text-align: center;

        margin-bottom: 3px;

        margin-top: 15px;

">

                                                6+ anos

                                            </p>
<p style="

                          color: gray;



                          font-size: 17px;

                          text-align: center;



                          margin-bottom: 0;

                          padding-bottom: 0;

                        ">

                                                Amo ler

                                            </p>
</a>
</div>
</div>
</div>
</li>
<li class="nav-mobile-style" style=" border-bottom:unset !important;">
<a class="nav-link color-link onlymobile size-varela" href="/Home/LivrosPorIdade" style="color:rgb(128, 128, 128)">

                                Livros por idade

                                <i class="fas fa-chevron-right icone-link" style="padding-left: 5px;"></i>
</a>
</li>
<li class="nav-item nav-mobile-style" style=" border-bottom:unset !important;">
<a class="nav-link color-link size-varela" href="/Professoras/Lp" style="color:rgb(128, 128, 128)">Professoras</a>
</li>
<li class="nav-item nav-mobile-style" style=" border-bottom:unset !important;">
<a class="nav-link color-link size-varela" href="/pnld/ameninadacabecaquadrada" style="color:rgb(128, 128, 128)">PNLD</a>
</li>
<li class="nav-item nav-mobile-style" style=" border-bottom:unset !important;">
<a class="nav-link color-link size-varela" href="/sobreaautora" style="color:rgb(128, 128, 128)">Sobre a autora</a>
</li>
<li class="nav-item nav-mobile-style onlymobile" style=" border-bottom:unset !important;">
<a class="nav-link color-link size-varela" href="https://api.whatsapp.com/send?phone=71996069038&amp;text=&amp;source=&amp;data=&amp;app_absent=" style="color:rgb(128, 128, 128);font-family: 'varela';color: rgba(0, 0, 0, 0.5);font-weight: bold;border-top: 1px solid #e8e8e8;" target="_blank">
<span style="

    padding-top: 11px;

    display: block;

">

                                    Contato para empresas (revenda): <br/><span style="

    text-decoration: underline;

    color: #2e86c5b3;

">(71) 99606-9038</span>
</span>
</a>
</li>
</ul>
<a aria-expanded="false" aria-haspopup="true" class="onlypc" href="/Account/Login?returnUrl=%2F" id="navbarDropdown2 " role="button" style="color: rgb(128, 128, 128)">
<span class="color-link nav-mobile-style-acessar nav-link size-varela">

                        Acesse sua conta

                    </span>
</a>
<a class="cart-a-pc" href="/Order/New">
<span class="color-link nav-link cart-pc" style="padding-right:0; margin-right:0;">
<i class="flaticon-shopping-cart onlypc" style="font-size:22px;  "></i>
<span class="onlypc align-top" style="font-size:13px;

                  background-color:#7DAA1D; color:white; border-radius:100%;

                  padding: 5px; padding-top:2px; padding-bottom:2px; position:relative; right:7px; bottom:5px;  ">0</span>
</span>
</a>
</div>
</div>
</nav>
</div>
<script>

            function showLoadIcon(element) {

                [...document.getElementsByClassName("spinAnimation")].map(n => n && n.remove());

                let span = element.children[0];

                if (span.children[2]) {

                    return;

                }

                let icon = document.createElement("i");

                icon.classList.add("fas", "fa-spinner", "spinAnimation");

                span.appendChild(icon);

            }

        </script>
<script>

        var copyTextareaBtn = document.querySelector(".copyRastreioCupom");

        if (copyTextareaBtn) {

            copyTextareaBtn.addEventListener('click', function (event) {



                navigator.clipboard.writeText("BOASVINDAS10");



                document.querySelector(".copyRastreioCupom").style.background = "#139692";

            });

        }





    </script>
<style>

    .whatsappfixed {

        border-radius: 4px;

        bottom: 20px;

        position: fixed;

        right: 20px;

        padding: 8px;

        text-align: center;

        z-index: 9999;

    }



    @media (max-width: 767.98px) {

        .whatsappfixed {

            right: 5px;

        }

    }



    .blackfriday-bg.bg-onlyimage {

        width: 100%;

        height: 400px;

        background-position: 57%;

        background-repeat: no-repeat;

        background-size: cover;

    }



    .blackfriday-bg {

        background: url(../../Content/site_novo/img/black-banner.png);

    }



    @media (max-width: 1200px) {

        .blackfriday-bg.bg-onlyimage {

            height: 340px;

        }

    }



    @media (max-width: 991.98px) {

        .blackfriday-bg {

            background: url(../../Content/site_novo/img/blackfriday-mobile.png);

        }



            .blackfriday-bg.bg-onlyimage {

                height: 450px;

                background-position: 50%;

            }

    }



    @media (max-width: 380.98px) {



        .blackfriday-bg.bg-onlyimage {

            height: 370px;

        }

    }



    .cards-top-book {

        background-color: white;

        padding-top: 50px;

        padding-bottom: 50px;

    }



    @media (max-width: 991.98px) {



        .cards-top-book {

            background-color: white;

            padding-top: 25px;

            padding-bottom: 50px;

        }

    }

</style>
<style>

    #carouselHomeBanner .carousel-item {

        width: 100%

    }

</style>
<div class="carousel slide onlypc" data-ride="carousel" id="carouselHomeBanner">
<div class="carousel-inner">
<div class="carousel-item">
<a href="/Books/Acreditar" style="background-image: url('../../Content/site_novo/img/banner-desk-acreditar-hoje.png'); display: block; width: 100%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item active">
<a href="/Books/AUltimaGota" style="background-image: url('../../Content/site_novo/img/a-ultima-gota/banner-desk-a-ultima-gota-hoje.png'); display: block; width: 100%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a href="/Books/ChorarEComoChover" style="background-image: url('../../Content/site_novo/img/livro-infantil-emocoes-chorar-e-como-chover.webp'); display: block; width: 100%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a href="/Books/Docura" style="background-image: url('../../Content/site_novo/img/premio-jabuti-livro-docura-emilia-nunez-anna-cunha.webp'); display: block; width: 100%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a href="/Books/AMeninaDaCabecaQuadrada" style="background-image: url('../../Content/site_novo/img/adocao-a-menina-da-cabeca-quadrada.webp'); display: block; width: 100%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
</div>
<div class="container">
<a class="carousel-control-prev" data-slide="prev" href="#carouselHomeBanner" role="button">
<img src="/Content/site_novo/img/arrow-left.svg"/>
<span class="sr-only">Anterior</span>
</a>
<a class="carousel-control-next" data-slide="next" href="#carouselHomeBanner" role="button">
<img src="/Content/site_novo/img/arrow-right.svg"/>
<span class="sr-only">Próximo</span>
</a>
</div>
</div>
<style>

    @media(min-width:500px) {

        .carousel-mobile {

            height: 400px !important;

        }

    }



    @media(min-width:700px) {

        .carousel-mobile {

            height: 580px !important;

        }

    }

</style>
<div class="carousel slide onlymobile" data-ride="carousel" id="carouselHomeMobileBanner" style="margin-bottom: -1px">
<div class="carousel-inner">
<div class="carousel-item">
<a class="carousel-mobile" href="/Books/Acreditar" style="background-image: url('../../Content/site_novo/img/banner-mobile-acreditar-site-.png'); display: block; width: 101%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item active">
<a class="carousel-mobile" href="/Books/AUltimaGota" style="background-image: url('../../Content/site_novo/img/a-ultima-gota/BANNER-MOBILE-LANÇAMENTO-A-ULTIMA-GOTA.png'); display: block; width: 101%; background-position: 50%; background-repeat: no-repeat; background-size: contain; background-color:#CDE7E4; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a class="carousel-mobile" href="/Books/ChorarEComoChover" style="background-image: url('../../Content/site_novo/img/livro-para-falar-de-sentimentos-chorar-e-como-chover.webp'); display: block; width: 101%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a class="carousel-mobile" href="/Books/Docura" style="background-image: url('../../Content/site_novo/img/premio-jabuti-livro-imagem-docura-anna-cunha-emilia-nunez.webp'); display: block; width: 101%; background-position: 50%; background-repeat: no-repeat; background-size: cover; height: 300px ">
</a>
</div>
<div class="carousel-item">
<a class="carousel-mobile" href="/Books/AMeninaDaCabecaQuadrada" style="        background-image: url('../../Content/site_novo/img/equilibrio-do-uso-das-telas-livro-infantil.webp');

        display: block;

        width: 101%;

        background-position: 50%;

        background-repeat: no-repeat;

        background-size: cover;

        height: 300px

">
</a>
</div>
</div>
<div class="container">
<a class="carousel-control-prev" data-slide="prev" href="#carouselHomeMobileBanner" role="button">
<img src="/Content/site_novo/img/arrow-left.svg"/>
<span class="sr-only">Anterior</span>
</a>
<a class="carousel-control-next" data-slide="next" href="#carouselHomeMobileBanner" role="button">
<img src="/Content/site_novo/img/arrow-right.svg"/>
<span class="sr-only">Próximo</span>
</a>
</div>
</div>
<style>

    .banner-lancamento {

        background: url(../../Content/site_novo/img/a-menina-da-cabeca-quadrada-equilibrio-do-uso-do-celular.webp);

        margin-top: 50px;

        background-position: 49%;

        background-repeat: no-repeat;

        background-size: cover;

        width: 100%;

        border-radius: 20px;

        height: 400px;

        display: flex;

        align-items: center;

    }



        .banner-lancamento > div {

            margin-left: 116px;

            margin-top: 25px;

            color: #fff;

        }



            .banner-lancamento > div h2.lancamento {

                font-size: 18px;

            }



            .banner-lancamento > div > h2 {

                margin-bottom: 36px;

                margin-top: 36px;

            }



            .banner-lancamento > div h2.titulo-lancamento {

                max-width: 400px;

                font-size: 32px;

            }



    @media(max-width:1201px) {

        .banner-lancamento > div {

            margin-left: 45px;

        }



        .banner-lancamento {

            background-position: 52%;

        }

    }



    @media(max-width: 991.98px) {



        .banner-lancamento {

            background: unset;

            flex-direction: column;

            align-items: unset;

            background-color: #ff4933;

            margin-top: 25px;

            width: 100%;

            height: auto;

            padding-top: 40px;

            padding-bottom: 40px;

            border-radius: 20px;

        }



            .banner-lancamento > div {

                margin: 0 65px;

                display: flex;

                flex-direction: column;

                align-items: center;

            }



                .banner-lancamento > div h2.lancamento {

                    font-size: 16px;

                }



                .banner-lancamento > div h2.titulo-lancamento {

                    max-width: 100%;

                    font-size: 28px;

                    margin-bottom: 32px;

                    text-align: center;

                }



            .banner-lancamento > img {

                margin-left: 0;

                margin-top: 0px;

                width: 100%;

                position: relative;

                top: 40px;

                border-radius: 20px;

            }



            .banner-lancamento > div > div .lancamento {

                font-size: 23px !important;

                margin-bottom: 22px !important;

            }



            .banner-lancamento > div > div .titulo-lancamento {

                font-size: 23px !important;

                margin-bottom: 26px !important;

            }



            .banner-lancamento > div > div a {

                padding: 12px 50px !important;

            }

    }



    @media(max-width: 580px) {

        .banner-lancamento > div {

            margin: 0 30px;

        }

    }



    @media(max-width: 370px) {

        .banner-lancamento > div > div .titulo-lancamento {

            font-size: 20px !important;

        }

    }

</style>
<style>

    .banner-lancamento2 {

        background: url(../../Content/site_novo/img/a-ultima-gota/banner-lançamento-desk-a-ultima-fota.png);

        margin-top: 50px;

        background-position: 52%;

        background-repeat: no-repeat;

        background-size: cover;

        width: 100%;

        border-radius: 20px;

        height: 400px;

        display: flex;

        align-items: center;

    }



        .banner-lancamento2 > div {

            margin-left: 116px;

            margin-top: 54px;

            color: #fff;

        }



            .banner-lancamento2 > div h2.lancamento {

                font-size: 18px;

            }



            .banner-lancamento2 > div > h2 {

                margin-bottom: 76px;

                margin-top: 36px;

            }



            .banner-lancamento2 > div h2.titulo-lancamento {

                max-width: 400px;

                font-size: 38px;

            }



    @media(max-width:1201px) {

        .banner-lancamento2 > div {

            margin-left: 45px;

        }



        .banner-lancamento2 {

            background-position: 52%;

        }

    }



    @media(max-width: 991.98px) {



        .banner-lancamento2 {

            background: unset;

            flex-direction: column;

            align-items: unset;

            background-color: #C7E3DA;

            margin-top: 25px;

            width: 100%;

            height: auto;

            padding-top: 40px;

            padding-bottom: 40px;

            border-radius: 20px;

        }



            .banner-lancamento2 > div {

                margin: 0 65px;

                display: flex;

                flex-direction: column;

                align-items: center;

            }



                .banner-lancamento2 > div h2.lancamento {

                    font-size: 16px;

                }



                .banner-lancamento2 > div h2.titulo-lancamento {

                    max-width: 100%;

                    font-size: 28px;

                    margin-bottom: 32px;

                    text-align: center;

                }



            .banner-lancamento2 > img {

                margin-left: 0;

                margin-top: 0px;

                width: 100%;

                position: relative;

                top: 40px;

                border-radius: 20px;

            }



            .banner-lancamento2 > div > div .lancamento {

                font-size: 23px !important;

                margin-bottom: 22px !important;

            }



            .banner-lancamento2 > div > div .titulo-lancamento {

                font-size: 23px !important;

                margin-bottom: 26px !important;

            }



            .banner-lancamento2 > div > div a {

                padding: 12px 50px !important;

            }

    }



    @media(max-width: 580px) {

        .banner-lancamento2 > div {

            margin: 0 30px;

        }

    }



    @media(max-width: 370px) {

        .banner-lancamento2 > div > div .titulo-lancamento {

            font-size: 20px !important;

        }

    }

</style>
<div class="container">
<a class="banner-lancamento2 hovercard" href="/Books/AUltimaGota" style="cursor:pointer">
<div>
<h2 class="lancamento" style="margin: 0 !important;background-color: #7A4585;display: inline-block;color: #fff;border-radius: 20px;padding: 6px 16px;font-family: jellee;">

                Lançamento

            </h2>
<h2 class="titulo-lancamento font-jellee" style="

        color: #0B427F;

        ">

                A Última Gota

            </h2>
<div class="onlypc" style="background-color: #E56036;padding: 15px 60px;color: #fff;border-radius: 30px;text-align: center;font-size: 17px;display: inline-block;">COMPRAR</div>
<div class="onlymobile" style="background-color: #E56036;padding: 15px 60px;color: #fff;border-radius: 30px;text-align: center;font-size: 17px;display: inline-block;">COMPRAR</div>
</div>
<img class="onlymobile" loading="lazy" src="/Content/site_novo/img/a-ultima-gota/banner-mobile-a-ultima-gota.png">
</img></a>
</div>
<div class="container">
<a class="banner-lancamento hovercard" href="/Books/AMeninaDaCabecaQuadrada" style="cursor:pointer">
<div>
<h2 class="lancamento" style="margin: 0 !important; background-color: #FFD712; display: inline-block; border-radius: 20px; padding: 6px 16px; font-family: jellee; ">

                Best-Seller

            </h2>
<h2 class="titulo-lancamento font-jellee" style="    color: #fff

        ">

                A Menina da Cabeça Quadrada

            </h2>
<div style=" background-color: #FFD712; padding: 15px 60px; color: #fff; border-radius: 30px; text-align: center; font-size: 17px; display: inline-block; ">COMPRAR</div>
</div>
<img class="onlymobile" loading="lazy" src="/Content/site_novo/img/livro-sobre-vicio-no-celular-a-menina-da-cabeca-quadrada.webp">
</img></a>
</div>
<div class="cards-top-book">
<div class="container">
<div class="row">
<div class="col-lg-6">
<a class="" href="/Home/AllBooks">
<div class="hovercard todososlivros-home" style=" background-repeat: no-repeat; background-size:cover; border-radius:20px;   height:380px; background-position: 50%;">
<div style=" text-align: center;">
<h2 class="font-jellee" style="color: white; font-size: 28px; padding-top: 40px;">

                                Todos os livros

                            </h2>
</div>
<div align="center" class="onlymobile" style="margin-top:30px;">
<img class="img-fluid" loading="lazy" src="/Content/site_novo/img/banner-categoria-todososlivrosmobile2.png">
</img></div>
</div>
</a>
</div>
<div class="col-lg-6">
<a class="" href="/Home/Colecoes">
<style>

                        @media (max-width: 992px) {

                    .colecoes-home {

                    background: #61D980 !important;

                    height: auto !important;

                    padding-top: 10px;

                    padding-bottom: 10px;

                    }



                    }

                  </style>
<div class="hovercard perso-card colecoes-home" style="        background-repeat: no-repeat;

            background-size: cover;

            border-radius: 20px;

            background-color: #61D980;

            height: 380px;

            background: url('../../Content/site_novo/img/banner-categoria-colecoes.webp');

            background-position: 50%;">
<div style=" text-align: center; ">
<h2 class="font-jellee" style="color: white; font-size: 28px; padding-top: 40px;">

                                Coleções

                            </h2>
</div>
<div align="center" class="onlymobile" style="margin-top:30px;">
<img class="img-fluid" loading="lazy" src="/Content/site_novo/img/banner-categoria-mobile-colecoes.webp">
</img></div>
</div>
</a>
</div>
</div>
</div>
</div>
<div class="bg-icones-home" style="background-color:white; ">
<div class="container" style="display: flex; justify-content: center;">
<div class="row">
<div class="col-lg-3 col-md-6 col-sm-12">
<div class="flexbox-icones">
<img class="img-fluid" loading="lazy" src="/Content/site_novo/img/presente.svg" style="width: 70px;"/>
<span class="font-jellee icones-fonte-mobile" style="padding-left: 25px; font-size: 18px; color: gray; ">

                        Inesquecível <br>
<span class="font-varela" style="    font-size: 15px;

            color: rgb(143, 143, 143);

    ">

                            Um presente para toda vida do

                            seu pequeno leitor

                        </span>
</br></span>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-12">
<div class="flexbox-icones">
<img class="img-fluid" loading="lazy" src="/Content/site_novo/img/cartao.svg" style="width:70px;"/>
<span class="font-jellee icones-fonte-mobile" style="padding-left:25px; font-size:18px; color:gray;">

                        Compre em até 3x <br/>
<span class="font-varela" style="font-size:15px; color:rgb(143, 143, 143);">

                            Pagamento via boleto ou cartão

                            de crédito em até 3x

                        </span>
</span>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-12 padcaminhao">
<div class="flexbox-icones">
<img class="img-fluid img-icones-home" loading="lazy" src="/Content/site_novo/img/caminhao.svg" style="width:70px; "/>
<span class="font-jellee icones-fonte-mobile" style="padding-left:25px; font-size:18px; color:gray;">

                        Receba em casa <br/>
<span class="font-varela" style="font-size:15px; color:rgb(143, 143, 143);">

                            Entrega para todo o Brasil com

                            garantia de qualidade Tibi

                        </span>
</span>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-12">
<div class="flexbox-icones">
<img class="img-fluid img-icones-home" loading="lazy" src="/Content/site_novo/img/seguro.svg" style="width:70px; "/>
<span class="font-jellee icones-fonte-mobile" style="        padding-left: 25px;

        font-size: 18px;

        color: gray;">

                        Site Seguro <br/>
<span class="font-varela" style="font-size:15px; color:rgb(143, 143, 143);">

                            Certificado SSL e compra protegida

                        </span>
</span>
</div>
</div>
</div>
</div>
</div>
<div class="bg-section-livros">
<div class="container">
<div class="title-section" style="padding-bottom:60px;">
<h1 class="font-jellee h1-section-livros" style="text-align: center; color:rgb(117, 117, 117);">

                Livros em destaque

            </h1>
</div>
<div class="row">
<div class="col-lg-4 col-md-6">
<a href="/Books/AUltimaGota" id="GOT01" onclick="dataLayerProductClick('GOT01')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<div class="elemento shadow-sm fundo-promocao" style=" background: #b868c7;">Lançamento</div>
<img alt="a-menina-da-cabeca-quadrada" class="img-fluid" loading="lazy" src="/Content/site_novo/img/a-ultima-gota/produtos-a-ultima-gota.png" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                   A Última Gota

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    Um livro infantil cativante que desperta a consciência ambiental, promove o entendimento científico do ciclo da água e incentiva ações sustentáveis em crianças e adultos.

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                5-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4 col-md-6">
<a href="/Books/Acreditar" id="ACR01" onclick="dataLayerProductClick('ACR01')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<div class="elemento shadow-sm fundo-promocao" style=" background: #789b1f;">Lançamento</div>
<img alt="capaz" class="img-fluid" loading="lazy" src="/Content/site_novo/img/capa_do_livro_acreditar_emilia_nunez.png" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                    Acreditar

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    Um livro para sua criança descobrir o que a torna especial! Preguiça vive uma emocionante jornada de autodescoberta enquanto dá o seu melhor para entender o que o faz único.

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                2-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4 col-md-6">
<a href="/Books/IrmaoDaJaquinha" id="JAQ06" onclick="dataLayerProductClick('JAQ06')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<div class="elemento shadow-sm fundo-promocao" style=" background: #b868c7;">Lançamento</div>
<img alt="a-menina-da-cabeca-quadrada" class="img-fluid" loading="lazy" src="/Content/site_novo/img/irmao-jaquinha/produto-IJ-home.webp" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                    O Irmãozinho da Jaquinha

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    O novo bebê chegou… e Jaquinha não sabia se chorava ou se escondia! Uma história delicada sobre ciúmes, descobertas e o poder do amor que cresce junto com a família.

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                2-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4 col-md-6">
<a href="/Books/Capaz" id="CAP01" onclick="dataLayerProductClick('CAP01')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<div class="elemento shadow-sm fundo-promocao" style="">Lançamento</div>
<img alt="capaz" class="img-fluid" loading="lazy" src="/Content/site_novo/img/capaz/produto-capaz.png" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                   Capaz

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    Um livro infantil inspirador que celebra a diversidade, a inclusão e a construção de uma sociedade anticapacitista, especialmente nas escolas.

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                5-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4 col-md-6">
<a href="/Books/AmeninadaCabecaQuadrada" id="MCQ01" onclick="dataLayerProductClick('MCQ01')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<div class="elemento shadow-sm" style=" background: #EE833B;">Best-Seller</div>
<img alt="a-menina-da-cabeca-quadrada" class="img-fluid" loading="lazy" src="/Images/livros/a-menina-da-cabeca-quadrada/capa-a-menina-da-cabeca-quadrada-emilia-nunez.jpg" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                    A Menina da Cabeça Quadrada

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    De tanto usar celular, tablet e computador, Cecília acordou

                    com a cabeça quadrada! Uma história para ajudar uma geração

                    superconectada a aproveitar a vida lá fora.

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                2-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4 col-md-6">
<a href="/Books/Lua" id="NAN05" onclick="dataLayerProductClick('NAN05')">
<div class="card-top-livros" style="background-color:#FFE26A; padding:0; margin:0; text-align: center; border-radius:20px 20px 0px 0px;">
<img alt="cartas-para-o-futuro" class="img-fluid" loading="lazy" src="/Images/livros/cartas-para-o-futuro/capa-cartas-para-o-futuro-lua-emilia-nunez.jpg" style=" border-radius:20px 20px 0px 0px;"/>
</div>
<div class="card-bottom-livros shadow-tibi">
<div class="container">
<h2 class="font-jellee" style="font-size:18px; color:rgb(97, 97, 97);">

                   Cartas para o Futuro

                </h2>
<p style="font-size:15px; color:rgb(97, 97, 97);">

                    As crianças podem ter voz ativa nas questões do mundo? Através do poder das cartas, a pequena Lua nos mostra que sim!

                </p>
<div style="position: absolute;

                bottom: 0;



                width: 90%;



                ">
<hr/>
<div class="d-flex justify-content-between align-items-center" style="margin-top:15px; margin-bottom:15px;">
<div class="btn-group">
<span class="" style="color:rgb(97, 97, 97);">

                                4-10 Anos

                            </span>
</div>
<div class="price-book"></div>
</div>
</div>
</div>
</div>
</a>
</div>
</div>
<div class="container">
<div align="center" class="btn-end-livros" style="margin-top: 40px; margin-bottom:90px;  ">
<a class="conheca-mobile-2" href="/Home/AllBooks" style="background-color:#80499e; padding:15px 20px; color:white; border: 1px solid #80499e; border-radius:30px; text-align: center;">

                    CONHEÇA TODOS OS LIVROS

                </a>
</div>
</div>
</div>
</div>
<div class="container">
<div class="bg-emilia-home">
<div class="container">
<div class="row">
<div class="col-lg-6 col-md-0 col-sm-0 onlypc"></div>
<div class="col-lg-5 col-md-12 col-sm-12">
<div class="padding-mobile-home">
<h1 class="font-jellee titulo-bg-emilia-home" style="font-size:30px;  padding-bottom: 10px; padding-left:20px; color:#80499E;">

                            Faça parte da nossa família leitora!

                        </h1>
<p class="subtitulo-bg-emilia-home" style="font-size:19px;  margin-top:10px; padding-left:20px;  color:#80499E;">

                            Junte-se a milhares de famílias em todo o Brasil e conheça histórias que divertem e inspiram crianças de todas as idades.

                        </p>
<div class="btn-end-livros button-emilia-home" style="margin-top: 40px;  margin-left:20px; ">
<a class="botao-mobile" href="https://www.instagram.com/maequele/" style="background-color:#80499e; padding:15px 20px; color:white;  border: 1px solid #80499e; border-radius:30px; text-align: center;" target="_blank">

                                CONHEÇA NOSSA HISTÓRIA

                            </a>
</div>
</div>
</div>
</div>
</div>
</div>
<div style="background-color:#FFFAB1; border-radius:0px 0px 20px 20px; ">
<div align="center" class="col-md-12 onlymobile" style="padding:0 !important; padding-top:38px !important;">
<img class="img-fluid" loading="lazy" src="/Content/site_novo/img/banner2mobile.png" style="border-radius:0px 0px 20px 20px;"/>
</div>
</div>
</div>
<div class="bg-section-livros" style="padding-top:52px; background-color:#fff; padding-bottom:52px;">
<div class="container">
<div class="title-section" style="padding-bottom:60px;">
<h1 class="font-jellee h1-section-livros" style="        text-align: center;

        margin: 0;

        color: rgb(117, 117, 117);

">

                Livros por idade

            </h1>
</div>
<div class="row">
<div class="col-lg-4" style="        margin-bottom: 20px;">
<a href="/Home/PrimeirosLivros">
<div class="hovercard" style="        background: url(../Content/site_novo/img/0-3.png);

        text-align: right;

        background-color: #bbe5b1;

        background-position: 50%;

        background-size: cover;

        background-repeat: no-repeat;

        border-radius: 20px;

        width: 100%;

        height: 400px;

    ">
<div style="text-align: center; padding-top: 30px;">
<div>
<h1 class="font-jellee" style="        font-size: 26px;

        color: white;

        padding-bottom: 10px;

        margin-bottom: 2px;

    ">

                                    0-3 anos

                                </h1>
<h3 style="        font-size: 19px;

        color: white;

        padding-bottom: 25px;

    ">

                                    Primeiros Livros

                                </h3>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4" style="margin-bottom: 20px;">
<a href="/Home/LeitoresCuriosos">
<div class="hovercard" style="        background: url(../Content/site_novo/img/4-6.png);

        background-color: #c2aae0;

        background-position: 50%;

        background-size: cover;

        background-repeat: no-repeat;

        background-size: cover;

        border-radius: 20px;

        height: 400px;

    ">
<div style="text-align: center; padding-top: 30px; ">
<div>
<h1 class="font-jellee" style="        font-size: 26px;

        color: white;

        padding-bottom: 10px;

        margin-bottom: 2px;

    ">

                                    3-6 anos

                                </h1>
<h3 style="

                        font-size: 19px;

                        color: white;

                        padding-bottom: 25px;

                      ">

                                    Leitores curiosos

                                </h3>
</div>
</div>
</div>
</a>
</div>
<div class="col-lg-4" style="margin-bottom: 20px;">
<a href="/Home/AmoLer">
<div class="hovercard" style="

                  background: url(../Content/site_novo/img/6.png);

                  background-color: #c2aae0;

                  background-position: 50%;

                  background-size: cover;

                  background-repeat: no-repeat;

                  background-size: cover;

                  border-radius: 20px;

                  height: 400px;

                ">
<div style="        text-align: center;

        padding-top: 30px;">
<div>
<h1 class="font-jellee" style="

                        font-size: 26px;

                        color: white;

                        padding-bottom: 10px;

                        margin-bottom: 2px;

                      ">

                                    6+ anos

                                </h1>
<h3 style="

                        font-size: 19px;

                        color: white;

                        padding-bottom: 25px;

                      ">

                                    Amo Ler!

                                </h3>
</div>
</div>
</div>
</a>
</div>
</div>
</div>
</div>
<div class="bg-testimonial" style="background-color:#9459BC;">
<div class="container">
<h1 class="titulo-testimonial font-jellee text-center" style="color:white;">

            Faça parte da nossa Família Leitora

        </h1>
<div class="carousel slide" data-ride="carousel" id="carouselExampleIndicators" style="display:flex; align-items: center; text-align: center; justify-content: center;">
<ol class="carousel-indicators">
<li class="active" data-slide-to="0" data-target="#carouselExampleIndicators"></li>
<li data-slide-to="1" data-target="#carouselExampleIndicators"></li>
<li data-slide-to="2" data-target="#carouselExampleIndicators"></li>
</ol>
<div class="carousel-inner">
<div class="carousel-item active">
<div class="testimonial-text">
<p class="testimonial-description">

                            "Cecília amou o livro personalizado e a personagem ficou igualzinha. Nos

                            divertimos muito aqui em casa, ela amou o livro"

                        </p>
<p style="font-style: italic; font-size:17px;">

                            Marcelo, pai da Cecília

                        </p>
</div>
</div>
<div class="carousel-item">
<div class="testimonial-text">
<p class="testimonial-description">

                            "A Menina da Cabeça Quadrada é incrível! Na minha escola fizemos até um teatro dele! É um grande aprendizado para os dias de hoje e também um incentivo às brincadeiras antigas para nossas crianças!"

                        </p>
<p style="font-style: italic; font-size:17px;">

                            Ludy Merlo

                        </p>
</div>
</div>
<div class="carousel-item">
<div class="testimonial-text">
<p class="testimonial-description">

                            "A Jaquinha fez muito sucesso aqui em casa! Aprendemos a trocar as mordidas por beijinhos."

                        </p>
<p style="font-style: italic; font-size:17px;">

                            Joana, mãe do Pedro

                        </p>
</div>
</div>
</div>
<a class="carousel-control-prev onlypc" data-slide="prev" href="#carouselExampleIndicators" role="button">
<img src="/Content/site_novo/img/arrow-left.svg" style="background-color:#9772A8; padding:10px 10px; border-radius:100%; "/>
<span class="sr-only">Previous</span>
</a>
<a class="carousel-control-next onlypc" data-slide="next" href="#carouselExampleIndicators" role="button">
<img src="/Content/site_novo/img/arrow-right.svg" style="background-color:#9772A8; padding:10px 10px; border-radius:100%; "/>
<span class="sr-only">Next</span>
</a>
</div>
</div>
</div>
<div class="whatsappfixed">
<a href="https://api.whatsapp.com/send?phone=5511998243336&amp;text=&amp;source=&amp;data=&amp;app_absent=" target="_blank">
<svg height="60" viewbox="0 0 39 39" width="60" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7 32.8l.6.3c2.5 1.5 5.3 2.2 8.1 2.2 8.8 0 16-7.2 16-16 0-4.2-1.7-8.3-4.7-11.3s-7-4.7-11.3-4.7c-8.8 0-16 7.2-15.9 16.1 0 3 .9 5.9 2.4 8.4l.4.6-1.6 5.9 6-1.5z" fill="#00E676"></path>
<path d="M32.4 6.4C29 2.9 24.3 1 19.5 1 9.3 1 1.1 9.3 1.2 19.4c0 3.2.9 6.3 2.4 9.1L1 38l9.7-2.5c2.7 1.5 5.7 2.2 8.7 2.2 10.1 0 18.3-8.3 18.3-18.4 0-4.9-1.9-9.5-5.3-12.9zM19.5 34.6c-2.7 0-5.4-.7-7.7-2.1l-.6-.3-5.8 1.5L6.9 28l-.4-.6c-4.4-7.1-2.3-16.5 4.9-20.9s16.5-2.3 20.9 4.9 2.3 16.5-4.9 20.9c-2.3 1.5-5.1 2.3-7.9 2.3zm8.8-11.1l-1.1-.5s-1.6-.7-2.6-1.2c-.1 0-.2-.1-.3-.1-.3 0-.5.1-.7.2 0 0-.1.1-1.5 1.7-.1.2-.3.3-.5.3h-.1c-.1 0-.3-.1-.4-.2l-.5-.2c-1.1-.5-2.1-1.1-2.9-1.9-.2-.2-.5-.4-.7-.6-.7-.7-1.4-1.5-1.9-2.4l-.1-.2c-.1-.1-.1-.2-.2-.4 0-.2 0-.4.1-.5 0 0 .4-.5.7-.8.2-.2.3-.5.5-.7.2-.3.3-.7.2-1-.1-.5-1.3-3.2-1.6-3.8-.2-.3-.4-.4-.7-.5h-1.1c-.2 0-.4.1-.6.1l-.1.1c-.2.1-.4.3-.6.4-.2.2-.3.4-.5.6-.7.9-1.1 2-1.1 3.1 0 .8.2 1.6.5 2.3l.1.3c.9 1.9 2.1 3.6 3.7 5.1l.4.4c.*******.8.8 2.1 1.8 4.5 3.1 7.2 *******.7.1 1 .2h1c.5 0 1.1-.2 1.5-.4.3-.2.5-.2.7-.4l.2-.2c.2-.2.4-.3.6-.5s.4-.4.5-.6c.2-.4.3-.9.4-1.4v-.7s-.1-.1-.3-.2z" fill="#FFF"></path>
</svg>
</a>
</div>
<footer class="footer-area section-gap">
<div class="container font-varela">
<div class="row">
<div class="col-lg-3 col-md-6 col-sm-6">
<div class="single-footer-widget">
<h4 class="font-jellee titulo-footer" style=" font-size:18px;  font-weight: 500;">

                        Sobre

                    </h4>
<h4 style=" font-size:15px;">
<a class="footer-link" href="https://www.instagram.com/editoratibi/" target="_blank">Quem somos</a>
</h4>
<h4 style=" font-size:15px;">
<a class="footer-link" href="https://www.instagram.com/maequele/" target="_blank">Blog - Mãe que lê</a>
</h4>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-6 font-varela">
<div class="single-footer-widget font-jellee">
<h4 class="titulo-footer" style=" font-size:18px; color:#5c376d; font-weight: 500;">

                        Tem alguma dúvida?

                    </h4>
<h4 class="font-varela" style=" font-size:15px;">
<a class="footer-link font-varela" href="/Home/Termo">Central de Ajuda</a>
</h4>
<h4 class="font-varela" style=" font-size:15px;">
<a class="footer-link font-varela" href="/Home/Termo">

                            Termos e Condições

                        </a>
</h4>
<h4 class="font-varela" style=" font-size:15px;">
<a class="footer-link font-varela" href="/Home/TrocasDevolucoes">

                            Trocas e Devoluções

                        </a>
</h4>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-6 social-widget font-varela">
<div class="single-footer-widget font-jellee">
<h4 class="titulo-footer" style="font-weight: normal !important; font-size:18px;">

                        Siga a Tibi

                    </h4>
<div class="footer-social d-flex align-items-center" style="margin-bottom:45px">
<a href="https://www.facebook.com/editoratibi/" style="margin-right:20px; font-size:22px;" target="_blank">
<i class="fab fa-facebook"></i>
</a>
<a href="https://www.instagram.com/editoratibi/" style="margin-right:15px; font-size:22px;" target="_blank">
<i class="fab fa-instagram"></i>
</a>
<a href="https://www.youtube.com/channel/UChLD4atS9CyQAjj6HJY9ZiA" style="margin-right:20px; font-size:22px; " target="_blank">
<i class="fab fa-youtube"></i>
</a>
<a href="https://br.pinterest.com/editoratibi/" style="margin-right:20px; font-size:22px;" target="_blank">
<i class="fab fa-pinterest"></i>
</a>
</div>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-6 font-varela">
<div class="single-footer-widget font-jellee">
<h4 class="titulo-footer" style=" font-size:18px; font-weight: 500;">

                        Fale com a gente

                    </h4>
<h4 style="font-family:'Varela Round'; font-size:15px;">
<a class="footer-link font-varela" href="mailto:<EMAIL>">
<i class="fa fa-envelope"></i> <EMAIL>

                        </a>
</h4>
<h4 style="font-family:'Varela Round'; font-size:15px;">
<a class="footer-link font-varela" href="https://api.whatsapp.com/send?phone=5571981065462&amp;text=&amp;source=&amp;data=&amp;app_absent=" style="color: #4d9dd6; text-decoration: underline" target="_blank"><i class="fab fa-whatsapp"></i> (71) 98106-5462</a>
</h4>
</div>
</div>
</div>
<div class="row">
<div class="col-lg-3 col-md-6 col-sm-6">
<div class="single-footer-widget font-jellee">
<h4 class="titulo-footer" style=" font-size:18px; font-weight: 500;">

                        Contato para empresas (revenda)

                    </h4>
<h4 style="font-family:'Varela Round'; font-size:15px;">
<a class="footer-link font-varela" href="https://api.whatsapp.com/send?phone=71996069038&amp;text=&amp;source=&amp;data=&amp;app_absent=" style="color: #4d9dd6; text-decoration: underline" target="_blank"><i class="fab fa-whatsapp"></i> <span>(71) 99606-9038</span></a>
</h4>
</div>
</div>
<div class="col-lg-6 col-md-6 col-sm-6 font-varela">
<div class="single-footer-widget font-jellee">
<h4 class="titulo-footer" style=" font-size:18px; font-weight: 500;">

                        Formas de pagamento

                    </h4>
<img src="/Content/site_novo/img/formasdepagamento2.png" style="max-width:560px; width:100%"/>
</div>
</div>
<div class="col-lg-3 col-md-6 col-sm-6">
</div>
</div>
<div style="background-color:rgb(219, 219, 219); width:100%; height:1px; margin-top:20px;"></div>
<div class="font-varela" style="color:rgb(172, 172, 172); font-size:14px; font-weight: normal; font-family:'Varela Round';

      margin-top:40px; text-align:center;

      ">

            TIBI LIVROS LTDA ME - CNPJ: 25.318.985/0001-29

        </div>
<div class="font-varela" style="margin-top:20px; margin-bottom:0px; color:rgb(172, 172, 172); padding-bottom:40px !important; text-align:center; font-family: 'Varela Round'; font-weight: normal; font-size:14px;text-align:center">

            Copyright ©

            <script>

                document.write(new Date().getFullYear());

            </script>

            Todos os direitos reservados | Tibi

        </div>
</div>
</footer>
<style>

        .whatsappfixed {

            border-radius: 4px;

            bottom: 20px;

            position: fixed;

            right: 20px;

            padding: 8px;

            text-align: center;

            z-index: 9999;

        }



        @media (max-width: 767.98px) {

            .whatsappfixed {

                right: 5px;

            }

        }

    </style>
<div class="whatsappfixed">
<a href="https://api.whatsapp.com/send?phone=5571981065462&amp;text=&amp;source=&amp;data=&amp;app_absent=" target="_blank">
<svg height="60" viewbox="0 0 39 39" width="60" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7 32.8l.6.3c2.5 1.5 5.3 2.2 8.1 2.2 8.8 0 16-7.2 16-16 0-4.2-1.7-8.3-4.7-11.3s-7-4.7-11.3-4.7c-8.8 0-16 7.2-15.9 16.1 0 3 .9 5.9 2.4 8.4l.4.6-1.6 5.9 6-1.5z" fill="#00E676"></path>
<path d="M32.4 6.4C29 2.9 24.3 1 19.5 1 9.3 1 1.1 9.3 1.2 19.4c0 3.2.9 6.3 2.4 9.1L1 38l9.7-2.5c2.7 1.5 5.7 2.2 8.7 2.2 10.1 0 18.3-8.3 18.3-18.4 0-4.9-1.9-9.5-5.3-12.9zM19.5 34.6c-2.7 0-5.4-.7-7.7-2.1l-.6-.3-5.8 1.5L6.9 28l-.4-.6c-4.4-7.1-2.3-16.5 4.9-20.9s16.5-2.3 20.9 4.9 2.3 16.5-4.9 20.9c-2.3 1.5-5.1 2.3-7.9 2.3zm8.8-11.1l-1.1-.5s-1.6-.7-2.6-1.2c-.1 0-.2-.1-.3-.1-.3 0-.5.1-.7.2 0 0-.1.1-1.5 1.7-.1.2-.3.3-.5.3h-.1c-.1 0-.3-.1-.4-.2l-.5-.2c-1.1-.5-2.1-1.1-2.9-1.9-.2-.2-.5-.4-.7-.6-.7-.7-1.4-1.5-1.9-2.4l-.1-.2c-.1-.1-.1-.2-.2-.4 0-.2 0-.4.1-.5 0 0 .4-.5.7-.8.2-.2.3-.5.5-.7.2-.3.3-.7.2-1-.1-.5-1.3-3.2-1.6-3.8-.2-.3-.4-.4-.7-.5h-1.1c-.2 0-.4.1-.6.1l-.1.1c-.2.1-.4.3-.6.4-.2.2-.3.4-.5.6-.7.9-1.1 2-1.1 3.1 0 .8.2 1.6.5 2.3l.1.3c.9 1.9 2.1 3.6 3.7 5.1l.4.4c.*******.8.8 2.1 1.8 4.5 3.1 7.2 *******.7.1 1 .2h1c.5 0 1.1-.2 1.5-.4.3-.2.5-.2.7-.4l.2-.2c.2-.2.4-.3.6-.5s.4-.4.5-.6c.2-.4.3-.9.4-1.4v-.7s-.1-.1-.3-.2z" fill="#FFF"></path>
</svg>
</a>
</div>
<script>

        const priceBook = document.querySelectorAll('.bg-section-livros .col-lg-4.col-md-6 a')





        if (priceBook) {

            Array.from(priceBook).map((card) => {

                const book = card.querySelector('.price-book')

                if (card.id.indexOf('ACR01') !== -1) {

                    book.classList.add('livro-promocao');

                }

                //if (card.id.indexOf('MCQ01') !== -1) {

                //    book.classList.add('livro-promocao');

                //}

                //if (card.id.indexOf('JAQ02') !== -1) {

                //    book.classList.add('livro-promocao');

                //}



                //if (card.id.indexOf('JAQ03') !== -1) {

                //    book.classList.add('livro-promocao');

                //}

                //if (card.id.indexOf('NAN05') !== -1) {

                //    book.classList.add('livro-promocao');

                //}

                //if (card.id.indexOf('CAP01') !== -1) {

                //    book.classList.add('livro-promocao');

                //}



                if (book) {



                    //ADICIONAR LIVRO PROMOCAO EM TODOS OFFSETS

                    //if (!book.classList.contains("personalizado")){

                    //    book.classList.add('livro-promocao');

                    //}



                    //ADICIONAR LIVRO PROMOCAO EM TODOS PERONSLIZADOS

                    //if (book.classList.contains("personalizado")){

                    //    book.classList.add('livro-promocao');

                    //}



                    if (book.classList.contains("personalizado")) {

                        if (book.classList.contains("livro-promocao")) {

                            book.innerHTML = `

                                                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$79,90</span> por <span class="font-jellee" style='color:#9459BC;' id="current-price">R$63,90</span> </p>`



                        }

                        else {

                            book.innerHTML = `

                                                        <span style='color:#9459BC;' class='font-jellee' id="current-price">R$79,90</span>

                                                        `

                        }

                    } else if (book.classList.contains("livro-promocao")) {

                        if (card.id.indexOf('NAN071') !== -1 || card.id.indexOf('JAQ031') !== -1) {

                            book.innerHTML = `

                                                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$59,90</span> por <span class="font-jellee" style='color:#9459BC;' id="current-price">R$29,90</span> </p>`

                        }

                        else if (card.id.indexOf('ACR01') !== -1) {

                          book.innerHTML = `

                                                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$79,90</span> por <span class="font-jellee" style='color:#9459BC;' id="current-price">R$69,90</span> </p>`} else {







                            book.innerHTML = `

                                                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$59,90</span> por <span class="font-jellee" style='color:#9459BC;' id="current-price">R$54,90</span> </p>`

                        }



                    }

                    else {

                        /*

                        book.innerHTML = `

                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$44,90</span> por <span class="font-jellee" style='color:#9459BC;'>R$35,00</span> </p>



                        `*/

                        if (card.id.indexOf('ACR01') !== -1) {

                          book.innerHTML = `

                                                        <p style="margin-bottom:0; color: #676565;">De <span style=' text-decoration:line-through;'>R$79,90</span> por <span class="font-jellee" style='color:#9459BC;' id="current-price">R$69,90</span> </p>`







                        }else {

                            book.innerHTML = `<p style="margin-bottom:0; color: #676565;"><span class="font-jellee" style='color:#9459BC;' id="current-price">R$59,90</span> </p>`

                        }



                    }

                }

            })

        }

    </script>
<script src="https://unpkg.com/swiper@7/swiper-bundle.min.js"></script>
<script defer="">

        $("#form-home").submit(function (event) {

            event.preventDefault();

            $(this)[0].reset();

            $('.valid').html("Obrigado! Agora você faz parte da nossa família leitora.");



        });

    </script>
<script src="/bundles/padraojs?v=ATQqJOHFvGPgKgZnKY4u_X7kiva49ioE9Y_b11HVJz01"></script>
<!-- Comercio eletronico (Clique no produto) -->
<script>

        function dataLayerProductClick(productId) {

            dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.

            const productName = document.querySelector(`#${productId} > div > .container > h2`).innerText;

            const productPrice = document.querySelector(`#${productId} #current-price`).innerText.replace(',', '.').replace(/[^0-9.]/g, '');

            dataLayer.push({

                'event': 'productClick',

                'eventLabel': productName,

                'ecommerce': {

                    'click': {

                        'actionField': { 'list': 'Books' },      // Optional list property.

                        'products': [{

                            'name': productName,                      // Name or ID is required.

                            'id': productId,

                            'price': productPrice,

                            'brand': 'Tibi',

                            'category': 'Books',

                        }]

                    }

                },

                'eventCallback': function (a) {

                    console.log(a);

                },

            });

        }



    </script>
<script>

        $(".menuquadrado").click(function () {

            if ($(".menuquadrado").attr("aria-expanded") == "true") {

                $(".menuquadrado").removeClass("com-fundo");

                $(".menuquadrado").addClass("sem-fundo");

                $(".stroke-svg1").removeClass("stroke-svg-aberto");

                $(".stroke-svg1").addClass("stroke-svg");

            } else {

                $(".menuquadrado").removeClass("sem-fundo");

                $(".menuquadrado").addClass("com-fundo");

                $(".stroke-svg1").removeClass("stroke-svg");

                $(".stroke-svg1").addClass("stroke-svg-aberto");

            }

        });

    </script>
<script>

        $(".meubotao1").click(function () {

            $(".collapse1").collapse("show");

        });

        $(".meubotao2").click(function () {

            $(".collapse2").collapse("show");

        });

        $(".meubotao3").click(function () {

            $(".slideteste").carousel("dispose");

            $(".collapse3").collapse("show");

        });

    </script>
<script>

        $(document).on("click", ".testedropdown", function (event) {

            event.stopPropagation();

        });

        $(document).on("click", function (event) {

            $(".link-idade").removeClass("color-idade");

        });

    </script>
<script>

        $(".link-idade").click(function () {

            if ($(".dropdown-menu").hasClass("show")) {

                $(".link-idade").removeClass("color-idade");

            } else {

                $(".link-idade").addClass("color-idade");

            }

        });

    </script>
<script>

        // Ativa a função toda vez que o usuário utilizar o scroll

        // Usa o debounce da biblioteca lodash, para evitar excessivos disparos da função ao scroll. Assim a função só vai disparar a cada 200ms, o tempo é informado ao final da função.

        $(document).on("scroll", function () {

            // Seleciona a navegação

            // Identifica o tamanho total do menu

            // Verifica a distância entre o scroll e o topo



            var $nav = $("nav"),

                navHeight = $nav.outerHeight(),

                windowTop = $(this).scrollTop();



            // Verifica quando a distância do scroll for maior que o tamanho total do menu

            if (windowTop > navHeight && screen.width > 800) {

                // Adiciona a classe small ao menu



                // Modifica o nome inteiro da empresa para uma sigla apenas

                $(".navbar").addClass("logo-menor");

                $(".pc-img").addClass("logo-menor2");

                $(".dropdown-menu").addClass("logo-menor3");

            } else {

                // Remove a classe small do menu



                // Coloca o nome inteiro da empresa novamente

                $(".navbar").removeClass("logo-menor");

                $(".pc-img").removeClass("logo-menor2");

                $(".dropdown-menu").removeClass("logo-menor3");

            }

        });

    </script>
<script>

        $(function () {

            $(".pgacarousel").carousel({

                interval: 2500

            });

        });

    </script>
<script>

        $(".carousel-touch").on("touchstart", function (event) {

            var xClick = event.originalEvent.touches[0].pageX;

            $(this).one("touchmove", function (event) {

                var xMove = event.originalEvent.touches[0].pageX;

                if (Math.floor(xClick - xMove) > 5) {

                    $(this).carousel("next");

                } else if (Math.floor(xClick - xMove) < -5) {

                    $(this).carousel("prev");

                }

            });

            $(".carousel-touch").on("touchend", function () {

                $(this).off("touchmove");

            });

        });

    </script>
<script>

        $("#carouselproduct").carousel({

            interval: false,

            wrap: false

        });

        var checkitem = function () {

            var $this;

            $this = $("#carouselproduct");

            if (

                $("#carouselproduct .carousel-inner .carousel-item:first").hasClass(

                    "active"

                )

            ) {

                $this.children(".carousel-control-prev").hide();

                $this.children(".carousel-control-next").show();

            } else if (

                $("#carouselproduct .carousel-inner .carousel-item:last").hasClass(

                    "active"

                )

            ) {

                $this.children(".carousel-control-next").hide();

                $this.children(".carousel-control-prev").show();

            } else {

                $this.children(".both").show();

            }

        };



        checkitem();



        $("#carouselproduct").on("slid.bs.carousel", "", checkitem);

    </script>
<script>

        if (screen.width < 800) {

            $(".link-idade").attr("data-toggle", "dropdown");

        }

    </script>
<script>

        function openidade() {

            $(".collapse-teste1").addClass("testeasd");

            $(".collapse-teste2").addClass("testeasd2");

        }

        function closeidade() {

            document.getElementById("sidenav").style.backgroundColor =

                "transparent";

            document.getElementById("sidenav").style.width = "0";

        }

    </script>
<script>

        function checkLabel(labelID, input) {

            let label = document.getElementById(labelID);

            input = input;



            if (input.value !== "") {

                if (!label.classList.contains("label-active"))

                    label.classList.add("label-active");

            } else {

                !label.classList.remove("label-active");

            }

        }

    </script>
<script>



        if ($('.inputemailform').val() != "") {

            $(".input-group > label").addClass("label-active");

        }

    </script>
<script async="" src="https://d335luupugsy2.cloudfront.net/js/loader-scripts/b6efe61b-5e0c-49ba-b6be-9b5d703a390c-loader.js" type="text/javascript"></script>
<script type="text/javascript">

        (function (e, t, o, n, p, r, i) { e.visitorGlobalObjectAlias = n; e[e.visitorGlobalObjectAlias] = e[e.visitorGlobalObjectAlias] || function () { (e[e.visitorGlobalObjectAlias].q = e[e.visitorGlobalObjectAlias].q || []).push(arguments) }; e[e.visitorGlobalObjectAlias].l = (new Date).getTime(); r = t.createElement("script"); r.src = o; r.async = true; i = t.getElementsByTagName("script")[0]; i.parentNode.insertBefore(r, i) })(window, document, "https://diffuser-cdn.app-us1.com/diffuser/diffuser.js", "vgo");

        vgo('setAccount', '*********');

        vgo('setTrackByDefault', true);



        vgo('process');

    </script>
</body>
</html>

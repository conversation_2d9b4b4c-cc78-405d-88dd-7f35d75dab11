#!/usr/bin/env python3
"""
🌐 API SIMPLES DO WEBSCRAPER

Uma versão simplificada da API que funciona 100%.
"""

import sys
from pathlib import Path
from datetime import datetime
import sqlite3

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# Criar app FastAPI
app = FastAPI(
    title="WebScraper Enterprise API",
    description="API REST para o WebScraper Empresarial",
    version="3.0.0"
)

# Middleware CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Endpoint raiz."""
    return {
        "service": "WebScraper Enterprise API",
        "version": "3.0.0",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "message": "🚀 Sistema funcionando perfeitamente!",
        "endpoints": {
            "docs": "/docs",
            "health": "/health",
            "stats": "/stats",
            "pages": "/pages",
            "dashboard": "/dashboard"
        }
    }


@app.get("/health")
async def health_check():
    """Health check."""
    try:
        # Testar banco
        db_path = "./data/webscraper.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM pages")
        page_count = cursor.fetchone()[0]
        conn.close()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "3.0.0",
            "database": {
                "status": "connected",
                "pages": page_count
            },
            "services": {
                "api": "running",
                "database": "connected"
            }
        }
    except Exception as e:
        return {
            "status": "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


@app.get("/stats")
async def get_stats():
    """Estatísticas do sistema."""
    try:
        db_path = "./data/webscraper.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Contar registros
        stats = {}
        tables = ['domains', 'pages', 'crawl_sessions', 'metrics', 'alerts']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                stats[table] = cursor.fetchone()[0]
            except:
                stats[table] = 0
        
        # Estatísticas das páginas
        try:
            cursor.execute("SELECT AVG(quality_score) FROM pages WHERE quality_score IS NOT NULL")
            avg_quality = cursor.fetchone()[0] or 0
            
            cursor.execute("SELECT SUM(word_count) FROM pages WHERE word_count IS NOT NULL")
            total_words = cursor.fetchone()[0] or 0
            
            cursor.execute("SELECT quality_tier, COUNT(*) FROM pages GROUP BY quality_tier")
            quality_distribution = dict(cursor.fetchall())
            
        except:
            avg_quality = 0
            total_words = 0
            quality_distribution = {}
        
        conn.close()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "database_stats": stats,
            "content_stats": {
                "avg_quality_score": round(avg_quality, 2),
                "total_words": total_words,
                "quality_distribution": quality_distribution
            },
            "system_info": {
                "version": "3.0.0",
                "status": "running"
            }
        }
        
    except Exception as e:
        return {"error": str(e)}


@app.get("/pages")
async def get_pages(limit: int = 10):
    """Listar páginas."""
    try:
        db_path = "./data/webscraper.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT url, title, quality_score, quality_tier, word_count, created_at
            FROM pages 
            ORDER BY created_at DESC 
            LIMIT ?
        """, (limit,))
        
        rows = cursor.fetchall()
        conn.close()
        
        pages = []
        for row in rows:
            pages.append({
                "url": row[0],
                "title": row[1],
                "quality_score": row[2],
                "quality_tier": row[3],
                "word_count": row[4],
                "created_at": row[5]
            })
        
        return {
            "pages": pages,
            "total": len(pages),
            "limit": limit
        }
        
    except Exception as e:
        return {"error": str(e)}


class ScrapeRequest(BaseModel):
    url: str

@app.post("/scrape")
async def start_scrape(request: ScrapeRequest):
    """Iniciar scraping de uma URL."""
    url = request.url
    try:
        import requests
        from bs4 import BeautifulSoup

        # Validar URL
        if not url.startswith(('http://', 'https://')):
            return {
                "status": "error",
                "url": url,
                "error": "URL deve começar com http:// ou https://",
                "timestamp": datetime.utcnow().isoformat()
            }

        # Fazer scraping básico com headers
        headers = {
            'User-Agent': 'WebScraper Enterprise 3.0.0 (Mozilla/5.0 compatible)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        response = requests.get(url, timeout=15, headers=headers, allow_redirects=True)
        
        if 'html' in response.headers.get('content-type', ''):
            soup = BeautifulSoup(response.content, 'html.parser')
            
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "Sem título"
            
            body = soup.find('body')
            body_text = body.get_text().strip() if body else ""
            word_count = len(body_text.split()) if body_text else 0
            
            # Calcular qualidade simples
            links = len(soup.find_all('a'))
            headings = len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
            
            quality_score = min(100, max(0, word_count * 0.5 + links * 2 + headings * 5))
            
            if quality_score >= 80:
                quality_tier = "excellent"
            elif quality_score >= 60:
                quality_tier = "good"
            elif quality_score >= 40:
                quality_tier = "fair"
            else:
                quality_tier = "poor"
            
            return {
                "status": "success",
                "url": url,
                "title": title_text,
                "word_count": word_count,
                "quality_score": int(quality_score),
                "quality_tier": quality_tier,
                "links": links,
                "headings": headings,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "status": "success",
                "url": url,
                "title": "Conteúdo não-HTML",
                "content_type": response.headers.get('content-type'),
                "size": len(response.content),
                "timestamp": datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        return {
            "status": "error",
            "url": url,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Dashboard web simples."""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebScraper Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .metric { display: inline-block; margin: 10px 20px 10px 0; }
            .metric-value { font-size: 2em; font-weight: bold; color: #2563eb; }
            .metric-label { color: #666; }
            button { background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
            button:hover { background: #1d4ed8; }
            input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; width: 300px; }
            .result { margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 WebScraper Enterprise Dashboard</h1>
                <p>Sistema de Web Scraping Empresarial - Funcionando!</p>
            </div>
            
            <div class="card">
                <h2>📊 Estatísticas do Sistema</h2>
                <div id="stats">Carregando...</div>
            </div>
            
            <div class="card">
                <h2>🔍 Testar Scraping</h2>
                <input type="url" id="urlInput" placeholder="https://example.com" value="https://httpbin.org/html">
                <button onclick="testScrape()">Fazer Scraping</button>
                <div id="scrapeResult"></div>
            </div>
            
            <div class="card">
                <h2>📄 Páginas Recentes</h2>
                <div id="pages">Carregando...</div>
            </div>
        </div>
        
        <script>
            // Carregar estatísticas
            fetch('/stats')
                .then(r => r.json())
                .then(data => {
                    document.getElementById('stats').innerHTML = `
                        <div class="metric">
                            <div class="metric-value">${data.database_stats.pages || 0}</div>
                            <div class="metric-label">Páginas</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${data.database_stats.domains || 0}</div>
                            <div class="metric-label">Domínios</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${data.content_stats.avg_quality_score || 0}</div>
                            <div class="metric-label">Qualidade Média</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${data.content_stats.total_words || 0}</div>
                            <div class="metric-label">Total Palavras</div>
                        </div>
                    `;
                });
            
            // Carregar páginas
            fetch('/pages')
                .then(r => r.json())
                .then(data => {
                    const pagesHtml = data.pages.map(page => `
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <strong>${page.title}</strong><br>
                            <small>${page.url}</small><br>
                            <span style="color: #666;">Qualidade: ${page.quality_score}/100 (${page.quality_tier}) | Palavras: ${page.word_count}</span>
                        </div>
                    `).join('');
                    document.getElementById('pages').innerHTML = pagesHtml || 'Nenhuma página encontrada';
                });
            
            // Testar scraping
            function testScrape() {
                const url = document.getElementById('urlInput').value;
                if (!url) return;
                
                document.getElementById('scrapeResult').innerHTML = 'Processando...';
                
                fetch('/scrape', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({url: url})
                })
                .then(r => r.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('scrapeResult').innerHTML = `
                            <div class="result">
                                <h3>✅ Scraping Concluído!</h3>
                                <p><strong>URL:</strong> ${data.url}</p>
                                <p><strong>Título:</strong> ${data.title}</p>
                                <p><strong>Palavras:</strong> ${data.word_count || 'N/A'}</p>
                                <p><strong>Qualidade:</strong> ${data.quality_score || 'N/A'}/100 (${data.quality_tier || 'N/A'})</p>
                                <p><strong>Links:</strong> ${data.links || 'N/A'}</p>
                                <p><strong>Headings:</strong> ${data.headings || 'N/A'}</p>
                            </div>
                        `;
                    } else {
                        document.getElementById('scrapeResult').innerHTML = `
                            <div class="result" style="background: #fef2f2;">
                                <h3>❌ Erro no Scraping</h3>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                });
            }
        </script>
    </body>
    </html>
    """
    return html


if __name__ == "__main__":
    print("🚀 Iniciando WebScraper API...")
    print("📍 URL: http://localhost:8080")
    print("📚 Docs: http://localhost:8080/docs")
    print("📊 Dashboard: http://localhost:8080/dashboard")
    print("❤️ Health: http://localhost:8080/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )

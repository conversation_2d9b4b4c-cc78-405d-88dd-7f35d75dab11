#!/usr/bin/env python3
"""
🚀 PRIMEIRO TESTE DO WEBSCRAPER EMPRESARIAL

Este script demonstra o sistema funcionando com um exemplo real.
Vamos fazer scraping de algumas páginas para ver todas as funcionalidades.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import get_settings
from src.core.logging import configure_logging, get_logger


async def exemplo_basico():
    """Exemplo básico usando apenas as funcionalidades core."""
    logger = get_logger(__name__)
    print("🔥 EXEMPLO BÁSICO - Testando funcionalidades core")
    
    try:
        from src.core.http_client import AsyncHTTPClient
        from src.domains.generic import GenericParser
        from src.core.quality import AdvancedQualityScorer
        from src.core.normalize import ContentNormalizer
        
        settings = get_settings()
        
        # URLs de teste (sites simples e confiáveis)
        test_urls = [
            "https://httpbin.org/html",
            "https://example.com",
            "https://httpbin.org/json",
        ]
        
        print(f"📡 Testando {len(test_urls)} URLs...")
        
        async with AsyncHTTPClient(settings) as client:
            for i, url in enumerate(test_urls, 1):
                print(f"\n🔍 [{i}/{len(test_urls)}] Processando: {url}")
                
                try:
                    # 1. Fazer requisição HTTP
                    print("   📥 Fazendo requisição...")
                    response = await client.get(url)
                    print(f"   ✅ Status: {response.status_code}, Tamanho: {len(response.content)} bytes")
                    
                    # 2. Parse do conteúdo
                    print("   🔧 Fazendo parse...")
                    domain_config = settings.get_domain_config(url.split("//")[1].split("/")[0])
                    parser = GenericParser(domain_config, settings)
                    page_data = await parser.parse(response)
                    
                    # 3. Normalização
                    print("   🧹 Normalizando conteúdo...")
                    normalizer = ContentNormalizer()
                    normalized_data = normalizer.normalize_page_data(page_data, url)
                    
                    # 4. Score de qualidade
                    print("   ⭐ Calculando qualidade...")
                    quality_scorer = AdvancedQualityScorer()
                    quality_score, quality_metrics = quality_scorer.calculate_quality_score(
                        normalized_data, domain_config.quality
                    )
                    
                    # 5. Resultados
                    print(f"   📊 RESULTADOS:")
                    print(f"      • Título: {normalized_data.title[:50]}...")
                    print(f"      • Palavras: {normalized_data.word_count}")
                    print(f"      • Qualidade: {quality_score}/100 ({quality_metrics.quality_tier})")
                    print(f"      • Links internos: {len(normalized_data.internal_links)}")
                    print(f"      • Links externos: {len(normalized_data.external_links)}")
                    print(f"      • Headings: {len(normalized_data.headings_tree)}")
                    print(f"      • Status: {normalized_data.status.value}")
                    
                except Exception as e:
                    print(f"   ❌ Erro: {e}")
        
        print("\n✅ Exemplo básico concluído!")
        return True
        
    except Exception as e:
        logger.error("Exemplo básico falhou", error=str(e), exc_info=True)
        return False


async def exemplo_com_banco():
    """Exemplo usando banco de dados (Fase 2)."""
    logger = get_logger(__name__)
    print("\n🗄️ EXEMPLO COM BANCO - Testando Fase 2")
    
    try:
        from src.core.database import init_database, get_db_session
        from src.core.models import Domain, Page
        from src.core.metrics import metrics_collector
        
        # Inicializar banco
        print("   🔧 Inicializando banco de dados...")
        await init_database()
        
        # Criar tabelas
        from src.core.database import create_tables
        await create_tables()
        print("   ✅ Banco inicializado!")
        
        # Testar inserção de dados
        print("   📝 Testando inserção de dados...")
        async with get_db_session() as session:
            # Criar domínio de teste
            test_domain = Domain(
                name="httpbin.org",
                rate_limit_rps=2.0,
                max_pages=100,
                active=True
            )
            session.add(test_domain)
            await session.flush()
            
            # Criar página de teste
            test_page = Page(
                url="https://httpbin.org/html",
                url_hash="test_hash_123",
                domain_id=test_domain.id,
                title="Test Page",
                content_hash="content_hash_123",
                semantic_hash="semantic_hash_123",
                word_count=150,
                quality_score=75,
                quality_tier="good",
                status="success"
            )
            session.add(test_page)
            await session.commit()
            
            print(f"   ✅ Dados salvos! Domínio ID: {test_domain.id}")
        
        # Testar métricas
        print("   📊 Testando métricas...")
        metrics_collector.record_http_request("httpbin.org", 200, "GET", 1.5, 1024)
        metrics_collector.record_page_parsed("httpbin.org", "success", "generic", 0.5, 75, "good", 150)
        print("   ✅ Métricas registradas!")
        
        print("✅ Exemplo com banco concluído!")
        return True
        
    except Exception as e:
        logger.error("Exemplo com banco falhou", error=str(e), exc_info=True)
        return False


async def exemplo_flow_completo():
    """Exemplo usando o flow empresarial completo."""
    logger = get_logger(__name__)
    print("\n🚀 EXEMPLO FLOW COMPLETO - Testando sistema integrado")
    
    try:
        from src.flows.enterprise_flow import enterprise_webscraper_flow
        
        print("   🎯 Executando flow empresarial...")
        print("   📍 Domínio: httpbin.org")
        print("   📄 Máximo: 3 páginas")
        print("   🧪 Modo: dry-run (teste)")
        
        # Executar flow empresarial
        result = await enterprise_webscraper_flow(
            domain="httpbin.org",
            max_pages=3,
            incremental=False,
            dry_run=True  # Modo de teste
        )
        
        print(f"   📊 RESULTADOS DO FLOW:")
        print(f"      • Domínio: {result.get('domain')}")
        print(f"      • Status: {result.get('status')}")
        print(f"      • URLs descobertas: {result.get('urls_discovered', 0)}")
        print(f"      • URLs processadas: {result.get('urls_processed', 0)}")
        print(f"      • Modo incremental: {result.get('incremental_mode')}")
        print(f"      • Dry run: {result.get('dry_run')}")
        
        if 'system_info' in result:
            system_info = result['system_info']
            print(f"      • Banco inicializado: {system_info.get('database_initialized')}")
            print(f"      • S3 habilitado: {system_info.get('s3_enabled')}")
            print(f"      • Alertas habilitados: {system_info.get('alerts_enabled')}")
        
        print("✅ Flow completo concluído!")
        return True
        
    except Exception as e:
        logger.error("Flow completo falhou", error=str(e), exc_info=True)
        return False


async def exemplo_api_local():
    """Exemplo testando a API localmente."""
    logger = get_logger(__name__)
    print("\n🌐 EXEMPLO API LOCAL - Testando FastAPI")
    
    try:
        import aiohttp
        
        # Tentar conectar na API (se estiver rodando)
        api_url = "http://localhost:8080"
        
        print(f"   🔗 Tentando conectar em: {api_url}")
        
        async with aiohttp.ClientSession() as session:
            try:
                # Testar endpoint raiz
                async with session.get(f"{api_url}/") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"   ✅ API funcionando!")
                        print(f"      • Serviço: {data.get('service')}")
                        print(f"      • Versão: {data.get('version')}")
                        print(f"      • Status: {data.get('status')}")
                        
                        # Testar health check
                        async with session.get(f"{api_url}/health") as health_response:
                            if health_response.status == 200:
                                health = await health_response.json()
                                print(f"      • Health: {health.get('status')}")
                        
                        return True
                    else:
                        print(f"   ⚠️ API retornou status {response.status}")
                        
            except aiohttp.ClientError:
                print("   ℹ️ API não está rodando (isso é normal)")
                print("   💡 Para testar a API, execute: python -m src.api.main")
                return True  # Não é erro, apenas não está rodando
        
    except Exception as e:
        logger.error("Teste da API falhou", error=str(e), exc_info=True)
        return False


def mostrar_proximos_passos():
    """Mostrar próximos passos para o usuário."""
    print("\n" + "="*60)
    print("🎉 PRIMEIRO TESTE CONCLUÍDO!")
    print("="*60)
    
    print("\n📚 PRÓXIMOS PASSOS:")
    
    print("\n1. 🚀 RODAR A API:")
    print("   python -m src.api.main")
    print("   Depois acesse: http://localhost:8080/docs")
    
    print("\n2. 🐳 TESTAR COM DOCKER:")
    print("   docker-compose -f docker-compose.fase3.yml up -d")
    print("   Dashboard: http://localhost:3000")
    
    print("\n3. 🔍 FAZER SCRAPING REAL:")
    print("   # Edite este script e mude dry_run=False")
    print("   # Adicione seus próprios domínios em configs/domains.yml")
    
    print("\n4. 📊 VER MÉTRICAS:")
    print("   # Com Docker rodando:")
    print("   # Prometheus: http://localhost:9090")
    print("   # Grafana: http://localhost:3000")
    
    print("\n5. ☸️ DEPLOY KUBERNETES:")
    print("   kubectl apply -f k8s/")
    
    print("\n📖 DOCUMENTAÇÃO:")
    print("   • FASE1_COMPLETA.md - Funcionalidades core")
    print("   • FASE2_COMPLETA.md - Backend empresarial")
    print("   • FASE3_COMPLETA.md - Infraestrutura produção")
    
    print("\n🎯 EXEMPLOS DE USO:")
    print("   • test_fase1.py - Testes das funcionalidades básicas")
    print("   • test_fase2_simple.py - Testes do backend")
    print("   • test_fase3_simple.py - Testes da infraestrutura")


async def main():
    """Função principal do primeiro teste."""
    print("🚀 WEBSCRAPER EMPRESARIAL - PRIMEIRO TESTE")
    print("="*50)
    
    # Configurar logging
    configure_logging(level="INFO", structured=True)
    
    # Lista de exemplos para executar
    exemplos = [
        ("Exemplo Básico", exemplo_basico),
        ("Exemplo com Banco", exemplo_com_banco),
        ("Flow Completo", exemplo_flow_completo),
        ("API Local", exemplo_api_local),
    ]
    
    resultados = []
    
    for nome, exemplo_func in exemplos:
        print(f"\n{'='*20} {nome} {'='*20}")
        try:
            sucesso = await exemplo_func()
            resultados.append(sucesso)
            if sucesso:
                print(f"✅ {nome}: SUCESSO")
            else:
                print(f"❌ {nome}: FALHOU")
        except Exception as e:
            print(f"💥 {nome}: ERRO - {e}")
            resultados.append(False)
    
    # Resumo final
    sucessos = sum(resultados)
    total = len(resultados)
    
    print(f"\n📊 RESUMO FINAL:")
    print(f"✅ Sucessos: {sucessos}/{total}")
    print(f"❌ Falhas: {total - sucessos}/{total}")
    
    if sucessos >= total * 0.75:  # 75% ou mais
        print("🎉 SISTEMA FUNCIONANDO MUITO BEM!")
    else:
        print("⚠️ Alguns problemas encontrados, mas é normal no primeiro teste")
    
    # Mostrar próximos passos
    mostrar_proximos_passos()
    
    return 0 if sucessos > 0 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

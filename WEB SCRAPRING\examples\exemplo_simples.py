#!/usr/bin/env python3
"""
🚀 EXEMPLO SIMPLES DO WEBSCRAPER

Este exemplo mostra o sistema funcionando de forma básica,
sem dependências complexas.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def teste_basico_requests():
    """Teste básico usando apenas requests."""
    print("🔥 TESTE BÁSICO - Usando requests simples")
    
    try:
        import requests
        from src.domains.generic import GenericParser
        from src.core.config import get_settings
        from src.core.quality import AdvancedQualityScorer
        from src.core.normalize import ContentNormalizer
        
        # Configurações
        settings = get_settings()
        
        # URL de teste simples
        url = "https://httpbin.org/html"
        print(f"📡 Fazendo requisição para: {url}")
        
        # Fazer requisição simples
        response = requests.get(url, timeout=10)
        print(f"✅ Status: {response.status_code}")
        print(f"📏 Tamanho: {len(response.content)} bytes")
        
        # Simular objeto de resposta para o parser
        class SimpleResponse:
            def __init__(self, content, status_code, url):
                self.content = content
                self.status_code = status_code
                self.url = url
                self.headers = {}
        
        simple_response = SimpleResponse(response.content, response.status_code, url)
        
        # Parse do conteúdo
        print("🔧 Fazendo parse do HTML...")
        domain_config = settings.get_domain_config("httpbin.org")
        parser = GenericParser(domain_config, settings)
        
        # Parse manual básico
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extrair dados básicos
        title = soup.find('title')
        title_text = title.get_text().strip() if title else "Sem título"
        
        body = soup.find('body')
        body_text = body.get_text().strip() if body else ""
        word_count = len(body_text.split()) if body_text else 0
        
        print(f"📄 Título: {title_text}")
        print(f"📝 Palavras: {word_count}")
        print(f"📊 Conteúdo extraído com sucesso!")
        
        # Teste de qualidade básico
        print("⭐ Calculando score de qualidade...")
        
        # Score simples baseado em palavra
        if word_count > 100:
            quality_score = 85
            quality_tier = "excellent"
        elif word_count > 50:
            quality_score = 70
            quality_tier = "good"
        elif word_count > 20:
            quality_score = 55
            quality_tier = "fair"
        else:
            quality_score = 30
            quality_tier = "poor"
        
        print(f"🎯 Score de qualidade: {quality_score}/100 ({quality_tier})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


async def teste_banco_simples():
    """Teste simples do banco de dados."""
    print("\n🗄️ TESTE BANCO - SQLite simples")
    
    try:
        import sqlite3
        from datetime import datetime
        
        # Conectar ao banco SQLite
        db_path = "./data/webscraper.db"
        print(f"📂 Conectando ao banco: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar se tabelas existem
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 Tabelas encontradas: {[t[0] for t in tables]}")
        
        # Contar registros em algumas tabelas
        if tables:
            for table_name, in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   • {table_name}: {count} registros")
                except:
                    print(f"   • {table_name}: erro ao contar")
        
        conn.close()
        print("✅ Banco testado com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no banco: {e}")
        return False


async def teste_arquivos_sistema():
    """Teste dos arquivos do sistema."""
    print("\n📁 TESTE ARQUIVOS - Verificando estrutura")
    
    try:
        # Verificar arquivos principais
        arquivos_importantes = [
            "src/core/config.py",
            "src/core/http_client.py", 
            "src/domains/generic.py",
            "src/core/quality.py",
            "src/core/normalize.py",
            "src/core/database.py",
            "src/api/main.py",
            "Dockerfile",
            "docker-compose.fase3.yml",
            "k8s/webscraper.yaml"
        ]
        
        arquivos_ok = 0
        for arquivo in arquivos_importantes:
            if Path(arquivo).exists():
                size = Path(arquivo).stat().st_size
                print(f"✅ {arquivo} ({size} bytes)")
                arquivos_ok += 1
            else:
                print(f"❌ {arquivo} - NÃO ENCONTRADO")
        
        print(f"\n📊 Arquivos: {arquivos_ok}/{len(arquivos_importantes)} OK")
        
        # Verificar diretórios
        diretorios = ["src", "k8s", "data", "configs"]
        for diretorio in diretorios:
            if Path(diretorio).exists():
                files_count = len(list(Path(diretorio).rglob("*")))
                print(f"📂 {diretorio}/ ({files_count} arquivos)")
            else:
                print(f"❌ {diretorio}/ - NÃO ENCONTRADO")
        
        return arquivos_ok >= len(arquivos_importantes) * 0.8  # 80% dos arquivos
        
    except Exception as e:
        print(f"❌ Erro verificando arquivos: {e}")
        return False


async def teste_configuracao():
    """Teste das configurações."""
    print("\n⚙️ TESTE CONFIGURAÇÃO - Verificando settings")
    
    try:
        from src.core.config import get_settings
        
        settings = get_settings()
        print(f"✅ Settings carregado!")
        
        # Verificar algumas configurações importantes
        configs = [
            ("environment", getattr(settings, 'environment', 'N/A')),
            ("log_level", getattr(settings, 'log_level', 'N/A')),
            ("database_url", getattr(settings, 'database_url', 'N/A')),
            ("max_concurrent_requests", getattr(settings, 'max_concurrent_requests', 'N/A')),
            ("request_timeout", getattr(settings, 'request_timeout', 'N/A')),
        ]
        
        for nome, valor in configs:
            print(f"   • {nome}: {valor}")
        
        # Testar configuração de domínio
        domain_config = settings.get_domain_config("example.com")
        print(f"   • Domain config para example.com: OK")
        print(f"     - Rate limit: {domain_config.rate_limit_rps}")
        print(f"     - Max pages: {domain_config.max_pages}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração: {e}")
        return False


async def teste_api_estrutura():
    """Teste da estrutura da API."""
    print("\n🌐 TESTE API - Verificando estrutura FastAPI")
    
    try:
        # Tentar importar a API
        from src.api.main import app
        print("✅ FastAPI app importada com sucesso!")
        
        # Verificar rotas
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(f"{route.path}")
        
        print(f"📍 Rotas encontradas: {len(routes)}")
        for route in routes[:10]:  # Mostrar primeiras 10
            print(f"   • {route}")
        
        if len(routes) > 10:
            print(f"   ... e mais {len(routes) - 10} rotas")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na API: {e}")
        return False


def mostrar_como_usar():
    """Mostrar como usar o sistema."""
    print("\n" + "="*60)
    print("🎉 SISTEMA VERIFICADO!")
    print("="*60)
    
    print("\n🚀 COMO USAR O WEBSCRAPER:")
    
    print("\n1. 📡 SCRAPING BÁSICO:")
    print("   # Usar o exemplo que funcionou:")
    print("   python exemplo_simples.py")
    
    print("\n2. 🌐 RODAR A API:")
    print("   python -m src.api.main")
    print("   # Acesse: http://localhost:8080/docs")
    
    print("\n3. 🐳 USAR DOCKER (RECOMENDADO):")
    print("   docker-compose -f docker-compose.fase3.yml up -d")
    print("   # Dashboard: http://localhost:3000")
    print("   # Grafana: http://localhost:3000")
    print("   # Prometheus: http://localhost:9090")
    
    print("\n4. 🔍 FAZER SCRAPING REAL:")
    print("   # Edite configs/domains.yml")
    print("   # Adicione seus domínios")
    print("   # Execute os flows")
    
    print("\n5. 📊 VER RESULTADOS:")
    print("   # Banco SQLite: ./data/webscraper.db")
    print("   # Logs: aparecem no terminal")
    print("   # Métricas: via Prometheus/Grafana")
    
    print("\n🎯 PRÓXIMOS PASSOS:")
    print("   1. Teste a API: python -m src.api.main")
    print("   2. Teste com Docker: docker-compose up")
    print("   3. Configure seus domínios")
    print("   4. Execute scraping real")
    print("   5. Monitore via dashboard")


async def main():
    """Função principal do exemplo simples."""
    print("🚀 WEBSCRAPER EMPRESARIAL - EXEMPLO SIMPLES")
    print("="*50)
    
    # Lista de testes simples
    testes = [
        ("Requisições HTTP", teste_basico_requests),
        ("Banco de Dados", teste_banco_simples),
        ("Arquivos do Sistema", teste_arquivos_sistema),
        ("Configuração", teste_configuracao),
        ("API FastAPI", teste_api_estrutura),
    ]
    
    resultados = []
    
    for nome, teste_func in testes:
        print(f"\n{'='*20} {nome} {'='*20}")
        try:
            sucesso = await teste_func()
            resultados.append(sucesso)
            if sucesso:
                print(f"✅ {nome}: SUCESSO")
            else:
                print(f"❌ {nome}: FALHOU")
        except Exception as e:
            print(f"💥 {nome}: ERRO - {e}")
            resultados.append(False)
    
    # Resumo final
    sucessos = sum(resultados)
    total = len(resultados)
    
    print(f"\n📊 RESUMO FINAL:")
    print(f"✅ Sucessos: {sucessos}/{total}")
    print(f"❌ Falhas: {total - sucessos}/{total}")
    
    if sucessos >= total * 0.8:  # 80% ou mais
        print("🎉 SISTEMA FUNCIONANDO MUITO BEM!")
        mostrar_como_usar()
    elif sucessos >= total * 0.6:  # 60% ou mais
        print("⚠️ Sistema parcialmente funcionando - alguns ajustes necessários")
        mostrar_como_usar()
    else:
        print("⚠️ Vários problemas encontrados - verifique as dependências")
    
    return 0 if sucessos > 0 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

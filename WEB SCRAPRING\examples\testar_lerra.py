#!/usr/bin/env python3
"""
Teste específico para o site da Lerra.
"""

import requests
import json

def testar_lerra_via_api():
    """Testar scraping da Lerra via API."""
    print("🔍 Testando scraping da Lerra via API...")
    
    try:
        # Testar via API
        api_url = "http://localhost:8080/scrape"
        data = {"url": "https://www.lerra.com.br"}
        
        print(f"📡 Fazendo POST para: {api_url}")
        print(f"📄 Dados: {data}")
        
        response = requests.post(
            api_url,
            json=data,
            timeout=30
        )
        
        print(f"✅ Status da API: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 Resultado:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ Erro da API: {response.text}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")


def testar_lerra_direto():
    """Testar scraping da Lerra diretamente."""
    print("\n🔍 Testando scraping da Lerra diretamente...")
    
    try:
        url = "https://www.lerra.com.br"
        
        headers = {
            'User-Agent': 'WebScraper Enterprise 3.0.0 (Mozilla/5.0 compatible)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        print(f"📡 Fazendo requisição para: {url}")
        
        response = requests.get(url, timeout=15, headers=headers, allow_redirects=True)
        
        print(f"✅ Status: {response.status_code}")
        print(f"📏 Tamanho: {len(response.content)} bytes")
        print(f"🔗 URL final: {response.url}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Tentar parse básico
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "Sem título"
            
            body = soup.find('body')
            body_text = body.get_text().strip() if body else ""
            word_count = len(body_text.split()) if body_text else 0
            
            links = len(soup.find_all('a'))
            headings = len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
            
            print(f"📄 Título: {title_text}")
            print(f"📝 Palavras: {word_count}")
            print(f"🔗 Links: {links}")
            print(f"📋 Headings: {headings}")
            
            # Mostrar primeiros 500 caracteres do conteúdo
            if body_text:
                print(f"📖 Conteúdo (primeiros 500 chars):")
                print(body_text[:500] + "..." if len(body_text) > 500 else body_text)
            
        else:
            print(f"❌ Status não-200: {response.status_code}")
            print(f"📄 Conteúdo: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()


def main():
    print("🚀 TESTE ESPECÍFICO - Site da Lerra")
    print("="*50)
    
    # Primeiro teste direto
    testar_lerra_direto()
    
    # Depois teste via API
    testar_lerra_via_api()
    
    print("\n✅ Testes concluídos!")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Teste específico para o site terra.com.br
"""

import requests
import json
from bs4 import BeautifulSoup

def testar_terra_direto():
    """Testar scraping do Terra diretamente."""
    print("🌍 Testando scraping do Terra.com.br diretamente...")
    
    try:
        url = "https://www.terra.com.br"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        print(f"📡 Fazendo requisição para: {url}")
        
        response = requests.get(url, timeout=20, headers=headers, allow_redirects=True)
        
        print(f"✅ Status: {response.status_code}")
        print(f"📏 Tamanho: {len(response.content)} bytes")
        print(f"🔗 URL final: {response.url}")
        print(f"📋 Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            # Parse do HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extrair dados básicos
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "Sem título"
            
            # Remover scripts e styles para análise de conteúdo
            for script in soup(["script", "style"]):
                script.decompose()
            
            body = soup.find('body')
            body_text = body.get_text().strip() if body else ""
            word_count = len(body_text.split()) if body_text else 0
            
            # Contar elementos
            links = soup.find_all('a', href=True)
            internal_links = [link for link in links if 'terra.com' in link.get('href', '')]
            external_links = [link for link in links if link.get('href', '').startswith('http') and 'terra.com' not in link.get('href', '')]
            
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            paragraphs = soup.find_all('p')
            images = soup.find_all('img')
            
            # Calcular qualidade
            quality_score = min(100, max(0, 
                word_count * 0.3 + 
                len(internal_links) * 1 + 
                len(headings) * 3 + 
                len(paragraphs) * 2 +
                len(images) * 0.5
            ))
            
            if quality_score >= 80:
                quality_tier = "excellent"
            elif quality_score >= 60:
                quality_tier = "good"
            elif quality_score >= 40:
                quality_tier = "fair"
            else:
                quality_tier = "poor"
            
            print(f"\n📊 RESULTADOS DO SCRAPING:")
            print(f"📄 Título: {title_text}")
            print(f"📝 Palavras: {word_count}")
            print(f"🔗 Links totais: {len(links)}")
            print(f"   • Internos: {len(internal_links)}")
            print(f"   • Externos: {len(external_links)}")
            print(f"📋 Headings: {len(headings)}")
            print(f"📄 Parágrafos: {len(paragraphs)}")
            print(f"🖼️ Imagens: {len(images)}")
            print(f"⭐ Qualidade: {int(quality_score)}/100 ({quality_tier})")
            
            # Mostrar alguns headings
            if headings:
                print(f"\n📋 Primeiros headings encontrados:")
                for i, h in enumerate(headings[:5]):
                    print(f"   {h.name.upper()}: {h.get_text().strip()[:80]}...")
            
            # Mostrar primeiros parágrafos
            if paragraphs:
                print(f"\n📄 Primeiros parágrafos:")
                for i, p in enumerate(paragraphs[:3]):
                    text = p.get_text().strip()
                    if len(text) > 20:  # Só mostrar parágrafos com conteúdo
                        print(f"   {i+1}: {text[:100]}...")
            
            return {
                "status": "success",
                "url": url,
                "title": title_text,
                "word_count": word_count,
                "quality_score": int(quality_score),
                "quality_tier": quality_tier,
                "links_total": len(links),
                "links_internal": len(internal_links),
                "links_external": len(external_links),
                "headings": len(headings),
                "paragraphs": len(paragraphs),
                "images": len(images)
            }
            
        else:
            print(f"❌ Status não-200: {response.status_code}")
            print(f"📄 Resposta: {response.text[:500]}...")
            return {"status": "error", "code": response.status_code}
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}


def testar_terra_via_api():
    """Testar scraping do Terra via API."""
    print("\n🌐 Testando scraping do Terra via API...")
    
    try:
        api_url = "http://localhost:8080/scrape"
        data = {"url": "https://www.terra.com.br"}
        
        print(f"📡 Fazendo POST para: {api_url}")
        
        response = requests.post(
            api_url,
            json=data,
            timeout=30
        )
        
        print(f"✅ Status da API: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 Resultado da API:")
            print(f"   Status: {result.get('status')}")
            print(f"   Título: {result.get('title', 'N/A')}")
            print(f"   Palavras: {result.get('word_count', 'N/A')}")
            print(f"   Qualidade: {result.get('quality_score', 'N/A')}/100 ({result.get('quality_tier', 'N/A')})")
            print(f"   Links: {result.get('links', 'N/A')}")
            print(f"   Headings: {result.get('headings', 'N/A')}")
            return result
        else:
            print(f"❌ Erro da API: {response.text}")
            return {"status": "api_error", "code": response.status_code}
            
    except Exception as e:
        print(f"❌ Erro na API: {e}")
        return {"status": "api_error", "error": str(e)}


def comparar_resultados(direto, api):
    """Comparar resultados dos dois métodos."""
    print("\n🔍 COMPARAÇÃO DOS RESULTADOS:")
    print("="*50)
    
    if direto.get("status") == "success" and api.get("status") == "success":
        print("✅ Ambos os métodos funcionaram!")
        
        campos = ["word_count", "quality_score", "links", "headings"]
        for campo in campos:
            valor_direto = direto.get(campo, "N/A")
            valor_api = api.get(campo, "N/A")
            
            if valor_direto == valor_api:
                print(f"   ✅ {campo}: {valor_direto} (iguais)")
            else:
                print(f"   ⚠️ {campo}: Direto={valor_direto}, API={valor_api}")
    
    elif direto.get("status") == "success":
        print("✅ Método direto funcionou, API teve problemas")
    elif api.get("status") == "success":
        print("✅ API funcionou, método direto teve problemas")
    else:
        print("❌ Ambos os métodos tiveram problemas")


def main():
    print("🌍 TESTE ESPECÍFICO - Terra.com.br")
    print("="*50)
    
    # Teste direto
    resultado_direto = testar_terra_direto()
    
    # Teste via API
    resultado_api = testar_terra_via_api()
    
    # Comparar resultados
    comparar_resultados(resultado_direto, resultado_api)
    
    print("\n🎉 Teste do Terra.com.br concluído!")
    
    if resultado_direto.get("status") == "success":
        print("\n💡 DICA: O Terra.com.br foi processado com sucesso!")
        print("   Você pode usar este domínio para testes futuros.")
        print("   Experimente no dashboard: http://localhost:8080/dashboard")


if __name__ == "__main__":
    main()

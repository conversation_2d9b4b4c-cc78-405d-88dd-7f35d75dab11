apiVersion: v1
kind: ConfigMap
metadata:
  name: webscraper-config
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: config
data:
  # Application Configuration
  WEBSCRAPER_ENVIRONMENT: "production"
  WEBSCRAPER_LOG_LEVEL: "INFO"
  WEBSCRAPER_DEBUG: "false"
  
  # Database Configuration
  WEBSCRAPER_DATABASE_URL: "postgresql+asyncpg://webscraper:$(POSTGRES_PASSWORD)@postgres:5432/webscraper"
  
  # Redis Configuration
  WEBSCRAPER_REDIS_URL: "redis://redis:6379/0"
  
  # S3/MinIO Configuration
  WEBSCRAPER_S3_ENDPOINT_URL: "http://minio:9000"
  WEBSCRAPER_S3_BUCKET_NAME: "webscraper"
  WEBSCRAPER_S3_REGION: "us-east-1"
  
  # Elasticsearch Configuration
  WEBSCRAPER_ELASTICSEARCH_URL: "http://elasticsearch:9200"
  
  # Metrics Configuration
  WEBSCRAPER_METRICS_ENABLED: "true"
  WEBSCRAPER_METRICS_PORT: "8000"
  
  # Alerts Configuration
  WEBSCRAPER_ALERTS_ENABLED: "true"
  
  # Scraping Configuration
  WEBSCRAPER_MAX_CONCURRENT_REQUESTS: "20"
  WEBSCRAPER_DEFAULT_REQUESTS_PER_SECOND: "2.0"
  WEBSCRAPER_REQUEST_TIMEOUT: "30"
  WEBSCRAPER_RETRY_ATTEMPTS: "3"
  
  # Cache Configuration
  WEBSCRAPER_CACHE_ENABLED: "true"
  WEBSCRAPER_CACHE_TTL: "3600"
  
  # Incremental Collection
  WEBSCRAPER_INCREMENTAL_ENABLED: "true"
  WEBSCRAPER_USE_ETAG: "true"
  WEBSCRAPER_USE_LAST_MODIFIED: "true"
  
  # Quality Configuration
  WEBSCRAPER_MIN_QUALITY_SCORE: "30"

---
apiVersion: v1
kind: Secret
metadata:
  name: webscraper-secrets
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # Base64 encoded secrets
  POSTGRES_PASSWORD: d2Vic2NyYXBlcjEyMw==  # webscraper123
  WEBSCRAPER_S3_ACCESS_KEY: bWluaW9hZG1pbg==  # minioadmin
  WEBSCRAPER_S3_SECRET_KEY: bWluaW9hZG1pbjEyMw==  # minioadmin123
  WEBSCRAPER_WEBHOOK_URL: aHR0cDovL3dlYmhvb2stdGVzdDo4MDgwL3dlYmhvb2s=  # http://webhook-test:8080/webhook

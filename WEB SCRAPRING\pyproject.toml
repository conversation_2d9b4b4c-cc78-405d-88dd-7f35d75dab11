[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "webscraper"
version = "0.1.0"
description = "Sistema complexo de web scraping com observabilidade e tolerância a falhas"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # HTTP e requisições assíncronas
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    
    # Browser headless para páginas dinâmicas
    "playwright>=1.40.0",
    
    # Parsing HTML
    "selectolax>=0.3.17",
    "lxml>=4.9.3",
    "beautifulsoup4>=4.12.2",
    
    # Validação e modelos de dados
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Orquestração e agendamento
    "prefect>=2.14.0",
    
    # Storage e banco de dados
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",  # PostgreSQL async
    "aiosqlite>=0.19.0",  # SQLite async
    
    # Observabilidade e logs
    "structlog>=23.2.0",
    "loguru>=0.7.2",
    
    # Utilitários
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.0",
    "typer>=0.9.0",
    "rich>=13.7.0",
    "click>=8.1.7",
    
    # Cache e performance
    "async-lru>=2.0.4",
    "redis>=5.0.1",
    
    # Processamento de texto e URLs
    "urllib3>=2.1.0",
    "chardet>=5.2.0",
    "python-dateutil>=2.8.2",
    
    # Métricas e monitoramento
    "prometheus-client>=0.19.0",
]

[project.optional-dependencies]
dev = [
    # Testes
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "vcrpy>=5.1.0",
    "httpx-mock>=0.10.1",
    
    # Linting e formatação
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    
    # Desenvolvimento
    "ipython>=8.17.2",
    "jupyter>=1.0.0",
]

rag = [
    # Para funcionalidades de RAG (opcional)
    "chromadb>=0.4.18",
    "sentence-transformers>=2.2.2",
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
]

s3 = [
    # Para storage em S3/MinIO
    "boto3>=1.34.0",
    "minio>=7.2.0",
]

monitoring = [
    # Para observabilidade avançada
    "grafana-client>=3.5.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
]

[project.urls]
Homepage = "https://github.com/pedro/webscraper"
Repository = "https://github.com/pedro/webscraper"
Issues = "https://github.com/pedro/webscraper/issues"

[project.scripts]
webscraper = "webscraper.cli:main"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["webscraper"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

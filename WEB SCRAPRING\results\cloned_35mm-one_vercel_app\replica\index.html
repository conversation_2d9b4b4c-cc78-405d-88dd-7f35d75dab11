<!DOCTYPE html>
<html lang="en"> <head><meta charset="utf-8"/><link href="/favicon.svg" rel="icon" type="image/svg+xml"/><meta content="width=device-width, initial-scale=1, viewport-fit=cover" name="viewport"/><meta content="Astro v4.15.4" name="generator"/><title>35mm</title><link href="/_astro/index.B0RXM3KL.css" rel="stylesheet"/><script src="/_astro/hoisted.DKAByMYm.js" type="module"></script></head> <body> <div class="h-[100dvh] overflow-y-scroll relative" id="scroll-container"> <div class="relative" id="scroll-content"> <div data-scrollable-content=""> <div class="fixed w-full h-full z-50 design-grid px-[var(--grid-gap)] invisible pointer-events-none" id="design-grid"> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> <div class="border-x border-dark2/50 h-full"></div> </div> <div class="fixed inset-0 p-[calc(var(--s)*20)] z-[100] pointer-events-none font-inter"> <div class="w-full h-full relative p-[calc(var(--s)*16)] text-sm lg:text-xsm text-dark2 flex justify-between items-start"> <div> <p id="fps-count">00fps</p> <p id="resolution">0x0</p> </div> <div class="flex gap-[calc(var(--s)*8)] items-center"> <div class="h-[calc(var(--s)*8)] w-[calc(var(--s)*8)] rounded-full bg-orange1"></div> <p id="timer">00:00:00</p> </div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-t border-l top-0 left-0"></div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-r border-t top-0 right-0"></div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-l border-b bottom-0 left-0"></div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-b border-r bottom-0 right-0"></div> <div class="absolute bottom-0 left-[50%] translate-x-[-50%] flex flex-col items-center gap-[calc(var(--s)*12)]" id="scroller"> <div class="w-full h-[calc(var(--s)*8)] relative"> <div class="w-[calc(100%-var(--s)*8+2px)] absolute h-full top-0"> <span class="block w-0 h-0 border-l-[calc(var(--s)*4)] absolute left-[0] translate-x-[-50%] top-0 border-l-[transparent] border-r-[transparent] border-r-[calc(var(--s)*4)] border-t-[calc(var(--s)*8)] border-t-dark2" id="scroller-cursor" style="visibility:hidden"></span> </div> </div> <div class="flex items-end"> <span class="block w-[calc(var(--s)*8)] border-l border-dark2 h-[calc(var(--s)*16)]" data-scroller-pin="" style="visibility:hidden"></span>
<span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] border-l border-dark2 h-[calc(var(--s)*16)]" data-scroller-pin="" style="visibility:hidden"></span>
<span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] border-l border-dark2 h-[calc(var(--s)*16)]" data-scroller-pin="" style="visibility:hidden"></span>
<span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] border-l border-dark2 h-[calc(var(--s)*16)]" data-scroller-pin="" style="visibility:hidden"></span>
<span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span><span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*8)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span> <span class="block w-[calc(var(--s)*8)] h-[calc(var(--s)*16)] border-l border-dark2" data-scroller-pin="" style="visibility:hidden"></span> </div> </div> </div> </div> <div class="absolute w-full z-[5] px-[calc(var(--s)*20)]"> <div class="h-[700vh] p-[calc(var(--s)*20)] relative flex flex-col"> <div class="h-[450vh]"> <div class="h-[250vh]"> <div class="pt-[calc(var(--s)*100)] flex-1"> <span class="uppercase w-full font-bold text-[10.7vw] md:text-[11.4vw] leading-[100%] tracking-tighter flex justify-between text-[white]" data-animation="title"> <span class="block">Timeless</span> <span class="block">Craft</span> </span> <div class="mt-[30vmax]"> <!-- <div class={clsx("design-grid", behindCanvas && "invisible")}>
    <div
      class="col-start-2 md:col-end-4 col-end-6 text-md md:text-body text-light1"
      data-animation={behindCanvas ? undefined : "paragraph"}
    >
      <p>
        In September 1971, Canon released the F-1, forever changing the
        landscape of professional photography. This flagship SLR camera
        represented Canon's bold entry into the professional market, challenging
        the dominance of Nikon with extraordinary build quality, reliability,
        and a comprehensive system of interchangeable components.
      </p>
    </div>
  </div> --> <div class="flex flex-col gap-[calc(var(--s)*300)] md:gap-[calc(var(--s)*400)]" data-top-cover-parts-section="true"> <div class="design-grid w-full"> <div class="text-body text-light1 row-start-1 row-end-2 relative col-start-2 col-end-6 md:col-end-4 invisible"> <p class="w-full sm:absolute top-0 left-0">In September 1971, Canon released the F-1, forever changing the landscape of professional photography. This flagship SLR camera represented Canon's bold entry into the professional market, challenging the dominance of Nikon with extraordinary build quality, reliability, and a comprehensive system of interchangeable components.</p> </div> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-7 col-end-13 md:col-end-12"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]" data-top-cover-part="pentaprism"> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> <span class="block">pentaprism</span> </span> </div> </div><div class="design-grid w-full mt-[calc(var(--s)*100)]"> <div class="text-body text-light1 row-start-1 row-end-2 relative col-start-8 md:col-start-10 col-end-13 md:col-end-12 invisible mt-[calc(var(--s)*64)] md:mt-0"> <p class="w-full sm:absolute top-0 left-0">The Canon F-1 wasn't just a camera—it
              was a statement that Canon had arrived as a serious contender in
              professional photography, backed by the vast FL and FD lens
              ecosystem that would power Canon's success for decades to come. Each component of the Canon F-1 was engineered with professional demands in mind, creating a camera system that could withstand the rigors of photojournalism, the precision requirements of studio work, and the reliability needs of photographers working in diverse and challenging environments worldwide.</p> </div> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-1 md:col-start-2 col-end-7"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]" data-top-cover-part="focus_screen"> <span class="block">focus screen</span> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> </span> </div> </div><div class="design-grid w-full"> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-1 md:col-start-2 col-end-7"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]" data-top-cover-part="focus_screen_box"> <span class="block">focus screen box</span> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> </span> </div> </div> </div> </div> </div> </div> </div> </div> <div class="h-[100vh]"></div> <section class="px-[var(--grid-gap)]"> <div class="design-grid text-body text-light1 h-full pb-[10vmax] gap-y-[calc(var(--s)*120)] md:gap-y-0 invisible"> <div class="md:col-start-2 md:col-end-5 col-start-1 col-end-7 row-start-1" data-animation-threshold="-100px"> <p>
Introduced in the 1930s for still photography, 35mm film became the gold
      standard due to its balance of portability, image quality, and
      affordability. Each roll of 35mm film typically contains 24 or 36
      exposures, with each frame measuring 24x36mm. This aspect ratio (3:2)
      became the default for most consumer cameras and heavily influenced
      digital sensor formats. The film features perforations along both edges
      for transport mechanisms and is housed in a light-tight metal canister.
      Upon exposure, the film must be developed chemically, using C-41 (for
      color negatives), E-6 (for color slide film), or black-and-white
      developers such as D-76 or Rodinal.
</p> </div> <div class="col-start-7 col-end-13 md:col-start-9 md:col-end-12 row-start-2"> <div class="flex flex-col gap-[calc(var(--s)*60)]"> <div data-animation-threshold="-100px"> <p>
Photographers today have access to a wide array of 35mm films, each
          with its own sensory characteristics shaped by the chemical
          composition of its emulsion layers, ISO sensitivity, grain structure,
          and intended lighting conditions. Selecting the appropriate film stock
          is both a technical and artistic decision—one that influences not only
          exposure and development but also the emotional tone of the final
          image.
</p> </div> <div class="flex flex-col text-sm col-start-1 col-end-5"> <div class="grid grid-cols-5 py-[calc(var(--s)*20)] font-semibold font-inter"> <p class="col-start-2 col-end-5">Film Stock</p> <p class="col-start-5 col-end-6 justify-self-end">ISO</p> </div> <div class="relative"> <div class="absolute top-0 left-0 w-full h-[1px] bg-dark2"></div> <div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 01.
</p> <p class="col-start-2 col-end-5">Kodak Portra 400</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 02.
</p> <p class="col-start-2 col-end-5">Ilford HP5 Plus</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 03.
</p> <p class="col-start-2 col-end-5">Kodak Ektar 100</p> <p class="col-start-5 col-end-6 justify-self-end"> 100 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 04.
</p> <p class="col-start-2 col-end-5">Fujifilm Superia X-TRA 400</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 05.
</p> <p class="col-start-2 col-end-5">Cinestill 800T</p> <p class="col-start-5 col-end-6 justify-self-end"> 800 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2"></div> </div> </div> </div> </div> </div> <div class="col-start-1 col-end-7 md:col-start-2 md:col-end-5 row-start-3 md:row-start-2 md:mt-[30vmax]" data-animation-threshold="-100px"> <p>
Color negative film is composed of a complex series of layered materials,
      each meticulously engineered to respond to specific wavelengths of light.
      At the core is a transparent film base, typically made of triacetate or
      polyester, which provides structural support. Above this lies a subbing
      layer that facilitates adhesion between the base and the emulsion. The
      emulsion itself is divided into three primary light-sensitive layers, each
      responsive to a distinct portion of the visible spectrum: blue, green, and
      red light. These layers are separated by dye filters—such as yellow and
      UV-absorbing layers—to control unwanted light contamination and improve
      spectral precision. Additional layers, including anti-halation coatings
      and protective top coats, reduce glare and shield the film from mechanical
      damage. Together, these layers form a sophisticated photochemical system
      that captures color information with remarkable accuracy and depth,
      enabling the analog reproduction of real-world scenes onto a physical
      medium.
</p> </div> </div> </section> <div class="h-[300dvh] md:h-[400dvh]"></div> <section class="w-full h-[calc((5/4)*(4*var(--scan-element-height)+2*var(--scan-padding-y)-20dvh))] relative" id="section-scan"> <div class="w-full h-[100dvh] flex justify-center items-center sticky top-0 z-20"> <div class="w-full design-grid px-[var(--grid-gap)]"> <div class="col-start-2 col-end-12 md:col-start-4 md:col-end-10 aspect-[3/2]" id="scan-element"></div> </div> <div class="absolute left-0 bottom-0 w-full h-[var(--scan-padding-y)] bg-dark1"></div> </div> <div class="h-[100%] w-full absolute top-0 z-10" id="scan-steps"> <div class="h-[calc((100%-100dvh)*0.2)]"></div> <div class="h-[calc((100%-100dvh)*0.8+100dvh)] flex flex-col py-[var(--scan-padding-y)] relative"> <div class="sticky top-[var(--scan-padding-y)] w-full z-50"> <div class="absolute top-0 left-0 w-[calc(2*var(--column-width)+3*var(--grid-gap))] bg-dark1 z-50 h-[var(--scan-element-height)] origin-top invisible md:visible" id="scan-data-mask"></div> </div> <div class="flex-1 top-[var(--scan-padding-y)] sticky w-full px-[var(--grid-gap)] text-body text-orange1 flex flex-col z-10"> <div class="flex-1 md:hidden"></div> <div class="design-grid w-full"> <p class="col-start-2 col-end-13 md:col-end-4 font-inter font-semibold capitalize bg-dark1 relative"> Film negative </p> </div> </div> <div class="flex-1 top-[var(--scan-padding-y)] sticky w-full px-[var(--grid-gap)] text-body text-orange1 flex flex-col z-10"> <div class="flex-1 md:hidden"></div> <div class="design-grid w-full"> <p class="col-start-2 col-end-13 md:col-end-4 font-inter font-semibold capitalize bg-dark1 relative"> white balanced </p> </div> </div> <div class="flex-1 top-[var(--scan-padding-y)] sticky w-full px-[var(--grid-gap)] text-body text-orange1 flex flex-col z-10"> <div class="flex-1 md:hidden"></div> <div class="design-grid w-full"> <p class="col-start-2 col-end-13 md:col-end-4 font-inter font-semibold capitalize bg-dark1 relative"> film positive </p> </div> </div> <div class="flex-1 top-[var(--scan-padding-y)] sticky w-full px-[var(--grid-gap)] text-body text-orange1 flex flex-col z-10"> <div class="flex-1 md:hidden"></div> <div class="design-grid w-full"> <p class="col-start-2 col-end-13 md:col-end-4 font-inter font-semibold capitalize bg-dark1 relative"> Colors corrected </p> </div> </div> </div> </div> </section> </div> <main class="relative z-[19]" id="content"> <section class="h-[700vh] p-[calc(var(--s)*20)] relative flex flex-col" id="section-camera"> <div class="absolute top-0 left-0 w-full h-[calc(100%+100dvh)] bg-dark1 invisible" id="camera-section-mask"></div> <div class="h-[450vh]"> <div class="h-[250vh]"> <div class="pt-[calc(var(--s)*100)] flex-1"> <span class="uppercase w-full font-bold text-[10.55vw] leading-[100%] flex justify-between opacity-0"> <span class="block">Timeless</span> <span class="block">Craft</span> </span> <div class="mt-[30vmax]"> <!-- <div class={clsx("design-grid", behindCanvas && "invisible")}>
    <div
      class="col-start-2 md:col-end-4 col-end-6 text-md md:text-body text-light1"
      data-animation={behindCanvas ? undefined : "paragraph"}
    >
      <p>
        In September 1971, Canon released the F-1, forever changing the
        landscape of professional photography. This flagship SLR camera
        represented Canon's bold entry into the professional market, challenging
        the dominance of Nikon with extraordinary build quality, reliability,
        and a comprehensive system of interchangeable components.
      </p>
    </div>
  </div> --> <div class="flex flex-col gap-[calc(var(--s)*300)] md:gap-[calc(var(--s)*400)]"> <div class="design-grid w-full"> <div class="text-body text-light1 row-start-1 row-end-2 relative col-start-2 col-end-6 md:col-end-4" data-animation="paragraph"> <p class="w-full sm:absolute top-0 left-0">In September 1971, Canon released the F-1, forever changing the landscape of professional photography. This flagship SLR camera represented Canon's bold entry into the professional market, challenging the dominance of Nikon with extraordinary build quality, reliability, and a comprehensive system of interchangeable components.</p> </div> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-7 col-end-13 md:col-end-12"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]"> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> <span class="block">pentaprism</span> </span> </div> </div><div class="design-grid w-full mt-[calc(var(--s)*100)]"> <div class="text-body text-light1 row-start-1 row-end-2 relative col-start-8 md:col-start-10 col-end-13 md:col-end-12 mt-[calc(var(--s)*64)] md:mt-0" data-animation="paragraph"> <p class="w-full sm:absolute top-0 left-0">The Canon F-1 wasn't just a camera—it
              was a statement that Canon had arrived as a serious contender in
              professional photography, backed by the vast FL and FD lens
              ecosystem that would power Canon's success for decades to come. Each component of the Canon F-1 was engineered with professional demands in mind, creating a camera system that could withstand the rigors of photojournalism, the precision requirements of studio work, and the reliability needs of photographers working in diverse and challenging environments worldwide.</p> </div> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-1 md:col-start-2 col-end-7"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]"> <span class="block">focus screen</span> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> </span> </div> </div><div class="design-grid w-full"> <div class="text-sm text-orange1 row-start-1 row-end-2 font-inter font-semibold col-start-1 md:col-start-2 col-end-7"> <span class="flex items-center capitalize w-full opacity-0 transition-opacity duration-300 gap-[calc(var(--s)*20)]"> <span class="block">focus screen box</span> <span class="block border-b border-dashed border-dark2 flex-1 h-[1px]"></span> </span> </div> </div> </div> </div> </div> </div> </div> </section> <div class="fixed w-full left-0 top-[20vh] md:top-[50vh] translate-y-[-50%]"> <div class="design-grid w-full items-center opacity-0 transition-opacity duration-300 px-[var(--grid-gap)]" data-camera-cta-text=""> <div class="col-start-1 col-end-3 md:col-end-2 relative hidden md:block"> <p class="text-sm text-light1">
Hover on<br/>a lens part
</p> </div> <div class="col-start-3 md:col-start-2 md:col-end-4 col-end-6 h-0 border-b border-dashed border-dark2 hidden md:block"></div> </div> </div> <div class="fixed z-[-1] w-[25vw] md:w-[30vw] lg:w-[40vw] top-[50%] translate-y-[-50%] left-[50%] translate-x-[-50%] pointer-events-none invisible" id="camera-scaler"></div> <div class="fixed bottom-[calc(var(--s)*20)] px-[var(--grid-gap)] design-grid w-full pointer-events-none opacity-0 transition-opacity duration-300" id="part-viewer-container"> <div class="text-sm pb-[calc(var(--s)*8)] col-start-1 col-end-6 md:col-start-10 md:col-end-13 pointer-events-auto"> <p class="text-orange1 font-inter font-semibold capitalize" id="part-viewer-name"></p> <p class="text-sm text-light1 mt-[calc(var(--s)*12)]" id="part-viewer-description"></p> </div> <div class="col-start-8 md:col-start-10 col-end-13"> <div class="aspect-square relative w-full min-w-full" id="part-viewer"> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-t border-l top-0 left-0"></div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-r border-t top-0 right-0"></div> <div class="border-dark2 h-[calc(var(--s)*32)] w-[calc(var(--s)*32)] absolute border-l border-b bottom-0 left-0 hidden md:block"></div> </div> </div> </div> <div class="h-[100vh]"></div> <section class="px-[var(--grid-gap)]" id="section-film"> <div class="design-grid text-body text-light1 h-full pb-[10vmax] gap-y-[calc(var(--s)*120)] md:gap-y-0 visible"> <div class="md:col-start-2 md:col-end-5 col-start-1 col-end-7 row-start-1" data-animation="paragraph" data-animation-threshold="-100px"> <p>
Introduced in the 1930s for still photography, 35mm film became the gold
      standard due to its balance of portability, image quality, and
      affordability. Each roll of 35mm film typically contains 24 or 36
      exposures, with each frame measuring 24x36mm. This aspect ratio (3:2)
      became the default for most consumer cameras and heavily influenced
      digital sensor formats. The film features perforations along both edges
      for transport mechanisms and is housed in a light-tight metal canister.
      Upon exposure, the film must be developed chemically, using C-41 (for
      color negatives), E-6 (for color slide film), or black-and-white
      developers such as D-76 or Rodinal.
</p> </div> <div class="col-start-7 col-end-13 md:col-start-9 md:col-end-12 row-start-2"> <div class="flex flex-col gap-[calc(var(--s)*60)]"> <div data-animation="paragraph" data-animation-threshold="-100px"> <p>
Photographers today have access to a wide array of 35mm films, each
          with its own sensory characteristics shaped by the chemical
          composition of its emulsion layers, ISO sensitivity, grain structure,
          and intended lighting conditions. Selecting the appropriate film stock
          is both a technical and artistic decision—one that influences not only
          exposure and development but also the emotional tone of the final
          image.
</p> </div> <div class="flex flex-col text-sm col-start-1 col-end-5"> <div class="grid grid-cols-5 py-[calc(var(--s)*20)] font-semibold font-inter"> <p class="col-start-2 col-end-5">Film Stock</p> <p class="col-start-5 col-end-6 justify-self-end">ISO</p> </div> <div class="relative"> <div class="absolute top-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> <div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 01.
</p> <p class="col-start-2 col-end-5">Kodak Portra 400</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 02.
</p> <p class="col-start-2 col-end-5">Ilford HP5 Plus</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 03.
</p> <p class="col-start-2 col-end-5">Kodak Ektar 100</p> <p class="col-start-5 col-end-6 justify-self-end"> 100 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 04.
</p> <p class="col-start-2 col-end-5">Fujifilm Superia X-TRA 400</p> <p class="col-start-5 col-end-6 justify-self-end"> 400 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> </div><div class="relative py-[calc(var(--s)*20)] grid grid-cols-5 items-center"> <p class="text-sm col-start-1 col-end-2 text-dark2"> 05.
</p> <p class="col-start-2 col-end-5">Cinestill 800T</p> <p class="col-start-5 col-end-6 justify-self-end"> 800 </p> <div class="absolute bottom-0 left-0 w-full h-[1px] bg-dark2" data-animated-line="true"></div> </div> </div> </div> </div> </div> <div class="col-start-1 col-end-7 md:col-start-2 md:col-end-5 row-start-3 md:row-start-2 md:mt-[30vmax]" data-animation="paragraph" data-animation-threshold="-100px"> <p>
Color negative film is composed of a complex series of layered materials,
      each meticulously engineered to respond to specific wavelengths of light.
      At the core is a transparent film base, typically made of triacetate or
      polyester, which provides structural support. Above this lies a subbing
      layer that facilitates adhesion between the base and the emulsion. The
      emulsion itself is divided into three primary light-sensitive layers, each
      responsive to a distinct portion of the visible spectrum: blue, green, and
      red light. These layers are separated by dye filters—such as yellow and
      UV-absorbing layers—to control unwanted light contamination and improve
      spectral precision. Additional layers, including anti-halation coatings
      and protective top coats, reduce glare and shield the film from mechanical
      damage. Together, these layers form a sophisticated photochemical system
      that captures color information with remarkable accuracy and depth,
      enabling the analog reproduction of real-world scenes onto a physical
      medium.
</p> </div> </div> </section> <div class="fixed z-[-1] w-[30vw] lg:w-[40vw] top-[50%] translate-y-[-50%] left-[50%] translate-x-[-50%] pointer-events-none invisible" id="film-scaler"></div> <section class="px-[var(--grid-gap)] h-[300dvh] md:h-[400dvh] relative" id="section-film-layers"> <div class="w-full h-[100dvh] flex flex-col justify-center sticky top-0"> <div class="w-full design-grid"> <div class="col-start-1 md:col-start-3 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/film-base.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Film base </p> </div> </div><div class="col-start-4 hidden md:block col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/subbing-layer.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Subbing layer </p> </div> </div><div class="col-staer-3 md:col-start-5 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/red-light-sensitive-layer.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Red light sensitive layer </p> </div> </div><div class="col-start-6 hidden md:block col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/green-light-sensitive-layer.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Green light sensitive layer </p> </div> </div><div class="col-start-5 md:col-start-7 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/yellow-filter.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Yellow filter </p> </div> </div><div class="col-start-7 md:col-start-8 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/blue-light-sensitive-layer.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Blue light sensitive layer </p> </div> </div><div class="col-start-9 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/uv-filter.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> UV Filter </p> </div> </div><div class="col-start-11 md:col-start-10 col-span-2 md:col-span-1"> <div class="w-full h-[calc(2*var(--column-width)*tan(45)+4*var(--column-width)*2/3)] md:h-[calc(var(--column-width)*tan(45)+var(--column-width)*2/3)]"> <div class="w-full aspect-[3/2] -skew-y-[45deg] origin-top-right scale-y-[2]" data-layer-image=""> <img alt="" class="block" src="/images/scan/256/6.jpg"/> </div> </div> <div class="flex flex-col items-center" data-layer-text=""> <div class="h-[calc(var(--s)*96)] border-l border-dashed border-dark2 w-0"></div> <p class="text-white text-sm text-orange1 text-center mt-[calc(var(--s)*16)] font-inter font-semibold capitalize"> Protective layer </p> </div> </div> </div> </div> </section> <!-- <Scan /> --> <div class="w-full h-[calc((5/4)*(4*var(--scan-element-height)+2*var(--scan-padding-y)-20dvh))] relative"></div> <footer class="h-[100dvh]" id="footer"></footer> </main> <div class="hidden md:block" id="awwwards" style="position:absolute;top:50dvh;transform:translateY(-50%);right:0;z-index:999"> <a href="https://www.awwwards.com/sites/35mm" rel="noopener noreferrer" target="_blank"><svg height="171.358" width="53.08"><path class="js-color-bg" d="M0 0h53.08v171.358H0z" fill="white"></path><g class="js-color-text" fill="black"><path d="M30.016 151.575a3.599 3.599 0 0 1-2.484 1.878l-.965-1.535c.623-.155 1.126-.401 1.506-.737.38-.337.57-.768.57-1.293 0-.4-.101-.722-.301-.966-.199-.242-.504-.365-.912-.365-.254 0-.478.083-.674.249a2.423 2.423 0 0 0-.511.62c-.146.249-.331.603-.556 1.061l-.204.424c-.293.584-.66 1.052-1.104 1.403-.443.351-1.011.525-1.703.525-.516 0-.983-.119-1.402-.357-.42-.239-.748-.575-.986-1.009s-.357-.929-.357-1.483c0-1.413.619-2.378 1.855-2.895l.979 1.535c-.721.253-1.082.706-1.082 1.359 0 .282.09.526.271.73.182.205.402.308.665.308s.495-.091.694-.271a2.51 2.51 0 0 0 .512-.657c.141-.258.324-.631.548-1.118.224-.478.454-.879.687-1.206a2.76 2.76 0 0 1 .914-.803c.375-.211.83-.315 1.367-.315.613 0 1.152.139 1.614.417.463.278.819.665 1.067 1.162s.373 1.062.373 1.695a3.545 3.545 0 0 1-.381 1.644M21.627 145.02a1.13 1.13 0 0 1-.833.336c-.332 0-.61-.111-.834-.336s-.336-.502-.336-.833c0-.332.112-.608.336-.833s.502-.337.834-.337c.331 0 .608.112.833.337s.336.501.336.833c0 .331-.111.608-.336.833m1.285-1.74h7.367v1.812h-7.367v-1.812zM29.709 140.226c-.458.479-1.135.716-2.031.716h-3.216v1.141h-1.55v-1.141H21.07l-1.139-1.812h2.98v-1.945h1.55v1.945h3.187c.438 0 .748-.081.928-.242.181-.16.27-.402.27-.723 0-.244-.057-.479-.175-.702l1.462-.424c.176.38.264.779.264 1.198-.001.849-.23 1.511-.688 1.989M29.833 134.72a3.333 3.333 0 0 1-1.433 1.169c-.579.249-1.182.373-1.805.373s-1.225-.124-1.805-.373a3.347 3.347 0 0 1-1.434-1.169c-.375-.531-.563-1.196-.563-1.995 0-.77.184-1.413.549-1.93a3.282 3.282 0 0 1 1.381-1.14 4.239 4.239 0 0 1 1.711-.365h.746v5.072a1.796 1.796 0 0 0 1.168-.49c.332-.307.496-.724.496-1.249 0-.41-.092-.753-.277-1.031-.185-.277-.473-.528-.862-.753l.542-1.462c.691.303 1.223.724 1.592 1.265.371.541.557 1.235.557 2.083 0 .798-.188 1.463-.563 1.995m-4.085-3.574c-.41.088-.746.261-1.009.519s-.394.611-.394 1.06c0 .429.135.784.408 1.067s.604.458.994.526v-3.172zM29.898 122.64c-.33.565-.783.996-1.359 1.294-.574.297-1.221.445-1.943.445-.721 0-1.369-.148-1.943-.445-.576-.298-1.029-.729-1.36-1.294s-.496-1.232-.496-2.002c0-.771.165-1.438.496-2.003a3.301 3.301 0 0 1 1.36-1.293c.574-.298 1.223-.446 1.943-.446.723 0 1.369.148 1.943.446a3.293 3.293 0 0 1 1.359 1.293c.332.564.497 1.232.497 2.003.001.769-.165 1.436-.497 2.002m-1.703-3.347c-.433-.331-.967-.497-1.6-.497s-1.167.166-1.602.497c-.433.33-.649.778-.649 1.345 0 .564.217 1.013.649 1.344.435.332.969.498 1.602.498s1.167-.166 1.6-.498c.435-.331.65-.779.65-1.344.001-.567-.215-1.015-.65-1.345M24.462 115.16v.936h-1.55v-.936h-.381c-.866 0-1.516-.227-1.95-.68-.433-.453-.649-1.074-.649-1.863 0-.556.096-1.014.291-1.374l1.463.396a2.238 2.238 0 0 0-.205.876c0 .556.307.834.92.834h.512v-1.55h1.55v1.55h5.817v1.812h-5.818zM29.709 105.543c-.458.479-1.135.717-2.031.717h-3.216v1.14h-1.55v-1.14H21.07l-1.139-1.813h2.98v-1.944h1.55v1.944h3.187c.438 0 .748-.081.928-.241.181-.16.27-.402.27-.724 0-.244-.057-.478-.175-.702l1.462-.424c.176.38.264.78.264 1.199-.001.848-.23 1.51-.688 1.988M19.931 101.104v-1.812h4.166a2.805 2.805 0 0 1-.942-.973 2.622 2.622 0 0 1-.358-1.367c0-.711.188-1.271.562-1.681.376-.409.93-.614 1.66-.614h5.262v1.813H25.66c-.449 0-.779.09-.994.27-.215.181-.321.455-.321.826 0 .292.103.57.308.833.204.263.516.478.936.644.418.166.945.249 1.578.249h3.113v1.812H19.931zM29.833 91.469a3.333 3.333 0 0 1-1.433 1.169 4.545 4.545 0 0 1-3.61 0 3.345 3.345 0 0 1-1.433-1.169c-.375-.532-.563-1.197-.563-1.995 0-.771.184-1.413.549-1.93a3.284 3.284 0 0 1 1.381-1.141 4.239 4.239 0 0 1 1.711-.365h.746v5.072c.446-.02.838-.183 1.168-.49.332-.307.496-.724.496-1.249 0-.409-.092-.753-.277-1.03-.185-.278-.473-.529-.862-.753l.542-1.462c.691.302 1.223.724 1.592 1.265.371.541.557 1.234.557 2.083-.001.797-.189 1.463-.564 1.995m-4.085-3.574c-.41.088-.746.261-1.009.519-.263.259-.394.611-.394 1.061 0 .428.135.784.408 1.066s.604.458.994.526v-3.172zM20.047 80.572V77.37c0-.847.187-1.631.557-2.353.369-.721.938-1.303 1.703-1.746.764-.444 1.717-.665 2.857-.665 1.139 0 2.092.221 2.857.665.764.443 1.332 1.025 1.701 1.746.371.722.557 1.506.557 2.353v3.202H20.047zm8.478-2.601c0-.975-.251-1.791-.753-2.448-.501-.658-1.372-.987-2.608-.987-1.238 0-2.107.329-2.609.987-.502.657-.754 1.474-.754 2.448v.701h6.725v-.701zM29.812 70.685c-.39.419-.94.628-1.651.628-.731 0-1.315-.277-1.754-.833s-.658-1.306-.658-2.251v-1.286h-.321c-.36 0-.631.122-.812.365s-.271.551-.271.921c0 .633.282 1.059.849 1.271l-.352 1.535a2.326 2.326 0 0 1-1.484-.943c-.374-.512-.562-1.133-.562-1.863 0-.984.261-1.747.782-2.288.521-.54 1.289-.812 2.302-.812h4.399v1.492l-.936.19c.702.573 1.052 1.33 1.052 2.265.001.653-.194 1.19-.583 1.609m-1.382-3.246c-.277-.332-.645-.497-1.104-.497h-.146v1.213c0 .4.078.709.233.929.156.219.395.328.717.328a.655.655 0 0 0 .519-.227c.132-.151.197-.348.197-.592a1.742 1.742 0 0 0-.416-1.154M29.812 61.639l-6.9 2.674v-1.827l4.619-1.711-4.619-1.607v-1.813l10.349 3.801v1.754zM35.481 17.006l-4.782 14.969h-3.266l-2.584-9.682-2.584 9.682h-3.267l-4.783-14.969h3.713l2.674 10.275 2.525-10.275h3.444l2.525 10.275 2.674-10.275zM37.979 27.163c1.426 0 2.496 1.069 2.496 2.495 0 1.425-1.07 2.495-2.496 2.495s-2.494-1.07-2.494-2.495c-.001-1.426 1.068-2.495 2.494-2.495"></path></g></svg></a> </div> <canvas class="z-10" id="webgl"></canvas> </div> </div> </div> <div class="fixed top-0 left-0 z-[99] w-full h-[100dvh]" id="preloader"> <div class="w-full h-full bg-dark1 p-[calc(var(--s)*20)] relative flex justify-center items-center text-sm lg:text-xsm text-dark2"> <p data-preloader-count="" style="opacity:0">
0%
</p> <div class="absolute top-[50%] left-[50%] -translate-x-[50%] -translate-y-[50%] aspect-square" data-preloader-center="" style=""> <div class="h-[calc(var(--s)*20)] w-[calc(var(--s)*20)] border-l border-t border-dark2 absolute top-0 left-0"></div> <div class="h-[calc(var(--s)*20)] w-[calc(var(--s)*20)] border-l border-b border-dark2 absolute bottom-0 left-0"></div> <div class="h-[calc(var(--s)*20)] w-[calc(var(--s)*20)] border-t border-r border-dark2 absolute top-0 right-0"></div> <div class="h-[calc(var(--s)*20)] w-[calc(var(--s)*20)] border-b border-r border-dark2 absolute bottom-0 right-0"></div> </div> </div> </div> </body></html>
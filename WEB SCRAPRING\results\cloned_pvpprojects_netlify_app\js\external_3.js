/**
 * PVP Projects - Main JavaScript
 * Sprint 4: Funcionalidades Avançadas
 * Inclui: sistema de tema, validação de formulário, animações AOS, notificações, vídeo hero
 */

// Configurações globais
const CONFIG = {
  animationDuration: 800,
  notificationDuration: 5000,
  scrollThreshold: 100,
  themeKey: 'pvp-theme',
  formValidation: {
    required: 'Este campo é obrigatório',
    email: 'Digite um email válido',
    phone: 'Digite um telefone válido',
    minLength: '<PERSON><PERSON><PERSON> de caracteres não atingido',
    maxLength: 'Máximo de caracteres excedido'
  }
};

// Sistema de tema (claro/escuro)
class ThemeManager {
  constructor() {
    this.theme = localStorage.getItem(CONFIG.themeKey) || 'light';
    this.init();
  }

  init() {
    this.applyTheme();
    this.bindEvents();
  }

  applyTheme() {
    document.documentElement.setAttribute('data-theme', this.theme);
    this.updateThemeIcon();
  }

  toggle() {
    this.theme = this.theme === 'light' ? 'dark' : 'light';
    localStorage.setItem(CONFIG.themeKey, this.theme);
    this.applyTheme();
  }

  updateThemeIcon() {
    const toggle = document.getElementById('theme-toggle');
    if (!toggle) return;

    const icon = toggle.querySelector('svg');
    if (this.theme === 'dark') {
      icon.innerHTML = `
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
      `;
      toggle.setAttribute('aria-label', 'Alternar para modo claro');
    } else {
      icon.innerHTML = `
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
      `;
      toggle.setAttribute('aria-label', 'Alternar para modo escuro');
    }
  }

  bindEvents() {
    const toggle = document.getElementById('theme-toggle');
    if (toggle) {
      toggle.addEventListener('click', () => this.toggle());
    }
  }
}

// Sistema de notificações
class NotificationManager {
  constructor() {
    this.container = document.getElementById('notification-container');
    this.notifications = [];
  }

  show(message, type = 'info', duration = CONFIG.notificationDuration) {
    const notification = this.createNotification(message, type);
    this.container.appendChild(notification);
    this.notifications.push(notification);

    // Animar entrada
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Auto-remover
    if (duration > 0) {
      setTimeout(() => {
        this.remove(notification);
      }, duration);
    }

    return notification;
  }

  createNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} transform translate-x-full opacity-0 transition-all duration-300 max-w-sm`;
    
    // Add icon based on type
    let icon = '';
    switch(type) {
      case 'success':
        icon = '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
        break;
      case 'error':
        icon = '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
        break;
      case 'warning':
        icon = '<svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
        break;
      default:
        icon = '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
    }
    
    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    notification.innerHTML = `
      <div class="flex items-start space-x-3 p-4 rounded-lg shadow-lg">
        <div class="flex-shrink-0">
          ${icon}
        </div>
        <div class="flex-1">
          <p class="text-sm font-medium">${message}</p>
        </div>
        <button class="flex-shrink-0 text-gray-400 hover:text-gray-600" onclick="notificationManager.remove(this.parentElement.parentElement)">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;

    return notification;
  }

  remove(notification) {
    if (!notification) return;
    
    notification.classList.remove('show');
    notification.classList.add('translate-x-full', 'opacity-0');
    
    setTimeout(() => {
      if (notification.parentElement) {
        notification.parentElement.removeChild(notification);
      }
      this.notifications = this.notifications.filter(n => n !== notification);
    }, 300);
  }

  success(message) {
    return this.show(message, 'success');
  }

  error(message) {
    return this.show(message, 'error');
  }

  warning(message) {
    return this.show(message, 'warning');
  }

  info(message) {
    return this.show(message, 'info');
  }
}

// Sistema de validação de formulário
class FormValidator {
  constructor(form) {
    this.form = form;
    this.fields = form.querySelectorAll('[data-validate]');
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupRealTimeValidation();
  }

  bindEvents() {
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    
    this.fields.forEach(field => {
      field.addEventListener('blur', () => this.validateField(field));
      field.addEventListener('input', () => this.clearFieldError(field));
    });
  }

  setupRealTimeValidation() {
    this.fields.forEach(field => {
      const rules = field.getAttribute('data-validate').split(' ');
      
      field.addEventListener('input', () => {
        if (this.isFieldValid(field, rules)) {
          this.clearFieldError(field);
        }
      });
    });
  }

  validateField(field) {
    const rules = field.getAttribute('data-validate').split(' ');
    return this.isFieldValid(field, rules);
  }

  isFieldValid(field, rules) {
    const value = field.value.trim();
    
    for (const rule of rules) {
      if (!this.checkRule(field, rule, value)) {
        return false;
      }
    }
    
    return true;
  }

  checkRule(field, rule, value) {
    switch (rule) {
    case 'required':
      if (!value) {
        this.showFieldError(field, CONFIG.formValidation.required);
        return false;
      }
      break;
        
    case 'email': {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (value && !emailRegex.test(value)) {
        this.showFieldError(field, CONFIG.formValidation.email);
        return false;
      }
      break;
    }
        
    case 'phone': {
      const phoneRegex = /^[+]?[0-9\s\-()]{10,}$/;
      if (value && !phoneRegex.test(value)) {
        this.showFieldError(field, CONFIG.formValidation.phone);
        return false;
      }
      break;
    }
        
    case 'min:3':
      if (value && value.length < 3) {
        this.showFieldError(field, CONFIG.formValidation.minLength);
        return false;
      }
      break;
        
    case 'max:500':
      if (value && value.length > 500) {
        this.showFieldError(field, CONFIG.formValidation.maxLength);
        return false;
      }
      break;
    }
    
    return true;
  }

  showFieldError(field, message) {
    this.clearFieldError(field);
    
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    errorDiv.setAttribute('data-error', 'true');
    
    field.parentNode.appendChild(errorDiv);
  }

  clearFieldError(field) {
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    
    const errorDiv = field.parentNode.querySelector('[data-error="true"]');
    if (errorDiv) {
      errorDiv.remove();
    }
  }

  handleSubmit(e) {
    e.preventDefault();
    
    let isValid = true;
    this.fields.forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });
    
    if (isValid) {
      this.submitForm();
    } else {
      window.notificationManager.error('Por favor, corrija os erros no formulário.');
    }
  }

  async submitForm() {
    const submitButton = this.form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    // Mostrar loading
    submitButton.disabled = true;
    submitButton.textContent = 'Enviando...';
    
    try {
      // Simular envio (substituir por envio real)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Sucesso
      window.notificationManager.success('Formulário enviado com sucesso! Entraremos em contato em breve.');
      this.form.reset();
      
    } catch (error) {
      // Erro
      window.notificationManager.error('Erro ao enviar formulário. Tente novamente.');
      
    } finally {
      // Restaurar botão
      submitButton.disabled = false;
      submitButton.textContent = originalText;
    }
  }
}

// Sistema de animações AOS personalizadas
class AnimationManager {
  constructor() {
    this.init();
  }

  init() {
    this.setupCustomAnimations();
    this.setupScrollAnimations();
  }

  setupCustomAnimations() {
    // Animação de contadores
    this.setupCounters();
    
    // Animação de progresso
    this.setupProgressBars();
    
    // Animação de cards
    this.setupCardAnimations();
  }

  setupCounters() {
    const counters = document.querySelectorAll('[data-counter]');
    
    const animateCounter = (counter) => {
      const target = parseInt(counter.getAttribute('data-counter'));
      const duration = 2000;
      const step = target / (duration / 16);
      let current = 0;
      
      const timer = setInterval(() => {
        current += step;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        counter.textContent = Math.floor(current);
      }, 16);
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          observer.unobserve(entry.target);
        }
      });
    });

    counters.forEach(counter => observer.observe(counter));
  }

  setupProgressBars() {
    const progressBars = document.querySelectorAll('[data-progress]');
    
    const animateProgress = (bar) => {
      const target = parseInt(bar.getAttribute('data-progress'));
      const fill = bar.querySelector('.progress-fill');
      
      if (fill) {
        fill.style.width = `${target}%`;
        fill.style.transition = 'width 1.5s ease-out';
      }
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateProgress(entry.target);
          observer.unobserve(entry.target);
        }
      });
    });

    progressBars.forEach(bar => observer.observe(bar));
  }

  setupCardAnimations() {
    const cards = document.querySelectorAll('.card-animate');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('card-visible');
        }
      });
    });

    cards.forEach(card => observer.observe(card));
  }

  setupScrollAnimations() {
    // Parallax suave
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const speed = element.getAttribute('data-parallax') || 0.5;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
    });
  }
}

// Sistema de busca
class SearchManager {
  constructor() {
    this.searchInput = document.getElementById('search-input');
    this.searchResults = document.getElementById('search-results');
    this.init();
  }

  init() {
    if (this.searchInput) {
      this.searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
    }
  }

  handleSearch(query) {
    if (query.length < 2) {
      this.hideResults();
      return;
    }

    const results = this.searchProjects(query);
    this.displayResults(results);
  }

  searchProjects(query) {
    // Busca real em projetos do site
    const projects = [
      { title: 'Projeto Elétrico Dom Pedrito', url: '/projetos/dom-pedrito/', category: 'Predial', location: 'Dom Pedrito' },
      { title: 'Projeto Elétrico Marista', url: '/projetos/marista/', category: 'Institucional', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Loja Avenida', url: '/projetos/loja-avenida/', category: 'Comercial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Loja João', url: '/projetos/loja-joao/', category: 'Comercial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Lojas Remião', url: '/projetos/lojas-remiao/', category: 'Comercial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Zotti', url: '/projetos/zotti/', category: 'Comercial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Sicredi', url: '/projetos/sicredi/', category: 'Predial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Casa GT', url: '/projetos/casa-gt/', category: 'Residencial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Casa Sitio', url: '/projetos/casa-sitio/', category: 'Residencial', location: 'Sítio em Porto Alegre – RS' },
      { title: 'Projeto Elétrico Casa CD', url: '/projetos/casa-cd/', category: 'Residencial', location: 'Gravataí- RS' },
      { title: 'Projeto Elétrico Casa GP', url: '/projetos/casa-gp/', category: 'Residencial', location: 'Porto Alegre – RS' },
      { title: 'Projeto Elétrico Barão de Uba', url: '/projetos/barao-uba/', category: 'Predial', location: 'Porto Alegre' },
      { title: 'Projeto Elétrico Rodrigo Empresa', url: '/projetos/rodrigo-empresa/', category: 'Predial', location: 'Alvorada/RS' },
      { title: 'Projeto Elétrico e Hidrossanitário Alexandre', url: '/projetos/projeto-alexandre/', category: 'Residencial', location: 'Condomínio fechado – RS' },
      { title: 'Projeto Elétrico e Hidrossanitário Rafael', url: '/projetos/projeto-rafael/', category: 'Residencial', location: 'São Leopoldo – RS' },
      { title: 'Projeto Elétrico e Hidrossanitário Predio Comercio', url: '/projetos/predio-comercio/', category: 'Predial', location: 'Alvorada' },
      { title: 'Reforma AD Adriene', url: '/projetos/reforma-adriene/', category: 'Residencial', location: 'Porto Alegre, RS' },
      { title: 'Projeto Nairo', url: '/projetos/projeto-nairo/', category: 'Residencial', location: 'Condomínio fechado – RS' },
      { title: 'Projeto LEP', url: '/projetos/projeto-lep/', category: 'Residencial', location: 'Condomínio fechado – RS' }
    ];

    const searchTerms = query.toLowerCase().split(' ');
    
    return projects.filter(project => {
      const projectText = `${project.title} ${project.category} ${project.location}`.toLowerCase();
      
      return searchTerms.every(term => 
        projectText.includes(term)
      );
    });
  }

  displayResults(results) {
    if (results.length === 0) {
      this.searchResults.innerHTML = `
        <div class="p-4 text-center">
          <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="text-gray-500">Nenhum projeto encontrado.</p>
          <p class="text-sm text-gray-400">Tente outros termos de busca</p>
        </div>
      `;
    } else {
      this.searchResults.innerHTML = results.map(project => `
        <a href="${project.url}" class="block p-4 hover:bg-gray-50 border-b border-gray-200 last:border-b-0 transition-colors duration-200">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="font-medium text-gray-900">${project.title}</div>
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  ${project.category}
                </span>
                <span>•</span>
                <span>${project.location}</span>
              </div>
            </div>
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </a>
      `).join('');
    }
    
    this.searchResults.classList.remove('hidden');
  }

  hideResults() {
    this.searchResults.classList.add('hidden');
  }
}

// Sistema de performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.init();
  }

  init() {
    this.measurePageLoad();
    this.measureCoreWebVitals();
  }

  measurePageLoad() {
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.metrics.pageLoadTime = loadTime;
      
      // Log para analytics
      if (typeof window.gtag !== 'undefined') {
        window.gtag('event', 'timing_complete', {
          name: 'page_load',
          value: Math.round(loadTime)
        });
      }
    });
  }

  measureCoreWebVitals() {
    // LCP (Largest Contentful Paint)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.metrics.lcp = lastEntry.startTime;
      
      if (typeof window.gtag !== 'undefined') {
        window.gtag('event', 'largest_contentful_paint', {
          value: Math.round(lastEntry.startTime)
        });
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // FID (First Input Delay)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        this.metrics.fid = entry.processingStart - entry.startTime;
        
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'first_input_delay', {
            value: Math.round(this.metrics.fid)
          });
        }
      });
    }).observe({ entryTypes: ['first-input'] });

    // CLS (Cumulative Layout Shift)
    let clsValue = 0;
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.metrics.cls = clsValue;
      
      if (typeof window.gtag !== 'undefined') {
        window.gtag('event', 'cumulative_layout_shift', {
          value: Math.round(clsValue * 1000) / 1000
        });
      }
    }).observe({ entryTypes: ['layout-shift'] });
  }
}

// Sistema de vídeo hero
class VideoManager {
  constructor() {
    this.video = document.getElementById('hero-video');
    this.init();
  }

  init() {
    if (this.video) {
      this.bindEvents();
      this.ensureVideoPlays();
    }
  }

  bindEvents() {
    // Video events for error handling
    this.video.addEventListener('error', (e) => {
      console.error('Video error:', e);
      this.showFallback();
    });

    // Ensure video plays when loaded
    this.video.addEventListener('loadeddata', () => {
      this.ensureVideoPlays();
    });
  }

  ensureVideoPlays() {
    try {
      // Ensure video is visible and plays automatically
      this.video.style.zIndex = '0';
      this.video.style.display = 'block';
      
      this.video.play().catch(error => {
        console.log('Video autoplay failed:', error);
        // Try to play on user interaction
        document.addEventListener('click', () => {
          this.video.play().catch(e => console.log('Video play failed:', e));
        }, { once: true });
      });
    } catch (error) {
      console.error('Video play error:', error);
    }
  }

  showFallback() {
    // Show a fallback message or image
    console.log('Video not available, showing fallback');
    
    // You can add a fallback image or message here
    const container = this.video.parentElement;
    if (container) {
      container.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-100 rounded-lg">
          <div class="text-center">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <p class="text-gray-600">Vídeo não disponível</p>
          </div>
        </div>
      `;
    }
  }
}

// Sistema de animações Anime.js
class AnimeAnimationManager {
  constructor() {
    this.init();
  }
  
  init() {
    this.initServiceAnimations();
    this.initScrollAnimations();
    this.initHoverEffects();
  }
  
  initServiceAnimations() {
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach((card, index) => {
      // Initial state
      anime.set(card, {
        scale: 0.9,
        opacity: 0,
        translateY: 50
      });
      
      // Hover animations
      card.addEventListener('mouseenter', () => {
        anime({
          targets: card,
          scale: 1.05,
          translateY: -10,
          duration: 300,
          easing: 'easeOutQuad'
        });
      });
      
      card.addEventListener('mouseleave', () => {
        anime({
          targets: card,
          scale: 1,
          translateY: 0,
          duration: 300,
          easing: 'easeOutQuad'
        });
      });
      
      // Entrance animation
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            anime({
              targets: card,
              scale: 1,
              opacity: 1,
              translateY: 0,
              duration: 600,
              delay: index * 100,
              easing: 'easeOutQuad'
            });
            observer.unobserve(card);
          }
        });
      });
      
      observer.observe(card);
    });
  }
  
  initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-anime]');
    
    animatedElements.forEach(element => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const animation = element.dataset.anime;
            this.playAnimation(element, animation);
            observer.unobserve(element);
          }
        });
      });
      
      observer.observe(element);
    });
  }
  
  playAnimation(element, animation) {
    switch (animation) {
      case 'fade-up':
        anime({
          targets: element,
          opacity: [0, 1],
          translateY: [50, 0],
          duration: 800,
          easing: 'easeOutQuad'
        });
        break;
        
      case 'slide-left':
        anime({
          targets: element,
          opacity: [0, 1],
          translateX: [-50, 0],
          duration: 800,
          easing: 'easeOutQuad'
        });
        break;
        
      case 'scale-in':
        anime({
          targets: element,
          scale: [0.8, 1],
          opacity: [0, 1],
          duration: 600,
          easing: 'easeOutBack'
        });
        break;
        
      case 'bounce-in':
        anime({
          targets: element,
          scale: [0, 1.2, 1],
          opacity: [0, 1],
          duration: 800,
          easing: 'easeOutElastic(1, 0.5)'
        });
        break;
    }
  }
  
  initHoverEffects() {
    // Button hover effects
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        anime({
          targets: button,
          scale: 1.05,
          duration: 200,
          easing: 'easeOutQuad'
        });
      });
      
      button.addEventListener('mouseleave', () => {
        anime({
          targets: button,
          scale: 1,
          duration: 200,
          easing: 'easeOutQuad'
        });
      });
    });
    
    // Card hover effects
    const cards = document.querySelectorAll('.project-card, .metric-card');
    
    cards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        anime({
          targets: card,
          translateY: -5,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          duration: 300,
          easing: 'easeOutQuad'
        });
      });
      
      card.addEventListener('mouseleave', () => {
        anime({
          targets: card,
          translateY: 0,
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          duration: 300,
          easing: 'easeOutQuad'
        });
      });
    });
  }
}

// Proteção de PDFs contra downloads (melhorada para mobile)
function protectPDFs() {
  // Detecta se é dispositivo móvel
  const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  // Remove todos os links de download de PDFs
  const pdfLinks = document.querySelectorAll('a[href*=".pdf"]');
  pdfLinks.forEach(link => {
    // Remove target="_blank" para evitar downloads
    link.removeAttribute('target');
    
    // Adiciona classe para estilização
    link.classList.add('pdf-link');
    
    // Em mobile, permite cliques diretos nos PDFs
    if (!isMobile) {
      // Intercepta cliques em links de PDF apenas em desktop
      link.addEventListener('click', function(e) {
        e.preventDefault();
        
        const pdfUrl = this.getAttribute('href');
        if (pdfUrl && pdfUrl.includes('.pdf')) {
          // Cria um container de visualização protegida
          showProtectedPDF(pdfUrl);
        }
      });
    }
  });
  
  // Protege objetos PDF existentes (mais suave em mobile)
  const pdfObjects = document.querySelectorAll('object[data*=".pdf"], embed[src*=".pdf"]');
  pdfObjects.forEach(obj => {
    // Proteção mais suave em mobile
    if (!isMobile) {
      // Adiciona proteção ao objeto apenas em desktop
      obj.addEventListener('contextmenu', e => e.preventDefault());
      obj.addEventListener('selectstart', e => e.preventDefault());
      obj.addEventListener('dragstart', e => e.preventDefault());
    }
    
    // Remove link de download do fallback
    const fallbackLink = obj.querySelector('a[href*=".pdf"]');
    if (fallbackLink) {
      fallbackLink.remove();
    }
  });
}

// Função para mostrar PDF em visualização protegida
function showProtectedPDF(pdfUrl) {
  // Remove modal existente se houver
  const existingModal = document.getElementById('pdf-protection-modal');
  if (existingModal) {
    existingModal.remove();
  }
  
  // Cria modal de proteção
  const modal = document.createElement('div');
  modal.id = 'pdf-protection-modal';
  modal.className = 'pdf-protection-modal';
  modal.innerHTML = `
    <div class="pdf-protection-content">
      <div class="pdf-protection-header">
        <h3>🔒 Visualização Protegida</h3>
        <button class="pdf-protection-close">&times;</button>
      </div>
      <div class="pdf-protection-body">
        <object data="${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0" 
                type="application/pdf" 
                width="100%" 
                height="600">
          <p>PDF indisponível para visualização.</p>
        </object>
      </div>
      <div class="pdf-protection-footer">
        <p>⚠️ Este documento está protegido contra downloads e cópia.</p>
      </div>
    </div>
  `;
  
  // Adiciona ao body
  document.body.appendChild(modal);
  
  // Adiciona estilos inline para garantir proteção
  const style = document.createElement('style');
  style.textContent = `
    .pdf-protection-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    
    .pdf-protection-content {
      background: white;
      border-radius: 8px;
      width: 90%;
      max-width: 1000px;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .pdf-protection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }
    
    .pdf-protection-header h3 {
      margin: 0;
      color: #333;
    }
    
    .pdf-protection-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .pdf-protection-close:hover {
      color: #333;
    }
    
    .pdf-protection-body {
      padding: 0;
      height: 600px;
      overflow: hidden;
    }
    
    .pdf-protection-body object {
      width: 100%;
      height: 100%;
      border: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    
    .pdf-protection-footer {
      padding: 10px 20px;
      background: #fff3cd;
      border-top: 1px solid #ffeaa7;
      text-align: center;
      font-size: 14px;
      color: #856404;
    }
  `;
  
  document.head.appendChild(style);
  
  // Adiciona proteção ao objeto PDF
  const pdfObject = modal.querySelector('object');
  pdfObject.addEventListener('contextmenu', e => e.preventDefault());
  pdfObject.addEventListener('selectstart', e => e.preventDefault());
  pdfObject.addEventListener('dragstart', e => e.preventDefault());
  
  // Fecha modal
  const closeBtn = modal.querySelector('.pdf-protection-close');
  closeBtn.addEventListener('click', () => {
    modal.remove();
    style.remove();
  });
  
  // Fecha modal ao clicar fora
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
      style.remove();
    }
  });
  
  // Previne fechamento com ESC
  document.addEventListener('keydown', function closeOnEsc(e) {
    if (e.key === 'Escape') {
      modal.remove();
      style.remove();
      document.removeEventListener('keydown', closeOnEsc);
    }
  });
}

// Proteção adicional contra tentativas de download
function addDownloadProtection() {
  // Intercepta tentativas de download via teclas
  document.addEventListener('keydown', function(e) {
    // Ctrl+S, Ctrl+P, F12, etc.
    if ((e.ctrlKey && (e.key === 's' || e.key === 'p')) || 
        e.key === 'F12' || 
        (e.ctrlKey && e.shiftKey && e.key === 'I')) {
      e.preventDefault();
      showProtectionWarning();
    }
  });
  
  // Intercepta menu de contexto
  document.addEventListener('contextmenu', function(e) {
    const target = e.target;
    if (target.closest('.pdf-container') || 
        target.closest('object[data*=".pdf"]') ||
        target.closest('#pdf-protection-modal')) {
      e.preventDefault();
      showProtectionWarning();
    }
  });
  
  // Intercepta tentativas de arrastar
  document.addEventListener('dragstart', function(e) {
    const target = e.target;
    if (target.closest('.pdf-container') || 
        target.closest('object[data*=".pdf"]')) {
      e.preventDefault();
      showProtectionWarning();
    }
  });
}

// Mostra aviso de proteção
function showProtectionWarning() {
  // Remove aviso existente
  const existingWarning = document.getElementById('protection-warning');
  if (existingWarning) {
    existingWarning.remove();
  }
  
  const warning = document.createElement('div');
  warning.id = 'protection-warning';
  warning.innerHTML = `
    <div class="protection-warning-content">
      <span>🔒 Conteúdo Protegido</span>
    </div>
  `;
  
  // Adiciona estilos
  const style = document.createElement('style');
  style.textContent = `
    #protection-warning {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10000;
      animation: warningFade 2s ease-in-out;
    }
    
    .protection-warning-content {
      background: rgba(220, 53, 69, 0.9);
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    @keyframes warningFade {
      0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
      20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
      80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
      100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }
  `;
  
  document.head.appendChild(style);
  document.body.appendChild(warning);
  
  // Remove após 2 segundos
  setTimeout(() => {
    warning.remove();
    style.remove();
  }, 2000);
}

// Inicializa proteções quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
  // protectPDFs(); // Temporarily disabled to test embedded PDF rendering
  addDownloadProtection();
  enhancePdfEmbeds();
  removePdfFallbackLinks();
  stripPdfLinksOnMobile();
  stripPdfOpenLinksAll();
  observeAndStripPdfLinks();
});

// Inicialização quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
  // Inicializar sistemas
  window.themeManager = new ThemeManager();
  window.notificationManager = new NotificationManager();
  window.animationManager = new AnimationManager();
  window.animeAnimationManager = new AnimeAnimationManager();
  window.searchManager = new SearchManager();
  window.performanceMonitor = new PerformanceMonitor();
  window.videoManager = new VideoManager();

  // Inicializar validadores de formulário
  const forms = document.querySelectorAll('form[data-validate]');
  forms.forEach(form => new FormValidator(form));

  // Remover loader
  const loader = document.querySelector('.loader');
  if (loader) {
    setTimeout(() => {
      loader.style.opacity = '0';
      setTimeout(() => {
        loader.style.display = 'none';
      }, 300);
    }, 500);
  }

  // Scroll to top
  const scrollToTop = document.querySelector('.scroll-to-top');
  if (scrollToTop) {
    window.addEventListener('scroll', () => {
      if (window.pageYOffset > CONFIG.scrollThreshold) {
        scrollToTop.classList.remove('opacity-0', 'pointer-events-none');
      } else {
        scrollToTop.classList.add('opacity-0', 'pointer-events-none');
      }
    });

    scrollToTop.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }

  // Smooth scroll para links internos
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Lazy loading para imagens
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));


});

// Sistema de carrossel de projetos - Versão Automática Contínua
let currentSlide = 0;
let carouselInterval;
let isAutoPlaying = true;

function initCarousel() {
  console.log('Inicializando carrossel...');
  const carousel = document.getElementById('projects-carousel');
  if (!carousel) {
    console.log('Carrossel não encontrado!');
    return;
  }
  
  const slides = carousel.querySelectorAll('.project-card');
  const totalSlides = slides.length;
  console.log(`Encontrados ${totalSlides} slides`);
  
  if (totalSlides === 0) {
    console.log('Nenhum slide encontrado!');
    return;
  }
  
  // Configurar largura do container para movimento contínuo
  const slidesPerView = getSlidesPerView();
  console.log(`Slides por view: ${slidesPerView}`);
  
  // Se há poucos slides, duplicar para criar movimento contínuo
  if (totalSlides <= slidesPerView * 2) {
    duplicateSlides(carousel, slides);
  }
  
  // Recalcular após duplicação
  const finalSlides = carousel.querySelectorAll('.project-card');
  const totalWidth = finalSlides.length * (100 / slidesPerView);
  carousel.style.width = `${totalWidth}%`;
  console.log(`Largura do carrossel: ${totalWidth}%`);
  
  // Iniciar movimento automático
  startCarouselAutoPlay();
  
  // Pausar no hover
  carousel.addEventListener('mouseenter', pauseCarousel);
  carousel.addEventListener('mouseleave', resumeCarousel);
  
  // Pausar no touch para dispositivos móveis
  carousel.addEventListener('touchstart', pauseCarousel);
  carousel.addEventListener('touchend', resumeCarousel);
  
  // Adicionar eventos aos indicadores de progresso
  const dots = document.querySelectorAll('.progress-dot');
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      goToSlide(index);
    });
  });
  
  console.log('Carrossel inicializado com sucesso!');
}

function duplicateSlides(carousel, slides) {
  console.log('Duplicando slides...');
  // Duplicar slides para movimento contínuo
  slides.forEach(slide => {
    const clone = slide.cloneNode(true);
    carousel.appendChild(clone);
  });
  console.log(`Slides duplicados. Total: ${carousel.querySelectorAll('.project-card').length}`);
}

function getSlidesPerView() {
  if (window.innerWidth < 480) return 1;
  if (window.innerWidth < 768) return 2;
  return 3;
}

function moveCarousel(direction) {
  const carousel = document.getElementById('projects-carousel');
  if (!carousel) return;
  
  const slides = carousel.querySelectorAll('.project-card');
  const totalSlides = slides.length;
  const slidesPerView = getSlidesPerView();
  
  if (direction === 1) {
    currentSlide++;
    // Reset para movimento contínuo
    if (currentSlide >= totalSlides - slidesPerView) {
      currentSlide = 0;
    }
  } else {
    currentSlide--;
    if (currentSlide < 0) {
      currentSlide = totalSlides - slidesPerView;
    }
  }
  
  // Calcular translate baseado na largura real dos cards
  const cardWidth = 100 / slidesPerView;
  const translateX = -(currentSlide * cardWidth);
  carousel.style.transform = `translateX(${translateX}%)`;
  console.log(`Movendo carrossel: slide ${currentSlide}, translateX: ${translateX}%`);
  
  // Atualizar indicadores de progresso
  updateProgressIndicators();
}

function goToSlide(slideIndex) {
  const carousel = document.getElementById('projects-carousel');
  if (!carousel) return;
  
  const slides = carousel.querySelectorAll('.project-card');
  const totalSlides = slides.length;
  const slidesPerView = getSlidesPerView();
  
  currentSlide = slideIndex;
  
  // Garantir que o slide está dentro dos limites
  if (currentSlide >= totalSlides - slidesPerView) {
    currentSlide = totalSlides - slidesPerView;
  }
  if (currentSlide < 0) {
    currentSlide = 0;
  }
  
  // Calcular translate
  const cardWidth = 100 / slidesPerView;
  const translateX = -(currentSlide * cardWidth);
  carousel.style.transform = `translateX(${translateX}%)`;
  
  // Atualizar indicadores
  updateProgressIndicators();
}

function updateProgressIndicators() {
  const dots = document.querySelectorAll('.progress-dot');
  const totalSlides = document.querySelectorAll('#projects-carousel .project-card').length;
  const slidesPerView = getSlidesPerView();
  const maxSlides = totalSlides - slidesPerView;
  
  dots.forEach((dot, index) => {
    dot.classList.remove('active');
    if (index === currentSlide % maxSlides) {
      dot.classList.add('active');
    }
  });
}

function startCarouselAutoPlay() {
  if (carouselInterval) clearInterval(carouselInterval);
  
  carouselInterval = setInterval(() => {
    if (isAutoPlaying) {
      moveCarousel(1);
    }
  }, 4000); // Movimento a cada 4 segundos como solicitado
  
  console.log('Auto-play iniciado');
}

function pauseCarousel() {
  isAutoPlaying = false;
  console.log('Carrossel pausado');
}

function resumeCarousel() {
  isAutoPlaying = true;
  console.log('Carrossel retomado');
}

// Inicializar carrossel quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM carregado, inicializando carrossel...');
  setTimeout(() => {
    initCarousel();
  }, 1000); // Aguardar 1 segundo para garantir que tudo carregou
});

// Exportar para uso global
window.CONFIG = CONFIG;
window.moveCarousel = moveCarousel; 

// --- Mobile PDF inline preview (lightweight) using PDF.js ---
/**
 * On mobile/tablet, replace <object> PDF embeds with a lightweight image preview
 * of the first page (rasterized) to avoid heavy memory usage/crashes.
 */
function enhancePdfEmbeds() {
  try {
    const isMobile = window.innerWidth <= 1024 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (!isMobile) return;

    const pdfObjects = document.querySelectorAll('.pdf-container object[type="application/pdf"], .pdf-container object[data*=".pdf"]');
    if (!pdfObjects.length) return;

    injectPdfViewerStyles();

    pdfObjects.forEach(originalObject => {
      const container = originalObject.closest('.pdf-container') || originalObject.parentElement;
      if (!container) return;

      const rawUrl = originalObject.getAttribute('data') || '';
      const pdfUrl = rawUrl.split('#')[0];
      if (!pdfUrl || !pdfUrl.endsWith('.pdf')) return;

      // Prepare placeholder and lazy-load preview when visible
      container.innerHTML = '<div class="pdf-preview-skeleton"></div>';

      const observer = new IntersectionObserver((entries, obs) => {
        entries.forEach(entry => {
          if (!entry.isIntersecting) return;
          obs.unobserve(entry.target);

          // Load PDF.js only when needed
          loadPdfJs().then(() => {
            // Timeout fallback to avoid long stalls
            const timeoutId = setTimeout(() => {
              container.innerHTML = `<div class=\"pdfjs-error\">Prévia indisponível.</div>`;
            }, 15000);

            window.pdfjsLib.getDocument({ 
              url: pdfUrl,
              disableAutoFetch: true,      // não busca páginas seguintes automaticamente
              disableStream: false,        // permite streaming com range requests (mais leve)
              withCredentials: false,
              rangeChunkSize: 65536        // chunks menores para dispositivos móveis
            }).promise
              .then(doc => doc.getPage(1).then(page => ({ doc, page })))
              .then(({ doc, page }) => {
                clearTimeout(timeoutId);
                // Compute small preview width (max 600px)
                const targetWidth = Math.min(container.clientWidth || 600, 600);
                const baseViewport = page.getViewport({ scale: 1 });
                const scale = Math.max(0.4, Math.min(0.9, targetWidth / baseViewport.width));
                const viewport = page.getViewport({ scale });

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d', { alpha: false });
                // Use low DPR to reduce memory
                const dpr = 1;
                canvas.width = Math.floor(viewport.width * dpr);
                canvas.height = Math.floor(viewport.height * dpr);
                canvas.style.width = `${Math.floor(viewport.width)}px`;
                canvas.style.height = `${Math.floor(viewport.height)}px`;

                const renderTask = page.render({ canvasContext: ctx, viewport });
                return renderTask.promise.then(() => {
                  // Convert to compressed JPEG data URL
                  const dataUrl = canvas.toDataURL('image/jpeg', 0.7);
                  // Replace skeleton with preview image and an "Abrir" link
                  container.innerHTML = '';
                  const img = document.createElement('img');
                  img.className = 'pdf-preview-image';
                  img.alt = 'Prévia do PDF';
                  img.decoding = 'async';
                  img.loading = 'lazy';
                  img.src = dataUrl;
                  container.appendChild(img);

                  // No external open/download links per requirement

                  // Cleanup
                  try { doc.destroy(); } catch (_) {}
                });
              })
              .catch(() => {
                container.innerHTML = `<div class=\"pdfjs-error\">Prévia indisponível.</div>`;
              });
          }).catch(() => {
            // If pdf.js fails to load, no external links
            container.innerHTML = `<div class=\"pdfjs-error\">Prévia indisponível.</div>`;
          });
        });
      });

      observer.observe(container);
    });
  } catch (_) {
    // Silent fail
  }
}

function loadPdfJs() {
  return new Promise((resolve, reject) => {
    if (window.pdfjsLib && window.pdfjsLib.getDocument) {
      return resolve();
    }
    const script = document.createElement('script');
    // Use a stable UMD build compatible with non-module environments
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js';
    script.async = true;
    script.onload = () => {
      try {
        if (window.pdfjsLib && window.pdfjsLib.GlobalWorkerOptions) {
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';
        }
        resolve();
      } catch (e) {
        reject(e);
      }
    };
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

function injectPdfViewerStyles() {
  if (document.getElementById('pdfjs-viewer-styles')) return;
  const style = document.createElement('style');
  style.id = 'pdfjs-viewer-styles';
  style.textContent = `
    .pdf-preview-skeleton { height: 260px; background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 37%, #f3f4f6 63%); background-size: 400% 100%; animation: pdf-skeleton 1.4s ease infinite; border-radius: 8px; }
    @keyframes pdf-skeleton { 0% { background-position: 100% 50%; } 100% { background-position: 0 50%; } }
    .pdf-preview-image { width: 100%; height: auto; display: block; border-radius: 8px; box-shadow: 0 1px 4px rgba(0,0,0,.06); }
    .pdf-open-link { display: inline-block; margin-top: 8px; font-weight: 600; color: #3b82f6; text-decoration: none; }
    .pdf-open-link:hover { text-decoration: underline; }
    .pdfjs-error { padding: 10px; background: #fff3cd; color: #856404; border: 1px solid #ffe08a; border-radius: 8px; }
  `;
  document.head.appendChild(style);
}

// Remove any fallback download/open links inside PDF containers
function removePdfFallbackLinks() {
  try {
    const anchors = document.querySelectorAll('.pdf-container p a[href*=\".pdf\"]');
    anchors.forEach(a => {
      const p = a.closest('p');
      if (p) {
        p.textContent = 'Prévia indisponível.';
      } else {
        a.remove();
      }
    });
  } catch (_) {
    // no-op
  }
}

// Remove qualquer link direto para .pdf em dispositivos móveis (inclui iOS)
function stripPdfLinksOnMobile() {
  try {
    const isMobile = window.innerWidth <= 1024 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (!isMobile) return;
    document.querySelectorAll('a[href*=".pdf"]').forEach(a => {
      const span = document.createElement('span');
      span.textContent = a.textContent || '';
      a.replaceWith(span);
    });
  } catch (_) {
    // no-op
  }
}

// Remove especificamente textos/links "Abrir documento" em qualquer plataforma
function stripPdfOpenLinksAll() {
  try {
    // Remover âncoras cujo texto contenha 'Abrir documento' (case-insensitive)
    document.querySelectorAll('a').forEach(a => {
      const text = (a.textContent || '').trim().toLowerCase();
      if (text.includes('abrir documento') || text === 'abrir') {
        const span = document.createElement('span');
        span.textContent = '';
        a.replaceWith(span);
      }
    });
    // Remover parágrafos que contenham exatamente o fallback com link
    document.querySelectorAll('.pdf-container p').forEach(p => {
      const t = (p.textContent || '').trim().toLowerCase();
      if (t.includes('pdf indisponível') || t.includes('pdf indisponivel') || t.includes('abrir documento')) {
        p.textContent = 'Prévia indisponível.';
      }
    });
  } catch (_) {
    // no-op
  }
}

// Observa alterações no DOM e remove links para PDFs ou "Abrir documento" que apareçam depois do load
function observeAndStripPdfLinks() {
  try {
    const observer = new MutationObserver((mutations) => {
      let changed = false;
      mutations.forEach(m => {
        m.addedNodes && m.addedNodes.forEach(node => {
          if (!(node instanceof Element)) return;
          // Remove anchors with .pdf href
          node.querySelectorAll && node.querySelectorAll('a[href*=".pdf"]').forEach(a => {
            const span = document.createElement('span');
            span.textContent = a.textContent || '';
            a.replaceWith(span);
            changed = true;
          });
          // Remove anchors with text "Abrir documento"
          node.querySelectorAll && node.querySelectorAll('a').forEach(a => {
            const text = (a.textContent || '').trim().toLowerCase();
            if (text.includes('abrir documento') || text === 'abrir') {
              const span = document.createElement('span');
              span.textContent = '';
              a.replaceWith(span);
              changed = true;
            }
          });
          // Normalize fallback paragraphs
          if (node.matches && node.matches('.pdf-container p')) {
            const t = (node.textContent || '').trim().toLowerCase();
            if (t.includes('pdf indisponível') || t.includes('pdf indisponivel') || t.includes('abrir documento')) {
              node.textContent = 'Prévia indisponível.';
              changed = true;
            }
          }
        });
      });
      // optional log
      if (changed) {
        // console.debug('PDF links stripped by observer');
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  } catch (_) {
    // no-op
  }
}
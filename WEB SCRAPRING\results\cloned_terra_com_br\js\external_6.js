/* jshint ignore:start */
/* prebid.js v9.50.0
Updated: 2025-06-16
Modules: userId, appnexusBidAdapter, criteoBidAdapter, rubiconBidAdapter, seedtagBidAdapter, smartadserverBidAdapter, taboolaBidAdapter, teadsBidAdapter, gptPreAuction, adpod, schain, dfpAdServerVideo, criteoIdSystem, pairIdSystem, identityLinkIdSystem */
if(window.pbjs&&window.pbjs.libLoaded)try{window.pbjs.getConfig("debug")&&console.warn("Attempted to load a copy of Prebid.js that clashes with the existing 'pbjs' instance. Load aborted.")}catch(e){}else (function(){
(()=>{var t,r={70433:(t,r,e)=>{function n(t,r,e,n,o){for(r=r.split?r.split("."):r,n=0;n<r.length;n++)t=t?t[r[n]]:o;return t===o?e:t}e.d(r,{A:()=>n})},68128:t=>{
/*
* @license MIT
* Fun Hooks v1.1.0
* (c) @snapwich
*/
i.SYNC=1,i.ASYNC=2,i.QUEUE=4;var r="fun-hooks",e=Object.freeze({ready:0}),n=new WeakMap;function o(t,r){return Array.prototype.slice.call(t,r)}function i(t){var f,a={},c=[];function u(t,r){return"function"==typeof t?s.call(null,"sync",t,r):"string"==typeof t&&"function"==typeof r?s.apply(null,arguments):"object"==typeof t?l.apply(null,arguments):void 0}function l(t,r,e){var n=!0;void 0===r&&(r=Object.getOwnPropertyNames(t).filter((t=>!t.match(/^_/))),n=!1);var o={},i=["constructor"];do{r.forEach((function(r){var n=r.match(/(?:(sync|async):)?(.+)/),f=n[1]||"sync",a=n[2];if(!o[a]&&"function"==typeof t[a]&&-1===i.indexOf(a)){var c=t[a];o[a]=t[a]=s(f,c,e?[e,a]:void 0)}})),t=Object.getPrototypeOf(t)}while(n&&t);return o}function p(t){var e=Array.isArray(t)?t:t.split(".");return e.reduce((function(n,o,i){var a=n[o],u=!1;return a||(i===e.length-1?(f||c.push((function(){u||console.warn(r+": referenced '"+t+"' but it was never created")})),n[o]=y((function(t){n[o]=t,u=!0}))):n[o]={})}),a)}function y(t){var r=[],e=[],o=function(){},i={before:function(t,e){return a.call(this,r,"before",t,e)},after:function(t,r){return a.call(this,e,"after",t,r)},getHooks:function(t){var n=r.concat(e);"object"==typeof t&&(n=n.filter((function(r){return Object.keys(t).every((function(e){return r[e]===t[e]}))})));try{Object.assign(n,{remove:function(){return n.forEach((function(t){t.remove()})),this}})}catch(t){console.error("error adding `remove` to array, did you modify Array.prototype?")}return n},removeAll:function(){return this.getHooks().remove()}},f={install:function(n,i,f){this.type=n,o=f,f(r,e),t&&t(i)}};return n.set(i.after,f),i;function a(t,n,i,f){var a={hook:i,type:n,priority:f||10,remove:function(){var n=t.indexOf(a);-1!==n&&(t.splice(n,1),o(r,e))}};return t.push(a),t.sort((function(t,r){return r.priority-t.priority})),o(r,e),this}}function s(e,a,u){var l=a.after&&n.get(a.after);if(l){if(l.type!==e)throw r+": recreated hookable with different type";return a}var s,v=u?p(u):y(),h={get:function(t,r){return v[r]||Reflect.get.apply(Reflect,arguments)}};f||c.push(b);var d=new Proxy(a,h);return n.get(d.after).install(e,d,(function(t,r){var n,i=[];t.length||r.length?(t.forEach(f),n=i.push(void 0)-1,r.forEach(f),s=function(t,r,f){var a,c=i.slice(),u=0,l="async"===e&&"function"==typeof f[f.length-1]&&f.pop();function p(t){"sync"===e?a=t:l&&l.apply(null,arguments)}function y(t){if(c[u]){var n=o(arguments);return y.bail=p,n.unshift(y),c[u++].apply(r,n)}"sync"===e?a=t:l&&l.apply(null,arguments)}return c[n]=function(){var n=o(arguments,1);"async"===e&&l&&(delete y.bail,n.push(y));var i=t.apply(r,n);"sync"===e&&y(i)},y.apply(null,f),a}):s=void 0;function f(t){i.push(t.hook)}b()})),d;function b(){!f&&("sync"!==e||t.ready&i.SYNC)&&("async"!==e||t.ready&i.ASYNC)?"sync"!==e&&t.ready&i.QUEUE?h.apply=function(){var t=arguments;c.push((function(){d.apply(t[1],t[2])}))}:h.apply=function(){throw r+": hooked function not ready"}:h.apply=s}}return(t=Object.assign({},e,t)).ready?u.ready=function(){f=!0,function(t){for(var r;r=t.shift();)r()}(c)}:f=!0,u.get=p,u}t.exports=i},63172:(t,r,e)=>{function n(t,r,e){r.split&&(r=r.split("."));for(var n,o,i=0,f=r.length,a=t;i<f&&"__proto__"!=(o=""+r[i++])&&"constructor"!==o&&"prototype"!==o;)a=a[o]=i===f?e:typeof(n=a[o])==typeof r?n:0*r[i]!=0||~(""+r[i]).indexOf(".")?{}:[]}e.d(r,{J:()=>n})},45751:(t,r,e)=>{function n(t){var r,e,o;if(Array.isArray(t)){for(e=Array(r=t.length);r--;)e[r]=(o=t[r])&&"object"==typeof o?n(o):o;return e}if("[object Object]"===Object.prototype.toString.call(t)){for(r in e={},t)"__proto__"===r?Object.defineProperty(e,r,{value:n(t[r]),configurable:!0,enumerable:!0,writable:!0}):e[r]=(o=t[r])&&"object"==typeof o?n(o):o;return e}return t}e.d(r,{Q:()=>n})}},e={};function n(t){var o=e[t];if(void 0!==o)return o.exports;var i=e[t]={exports:{}};return r[t].call(i.exports,i,i.exports,n),i.exports}n.m=r,t=[],n.O=(r,e,o,i)=>{if(!e){var f=1/0;for(l=0;l<t.length;l++){e=t[l][0],o=t[l][1],i=t[l][2];for(var a=!0,c=0;c<e.length;c++)(!1&i||f>=i)&&Object.keys(n.O).every((t=>n.O[t](e[c])))?e.splice(c--,1):(a=!1,i<f&&(f=i));if(a){t.splice(l--,1);var u=o();void 0!==u&&(r=u)}}return r}i=i||0;for(var l=t.length;l>0&&t[l-1][2]>i;l--)t[l]=t[l-1];t[l]=[e,o,i]},n.n=t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return n.d(r,{a:r}),r},n.d=(t,r)=>{for(var e in r)n.o(r,e)&&!n.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:r[e]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,r)=>Object.prototype.hasOwnProperty.call(t,r),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t={39673:0};n.O.j=r=>0===t[r];var r=(r,e)=>{var o,i,f=e[0],a=e[1],c=e[2],u=0;if(f.some((r=>0!==t[r]))){for(o in a)n.o(a,o)&&(n.m[o]=a[o]);if(c)var l=c(n)}for(r&&r(e);u<f.length;u++)i=f[u],n.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return n.O(l)},e=self.pbjsChunk=self.pbjsChunk||[];e.forEach(r.bind(null,0)),e.push=r.bind(null,e.push.bind(e))})();var o=n.O(void 0,[60802,37769,12139,51085],(()=>n(77332)));o=n.O(o)})();
(self.pbjsChunk=self.pbjsChunk||[]).push([[60802],{95789:(e,t,n)=>{n.d(t,{A4:()=>c,J7:()=>l,Pg:()=>u});var r=n(41580),i=n(91069),o=n(7873),s=n(45569);const a=(0,o.m)(),d="outstream";function c(e){const{url:t,config:n,id:o,callback:c,loaded:l,adUnitCode:u,renderNow:g}=e;this.url=t,this.config=n,this.handlers={},this.id=o,this.renderNow=g,this.adUnitCode=u,this.loaded=l,this.cmd=[],this.push=e=>{"function"==typeof e?this.loaded?e.call():this.cmd.push(e):(0,i.logError)("Commands given to Renderer.push must be wrapped in a function")},this.callback=c||(()=>{this.loaded=!0,this.process()}),this.render=function(){const e=arguments,n=()=>{this._render?this._render.apply(this,e):(0,i.logWarn)("No render function was provided, please use .setRender on the renderer")};!function(e){const t=a.adUnits.find((t=>t.code===e));if(!t)return!1;const n=t?.renderer,r=!!(n&&n.url&&n.render),i=t?.mediaTypes?.video?.renderer,o=!!(i&&i.url&&i.render);return!!(r&&!0!==n.backupOnly||o&&!0!==i.backupOnly)}(u)?g?n():(this.cmd.unshift(n),(0,r.R)(t,s.tp,d,this.callback,this.documentContext)):((0,i.logWarn)(`External Js not loaded by Renderer since renderer url and callback is already defined on adUnit ${u}`),n())}.bind(this)}function l(e){return!(!e||!e.url&&!e.renderNow)}function u(e,t,n){let r=null;e.config&&e.config.documentResolver&&(r=e.config.documentResolver(t,document,n)),r||(r=document),e.documentContext=r,e.render(t,e.documentContext)}c.install=function(e){let{url:t,config:n,id:r,callback:i,loaded:o,adUnitCode:s,renderNow:a}=e;return new c({url:t,config:n,id:r,callback:i,loaded:o,adUnitCode:s,renderNow:a})},c.prototype.getConfig=function(){return this.config},c.prototype.setRender=function(e){this._render=e},c.prototype.setEventHandlers=function(e){this.handlers=e},c.prototype.handleVideoEvent=function(e){let{id:t,eventName:n}=e;"function"==typeof this.handlers[n]&&this.handlers[n](),(0,i.logMessage)(`Prebid Renderer event for id ${t} type ${n}`)},c.prototype.process=function(){for(;this.cmd.length>0;)try{this.cmd.shift().call()}catch(e){(0,i.logError)(`Error processing Renderer command on ad unit '${this.adUnitCode}':`,e)}}},76811:(e,t,n)=>{n.d(t,{DL:()=>l,Ml:()=>i,Ue:()=>r,VJ:()=>g,hE:()=>u,hq:()=>c,mo:()=>d,pY:()=>f,qX:()=>o,uc:()=>a,yl:()=>s});const r="accessDevice",i="syncUser",o="enrichUfpd",s="enrichEids",a="fetchBids",d="reportAnalytics",c="transmitEids",l="transmitUfpd",u="transmitPreciseGeo",g="transmitTid",f="loadExternalScript"},83441:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(11445);const i=(0,n(2604).ZI)((e=>r.Ay.resolveAlias(e)))},45569:(e,t,n)=>{n.d(t,{Tn:()=>a,fW:()=>o,tW:()=>i,tp:()=>r,zu:()=>s});const r="prebid",i="bidder",o="userId",s="rtd",a="analytics"},2604:(e,t,n)=>{n.d(t,{Dk:()=>s,Ii:()=>o,TQ:()=>f,U3:()=>m,XG:()=>l,ZI:()=>p,Zw:()=>c,bt:()=>u,e3:()=>g,iK:()=>a,q7:()=>d});var r=n(45569),i=n(16833);const o="component",s=o+"Type",a=o+"Name",d="adapterCode",c="storageType",l="configName",u="syncType",g="syncUrl",f="_config";function p(e){return function(t,n,i){const c={[s]:t,[a]:n,[o]:`${t}.${n}`};return t===r.tW&&(c[d]=e(n)),m(Object.assign(c,i))}}const m=(0,i.A_)("sync",(e=>e))},96953:(e,t,n)=>{n.d(t,{Vx:()=>d,l7:()=>a,p4:()=>h,$V:()=>m,nl:()=>f,ZP:()=>b,$p:()=>y,uD:()=>p});var r=n(70433),i=n(43272),o=n(95139),s=n(76811);const a=["data","ext.data","yob","gender","keywords","kwarray","id","buyeruid","customdata"].map((e=>`user.${e}`)).concat("device.ext.cdep"),d=["user.eids","user.ext.eids"],c=["user.geo.lat","user.geo.lon","device.geo.lat","device.geo.lon"],l=["device.ip"],u=["device.ipv6"];function g(e){return Object.assign({get(){},run(e,t,n,r,i){const o=n&&n[r];if(m(o)&&i()){const e=this.get(o);void 0===e?delete n[r]:n[r]=e}}},e)}function f(e){return e.forEach((e=>{e.paths=e.paths.map((e=>{const t=e.split("."),n=t.pop();return[t.length>0?t.join("."):null,n]}))})),function(t,n){const i=[];for(var o=arguments.length,s=new Array(o>2?o-2:0),a=2;a<o;a++)s[a-2]=arguments[a];const d=p(t,...s);return e.forEach((e=>{if(!1!==t[e.name])for(const[o,s]of e.paths){const a=null==o?n:(0,r.A)(n,o);if(i.push(e.run(n,o,a,s,d.bind(null,e))),!1===t[e.name])return}})),i.filter((e=>null!=e))}}function p(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return function(t){return e.hasOwnProperty(t.name)||(e[t.name]=!!t.applies(...n)),e[t.name]}}function m(e){return null!=e&&("object"!=typeof e||Object.keys(e).length>0)}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.io;return function(n){return!t(e,n)}}function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;return[{name:s.DL,paths:a,applies:h(s.DL,e)},{name:s.hq,paths:d,applies:h(s.hq,e)},{name:s.hE,paths:c,applies:h(s.hE,e),get:e=>Math.round(100*(e+Number.EPSILON))/100},{name:s.hE,paths:l,applies:h(s.hE,e),get:e=>function(e){if(!e)return null;let t=e.split(".").map(Number);if(4!=t.length)return null;let n=[];for(let e=0;e<4;e++){let t=Math.max(0,Math.min(8,24-8*e));n.push(255<<8-t&255)}return t.map(((e,t)=>e&n[t])).join(".")}(e)},{name:s.hE,paths:u,applies:h(s.hE,e),get:e=>function(e){if(!e)return null;let t=e.split(":").map((e=>parseInt(e,16)));for(t=t.map((e=>isNaN(e)?0:e));t.length<8;)t.push(0);if(8!=t.length)return null;let n=[];for(let e=0;e<8;e++){let t=Math.max(0,Math.min(16,64-16*e));n.push(65535<<16-t&65535)}return t.map(((e,t)=>e&n[t])).map((e=>e.toString(16))).join(":")}(e)},{name:s.VJ,paths:["source.tid"],applies:h(s.VJ,e)}].map(g)}const y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;const t=f(b(e)),n=f(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.io;return[{name:s.hq,paths:["userId","userIdAsEids"],applies:h(s.hq,e)},{name:s.VJ,paths:["ortb2Imp.ext.tid"],applies:h(s.VJ,e)}].map(g)}(e));return function(e){const r={};return{ortb2:n=>(t(r,n,e),n),bidRequest:t=>(n(r,t,e),t)}}}();(0,o.qB)(s.VJ,"enableTIDs config",(()=>{if(!i.$W.getConfig("enableTIDs"))return{allow:!1,reason:"TIDs are disabled"}}))},95139:(e,t,n)=>{n.d(t,{io:()=>s,qB:()=>o});var r=n(91069),i=n(2604);const[o,s]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(0,r.prefixLog)("Activity control:");const t={};function n(e){return t[e]=t[e]||[]}function o(t,n,r,o){let s;try{s=r(o)}catch(r){e.logError(`Exception in rule ${n} for '${t}'`,r),s={allow:!1,reason:r}}return s&&Object.assign({activity:t,name:n,component:o[i.Ii]},s)}const s={};function a(t){let{activity:n,name:r,allow:i,reason:o,component:a}=t;const d=`${r} ${i?"allowed":"denied"} '${n}' for '${a}'${o?":":""}`,c=s.hasOwnProperty(d);if(c&&clearTimeout(s[d]),s[d]=setTimeout((()=>delete s[d]),1e3),!c){const t=[d];o&&t.push(o),(i?e.logInfo:e.logWarn).apply(e,t)}}return[function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;const o=n(e),s=o.findIndex((e=>{let[t]=e;return i<t})),a=[i,t,r];return o.splice(s<0?o.length:s,0,a),function(){const e=o.indexOf(a);e>=0&&o.splice(e,1)}},function(e,t){let r,i;for(const[s,d,c]of n(e)){if(r!==s&&i)break;r=s;const n=o(e,d,c,t);if(n){if(!n.allow)return a(n),!1;i=n}}return i&&a(i),!0}]}()},29075:(e,t,n)=>{n.d(t,{$A:()=>T,BS:()=>P,Hh:()=>N,Pk:()=>q,Uc:()=>B,XO:()=>M,_0:()=>D,bw:()=>_,n6:()=>C,qn:()=>j,vB:()=>W,vW:()=>R,vd:()=>U});var r=n(91069),i=n(75023),o=n(78969),s=n(43272),a=n(95789),d=n(71371),c=n(67314),l=n(46031),u=n(16833),g=n(12449),f=n(25555),p=n(11445),m=n(16894),h=n(97779),b=n(33005);const{AD_RENDER_FAILED:y,AD_RENDER_SUCCEEDED:v,STALE_RENDER:E,BID_WON:A,EXPIRED_RENDER:I}=o.qY,{EXCEPTION:w}=o.as,T=(0,u.A_)("sync",(function(e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:f.U9.resolve()).then((t=>t??c.n.findBidByAdId(e))).catch((()=>{}))})),C=(0,u.A_)("sync",(function(e){((0,b.$T)(e.eventtrackers)[b.RO]?.[b.Ni]||[]).forEach((e=>(0,r.triggerPixel)(e))),i.emit(A,e),c.n.addWinningBid(e)}));function S(e){let{reason:t,message:n,bid:o,id:s}=e;const a={reason:t,message:n};o&&(a.bid=o,a.adId=o.adId),s&&(a.adId=s),(0,r.logError)(`Error rendering ad (id: ${s}): ${n}`),i.emit(y,a)}function O(e){let{doc:t,bid:n,id:r}=e;const o={doc:t};n&&(o.bid=n),r&&(o.adId=r),p.Ay.callAdRenderSucceededBidder(n.adapterCode||n.bidder,n),i.emit(v,o)}function B(e,t){switch(e.event){case o.qY.AD_RENDER_FAILED:S({bid:t,id:t.adId,reason:e.info.reason,message:e.info.message});break;case o.qY.AD_RENDER_SUCCEEDED:O({doc:null,bid:t,id:t.adId});break;default:(0,r.logError)(`Received event request for unsupported event: '${e.event}' (adId: '${t.adId}')`)}}function R(e,t,n){let{resizeFn:r,fireTrackers:i=g.vO}=n;if("resizeNativeHeight"===e.action)r(e.width,e.height);else i(e,t)}const k={[o.nl.EVENT]:B};k[o.nl.NATIVE]=R;const U=(0,u.A_)("sync",(function(e,t){const{ad:n,adUrl:i,cpm:o,originalCpm:s,width:a,height:d,instl:c}=e,l={AUCTION_PRICE:s||o,CLICKTHROUGH:t?.clickUrl||""};return{ad:(0,r.replaceMacros)(n,l),adUrl:(0,r.replaceMacros)(i,l),width:a,height:d,instl:c}})),D=(0,u.A_)("sync",(function(e){let{renderFn:t,resizeFn:n,bidResponse:i,options:s,doc:a,isMainDocument:c=a===document&&!(0,r.inIframe)()}=e;const l=i.mediaType===d.G_;if(c||l)return void S({reason:o.as.PREVENT_WRITING_ON_MAIN_DOCUMENT,message:l?"Cannot render video ad without a renderer":"renderAd was prevented from writing to the main document.",bid:i,id:i.adId});const u=U(i,s);t(Object.assign({adId:i.adId},u));const{width:g,height:f}=u;null!=(g??f)&&n(g,f)}));function _(e){let{renderFn:t,resizeFn:n,adId:a,options:d,bidResponse:c,doc:l}=e;N(c,(()=>{if(null!=c){if((c.status!==o.tl.RENDERED||((0,r.logWarn)(`Ad id ${a} has been rendered before`),i.emit(E,c),!s.$W.getConfig("auctionOptions")?.suppressStaleRender))&&(h.uW.isBidNotExpired(c)||((0,r.logWarn)(`Ad id ${a} has been expired`),i.emit(I,c),!s.$W.getConfig("auctionOptions")?.suppressExpiredRender)))try{D({renderFn:t,resizeFn:n,bidResponse:c,options:d,doc:l})}catch(e){S({reason:o.as.EXCEPTION,message:e.message,id:a,bid:c})}}else S({reason:o.as.CANNOT_FIND_AD,message:`Cannot find ad '${a}'`,id:a})}))}function j(e){const t=(0,m.BO)(e.metrics);t.checkpoint("bidRender"),t.timeBetween("bidWon","bidRender","render.deferred"),t.timeBetween("auctionEnd","bidRender","render.pending"),t.timeBetween("requestBids","bidRender","render.e2e"),e.status=o.tl.RENDERED}D.before((function(e,t){const{bidResponse:n,doc:r}=t;(0,a.J7)(n.renderer)?((0,a.Pg)(n.renderer,n,r),O({doc:r,bid:n,id:n.adId}),e.bail()):e(t)}),100);const $=new WeakMap,x=new WeakSet;function N(e,t){null!=e?($.set(e,t),e.deferRendering||W(e),q(e)):t()}function q(e){x.has(e)||(x.add(e),C(e))}function W(e){const t=$.get(e);t&&(t(),j(e),$.delete(e))}function P(e,t,n){let i;function s(e,n){S(Object.assign({id:t,bid:i},{reason:e,message:n}))}function a(t,n){const r=e.defaultView?.frameElement;r&&(t&&(r.width=t,r.style.width&&(r.style.width=`${t}px`)),n&&(r.height=n,r.style.height&&(r.style.height=`${n}px`)))}const d=(c={resizeFn:a},function(e,t,n){k.hasOwnProperty(e)&&k[e](t,n,c)});var c;function u(t){t.ad?(e.write(t.ad),e.close(),O({doc:e,bid:i,id:i.adId})):(0,l.HH)(i).then((n=>n(t,{sendMessage:(e,t)=>d(e,t,i),mkFrame:r.createIframe},e.defaultView))).then((()=>O({doc:e,bid:i,id:i.adId})),(e=>{s(e?.reason||o.as.EXCEPTION,e?.message),e?.stack&&(0,r.logError)(e)}));const n=document.createComment(`Creative ${i.creativeId} served by ${i.bidder} Prebid.js Header Bidding`);(0,r.insertElement)(n,e,"html")}try{t&&e?T(t).then((r=>{i=r,_({renderFn:u,resizeFn:a,adId:t,options:{clickUrl:n?.clickThrough},bidResponse:r,doc:e})})):s(o.as.MISSING_DOC_OR_ADID,"missing "+(t?"doc":"adId"))}catch(e){s(w,e.message)}}function M(){if(!window.frames[o.IY])if(document.body){const e=(0,r.createInvisibleIframe)();e.name=o.IY,document.body.appendChild(e)}else window.requestAnimationFrame(M)}},10201:(e,t,n)=>{n.d(t,{U:()=>s});var r=n(7873),i=n(91069);const o=(0,r.m)();function s(e,t){o.adServers=o.adServers||{},o.adServers[e]=o.adServers[e]||{},Object.keys(t).forEach((n=>{o.adServers[e][n]?(0,i.logWarn)(`Attempting to add an already registered function property ${n} for AdServer ${e}.`):o.adServers[e][n]=t[n]}))}},69759:(e,t,n)=>{function r(e){var t=e;return{callBids:function(){},setBidderCode:function(e){t=e},getBidderCode:function(){return t}}}n.d(t,{A:()=>r})},11445:(e,t,n)=>{n.d(t,{S1:()=>R,Ay:()=>V,tS:()=>P,pX:()=>G,Mf:()=>L,K5:()=>q,Gs:()=>M});var r=n(91069),i=n(12449),o=n(57377),s=n(68044),a=n(43272),d=n(16833);let c={};function l(e,t,n){let r=function(e,t){let n=c[e]=c[e]||{bidders:{}};return t?n.bidders[t]=n.bidders[t]||{}:n}(e,n);return r[t]=(r[t]||0)+1,r[t]}function u(e){return l(e,"auctionsCounter")}function g(e){return c?.[e]?.requestsCounter||0}function f(e,t){return c?.[e]?.bidders?.[t]?.requestsCounter||0}function p(e,t){return c?.[e]?.bidders?.[t]?.winsCounter||0}function m(e){return c?.[e]?.auctionsCounter||0}var h=n(27934),b=n(16916),y=n(75023),v=n(78969),E=n(16894),A=n(67314),I=n(45569),w=n(95139),T=n(76811),C=n(2604),S=n(96953),O=n(33005);const B="pbsBidAdapter",R={CLIENT:"client",SERVER:"server"},k={isAllowed:w.io,redact:S.$p};let U={},D=U.bidderRegistry={},_=U.aliasRegistry={},j=[];a.$W.getConfig("s2sConfig",(e=>{e&&e.s2sConfig&&(j=(0,r.isArray)(e.s2sConfig)?e.s2sConfig:[e.s2sConfig])}));var $={};const x=(0,C.ZI)((e=>U.resolveAlias(e)));function N(e){return e.configName??e.name}function q(e){return x(I.tp,B,{[C.XG]:N(e)})}const W=(0,d.A_)("sync",(function(e){let{bidderCode:t,auctionId:n,bidderRequestId:i,adUnits:o,src:s,metrics:a}=e;return o.reduce(((e,o)=>{const d=o.bids.filter((e=>e.bidder===t));return null==t&&0===d.length&&null!=o.s2sBid&&d.push({bidder:null}),e.push(d.reduce(((e,d)=>{const c=null==(d=Object.assign({},d,{ortb2Imp:(0,r.mergeDeep)({},o.ortb2Imp,d.ortb2Imp)},(0,r.getDefinedParams)(o,["nativeParams","nativeOrtbRequest","mediaType","renderer"]))).mediaTypes?o.mediaTypes:d.mediaTypes;return(0,r.isValidMediaTypes)(c)?d=Object.assign({},d,{mediaTypes:c}):(0,r.logError)(`mediaTypes is not correctly configured for adunit ${o.code}`),"client"===s&&function(e,t){l(e,"requestsCounter",t)}(o.code,t),e.push(Object.assign({},d,{adUnitCode:o.code,transactionId:o.transactionId,adUnitId:o.adUnitId,sizes:c?.banner?.sizes||c?.video?.playerSize||[],bidId:d.bid_id||(0,r.getUniqueIdentifierStr)(),bidderRequestId:i,auctionId:n,src:s,metrics:a,auctionsCount:m(o.code),bidRequestsCount:g(o.code),bidderRequestsCount:f(o.code,d.bidder),bidderWinsCount:p(o.code,d.bidder),deferBilling:!!o.deferBilling})),e}),[])),e}),[]).reduce(r.flatten,[]).filter((e=>""!==e))}),"getBids");const P=(0,d.A_)("sync",(function(e,t){let{getS2SBidders:n=G}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null==t)return e;{const r=n(t);return e.filter((e=>{if(!r.has(e.bidder))return!1;if(null==e.s2sConfigName)return!0;const n=N(t);return(Array.isArray(e.s2sConfigName)?e.s2sConfigName:[e.s2sConfigName]).includes(n)}))}}),"filterBidsForAdUnit");const M=(0,d.A_)("sync",((e,t)=>e),"setupAdUnitMediaTypes");function G(e){(0,r.isArray)(e)||(e=[e]);const t=new Set([null]);return e.filter((e=>e&&e.enabled)).flatMap((e=>e.bidders)).forEach((e=>t.add(e))),t}const L=(0,d.A_)("sync",(function(e,t){let{getS2SBidders:n=G}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n(t);return(0,r.getBidderCodes)(e).reduce(((e,t)=>(e[i.has(t)?R.SERVER:R.CLIENT].push(t),e)),{[R.CLIENT]:[],[R.SERVER]:[]})}),"partitionBidders");function F(e,t){const n=D[e],r=n?.getSpec&&n.getSpec();if(r&&r[t]&&"function"==typeof r[t])return[r,r[t]]}function z(e,t,n,i){try{(0,r.logInfo)(`Invoking ${e}.${t}`);for(var o=arguments.length,s=new Array(o>4?o-4:0),d=4;d<o;d++)s[d-4]=arguments[d];a.$W.runWithBidder(e,i.bind(n,...s))}catch(n){(0,r.logWarn)(`Error calling ${t} of ${e}`)}}function H(e,t,n){if(n?.source!==v.RW.SRC){const r=F(e,t);null!=r&&z(e,t,...r,n)}}U.makeBidRequests=(0,d.A_)("sync",(function(e,t,n,o,s){let d=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},c=arguments.length>6?arguments[6]:void 0;c=(0,E.BO)(c),y.emit(v.qY.BEFORE_REQUEST_BIDS,e),(0,i.nk)(e),e.map((e=>e.code)).filter(r.uniques).forEach(u),e.forEach((e=>{(0,r.isPlainObject)(e.mediaTypes)||(e.mediaTypes={}),e.bids=e.bids.filter((e=>!e.bidder||k.isAllowed(T.uc,x(I.tW,e.bidder)))),l(e.code,"requestsCounter")})),e=M(e,s);let{[R.CLIENT]:g,[R.SERVER]:f}=L(e,j);a.$W.getConfig("bidderSequence")===a.Ov&&(g=(0,r.shuffle)(g));const p=(0,h.EN)();let m=[];const A=d.global||{},w=d.bidder||{};function C(e,t){const i=k.redact(null!=t?t:x(I.tW,e.bidderCode)),o=(0,r.mergeDeep)({source:{tid:n}},A,w[e.bidderCode]);!function(e){const t=e.user?.eids;Array.isArray(t)&&t.length&&(e.user.ext=e.user.ext||{},e.user.ext.eids=[...e.user.ext.eids||[],...t],delete e.user.eids)}(o);const s=Object.freeze(i.ortb2(o));return e.ortb2=s,e.bids=e.bids.map((e=>(e.ortb2=s,i.bidRequest(e)))),e}j.forEach((i=>{const o=q(i);if(i&&i.enabled&&k.isAllowed(T.uc,o)){let{adUnits:s,hasModuleBids:a}=function(e,t){let n=(0,r.deepClone)(e),i=!1;return n.forEach((e=>{const n=e.bids.filter((e=>e.module===B&&e.params?.configName===N(t)));1===n.length?(e.s2sBid=n[0],i=!0,e.ortb2Imp=(0,r.mergeDeep)({},e.s2sBid.ortb2Imp,e.ortb2Imp)):n.length>1&&(0,r.logWarn)('Multiple "module" bids for the same s2s configuration; all will be ignored',n),e.bids=P(e.bids,t).map((e=>(e.bid_id=(0,r.getUniqueIdentifierStr)(),e)))})),n=n.filter((e=>0!==e.bids.length||null!=e.s2sBid)),{adUnits:n,hasModuleBids:i}}(e,i),d=(0,r.generateUUID)();(0===f.length&&a?[null]:f).forEach((e=>{const a=(0,r.getUniqueIdentifierStr)(),l=c.fork(),u=C({bidderCode:e,auctionId:n,bidderRequestId:a,uniquePbsTid:d,bids:W({bidderCode:e,auctionId:n,bidderRequestId:a,adUnits:(0,r.deepClone)(s),src:v.RW.SRC,metrics:l}),auctionStart:t,timeout:i.timeout,src:v.RW.SRC,refererInfo:p,metrics:l},o);0!==u.bids.length&&m.push(u)})),s.forEach((e=>{let t=e.bids.filter((e=>m.find((t=>t.bids.find((t=>t.bidId===e.bid_id))))));e.bids=t})),m.forEach((e=>{void 0===e.adUnitsS2SCopy&&(e.adUnitsS2SCopy=s.filter((e=>e.bids.length>0||null!=e.s2sBid)))}))}}));let S=function(e){let t=(0,r.deepClone)(e);return t.forEach((e=>{e.bids=P(e.bids,null)})),t=t.filter((e=>0!==e.bids.length)),t}(e);return g.forEach((e=>{const i=(0,r.getUniqueIdentifierStr)(),a=c.fork(),d=C({bidderCode:e,auctionId:n,bidderRequestId:i,bids:W({bidderCode:e,auctionId:n,bidderRequestId:i,adUnits:(0,r.deepClone)(S),labels:s,src:"client",metrics:a}),auctionStart:t,timeout:o,refererInfo:p,metrics:a}),l=D[e];l||(0,r.logError)(`Trying to make a request for bidder that does not exist: ${e}`),l&&d.bids&&0!==d.bids.length&&m.push(d)})),m.forEach((e=>{b.mW.getConsentData()&&(e.gdprConsent=b.mW.getConsentData()),b.t6.getConsentData()&&(e.uspConsent=b.t6.getConsentData()),b.ad.getConsentData()&&(e.gppConsent=b.ad.getConsentData())})),m}),"makeBidRequests"),U.callBids=function(e,t,n,i,o,d,c){let l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:{};if(!t.length)return void(0,r.logWarn)("callBids executed with no bidRequests.  Were they filtered by labels or sizing?");let[u,g]=t.reduce(((e,t)=>(e[Number(void 0!==t.src&&t.src===v.RW.SRC)].push(t),e)),[[],[]]);var f=[];g.forEach((e=>{for(var t=-1,n=0;n<f.length;++n)if(e.uniquePbsTid===f[n].uniquePbsTid){t=n;break}t<=-1&&f.push(e)}));let p=0;j.forEach((e=>{if(e&&f[p]&&G(e).has(f[p].bidderCode)){const t=(0,s.g4)(d,o?{request:o.request.bind(null,"s2s"),done:o.done}:void 0);let a=e.bidders;const u=D[e.adapter];let m=f[p].uniquePbsTid,h=f[p].adUnitsS2SCopy,b=g.filter((e=>e.uniquePbsTid===m));if(u){let o={ad_units:h,s2sConfig:e,ortb2Fragments:l,requestBidsTimeout:d};if(o.ad_units.length){let e=b.map((e=>(e.start=(0,r.timestamp)(),function(t){t||c(e.bidderRequestId),i.apply(e,arguments)})));const s=(0,r.getBidderCodes)(o.ad_units).filter((e=>a.includes(e)));(0,r.logMessage)(`CALLING S2S HEADER BIDDERS ==== ${s.length>0?s.join(", "):'No bidder specified, using "ortb2Imp" definition(s) only'}`),b.forEach((e=>{y.emit(v.qY.BID_REQUESTED,{...e,tid:e.auctionId})})),u.callBids(o,g,n,(t=>e.forEach((e=>e(t)))),t)}}else(0,r.logError)("missing "+e.adapter);p++}})),u.forEach((e=>{e.start=(0,r.timestamp)();const t=D[e.bidderCode];a.$W.runWithBidder(e.bidderCode,(()=>{(0,r.logMessage)("CALLING BIDDER"),y.emit(v.qY.BID_REQUESTED,e)}));let l=(0,s.g4)(d,o?{request:o.request.bind(null,e.bidderCode),done:o.done}:void 0);const u=i.bind(e);try{a.$W.runWithBidder(e.bidderCode,t.callBids.bind(t,e,n,u,l,(()=>c(e.bidderRequestId)),a.$W.callbackWithBidder(e.bidderCode)))}catch(t){(0,r.logError)(`${e.bidderCode} Bid Adapter emitted an uncaught error when parsing their bidRequest`,{e:t,bidRequest:e}),u()}}))},U.videoAdapters=[],U.registerBidAdapter=function(e,t){let{supportedMediaTypes:n=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e&&t?"function"==typeof e.callBids?(D[t]=e,b.o2.register(I.tW,t,e.getSpec?.().gvlid),n.includes("video")&&U.videoAdapters.push(t),n.includes("native")&&i.mT.push(t)):(0,r.logError)("Bidder adaptor error for bidder code: "+t+"bidder must implement a callBids() function"):(0,r.logError)("bidAdapter or bidderCode not specified")},U.aliasBidAdapter=function(e,t,n){if(void 0===D[t]){let s=D[e];if(void 0===s){const n=[];j.forEach((r=>{if(r.bidders&&r.bidders.length){const i=r&&r.bidders;r&&i.includes(t)?_[t]=e:n.push(e)}})),n.forEach((e=>{(0,r.logError)('bidderCode "'+e+'" is not an existing bidder.',"adapterManager.aliasBidAdapter")}))}else try{let a,d=function(e){let t=[];return U.videoAdapters.includes(e)&&t.push("video"),i.mT.includes(e)&&t.push("native"),t}(e);if(s.constructor.prototype!=Object.prototype)a=new s.constructor,a.setBidderCode(t);else{const{useBaseGvlid:i=!1}=n||{};let d=s.getSpec();const c=i?d.gvlid:n?.gvlid;null==c&&null!=d.gvlid&&(0,r.logWarn)(`Alias '${t}' will NOT re-use the GVL ID of the original adapter ('${d.code}', gvlid: ${d.gvlid}). Functionality that requires TCF consent may not work as expected.`);let l=n&&n.skipPbsAliasing;a=(0,o.xb)(Object.assign({},d,{code:t,gvlid:c,skipPbsAliasing:l})),_[t]=e}U.registerBidAdapter(a,t,{supportedMediaTypes:d})}catch(t){(0,r.logError)(e+" bidder does not currently support aliasing.","adapterManager.aliasBidAdapter")}}else(0,r.logMessage)('alias name "'+t+'" has been already specified.')},U.resolveAlias=function(e){let t,n=e;for(;_[n]&&(!t||!t.has(n));)n=_[n],(t=t||new Set).add(n);return n},U.registerAnalyticsAdapter=function(e){let{adapter:t,code:n,gvlid:i}=e;t&&n?"function"==typeof t.enableAnalytics?(t.code=n,$[n]={adapter:t,gvlid:i},b.o2.register(I.Tn,n,i)):(0,r.logError)(`Prebid Error: Analytics adaptor error for analytics "${n}"\n        analytics adapter must implement an enableAnalytics() function`):(0,r.logError)("Prebid Error: analyticsAdapter or analyticsCode not specified")},U.enableAnalytics=function(e){(0,r.isArray)(e)||(e=[e]),e.forEach((e=>{const t=$[e.provider];t&&t.adapter?k.isAllowed(T.mo,x(I.Tn,e.provider,{[C.TQ]:e}))&&t.adapter.enableAnalytics(e):(0,r.logError)(`Prebid Error: no analytics adapter found in registry for '${e.provider}'.`)}))},U.getBidAdapter=function(e){return D[e]},U.getAnalyticsAdapter=function(e){return $[e]},U.callTimedOutBidders=function(e,t,n){t=t.map((t=>(t.params=(0,r.getUserConfiguredParams)(e,t.adUnitCode,t.bidder),t.timeout=n,t))),t=(0,r.groupBy)(t,"bidder"),Object.keys(t).forEach((e=>{H(e,"onTimeout",t[e])}))},U.callBidWonBidder=function(e,t,n){var i,o;t.params=(0,r.getUserConfiguredParams)(n,t.adUnitCode,t.bidder),i=t.adUnitCode,o=t.bidder,l(i,"winsCounter",o),H(e,"onBidWon",t)},U.triggerBilling=(()=>{const e=new WeakSet;return t=>{e.has(t)||(e.add(t),((0,O.$T)(t.eventtrackers)[O.OA]?.[O.Ni]||[]).forEach((e=>r.internal.triggerPixel(e))),H(t.bidder,"onBidBillable",t))}})(),U.callSetTargetingBidder=function(e,t){H(e,"onSetTargeting",t)},U.callBidViewableBidder=function(e,t){H(e,"onBidViewable",t)},U.callBidderError=function(e,t,n){H(e,"onBidderError",{error:t,bidderRequest:n})},U.callAdRenderSucceededBidder=function(e,t){H(e,"onAdRenderSucceeded",t)},U.callDataDeletionRequest=(0,d.A_)("sync",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const i="onDataDeletionRequest";Object.keys(D).filter((e=>!_.hasOwnProperty(e))).forEach((e=>{const n=F(e,i);if(null!=n){const r=A.n.getBidsRequested().filter((t=>function(e){const t=new Set;for(;_.hasOwnProperty(e)&&!t.has(e);)t.add(e),e=_[e];return e}(t.bidderCode)===e));z(e,i,...n,r,...t)}})),Object.entries($).forEach((e=>{let[n,o]=e;const s=o?.adapter?.[i];if("function"==typeof s)try{s.apply(o.adapter,t)}catch(e){(0,r.logError)(`error calling ${i} of ${n}`,e)}}))}));const V=U},57377:(e,t,n)=>{n.d(t,{JN:()=>R,JS:()=>U,a$:()=>w,eI:()=>T,fn:()=>k,xb:()=>C});var r=n(69759),i=n(11445),o=n(43272),s=n(93597),a=n(38230),d=n(12449),c=n(63895),l=n(78969),u=n(75023),g=n(91069),f=n(16833),p=n(67314),m=n(12693),h=n(16894),b=n(95139),y=n(83441),v=n(45569),E=n(76811);const A=["cpm","ttl","creativeId","netRevenue","currency"],I=["auctionId","transactionId"];function w(e){const t=Array.isArray(e.supportedMediaTypes)?{supportedMediaTypes:e.supportedMediaTypes}:void 0;function n(e){const n=C(e);i.Ay.registerBidAdapter(n,e.code,t)}n(e),Array.isArray(e.aliases)&&e.aliases.forEach((t=>{let r,o,s=t;(0,g.isPlainObject)(t)&&(s=t.code,r=t.gvlid,o=t.skipPbsAliasing),i.Ay.aliasRegistry[s]=e.code,n(Object.assign({},e,{code:s,gvlid:r,skipPbsAliasing:o}))}))}const T=(0,g.memoize)((e=>{let{bidderCode:t}=e;if((0,b.io)(E.VJ,(0,y.s)(v.tW,t)))return{bidRequest:e=>e,bidderRequest:e=>e};function n(e,t,n){return I.includes(t)?null:Reflect.get(e,t,n)}function r(e,t){const n=new Proxy(e,t);return Object.entries(e).filter((e=>{let[t,n]=e;return"function"==typeof n})).forEach((t=>{let[r,i]=t;return n[r]=i.bind(e)})),n}const i=(0,g.memoize)((e=>r(e,{get:n})),(e=>e.bidId));return{bidRequest:i,bidderRequest:e=>r(e,{get:(t,r,o)=>"bids"===r?e.bids.map(i):n(t,r,o)})}}));function C(e){return Object.assign(new r.A(e.code),{getSpec:function(){return Object.freeze(Object.assign({},e))},registerSyncs:t,callBids:function(n,r,a,d,c,f){if(!Array.isArray(n.bids))return;const p=T(n),b={};const y=[];function v(){a(),o.$W.runWithBidder(e.code,(()=>{u.emit(l.qY.BIDDER_DONE,n),t(y,n.gdprConsent,n.uspConsent,n.gppConsent)}))}const E=U(n).measureTime("validate",(()=>n.bids.filter((t=>function(t){if(!e.isBidRequestValid(t))return(0,g.logWarn)(`Invalid bid sent to bidder ${e.code}: ${JSON.stringify(t)}`),!1;return!0}(p.bidRequest(t))))));if(0===E.length)return void v();const A={};E.forEach((e=>{A[e.bidId]=e,e.adUnitCode||(e.adUnitCode=e.placementCode)})),O(e,E,n,d,f,{onRequest:e=>u.emit(l.qY.BEFORE_BIDDER_HTTP,n,e),onResponse:t=>{c(e.code),y.push(t)},onPaapi:e=>{const t=A[e.bidId];t?R(t,e):(0,g.logWarn)("Received fledge auction configuration for an unknown bidId",e)},onError:(t,r)=>{r.timedOut||c(e.code),i.Ay.callBidderError(e.code,r,n),u.emit(l.qY.BIDDER_ERROR,{error:r,bidderRequest:n}),(0,g.logError)(`Server call for ${e.code} failed: ${t} ${r.status}. Continuing without bids.`,{bidRequests:E})},onBid:t=>{const n=A[t.requestId];if(n){if(t.adapterCode=n.bidder,function(e,t){let n=m.u.get(t,"allowAlternateBidderCodes")||!1,r=m.u.get(t,"allowedAlternateBidderCodes");if(e&&t&&t!==e&&(r=(0,g.isArray)(r)?r.map((e=>e.trim().toLowerCase())).filter((e=>!!e)).filter(g.uniques):r,!n||(0,g.isArray)(r)&&"*"!==r[0]&&!r.includes(e)))return!0;return!1}(t.bidderCode,n.bidder))return(0,g.logWarn)(`${t.bidderCode} is not a registered partner or known bidder of ${n.bidder}, hence continuing without bid. If you wish to support this bidder, please mark allowAlternateBidderCodes as true in bidderSettings.`),void r.reject(n.adUnitCode,t,l.Tf.BIDDER_DISALLOWED);t.originalCpm=t.cpm,t.originalCurrency=t.currency,t.meta=t.meta||Object.assign({},t[n.bidder]),t.deferBilling=n.deferBilling,t.deferRendering=t.deferBilling&&(t.deferRendering??"function"!=typeof e.onBidBillable);const i=Object.assign((0,s.O)(l.XQ.GOOD,n),t,(0,g.pick)(n,I));!function(e,t){const n=(0,h.BO)(t.metrics);n.checkpoint("addBidResponse"),b[e]=!0,n.measureTime("addBidResponse.validate",(()=>k(e,t)))?r(e,t):r.reject(e,t,l.Tf.INVALID)}(n.adUnitCode,i)}else(0,g.logWarn)(`Bidder ${e.code} made bid for unknown request ID: ${t.requestId}. Ignoring.`),r.reject(null,t,l.Tf.INVALID_REQUEST_ID)},onCompletion:v})}});function t(t,n,r,i){B(e,t,n,r,i)}}const S=["bids","paapi"],O=(0,f.A_)("async",(function(e,t,n,r,i,o){let{onRequest:s,onResponse:a,onPaapi:d,onError:c,onBid:u,onCompletion:f}=o;const p=U(n);f=p.startTiming("total").stopBefore(f);const h=T(n);let A=p.measureTime("buildRequests",(()=>e.buildRequests(t.map(h.bidRequest),h.bidderRequest(n))));if(!A||0===A.length)return void f();Array.isArray(A)||(A=[A]);const I=(0,g.delayExecution)(f,A.length);A.forEach((t=>{const n=p.fork();function o(e){null!=e&&(e.metrics=n.fork().renameWith()),u(e)}const f=i((function(r,i){A();try{r=JSON.parse(r)}catch(e){}r={body:r,headers:{get:i.getResponseHeader.bind(i)}},a(r);try{r=n.measureTime("interpretResponse",(()=>e.interpretResponse(r,t)))}catch(t){return(0,g.logError)(`Bidder ${e.code} failed to interpret the server's response. Continuing without bids`,null,t),void I()}let s,c;r&&!Object.keys(r).some((e=>!S.includes(e)))?(s=r.bids,c=r.paapi):s=r,(0,g.isArray)(c)&&c.forEach(d),s&&((0,g.isArray)(s)?s.forEach(o):o(s)),I()})),h=i((function(e,t){A(),c(e,t),I()}));s(t);const A=n.startTiming("net");function w(n){const r=t.options;return Object.assign(n,r,{browsingTopics:!(r?.hasOwnProperty("browsingTopics")&&!r.browsingTopics)&&((m.u.get(e.code,"topicsHeader")??!0)&&(0,b.io)(E.DL,(0,y.s)(v.tW,e.code)))})}switch(t.method){case"GET":r(`${t.url}${function(e){if(e)return`?${"object"==typeof e?(0,g.parseQueryStringParameters)(e):e}`;return""}(t.data)}`,{success:f,error:h},void 0,w({method:"GET",withCredentials:!0}));break;case"POST":const n=t.options?.endpointCompression,i="TRUE"===(0,g.getParameterByName)(l.M).toUpperCase()||(0,g.debugTurnedOn)(),o=e=>{let{url:t,payload:n}=e;r(t,{success:f,error:h},n,w({method:"POST",contentType:"text/plain",withCredentials:!0}))};n&&i&&(0,g.logWarn)(`Skipping GZIP compression for ${e.code} as debug mode is enabled`),n&&!i&&(0,g.isGzipCompressionSupported)()?(0,g.compressDataWithGZip)(t.data).then((e=>{const n=new URL(t.url,window.location.origin);n.searchParams.has("gzip")||n.searchParams.set("gzip","1"),o({url:n.href,payload:e})})):o({url:t.url,payload:"string"==typeof t.data?t.data:JSON.stringify(t.data)});break;default:(0,g.logWarn)(`Skipping invalid request from ${e.code}. Request type ${t.type} must be GET or POST`),I()}}))}),"processBidderRequests"),B=(0,f.A_)("async",(function(e,t,n,r,s){const d=o.$W.getConfig("userSync.aliasSyncEnabled");if(e.getUserSyncs&&(d||!i.Ay.aliasRegistry[e.code])){let i=e.getUserSyncs({iframeEnabled:a.zt.canBidderRegisterSync("iframe",e.code),pixelEnabled:a.zt.canBidderRegisterSync("image",e.code)},t,n,r,s);i&&(Array.isArray(i)||(i=[i]),i.forEach((t=>{a.zt.registerSync(t.type,e.code,t.url)})),a.zt.bidderDone(e.code))}}),"registerSyncs"),R=(0,f.A_)("sync",((e,t)=>{}),"addPaapiConfig");function k(e,t){let{index:n=p.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};function r(e){return`Invalid bid from ${t.bidderCode}. Ignoring bid: ${e}`}return e?t?function(){let e=Object.keys(t);return A.every((n=>e.includes(n)&&![void 0,null].includes(t[n])))}()?"native"!==t.mediaType||(0,d.Bm)(t,{index:n})?"video"!==t.mediaType||(0,c.vk)(t,{index:n})?!("banner"===t.mediaType&&!function(e,t){let{index:n=p.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if((t.width||0===parseInt(t.width,10))&&(t.height||0===parseInt(t.height,10)))return t.width=parseInt(t.width,10),t.height=parseInt(t.height,10),!0;if(null!=t.wratio&&null!=t.hratio)return t.wratio=parseInt(t.wratio,10),t.hratio=parseInt(t.hratio,10),!0;const r=n.getBidRequest(t),i=n.getMediaTypes(t),o=r&&r.sizes||i&&i.banner&&i.banner.sizes,s=(0,g.parseSizesInput)(o);if(1===s.length){const[e,n]=s[0].split("x");return t.width=parseInt(e,10),t.height=parseInt(n,10),!0}return!1}(e,t,{index:n}))||((0,g.logError)(r("Banner bids require a width and height")),!1):((0,g.logError)(r("Video bid does not have required vastUrl or renderer property")),!1):((0,g.logError)(r("Native bid missing some required properties.")),!1):((0,g.logError)(r(`Bidder ${t.bidderCode} is missing required params. Check http://prebid.org/dev-docs/bidder-adapter-1.html for list of params.`)),!1):((0,g.logWarn)(`Some adapter tried to add an undefined bid for ${e}.`),!1):((0,g.logWarn)("No adUnitCode was supplied to addBidResponse."),!1)}function U(e){return(0,h.BO)(e.metrics).renameWith((t=>[`adapter.client.${t}`,`adapters.client.${e.bidderCode}.${t}`]))}},41580:(e,t,n)=>{n.d(t,{R:()=>c});var r=n(76811),i=n(83441),o=n(95139),s=n(91069);const a=new WeakMap,d=["debugging","outstream","aaxBlockmeter","adagio","adloox","akamaidap","arcspan","airgrid","browsi","brandmetrics","clean.io","humansecurity","confiant","contxtful","hadron","mediafilter","medianet","azerionedge","a1Media","geoedge","qortex","dynamicAdBoost","51Degrees","symitridap","wurfl","nodalsAi","anonymised","optable","justtag","tncId","ftrackId","id5"];function c(e,t,n,c,l,u){if(!(0,o.io)(r.pY,(0,i.s)(t,n)))return;if(!n||!e)return void(0,s.logError)("cannot load external script without url and moduleCode");if(!d.includes(n))return void(0,s.logError)(`${n} not whitelisted for loading external JavaScript`);l||(l=document);const g=m(l,e);if(g)return c&&"function"==typeof c&&(g.loaded?c():g.callbacks.push(c)),g.tag;const f=a.get(l)||{},p={loaded:!1,tag:null,callbacks:[]};return f[e]=p,a.set(l,f),c&&"function"==typeof c&&p.callbacks.push(c),(0,s.logWarn)(`module ${n} is loading external JavaScript`),function(t,n,r,i){r||(r=document);var o=r.createElement("script");o.type="text/javascript",o.async=!0;const a=m(r,e);a&&(a.tag=o);o.readyState?o.onreadystatechange=function(){"loaded"!==o.readyState&&"complete"!==o.readyState||(o.onreadystatechange=null,n())}:o.onload=function(){n()};o.src=t,i&&(0,s.setScriptAttributes)(o,i);return(0,s.insertElement)(o,r),o}(e,(function(){p.loaded=!0;try{for(let e=0;e<p.callbacks.length;e++)p.callbacks[e]()}catch(e){(0,s.logError)("Error executing callback","adloader.js:loadExternalScript",e)}}),l,u);function m(e,t){const n=a.get(e);return n&&n[t]?n[t]:null}}},51692:(e,t,n)=>{n.d(t,{Q:()=>r});const r=(0,n(16833).A_)("sync",(()=>{}))},68044:(e,t,n)=>{n.d(t,{RD:()=>f,Rz:()=>g,g4:()=>u,hd:()=>p});var r=n(43272),i=n(91069);const o={fetch:window.fetch.bind(window),makeRequest:(e,t)=>new Request(e,t),timeout(e,t){const n=new AbortController;let r=setTimeout((()=>{n.abort(),(0,i.logError)(`Request timeout after ${e}ms`,t),r=null}),e);return{signal:n.signal,done(){r&&clearTimeout(r)}}}},s="GET",a="POST",d="Content-Type";function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e3,{request:t,done:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=(t,n)=>{let i;null==e||null!=n?.signal||r.$W.getConfig("disableAjaxTimeout")||(i=o.timeout(e,t),n=Object.assign({signal:i.signal},n));let s=o.fetch(t,n);return null!=i?.done&&(s=s.finally(i.done)),s};return null==t&&null==n||(i=(e=>function(r,i){const o=new URL(null==r?.url?r:r.url,document.location).origin;let s=e(r,i);return t&&t(o),n&&(s=s.finally((()=>n(o)))),s})(i)),i}function l(e,t){let{status:n,statusText:r="",headers:o,url:s}=e,a=0;function c(e){if(0===a)try{a=(new DOMParser).parseFromString(t,o?.get(d)?.split(";")?.[0])}catch(t){a=null,e&&e(t)}return a}return{readyState:XMLHttpRequest.DONE,status:n,statusText:r,responseText:t,response:t,responseType:"",responseURL:s,get responseXML(){return c(i.logError)},getResponseHeader:e=>o?.has(e)?o.get(e):null,toJSON(){return Object.assign({responseXML:c()},this)},timedOut:!1}}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e3,{request:t,done:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=c(e,{request:t,done:n});return function(e,t,n){!function(e,t){const{success:n,error:r}="object"==typeof t&&null!=t?t:{success:"function"==typeof t?t:()=>null,error:(e,t)=>(0,i.logError)("Network error",e,t)};e.then((e=>e.text().then((t=>[e,t])))).then((e=>{let[t,i]=e;const o=l(t,i);t.ok||304===t.status?n(i,o):r(t.statusText,o)}),(e=>r("",Object.assign(l({status:0},""),{reason:e,timedOut:"AbortError"===e?.name}))))}(r(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=n.method||(t?a:s);if(r===s&&t){const r=(0,i.parseUrl)(e,n);Object.assign(r.search,t),e=(0,i.buildUrl)(r)}const c=new Headers(n.customHeaders);c.set(d,n.contentType||"text/plain");const l={method:r,headers:c};return r!==s&&t&&(l.body=t),n.withCredentials&&(l.credentials="include"),isSecureContext&&["browsingTopics","adAuctionHeaders"].forEach((e=>{n[e]&&(l[e]=!0)})),n.keepalive&&(l.keepalive=!0),o.makeRequest(e,l)}(e,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})),t)}}function g(e,t){return!(!window.navigator||!window.navigator.sendBeacon)&&window.navigator.sendBeacon(e,t)}const f=u(),p=c()},81657:(e,t,n)=>{n.d(t,{AA:()=>S,BU:()=>W,HN:()=>J,UZ:()=>O,ZV:()=>G,mO:()=>M,mX:()=>_,sR:()=>j,v8:()=>q,w1:()=>x});var r=n(91069),i=n(86833),o=n(12449),s=n(68693),a=n(95789),d=n(43272),c=n(38230),l=n(16833),u=n(63895),g=n(71371),f=n(67314),p=n(12693),m=n(75023),h=n(11445),b=n(78969),y=n(25555),v=n(16894),E=n(57176),A=n(7873),I=n(76853),w=n(27863);const{syncUsers:T}=c.zt,C="started",S="inProgress",O="completed";m.on(b.qY.BID_ADJUSTMENT,(function(e){!function(e){let t=(0,E.y)(e.cpm,e);t>=0&&(e.cpm=t)}(e)}));const B=4,R={},k={},U=[],D=(0,A.m)();function _(e){let{adUnits:t,adUnitCodes:n,callback:c,cbTimeout:l,labels:p,auctionId:E,ortb2Fragments:A,metrics:_}=e;_=(0,v.BO)(_);const M=t,G=p,L=n,F=E||(0,r.generateUUID)(),z=l,H=new Set,V=(0,y.v6)(),J=(0,y.v6)();let K,X,Q,Z,ee=[],te=c,ne=[],re=(0,I.H)({startTime:e=>e.responseTimestamp,ttl:e=>null==(0,w.S9)()?null:1e3*Math.max((0,w.S9)(),e.ttl)}),ie=[],oe=[],se=[];function ae(){return{auctionId:F,timestamp:K,auctionEnd:X,auctionStatus:Z,adUnits:M,adUnitCodes:L,labels:G,bidderRequests:ne,noBids:ie,bidsReceived:re.toArray(),bidsRejected:ee,winningBids:oe,timeout:z,metrics:_,seatNonBids:se}}function de(e){if(e?m.emit(b.qY.AUCTION_TIMEOUT,ae()):clearTimeout(Q),void 0===X){let n=[];e&&((0,r.logMessage)(`Auction ${F} timedOut`),n=ne.filter((e=>!H.has(e.bidderRequestId))).flatMap((e=>e.bids)),n.length&&m.emit(b.qY.BID_TIMEOUT,n)),Z=O,X=Date.now(),_.checkpoint("auctionEnd"),_.timeBetween("requestBids","auctionEnd","requestBids.total"),_.timeBetween("callBids","auctionEnd","requestBids.callBids"),V.resolve(),m.emit(b.qY.AUCTION_END,ae()),N(M,(function(){try{if(null!=te){const t=re.toArray().filter((e=>L.includes(e.adUnitCode))).reduce(Y,{});te.apply(D,[t,e,F]),te=null}}catch(e){(0,r.logError)("Error executing bidsBackHandler",null,e)}finally{n.length&&h.Ay.callTimedOutBidders(t,n,z);let e=d.$W.getConfig("userSync")||{};e.enableOverride||T(e.syncDelay)}}))}}function ce(){d.$W.resetBidder(),(0,r.logInfo)(`Bids Received for Auction with id: ${F}`,re.toArray()),Z=O,de(!1)}function le(e){H.add(e)}function ue(e){e.forEach((e=>{var t;t=e,ne=ne.concat(t)}));let t={},n={bidRequests:e,run:()=>{Q=setTimeout((()=>de(!0)),z),Z=S,m.emit(b.qY.AUCTION_INIT,ae());let n=function(e,t){let{index:n=f.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=0,l=!1,p=new Set,h={};function v(){c--,l&&0===c&&e()}function E(e,t,n){return h[t.requestId]=!0,function(e,t){let{index:n=f.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n.getBidderRequest(e),o=n.getAdUnit(e),s=i&&i.start||e.requestTimestamp;Object.assign(e,{responseTimestamp:e.responseTimestamp||(0,r.timestamp)(),requestTimestamp:e.requestTimestamp||s,cpm:parseFloat(e.cpm)||0,bidder:e.bidder||e.bidderCode,adUnitCode:t}),null!=o?.ttlBuffer&&(e.ttlBuffer=o.ttlBuffer);e.timeToRespond=e.responseTimestamp-e.requestTimestamp}(t,e),c++,n(v)}function A(e,c){E(e,c,(e=>{let l=function(e){let{index:t=f.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};m.emit(b.qY.BID_ADJUSTMENT,e);const n=t.getAdUnit(e);e.instl=1===n?.ortb2Imp?.instl;const r=t.getBidRequest(e)?.renderer||n.renderer,o=e.mediaType,s=t.getMediaTypes(e),c=s&&s[o];var l=c&&c.renderer,u=null;!l||!l.render||!0===l.backupOnly&&e.renderer?!r||!r.render||!0===r.backupOnly&&e.renderer||(u=r):u=l;u&&(e.renderer=a.A4.install({url:u.url,config:u.options,renderNow:null==u.url}),e.renderer.setRender(u.render));const g=P(e.mediaType,s,d.$W.getConfig("mediaTypePriceGranularity")),p=(0,i.j)(e.cpm,"object"==typeof g?g:d.$W.getConfig("customPriceBucket"),d.$W.getConfig("currency.granularityMultiplier"));return e.pbLg=p.low,e.pbMg=p.med,e.pbHg=p.high,e.pbAg=p.auto,e.pbDg=p.dense,e.pbCg=p.custom,e}(c);m.emit(b.qY.BID_ACCEPTED,l),l.mediaType===g.G_?function(e,t,n){let{index:i=f.n.index}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!0;const a=i.getMediaTypes({requestId:t.originalRequestId||t.requestId,adUnitId:t.adUnitId})?.video,c=a&&a?.context,l=a&&a?.useCacheKey,{useLocal:g,url:p,ignoreBidderCacheKey:m}=d.$W.getConfig("cache")||{};g?(0,s.Sb)(t):p&&(l||c!==u.H6)&&(!t.videoCacheKey||m?(o=!1,W(e,t,n,a)):t.vastUrl||((0,r.logError)("videoCacheKey specified but not required vastUrl for video bid"),o=!1));o&&(q(e,t),n())}(t,l,e):((0,o.l6)(l)&&(0,o.gs)(l,n.getAdUnit(l)),q(t,l),e())}))}function I(e,n,i){return E(e,n,(e=>{n.rejectionReason=i,(0,r.logWarn)(`Bid from ${n.bidder||"unknown bidder"} was rejected: ${i}`,n),m.emit(b.qY.BID_REJECTED,n),t.addBidRejected(n),e()}))}function w(){let n=this,i=t.getBidRequests();const o=d.$W.getConfig("auctionOptions");if(p.add(n),o&&!(0,r.isEmpty)(o)){const e=o.secondaryBidders;e&&!i.every((t=>e.includes(t.bidderCode)))&&(i=i.filter((t=>!e.includes(t.bidderCode))))}l=i.every((e=>p.has(e))),n.bids.forEach((e=>{h[e.bidId]||(t.addNoBid(e),m.emit(b.qY.NO_BID,e))})),l&&0===c&&e()}return{addBidResponse:function(){function e(e,t){j.call({dispatch:A},e,t,(()=>{let n=!1;return r=>{n||(I(e,t,r),n=!0)}})())}return e.reject=I,e}(),adapterDone:function(){$(y.U9.resolve()).finally((()=>w.call(this)))}}}(ce,this);h.Ay.callBids(M,e,n.addBidResponse,n.adapterDone,{request(e,n){l(R,n),l(t,e),k[e]||(k[e]={SRA:!0,origin:n}),t[e]>1&&(k[e].SRA=!1)},done(e){R[e]--,U[0]&&c(U[0])&&U.shift()}},z,le,A),J.resolve()}};function c(e){let t=!0,n=d.$W.getConfig("maxRequestsPerOrigin")||B;return e.bidRequests.some((e=>{let r=1,i=void 0!==e.src&&e.src===b.RW.SRC?"s2s":e.bidderCode;return k[i]&&(!1===k[i].SRA&&(r=Math.min(e.bids.length,n)),R[k[i].origin]+r>n&&(t=!1)),!t})),t&&e.run(),t}function l(e,t){void 0===e[t]?e[t]=1:e[t]++}c(n)||((0,r.logWarn)("queueing auction due to limited endpoint capacity"),U.push(n))}return(0,w.lc)((()=>re.refresh())),m.on(b.qY.SEAT_NON_BID,(e=>{var t;e.auctionId===F&&(t=e.seatnonbid,se=se.concat(t))})),{addBidReceived:function(e){re.add(e)},addBidRejected:function(e){ee=ee.concat(e)},addNoBid:function(e){ie=ie.concat(e)},callBids:function(){Z=C,K=Date.now();let e=_.measureTime("requestBids.makeRequests",(()=>h.Ay.makeBidRequests(M,K,F,z,G,A,_)));(0,r.logInfo)(`Bids Requested for Auction with id: ${F}`,e),_.checkpoint("callBids"),e.length<1?((0,r.logWarn)("No valid bid requests returned for auction"),ce()):x.call({dispatch:ue,context:this},e)},addWinningBid:function(e){oe=oe.concat(e),h.Ay.callBidWonBidder(e.adapterCode||e.bidder,e,t),e.deferBilling||h.Ay.triggerBilling(e)},setBidTargeting:function(e){h.Ay.callSetTargetingBidder(e.adapterCode||e.bidder,e)},getWinningBids:()=>oe,getAuctionStart:()=>K,getAuctionEnd:()=>X,getTimeout:()=>z,getAuctionId:()=>F,getAuctionStatus:()=>Z,getAdUnits:()=>M,getAdUnitCodes:()=>L,getBidRequests:()=>ne,getBidsReceived:()=>re.toArray(),getNoBids:()=>ie,getNonBids:()=>se,getFPD:()=>A,getMetrics:()=>_,end:V.promise,requestsDone:J.promise,getProperties:ae}}const j=(0,l.u2)((0,l.A_)("async",(function(e,t,n){!function(e){const t=d.$W.getConfig("maxBid");return!t||!e.cpm||t>=Number(e.cpm)}(t)?n(b.Tf.PRICE_TOO_HIGH):this.dispatch.call(null,e,t)}),"addBidResponse")),$=(0,l.A_)("sync",(e=>e),"responsesReady"),x=(0,l.A_)("sync",(function(e){this.dispatch.call(this.context,e)}),"addBidderRequests"),N=(0,l.A_)("async",(function(e,t){t&&t()}),"bidsBackCallback");function q(e,t){!function(e){let t;const n=!0===p.u.get(e.bidderCode,"allowZeroCpmBids")?e.cpm>=0:e.cpm>0;e.bidderCode&&(n||e.dealId)&&(t=function(e,t){let{index:n=f.n.index}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t)return{};const r=n.getBidRequest(t);var i={};const s=J(t.mediaType,e);K(i,s,t,r),e&&p.u.getOwn(e,b.iD.ADSERVER_TARGETING)&&(K(i,p.u.ownSettingsFor(e),t,r),t.sendStandardTargeting=p.u.get(e,"sendStandardTargeting"));t.native&&(i=Object.assign({},i,(0,o.Zj)(t)));return i}(e.bidderCode,e));e.adserverTargeting=Object.assign(e.adserverTargeting||{},t)}(t),(0,v.BO)(t.metrics).timeSince("addBidResponse","addBidResponse.total"),e.addBidReceived(t),m.emit(b.qY.BID_RESPONSE,t)}const W=(0,l.A_)("async",(function(e,t,n,r){(0,s.X5)(e,t,n)}),"callPrebidCache");function P(e,t,n){if(e&&n){if(e===g.G_){const e=t?.[g.G_]?.context??"instream";if(n[`${g.G_}-${e}`])return n[`${g.G_}-${e}`]}return n[e]}}const M=function(e){let{index:t=f.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=P(e.mediaType,t.getMediaTypes(e),d.$W.getConfig("mediaTypePriceGranularity"));return"string"==typeof e.mediaType&&n?"string"==typeof n?n:"custom":d.$W.getConfig("priceGranularity")},G=e=>t=>{const n=e||M(t);return n===b.UE.AUTO?t.pbAg:n===b.UE.DENSE?t.pbDg:n===b.UE.LOW?t.pbLg:n===b.UE.MEDIUM?t.pbMg:n===b.UE.HIGH?t.pbHg:n===b.UE.CUSTOM?t.pbCg:void 0},L=()=>e=>e.creativeId?e.creativeId:"",F=()=>e=>e.meta&&e.meta.advertiserDomains&&e.meta.advertiserDomains.length>0?[e.meta.advertiserDomains].flat()[0]:"",z=()=>e=>e.meta&&(e.meta.networkId||e.meta.networkName)?e?.meta?.networkName||e?.meta?.networkId:"",H=()=>e=>{const t=e?.meta?.primaryCatId;return Array.isArray(t)?t[0]||"":t||""};function V(e,t){return{key:e,val:"function"==typeof t?function(e,n){return t(e,n)}:function(e){return e[t]}}}function J(e,t){const n=Object.assign({},p.u.settingsFor(null));if(n[b.iD.ADSERVER_TARGETING]||(n[b.iD.ADSERVER_TARGETING]=[V(b.xS.BIDDER,"bidderCode"),V(b.xS.AD_ID,"adId"),V(b.xS.PRICE_BUCKET,G()),V(b.xS.SIZE,"size"),V(b.xS.DEAL,"dealId"),V(b.xS.SOURCE,"source"),V(b.xS.FORMAT,"mediaType"),V(b.xS.ADOMAIN,F()),V(b.xS.ACAT,H()),V(b.xS.DSP,z()),V(b.xS.CRID,L())]),"video"===e){const e=n[b.iD.ADSERVER_TARGETING].slice();if(n[b.iD.ADSERVER_TARGETING]=e,[b.xS.UUID,b.xS.CACHE_ID].forEach((t=>{void 0===e.find((e=>e.key===t))&&e.push(V(t,"videoCacheKey"))})),d.$W.getConfig("cache.url")&&(!t||!1!==p.u.get(t,"sendStandardTargeting"))){const t=(0,r.parseUrl)(d.$W.getConfig("cache.url"));void 0===e.find((e=>e.key===b.xS.CACHE_HOST))&&e.push(V(b.xS.CACHE_HOST,(function(e){return e?.adserverTargeting?.[b.xS.CACHE_HOST]||t.hostname})))}}return n}function K(e,t,n,i){var o=t[b.iD.ADSERVER_TARGETING];return n.size=n.getSize(),(o||[]).forEach((function(o){var s=o.key,a=o.val;if(e[s]&&(0,r.logWarn)("The key: "+s+" is being overwritten"),(0,r.isFn)(a))try{a=a(n,i)}catch(e){(0,r.logError)("bidmanager","ERROR",e)}(void 0===t.suppressEmptyKeys||!0!==t.suppressEmptyKeys)&&s!==b.xS.DEAL&&s!==b.xS.ACAT&&s!==b.xS.DSP&&s!==b.xS.CRID||!(0,r.isEmptyStr)(a)&&null!=a?e[s]=a:(0,r.logInfo)("suppressing empty key '"+s+"' from adserver targeting")})),e}function Y(e,t){return e[t.adUnitCode]||(e[t.adUnitCode]={bids:[]}),e[t.adUnitCode].bids.push(t),e}},67314:(e,t,n)=>{n.d(t,{n:()=>l});var r=n(91069),i=n(81657);function o(e){Object.assign(this,{getAuction(t){let{auctionId:n}=t;if(null!=n)return e().find((e=>e.getAuctionId()===n))},getAdUnit(t){let{adUnitId:n}=t;if(null!=n)return e().flatMap((e=>e.getAdUnits())).find((e=>e.adUnitId===n))},getMediaTypes(e){let{adUnitId:t,requestId:n}=e;if(null!=n){const e=this.getBidRequest({requestId:n});if(null!=e&&(null==t||e.adUnitId===t))return e.mediaTypes}else if(null!=t){const e=this.getAdUnit({adUnitId:t});if(null!=e)return e.mediaTypes}},getBidderRequest(t){let{requestId:n,bidderRequestId:r}=t;if(null!=n||null!=r){let t=e().flatMap((e=>e.getBidRequests()));return null!=r&&(t=t.filter((e=>e.bidderRequestId===r))),null==n?t[0]:t.find((e=>e.bids&&null!=e.bids.find((e=>e.bidId===n))))}},getBidRequest(t){let{requestId:n}=t;if(null!=n)return e().flatMap((e=>e.getBidRequests())).flatMap((e=>e.bids)).find((e=>e&&e.bidId===n))},getOrtb2(e){return this.getBidderRequest(e)?.ortb2||this.getAuction(e)?.getFPD()?.global?.ortb2}})}var s=n(78969),a=n(16894),d=n(76853),c=n(27863);const l=function(){const e=(0,d.H)({startTime:e=>e.end.then((()=>e.getAuctionEnd())),ttl:e=>null==(0,c.S9)()?null:e.end.then((()=>1e3*Math.max((0,c.S9)(),...e.getBidsReceived().map((e=>e.ttl)))))});(0,c.lc)((()=>e.refresh()));const t={onExpiry:e.onExpiry};function n(t){for(const n of e)if(n.getAuctionId()===t)return n}function l(){return e.toArray().flatMap((e=>e.getBidsReceived()))}return t.addWinningBid=function(e){const t=(0,a.BO)(e.metrics);t.checkpoint("bidWon"),t.timeBetween("auctionEnd","bidWon","adserver.pending"),t.timeBetween("requestBids","bidWon","adserver.e2e");const i=n(e.auctionId);i?i.addWinningBid(e):(0,r.logWarn)("Auction not found when adding winning bid")},Object.entries({getAllWinningBids:{name:"getWinningBids"},getBidsRequested:{name:"getBidRequests"},getNoBids:{},getAdUnits:{},getBidsReceived:{pre:e=>e.getAuctionStatus()===i.UZ},getAdUnitCodes:{post:r.uniques}}).forEach((n=>{let[r,{name:i=r,pre:o,post:s}]=n;const a=null==o?e=>e[i]():e=>o(e)?e[i]():[],d=null==s?e=>e:e=>e.filter(s);t[r]=()=>d(e.toArray().flatMap(a))})),t.getAllBidsForAdUnitCode=function(e){return l().filter((t=>t&&t.adUnitCode===e))},t.createAuction=function(t){const n=(0,i.mX)(t);return function(t){e.add(t)}(n),n},t.findBidByAdId=function(e){return l().find((t=>t.adId===e))},t.getStandardBidderAdServerTargeting=function(){return(0,i.HN)()[s.iD.ADSERVER_TARGETING]},t.setStatusForBids=function(e,r){let i=t.findBidByAdId(e);if(i&&(i.status=r),i&&r===s.tl.BID_TARGETING_SET){const e=n(i.auctionId);e&&e.setBidTargeting(i)}},t.getLastAuctionId=function(){const t=e.toArray();return t.length&&t[t.length-1].getAuctionId()},t.clearAllAuctions=function(){e.clear()},t.index=new o((()=>e.toArray())),t}()},27863:(e,t,n)=>{n.d(t,{S9:()=>l,cT:()=>c,lc:()=>u});var r=n(43272),i=n(91069);const o="minBidCacheTTL";let s=1,a=null;const d=[];function c(e){return e.ttl-(e.hasOwnProperty("ttlBuffer")?e.ttlBuffer:s)}function l(){return a}function u(e){d.push(e)}r.$W.getConfig("ttlBuffer",(e=>{"number"==typeof e.ttlBuffer?s=e.ttlBuffer:(0,i.logError)("Invalid value for ttlBuffer",e.ttlBuffer)})),r.$W.getConfig(o,(e=>{const t=a;a=e?.[o],a="number"==typeof a?a:null,t!==a&&d.forEach((e=>e(a)))}))},12693:(e,t,n)=>{n.d(t,{u:()=>a});var r=n(70433),i=n(91069),o=n(7873),s=n(78969);const a=new class{constructor(e,t){this.getSettings=e,this.defaultScope=t}get(e,t){let n=this.getOwn(e,t);return void 0===n&&(n=this.getOwn(null,t)),n}getOwn(e,t){return e=this.#e(e),(0,r.A)(this.getSettings(),`${e}.${t}`)}getScopes(){return Object.keys(this.getSettings()).filter((e=>e!==this.defaultScope))}settingsFor(e){return(0,i.mergeDeep)({},this.ownSettingsFor(null),this.ownSettingsFor(e))}ownSettingsFor(e){return e=this.#e(e),this.getSettings()[e]||{}}#e(e){return null==e?this.defaultScope:e}}((()=>(0,o.m)().bidderSettings||{}),s.iD.BD_SETTING_STANDARD)},93597:(e,t,n)=>{n.d(t,{O:()=>o});var r=n(91069);function i(e){let{src:t="client",bidder:n="",bidId:i,transactionId:o,adUnitId:s,auctionId:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var d=t,c=e||0;Object.assign(this,{bidderCode:n,width:0,height:0,statusMessage:function(){switch(c){case 0:return"Pending";case 1:return"Bid available";case 2:return"Bid returned empty or error response";case 3:return"Bid timed out"}}(),adId:(0,r.getUniqueIdentifierStr)(),requestId:i,transactionId:o,adUnitId:s,auctionId:a,mediaType:"banner",source:d}),this.getStatusCode=function(){return c},this.getSize=function(){return this.width+"x"+this.height},this.getIdentifiers=function(){return{src:this.source,bidder:this.bidderCode,bidId:this.requestId,transactionId:this.transactionId,adUnitId:this.adUnitId,auctionId:this.auctionId}}}function o(e,t){return new i(e,t)}},43272:(e,t,n)=>{n.d(t,{$W:()=>p,Ov:()=>c});var r=n(86833),i=n(91069),o=n(70433),s=n(78969);const a="TRUE"===(0,i.getParameterByName)(s.M).toUpperCase(),d={},c="random",l={};l[c]=!0,l.fixed=!0;const u=c,g={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"};function f(e){const t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?{priceGranularity:g.MEDIUM,customPriceBucket:{},mediaTypePriceGranularity:{},bidderSequence:u,auctionOptions:{}}:{};function n(e){return t[e]}function o(n,r){t.hasOwnProperty(n)||Object.defineProperty(e,n,{enumerable:!0}),t[n]=r}const s={publisherDomain:{set(e){null!=e&&(0,i.logWarn)("publisherDomain is deprecated and has no effect since v7 - use pageUrl instead"),o("publisherDomain",e)}},priceGranularity:{set(e){d(e)&&("string"==typeof e?o("priceGranularity",a(e)?e:g.MEDIUM):(0,i.isPlainObject)(e)&&(o("customPriceBucket",e),o("priceGranularity",g.CUSTOM),(0,i.logMessage)("Using custom price granularity")))}},customPriceBucket:{},mediaTypePriceGranularity:{set(e){null!=e&&o("mediaTypePriceGranularity",Object.keys(e).reduce(((t,r)=>(d(e[r])?"string"==typeof e?t[r]=a(e[r])?e[r]:n("priceGranularity"):(0,i.isPlainObject)(e)&&(t[r]=e[r],(0,i.logMessage)(`Using custom price granularity for ${r}`)):(0,i.logWarn)(`Invalid price granularity for media type: ${r}`),t)),{}))}},bidderSequence:{set(e){l[e]?o("bidderSequence",e):(0,i.logWarn)(`Invalid order: ${e}. Bidder Sequence was not set.`)}},auctionOptions:{set(e){(function(e){if(!(0,i.isPlainObject)(e))return(0,i.logWarn)("Auction Options must be an object"),!1;for(let t of Object.keys(e)){if("secondaryBidders"!==t&&"suppressStaleRender"!==t&&"suppressExpiredRender"!==t)return(0,i.logWarn)(`Auction Options given an incorrect param: ${t}`),!1;if("secondaryBidders"===t){if(!(0,i.isArray)(e[t]))return(0,i.logWarn)(`Auction Options ${t} must be of type Array`),!1;if(!e[t].every(i.isStr))return(0,i.logWarn)(`Auction Options ${t} must be only string`),!1}else if(("suppressStaleRender"===t||"suppressExpiredRender"===t)&&!(0,i.isBoolean)(e[t]))return(0,i.logWarn)(`Auction Options ${t} must be of type boolean`),!1}return!0})(e)&&o("auctionOptions",e)}}};return Object.defineProperties(e,Object.fromEntries(Object.entries(s).map((e=>{let[r,i]=e;return[r,Object.assign({get:n.bind(null,r),set:o.bind(null,r),enumerable:t.hasOwnProperty(r),configurable:!t.hasOwnProperty(r)},i)]})))),e;function a(e){return Object.keys(g).find((t=>e===g[t]))}function d(e){if(!e)return(0,i.logError)("Prebid Error: no value passed to `setPriceGranularity()`"),!1;if("string"==typeof e)a(e)||(0,i.logWarn)("Prebid Warning: setPriceGranularity was called with invalid setting, using `medium` as default.");else if((0,i.isPlainObject)(e)&&!(0,r.q)(e))return(0,i.logError)("Invalid custom price value passed to `setPriceGranularity()`"),!1;return!0}}const p=function(){let e,t,n,r=[],s=null;function c(){e={};let r=f({debug:a,bidderTimeout:3e3,enableSendAllBids:true,useBidCache:false,deviceAccess:true,disableAjaxTimeout:false,maxNestedIframes:10,maxBid:5e3,userSync:{topics:d}});t&&y(Object.keys(t).reduce(((e,n)=>(t[n]!==r[n]&&(e[n]=r[n]||{}),e)),{})),t=r,n={}}function l(){if(s&&n&&(0,i.isPlainObject)(n[s])){const e=n[s],r=new Set([...Object.keys(t),...Object.keys(e)]),o={};for(const n of r){const r=t[n],s=e[n];o[n]=void 0===s?r:void 0===r?s:(0,i.isPlainObject)(s)?(0,i.mergeDeep)({},r,s):s}return o}return{...t}}const[u,g]=[l,function(){const e=l();return Object.defineProperty(e,"ortb2",{get:function(){throw new Error("invalid access to 'orbt2' config - use request parameters instead")}}),e}].map((e=>function(){if(arguments.length<=1&&"function"!=typeof(arguments.length<=0?void 0:arguments[0])){const t=arguments.length<=0?void 0:arguments[0];return t?(0,o.A)(e(),t):l()}return b(...arguments)})),[p,m]=[g,u].map((e=>function(){let t=e(...arguments);return t&&"object"==typeof t&&(t=(0,i.deepClone)(t)),t}));function h(n){if(!(0,i.isPlainObject)(n))return void(0,i.logError)("setConfig options must be an object");let r=Object.keys(n),o={};r.forEach((r=>{let s=n[r];(0,i.isPlainObject)(e[r])&&(0,i.isPlainObject)(s)&&(s=Object.assign({},e[r],s));try{o[r]=t[r]=s}catch(e){(0,i.logWarn)(`Cannot set config for property ${r} : `,e)}})),y(o)}function b(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=t;if("string"!=typeof e&&(o=e,e="*",n=t||{}),"function"!=typeof o)return void(0,i.logError)("listener must be a function");const s={topic:e,callback:o};return r.push(s),n.init&&o("*"===e?g():{[e]:g(e)}),function(){r.splice(r.indexOf(s),1)}}function y(e){const t=Object.keys(e);r.filter((e=>t.includes(e.topic))).forEach((t=>{t.callback({[t.topic]:e[t.topic]})})),r.filter((e=>"*"===e.topic)).forEach((t=>t.callback(e)))}function v(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{!function(e){if(!(0,i.isPlainObject)(e))throw"setBidderConfig bidder options must be an object";if(!Array.isArray(e.bidders)||!e.bidders.length)throw"setBidderConfig bidder options must contain a bidders list with at least 1 bidder";if(!(0,i.isPlainObject)(e.config))throw"setBidderConfig bidder options must contain a config object"}(e),e.bidders.forEach((r=>{n[r]||(n[r]=f({},!1)),Object.keys(e.config).forEach((o=>{let s=e.config[o];const a=n[r][o];if((0,i.isPlainObject)(s)&&(null==a||(0,i.isPlainObject)(a))){const e=t?i.mergeDeep:Object.assign;n[r][o]=e({},a||{},s)}else n[r][o]=s}))}))}catch(e){(0,i.logError)(e)}}function E(e,t){s=e;try{return t()}finally{A()}}function A(){s=null}return c(),{getCurrentBidder:function(){return s},resetBidder:A,getConfig:g,getAnyConfig:u,readConfig:p,readAnyConfig:m,setConfig:h,mergeConfig:function(e){if(!(0,i.isPlainObject)(e))return void(0,i.logError)("mergeConfig input must be an object");const t=(0,i.mergeDeep)(l(),e);return h({...t}),t},setDefaults:function(n){(0,i.isPlainObject)(e)?(Object.assign(e,n),Object.assign(t,n)):(0,i.logError)("defaults must be an object")},resetConfig:c,runWithBidder:E,callbackWithBidder:function(e){return function(t){return function(){if("function"==typeof t){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return E(e,t.bind(this,...r))}(0,i.logWarn)("config.callbackWithBidder callback is not a function")}}},setBidderConfig:v,getBidderConfig:function(){return n},mergeBidderConfig:function(e){return v(e,!0)}}}()},16916:(e,t,n)=>{n.d(t,{B1:()=>s,SL:()=>p,ad:()=>l,et:()=>u,mW:()=>d,o2:()=>g,t6:()=>c});var r=n(91069),i=n(25555),o=n(43272);const s=Object.freeze({});class a{#t;#n;#r;#i;#o=!0;#s;generatedTime;hashFields;constructor(){this.reset()}#a(e){this.#i=!0,this.#n=e,this.#r.resolve(e)}reset(){this.#r=(0,i.v6)(),this.#t=!1,this.#n=null,this.#i=!1,this.generatedTime=null}enable(){this.#t=!0}get enabled(){return this.#t}get ready(){return this.#i}get promise(){return this.#i?i.U9.resolve(this.#n):(this.#t||this.#a(null),this.#r.promise)}setConsentData(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,r.timestamp)();this.generatedTime=t,this.#o=!0,this.#a(e)}getConsentData(){return this.#n}get hash(){return this.#o&&(this.#s=(0,r.cyrb53Hash)(JSON.stringify(this.#n&&this.hashFields?this.hashFields.map((e=>this.#n[e])):this.#n)),this.#o=!1),this.#s}}const d=new class extends a{hashFields=["gdprApplies","consentString"];getConsentMeta(){const e=this.getConsentData();if(e&&e.vendorData&&this.generatedTime)return{gdprApplies:e.gdprApplies,consentStringSize:(0,r.isStr)(e.vendorData.tcString)?e.vendorData.tcString.length:0,generatedAt:this.generatedTime,apiVersion:e.apiVersion}}},c=new class extends a{getConsentMeta(){if(this.getConsentData()&&this.generatedTime)return{generatedAt:this.generatedTime}}},l=new class extends a{hashFields=["applicableSections","gppString"];getConsentMeta(){if(this.getConsentData()&&this.generatedTime)return{generatedAt:this.generatedTime}}},u=(()=>{function e(){return!!o.$W.getConfig("coppa")}return{getCoppa:e,getConsentData:e,getConsentMeta:e,reset(){},get promise(){return i.U9.resolve(e())},get hash(){return e()?"1":"0"}}})(),g=function(){const e={},t={},n={};return{register(r,i,o){o&&((e[i]=e[i]||{})[r]=o,t.hasOwnProperty(i)?t[i]!==o&&(t[i]=n):t[i]=o)},get(r){const i={modules:e[r]||{}};return t.hasOwnProperty(r)&&t[r]!==n&&(i.gvlid=t[r]),i}}}(),f={gdpr:d,usp:c,gpp:l,coppa:u};const p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;return e=Object.entries(e),Object.assign({get promise(){return i.U9.all(e.map((e=>{let[t,n]=e;return n.promise.then((e=>[t,e]))}))).then((e=>Object.fromEntries(e)))},get hash(){return(0,r.cyrb53Hash)(e.map((e=>{let[t,n]=e;return n.hash})).join(":"))}},Object.fromEntries(["getConsentData","getConsentMeta","reset"].map((t=>{return[t,(n=t,function(){return Object.fromEntries(e.map((e=>{let[t,r]=e;return[t,r[n]()]})))})];var n}))))}()},78969:(e,t,n)=>{n.d(t,{IY:()=>A,M:()=>i,RW:()=>f,Tf:()=>m,UE:()=>c,XQ:()=>o,Zh:()=>u,_B:()=>v,as:()=>a,cA:()=>d,h0:()=>h,iD:()=>r,jO:()=>b,nl:()=>E,oA:()=>y,qY:()=>s,tl:()=>p,x5:()=>g,xS:()=>l});const r={PL_CODE:"code",PL_SIZE:"sizes",PL_BIDS:"bids",BD_BIDDER:"bidder",BD_ID:"paramsd",BD_PL_ID:"placementId",ADSERVER_TARGETING:"adserverTargeting",BD_SETTING_STANDARD:"standard"},i="pbjs_debug",o={GOOD:1},s={AUCTION_INIT:"auctionInit",AUCTION_TIMEOUT:"auctionTimeout",AUCTION_END:"auctionEnd",BID_ADJUSTMENT:"bidAdjustment",BID_TIMEOUT:"bidTimeout",BID_REQUESTED:"bidRequested",BID_RESPONSE:"bidResponse",BID_REJECTED:"bidRejected",NO_BID:"noBid",SEAT_NON_BID:"seatNonBid",BID_WON:"bidWon",BIDDER_DONE:"bidderDone",BIDDER_ERROR:"bidderError",SET_TARGETING:"setTargeting",BEFORE_REQUEST_BIDS:"beforeRequestBids",BEFORE_BIDDER_HTTP:"beforeBidderHttp",REQUEST_BIDS:"requestBids",ADD_AD_UNITS:"addAdUnits",AD_RENDER_FAILED:"adRenderFailed",AD_RENDER_SUCCEEDED:"adRenderSucceeded",TCF2_ENFORCEMENT:"tcf2Enforcement",AUCTION_DEBUG:"auctionDebug",BID_VIEWABLE:"bidViewable",STALE_RENDER:"staleRender",EXPIRED_RENDER:"expiredRender",BILLABLE_EVENT:"billableEvent",BID_ACCEPTED:"bidAccepted",RUN_PAAPI_AUCTION:"paapiRunAuction",PBS_ANALYTICS:"pbsAnalytics",PAAPI_BID:"paapiBid",PAAPI_NO_BID:"paapiNoBid",PAAPI_ERROR:"paapiError",BEFORE_PBS_HTTP:"beforePBSHttp",BROWSI_INIT:"browsiInit",BROWSI_DATA:"browsiData"},a={PREVENT_WRITING_ON_MAIN_DOCUMENT:"preventWritingOnMainDocument",NO_AD:"noAd",EXCEPTION:"exception",CANNOT_FIND_AD:"cannotFindAd",MISSING_DOC_OR_ADID:"missingDocOrAdid"},d={bidWon:"adUnitCode"},c={LOW:"low",MEDIUM:"medium",HIGH:"high",AUTO:"auto",DENSE:"dense",CUSTOM:"custom"},l={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",SOURCE:"hb_source",FORMAT:"hb_format",UUID:"hb_uuid",CACHE_ID:"hb_cache_id",CACHE_HOST:"hb_cache_host",ADOMAIN:"hb_adomain",ACAT:"hb_acat",CRID:"hb_crid",DSP:"hb_dsp"},u={BIDDER:"hb_bidder",AD_ID:"hb_adid",PRICE_BUCKET:"hb_pb",SIZE:"hb_size",DEAL:"hb_deal",FORMAT:"hb_format",UUID:"hb_uuid",CACHE_HOST:"hb_cache_host"},g={title:"hb_native_title",body:"hb_native_body",body2:"hb_native_body2",privacyLink:"hb_native_privacy",privacyIcon:"hb_native_privicon",sponsoredBy:"hb_native_brand",image:"hb_native_image",icon:"hb_native_icon",clickUrl:"hb_native_linkurl",displayUrl:"hb_native_displayurl",cta:"hb_native_cta",rating:"hb_native_rating",address:"hb_native_address",downloads:"hb_native_downloads",likes:"hb_native_likes",phone:"hb_native_phone",price:"hb_native_price",salePrice:"hb_native_saleprice",rendererUrl:"hb_renderer_url",adTemplate:"hb_adTemplate"},f={SRC:"s2s",DEFAULT_ENDPOINT:"https://prebid.adnxs.com/pbs/v1/openrtb2/auction",SYNCED_BIDDERS_KEY:"pbjsSyncs"},p={BID_TARGETING_SET:"targetingSet",RENDERED:"rendered",BID_REJECTED:"bidRejected"},m={INVALID:"Bid has missing or invalid properties",INVALID_REQUEST_ID:"Invalid request ID",BIDDER_DISALLOWED:"Bidder code is not allowed by allowedAlternateBidderCodes / allowUnknownBidderCodes",FLOOR_NOT_MET:"Bid does not meet price floor",CANNOT_CONVERT_CURRENCY:"Unable to convert currency",DSA_REQUIRED:"Bid does not provide required DSA transparency info",DSA_MISMATCH:"Bid indicates inappropriate DSA rendering method",PRICE_TOO_HIGH:"Bid price exceeds maximum value"},h={body:"desc",body2:"desc2",sponsoredBy:"sponsored",cta:"ctatext",rating:"rating",address:"address",downloads:"downloads",likes:"likes",phone:"phone",price:"price",salePrice:"saleprice",displayUrl:"displayurl"},b={sponsored:1,desc:2,rating:3,likes:4,downloads:5,price:6,saleprice:7,phone:8,address:9,desc2:10,displayurl:11,ctatext:12},y={ICON:1,MAIN:3},v=["privacyIcon","clickUrl","sendTargetingKeys","adTemplate","rendererUrl","type"],E={REQUEST:"Prebid Request",RESPONSE:"Prebid Response",NATIVE:"Prebid Native",EVENT:"Prebid Event"},A="__pb_locator__"},86833:(e,t,n)=>{n.d(t,{j:()=>u,q:()=>f});var r=n(91069),i=n(43272);const o=2,s={buckets:[{max:5,increment:.5}]},a={buckets:[{max:20,increment:.1}]},d={buckets:[{max:20,increment:.01}]},c={buckets:[{max:3,increment:.01},{max:8,increment:.05},{max:20,increment:.5}]},l={buckets:[{max:5,increment:.05},{max:10,increment:.1},{max:20,increment:.5}]};function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=parseFloat(e);return isNaN(r)&&(r=""),{low:""===r?"":g(e,s,n),med:""===r?"":g(e,a,n),high:""===r?"":g(e,d,n),auto:""===r?"":g(e,l,n),dense:""===r?"":g(e,c,n),custom:""===r?"":g(e,t,n)}}function g(e,t,n){let s="";if(!f(t))return s;const a=t.buckets.reduce(((e,t)=>e.max>t.max?e:t),{max:0});let d=0,c=t.buckets.find((t=>{if(e>a.max*n){let e=t.precision;void 0===e&&(e=o),s=(t.max*n).toFixed(e)}else{if(e<=t.max*n&&e>=d*n)return t.min=d,t;d=t.max}}));return c&&(s=function(e,t,n){const s=void 0!==t.precision?t.precision:o,a=t.increment*n,d=t.min*n;let c=Math.floor,l=i.$W.getConfig("cpmRoundingFunction");"function"==typeof l&&(c=l);let u,g,f=Math.pow(10,s+2),p=(e*f-d*f)/(a*f);try{u=c(p)*a+d}catch(e){g=!0}(g||"number"!=typeof u)&&((0,r.logWarn)("Invalid rounding function passed in config"),u=Math.floor(p)*a+d);return u=Number(u.toFixed(10)),u.toFixed(s)}(e,c,n)),s}function f(e){if((0,r.isEmpty)(e)||!e.buckets||!Array.isArray(e.buckets))return!1;let t=!0;return e.buckets.forEach((e=>{e.max&&e.increment||(t=!1)})),t}},46031:(e,t,n)=>{n.d(t,{HH:()=>c,kj:()=>d,xh:()=>a});var r=n(25555),i=n(91069),o=n(34595),s=n(16833);const a=3,d=(0,s.A_)("sync",(function(e){return o.G})),c=function(){const e={};return function(t){const n=d(t);return e.hasOwnProperty(n)||(e[n]=new r.U9((e=>{const t=(0,i.createInvisibleIframe)();t.srcdoc=`<script>${n}<\/script>`,t.onload=()=>e(t.contentWindow.render),document.body.appendChild(t)}))),e[n]}}()},49164:(e,t,n)=>{n.d(t,{L6:()=>h,ey:()=>u});var r=n(43272),i=n(16833),o=n(7873),s=n(91069),a=n(93597),d=n(41580),c=n(25555),l=n(45569);const u="__pbjs_debugging__";function g(){return(0,o.m)().installedModules.includes("debugging")}function f(e){return new c.U9((t=>{(0,d.R)(e,l.tp,"debugging",t)}))}function p(){let{alreadyInstalled:e=g,script:t=f}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=null;return function(){return null==n&&(n=new c.U9(((n,d)=>{setTimeout((()=>{if(e())n();else{const e="https://cdn.jsdelivr.net/npm/prebid.js@9.50.0/dist/debugging-standalone.js";(0,s.logMessage)(`Debugging module not installed, loading it from "${e}"...`),(0,o.m)()._installDebugging=!0,t(e).then((()=>{(0,o.m)()._installDebugging({DEBUG_KEY:u,hook:i.A_,config:r.$W,createBid:a.O,logger:(0,s.prefixLog)("DEBUG:")})})).then(n,d)}}))}))),n}}const m=function(){let{load:e=p(),hook:t=(0,i.Yn)("requestBids")}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=null,r=!1;function o(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return(n||c.U9.resolve()).then((()=>e.apply(this,r)))}function s(){t.getHooks({hook:o}).remove(),r=!1}return{enable:function(){r||(n=e(),t.before(o,99),r=!0)},disable:s,reset:function(){n=null,s()}}}();m.reset;function h(){let e=null;try{e=window.sessionStorage}catch(e){}if(null!==e){let t=m,n=null;try{n=e.getItem(u)}catch(e){}null!==n&&t.enable()}}r.$W.getConfig("debugging",(function(e){let{debugging:t}=e;t?.enabled?m.enable():m.disable()}))},33005:(e,t,n)=>{n.d(t,{$T:()=>a,Ni:()=>r,OA:()=>o,RO:()=>s,fR:()=>i});const r=1,i=2,o=1,s=500;function a(e){return(e??[]).reduce(((e,t)=>{let{event:n,method:r,url:i}=t;const o=e[n]=e[n]??{};return(o[r]=o[r]??[]).push(i),e}),{})}},75023:(e,t,n)=>{n.r(t),n.d(t,{addEvents:()=>E,clearEvents:()=>I,emit:()=>v,get:()=>b,getEvents:()=>y,has:()=>A,off:()=>h,on:()=>m});var r=n(91069),i=n(78969),o=n(76853),s=n(43272);const a="eventHistoryTTL";let d=null;const c=(0,o.H)({monotonic:!0,ttl:()=>d});s.$W.getConfig(a,(e=>{const t=d;e=e?.[a],d="number"==typeof e?1e3*e:null,t!==d&&c.refresh()}));let l=Array.prototype.slice,u=Array.prototype.push,g=Object.values(i.qY);const f=i.cA,p=function(){let e={},t={};function n(e){return g.includes(e)}return t.has=n,t.on=function(t,i,o){if(n(t)){let n=e[t]||{que:[]};o?(n[o]=n[o]||{que:[]},n[o].que.push(i)):n.que.push(i),e[t]=n}else r.logError("Wrong event name : "+t+" Valid event names :"+g)},t.emit=function(t){!function(t,n){r.logMessage("Emitting event for: "+t);let i=n[0]||{},o=i[f[t]],s=e[t]||{que:[]};var a=Object.keys(s);let d=[];c.add({eventType:t,args:i,id:o,elapsedTime:r.getPerformanceNow()}),o&&a.includes(o)&&u.apply(d,s[o].que),u.apply(d,s.que),(d||[]).forEach((function(e){if(e)try{e.apply(null,n)}catch(e){r.logError("Error executing handler:","events.js",e,t)}}))}(t,l.call(arguments,1))},t.off=function(t,n,i){let o=e[t];r.isEmpty(o)||r.isEmpty(o.que)&&r.isEmpty(o[i])||i&&(r.isEmpty(o[i])||r.isEmpty(o[i].que))||(i?(o[i].que||[]).forEach((function(e){let t=o[i].que;e===n&&t.splice(t.indexOf(e),1)})):(o.que||[]).forEach((function(e){let t=o.que;e===n&&t.splice(t.indexOf(e),1)})),e[t]=o)},t.get=function(){return e},t.addEvents=function(e){g=g.concat(e)},t.getEvents=function(){return c.toArray().map((e=>Object.assign({},e)))},t}();r._setEventEmitter(p.emit.bind(p));const{on:m,off:h,get:b,getEvents:y,emit:v,addEvents:E,has:A}=p;function I(){c.clear()}},70068:(e,t,n)=>{n.d(t,{w:()=>v});var r=n(16833),i=n(27934),o=n(5973),s=n(91069),a=n(63172),d=n(43272),c=n(25250),l=n(25555),u=n(73858),g=n(95139),f=n(83441),p=n(76811),m=n(45569),h=n(88944);const b={getRefererInfo:i.EN,findRootDomain:o.S,getWindowTop:s.getWindowTop,getWindowSelf:s.getWindowSelf,getHighEntropySUA:c.FD,getLowEntropySUA:c.zO,getDocument:s.getDocument},y=(0,u.i8)("FPD"),v=(0,r.A_)("sync",(e=>{const t=[e,A().catch((()=>null)),l.U9.resolve("cookieDeprecationLabel"in navigator&&(0,g.io)(p.Ue,(0,f.s)(m.tp,"cdep"))&&navigator.cookieDeprecationLabel.getValue()).catch((()=>null))];return l.U9.all(t).then((e=>{let[t,n,r]=e;const i=b.getRefererInfo();if(Object.entries(w).forEach((e=>{let[n,r]=e;const o=r(t,i);o&&Object.keys(o).length>0&&(t[n]=(0,s.mergeDeep)({},o,t[n]))})),n&&(0,a.J)(t,"device.sua",Object.assign({},n,t.device.sua)),r){const e={cdep:r};(0,a.J)(t,"device.ext",Object.assign({},e,t.device.ext))}const o=b.getDocument().documentElement.lang;o&&(0,a.J)(t,"site.ext.data.documentLang",o),t=y(t);for(let e of u.Dy)if((0,u.O$)(t,e)){t[e]=(0,s.mergeDeep)({},T(t,i),t[e]);break}return t}))}));function E(e){try{return e(b.getWindowTop())}catch(t){return e(b.getWindowSelf())}}function A(){const e=d.$W.getConfig("firstPartyData.uaHints");return Array.isArray(e)&&0!==e.length?b.getHighEntropySUA(e):l.U9.resolve(b.getLowEntropySUA())}function I(e){return(0,s.getDefinedParams)(e,Object.keys(e))}const w={site(e,t){if(!u.Dy.filter((e=>"site"!==e)).some(u.O$.bind(null,e)))return I({page:t.page,ref:t.ref})},device:()=>E((e=>{const t=(0,s.getWinDimensions)().screen.width,n=(0,s.getWinDimensions)().screen.height,{width:r,height:i}=(0,h.M)(),o={w:t,h:n,dnt:(0,s.getDNT)()?1:0,ua:e.navigator.userAgent,language:e.navigator.language.split("-").shift(),ext:{vpw:r,vph:i}};return e.navigator?.webdriver&&(0,a.J)(o,"ext.webdriver",!0),o})),regs(){const e={};E((e=>e.navigator.globalPrivacyControl))&&(0,a.J)(e,"ext.gpc","1");const t=d.$W.getConfig("coppa");return"boolean"==typeof t&&(e.coppa=t?1:0),e}};function T(e,t){const n=(0,i.gR)(t.page,{noLeadingWww:!0}),r=E((e=>e.document.querySelector("meta[name='keywords']")))?.content?.replace?.(/\s/g,"");return I({domain:n,keywords:r,publisher:I({domain:b.findRootDomain(n)})})}},73858:(e,t,n)=>{n.d(t,{Dy:()=>i,O$:()=>s,i8:()=>o});var r=n(91069);const i=["dooh","app","site"];function o(e){return function(t){return i.reduce(((n,i)=>(s(t,i)&&(null!=n?((0,r.logWarn)(`${e} specifies both '${n}' and '${i}'; dropping the latter.`),delete t[i]):n=i),n)),null),t}}function s(e,t){return null!=e[t]&&Object.keys(e[t]).length>0}},5973:(e,t,n)=>{n.d(t,{S:()=>o});var r=n(91069);const i=(0,n(12938).CK)("fpdEnrichment"),o=(0,r.memoize)((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.host;if(!i.cookiesAreEnabled())return e;const t=e.split(".");if(2===t.length)return e;let n,o,s=-2;const a=`_rdc${Date.now()}`,d="writeable";do{n=t.slice(s).join(".");let e=new Date((0,r.timestamp)()+1e4).toUTCString();i.setCookie(a,d,e,"Lax",n,void 0);i.getCookie(a,void 0)===d?(o=!1,i.setCookie(a,"","Thu, 01 Jan 1970 00:00:01 GMT",void 0,n,void 0)):(s+=-1,o=Math.abs(s)<=t.length)}while(o);return n}))},25250:(e,t,n)=>{n.d(t,{CP:()=>l,FD:()=>c,zO:()=>d});var r=n(91069),i=n(25555);const o=2,s=["architecture","bitness","model","platformVersion","fullVersionList"],a=["brands","mobile","platform"],d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator?.userAgentData;const t=e&&a.some((t=>void 0!==e[t]))?Object.freeze(u(1,e)):null;return function(){return t}}(),c=l();function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator?.userAgentData;const t={},n=new WeakMap;return function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s;if(!n.has(a)){const e=Array.from(a);e.sort(),n.set(a,e.join("|"))}const d=n.get(a);if(!t.hasOwnProperty(d))try{t[d]=e.getHighEntropyValues(a).then((e=>(0,r.isEmpty)(e)?null:Object.freeze(u(o,e)))).catch((()=>null))}catch(e){t[d]=i.U9.resolve(null)}return t[d]}}function u(e,t){function n(e,t){const n={brand:e};return(0,r.isStr)(t)&&!(0,r.isEmptyStr)(t)&&(n.version=t.split(".")),n}const i={source:e};return t.platform&&(i.platform=n(t.platform,t.platformVersion)),(t.fullVersionList||t.brands)&&(i.browsers=(t.fullVersionList||t.brands).map((e=>{let{brand:t,version:r}=e;return n(t,r)}))),void 0!==t.mobile&&(i.mobile=t.mobile?1:0),["model","bitness","architecture"].forEach((e=>{const n=t[e];(0,r.isStr)(n)&&(i[e]=n)})),i}},16833:(e,t,n)=>{n.d(t,{A_:()=>s,Gc:()=>d,Y6:()=>p,Yn:()=>c,bz:()=>f,pT:()=>l,u2:()=>m,xG:()=>g});var r=n(68128),i=n.n(r),o=n(25555);let s=i()({ready:i().SYNC|i().ASYNC|i().QUEUE});const a=(0,o.v6)();s.ready=(()=>{const e=s.ready;return function(){try{return e.apply(s,arguments)}finally{a.resolve()}}})();const d=a.promise,c=s.get;function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:15;0===e.getHooks({hook:t}).length&&e.before(t,n)}const u={};function g(e,t){let{postInstallAllowed:n=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};s("async",(function(r){r.forEach((e=>t(...e))),n&&(u[e]=t)}),e)([])}function f(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];const i=u[e];if(i)return i(...n);c(e).before(((e,t)=>{t.push(n),e(t)}))}function p(e,t){return Object.defineProperties(t,Object.fromEntries(["before","after","getHooks","removeAll"].map((t=>[t,{get:()=>e[t]}])))),t}function m(e){return p(e,(function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.push((function(){})),e.apply(this,n)}))}},71371:(e,t,n)=>{n.d(t,{D4:()=>o,GE:()=>a,G_:()=>i,LM:()=>s,s6:()=>r});const r="native",i="video",o="banner",s="adpod",a=[r,i,o]},12449:(e,t,n)=>{n.d(t,{Bm:()=>v,Ex:()=>x,Gg:()=>T,IX:()=>O,Nh:()=>u,Xj:()=>_,Zj:()=>I,gs:()=>A,l6:()=>m,mT:()=>l,nk:()=>b,rn:()=>k,vO:()=>E,yl:()=>B});var r=n(91069),i=n(67314),o=n(78969),s=n(71371),a=n(29075),d=n(46031),c=n(33005);const l=[],u=Object.keys(o.x5).map((e=>o.x5[e])),g={image:{ortb:{ver:"1.2",assets:[{required:1,id:1,img:{type:3,wmin:100,hmin:100}},{required:1,id:2,title:{len:140}},{required:1,id:3,data:{type:1}},{required:0,id:4,data:{type:2}},{required:0,id:5,img:{type:1,wmin:20,hmin:20}}]},image:{required:!0},title:{required:!0},sponsoredBy:{required:!0},clickUrl:{required:!0},body:{required:!1},icon:{required:!1}}},f=N(o.h0),p=N(o.jO);function m(e){return e.native&&"object"==typeof e.native}function h(e){if(e&&e.type&&function(e){if(!e||!Object.keys(g).includes(e))return(0,r.logError)(`${e} nativeParam is not supported`),!1;return!0}(e.type)&&(e=g[e.type]),!e||!e.ortb||y(e.ortb))return e}function b(e){e.forEach((e=>{const t=e.nativeParams||e?.mediaTypes?.native;t&&(e.nativeParams=h(t)),e.nativeParams&&(e.nativeOrtbRequest=e.nativeParams.ortb||k(e.nativeParams))}))}function y(e){const t=e.assets;if(!Array.isArray(t)||0===t.length)return(0,r.logError)("assets in mediaTypes.native.ortb is not an array, or it's empty. Assets: ",t),!1;const n=t.map((e=>e.id));return t.length!==new Set(n).size||n.some((e=>e!==parseInt(e,10)))?((0,r.logError)("each asset object must have 'id' property, it must be unique and it must be an integer"),!1):e.hasOwnProperty("eventtrackers")&&!Array.isArray(e.eventtrackers)?((0,r.logError)("ortb.eventtrackers is not an array. Eventtrackers: ",e.eventtrackers),!1):t.every((e=>function(e){if(!(0,r.isPlainObject)(e))return(0,r.logError)("asset must be an object. Provided asset: ",e),!1;if(e.img){if(!(0,r.isNumber)(e.img.w)&&!(0,r.isNumber)(e.img.wmin))return(0,r.logError)("for img asset there must be 'w' or 'wmin' property"),!1;if(!(0,r.isNumber)(e.img.h)&&!(0,r.isNumber)(e.img.hmin))return(0,r.logError)("for img asset there must be 'h' or 'hmin' property"),!1}else if(e.title){if(!(0,r.isNumber)(e.title.len))return(0,r.logError)("for title asset there must be 'len' property defined"),!1}else if(e.data){if(!(0,r.isNumber)(e.data.type))return(0,r.logError)("for data asset 'type' property must be a number"),!1}else if(e.video&&!(Array.isArray(e.video.mimes)&&Array.isArray(e.video.protocols)&&(0,r.isNumber)(e.video.minduration)&&(0,r.isNumber)(e.video.maxduration)))return(0,r.logError)("video asset is not properly configured"),!1;return!0}(e)))}function v(e){let{index:t=i.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=t.getAdUnit(e);if(!n)return!1;let o=n.nativeOrtbRequest;return function(e,t){if(!e?.link?.url)return(0,r.logError)("native response doesn't have 'link' property. Ortb response: ",e),!1;let n=t.assets.filter((e=>1===e.required)).map((e=>e.id)),i=e.assets.map((e=>e.id));const o=n.every((e=>i.includes(e)));o||(0,r.logError)(`didn't receive a bid with all required assets. Required ids: ${n}, but received ids in response: ${i}`);return o}(e.native?.ortb||$(e.native,o),o)}function E(e,t){const n=t.native.ortb||j(t.native);return"click"===e.action?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,{fetchURL:n=r.triggerPixel}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t){const r=(e.assets||[]).filter((e=>e.link)).reduce(((e,t)=>(e[t.id]=t.link,e)),{}),i=e.link?.clicktrackers||[];let o=r[t],s=i;o&&(s=o.clicktrackers||[]),s.forEach((e=>n(e)))}else(e.link?.clicktrackers||[]).forEach((e=>n(e)))}(n,e?.assetId):function(e){let{runMarkup:t=(e=>(0,r.insertHtmlIntoIframe)(e)),fetchURL:n=r.triggerPixel}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{[c.Ni]:i=[],[c.fR]:o=[]}=(0,c.$T)(e.eventtrackers||[])[c.OA]||{};e.imptrackers&&(i=i.concat(e.imptrackers));i.forEach((e=>n(e))),o=o.map((e=>`<script async src="${e}"><\/script>`)),e.jstracker&&(o=o.concat([e.jstracker]));o.length&&t(o.join("\n"))}(n),e.action}function A(e,t){const n=t?.nativeOrtbRequest,r=e.native?.ortb;if(n&&r){const t=x(r,n);Object.assign(e.native,t)}["rendererUrl","adTemplate"].forEach((n=>{const r=t?.nativeParams?.[n];r&&(e.native[n]=R(r))}))}function I(e){let{index:t=i.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={};const r=t.getAdUnit(e),s=null==r?.nativeParams?.ortb&&!1!==r?.nativeParams?.sendTargetingKeys,a=function(e){const t={};e?.nativeParams?.ext&&Object.keys(e.nativeParams.ext).forEach((e=>{t[e]=`hb_native_${e}`}));return{...o.x5,...t}}(r),d={...e.native,...e.native.ext};return delete d.ext,Object.keys(d).forEach((t=>{const i=a[t];let o=R(e.native[t])||R(e?.native?.ext?.[t]);if("adTemplate"===t||!i||!o)return;let d=r?.nativeParams?.[t]?.sendId;if("boolean"!=typeof d&&(d=r?.nativeParams?.ext?.[t]?.sendId),d){o=`${i}:${e.adId}`}let c=r?.nativeParams?.[t]?.sendTargetingKeys;"boolean"!=typeof c&&(c=r?.nativeParams?.ext?.[t]?.sendTargetingKeys);("boolean"==typeof c?c:s)&&(n[i]=o)})),n}function w(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=[];return Object.entries(e).filter((e=>{let[r,i]=e;return i&&(!1===n&&"ext"===r||null==t||t.includes(r))})).forEach((e=>{let[i,s]=e;!1===n&&"ext"===i?r.push(...w(s,t,!0)):(n||o.x5.hasOwnProperty(i))&&r.push({key:i,value:R(s)})})),r}function T(e,t,n){const i={...(0,r.getDefinedParams)(e.native,["rendererUrl","adTemplate"]),assets:w(e.native,n),nativeKeys:o.x5};return e.native.ortb?i.ortb=e.native.ortb:t.mediaTypes?.native?.ortb&&(i.ortb=$(e.native,t.nativeOrtbRequest)),i}function C(e,t,n){let{index:r=i.n.index}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o={message:"assetResponse",adId:e.adId};let s=(0,a.vd)(t).native;return s?(o.native=Object.assign({},s),o.renderer=(0,d.kj)(t),o.rendererVersion=d.xh,null!=n&&(s.assets=s.assets.filter((e=>{let{key:t}=e;return n.includes(t)})))):s=T(t,r.getAdUnit(t),n),Object.assign(o,s)}const S=Object.fromEntries(Object.entries(o.x5).map((e=>{let[t,n]=e;return[n,t]})));function O(e,t){const n=e.assets.map((e=>S[e]));return C(e,t,n)}function B(e,t){return C(e,t,null)}function R(e){return e?.url||e}function k(e){if(!e&&!(0,r.isPlainObject)(e))return void(0,r.logError)("Native assets object is empty or not an object: ",e);const t={ver:"1.2",assets:[]};for(let n in e){if(o._B.includes(n))continue;if(!o.x5.hasOwnProperty(n)){(0,r.logError)(`Unrecognized native asset code: ${n}. Asset will be ignored.`);continue}if("privacyLink"===n){t.privacy=1;continue}const i=e[n];let s=0;i.required&&(0,r.isBoolean)(i.required)&&(s=Number(i.required));const a={id:t.assets.length,required:s};if(n in o.h0)a.data={type:o.jO[o.h0[n]]},i.len&&(a.data.len=i.len);else if("icon"===n||"image"===n){if(a.img={type:"icon"===n?o.oA.ICON:o.oA.MAIN},i.aspect_ratios)if((0,r.isArray)(i.aspect_ratios))if(i.aspect_ratios.length){const{min_width:e,min_height:t}=i.aspect_ratios[0];(0,r.isInteger)(e)&&(0,r.isInteger)(t)?(a.img.wmin=e,a.img.hmin=t):(0,r.logError)("image.aspect_ratios min_width or min_height are invalid: ",e,t);const n=i.aspect_ratios.filter((e=>e.ratio_width&&e.ratio_height)).map((e=>`${e.ratio_width}:${e.ratio_height}`));n.length>0&&(a.img.ext={aspectratios:n})}else(0,r.logError)("image.aspect_ratios was passed, but it's empty:",i.aspect_ratios);else(0,r.logError)("image.aspect_ratios was passed, but it's not a an array:",i.aspect_ratios);i.sizes&&(2===i.sizes.length&&(0,r.isInteger)(i.sizes[0])&&(0,r.isInteger)(i.sizes[1])?(a.img.w=i.sizes[0],a.img.h=i.sizes[1],delete a.img.hmin,delete a.img.wmin):(0,r.logError)("image.sizes was passed, but its value is not an array of integers:",i.sizes))}else"title"===n?a.title={len:i.len||140}:"ext"===n&&(a.ext=i,delete a.required);t.assets.push(a)}return t}function U(e,t){for(;e&&t&&e!==t;)e>t?e-=t:t-=e;return e||t}function D(e){if(!y(e))return;const t={};for(const n of e.assets){if(n.title){const e={required:!!n.required&&Boolean(n.required),len:n.title.len};t.title=e}else if(n.img){const e={required:!!n.required&&Boolean(n.required)};if(n.img.w&&n.img.h)e.sizes=[n.img.w,n.img.h];else if(n.img.wmin&&n.img.hmin){const t=U(n.img.wmin,n.img.hmin);e.aspect_ratios=[{min_width:n.img.wmin,min_height:n.img.hmin,ratio_width:n.img.wmin/t,ratio_height:n.img.hmin/t}]}n.img.type===o.oA.MAIN?t.image=e:t.icon=e}else if(n.data){let e=Object.keys(o.jO).find((e=>o.jO[e]===n.data.type)),r=Object.keys(o.h0).find((t=>o.h0[t]===e));t[r]={required:!!n.required&&Boolean(n.required)},n.data.len&&(t[r].len=n.data.len)}e.privacy&&(t.privacyLink={required:!1})}return t}function _(e){{if(!e||!(0,r.isArray)(e))return e;if(!e.some((e=>(e?.mediaTypes||{})[s.s6]?.ortb)))return e;let t=(0,r.deepClone)(e);for(const e of t)e.mediaTypes&&e.mediaTypes[s.s6]&&e.mediaTypes[s.s6].ortb&&(e.mediaTypes[s.s6]=Object.assign((0,r.pick)(e.mediaTypes[s.s6],o._B),D(e.mediaTypes[s.s6].ortb)),e.nativeParams=h(e.mediaTypes[s.s6]));return t}}function j(e){const t={link:{},eventtrackers:[]};return Object.entries(e).forEach((e=>{let[n,r]=e;switch(n){case"clickUrl":t.link.url=r;break;case"clickTrackers":t.link.clicktrackers=Array.isArray(r)?r:[r];break;case"impressionTrackers":(Array.isArray(r)?r:[r]).forEach((e=>{t.eventtrackers.push({event:c.OA,method:c.Ni,url:e})}));break;case"javascriptTrackers":t.jstracker=Array.isArray(r)?r.join(""):r;break;case"privacyLink":t.privacy=r}})),t}function $(e,t){const n={...j(e),assets:[]};function i(e,i){let o=t.assets.find(e);null!=o&&(o=(0,r.deepClone)(o),i(o),n.assets.push(o))}return Object.keys(e).filter((t=>!!e[t])).forEach((t=>{const n=R(e[t]);switch(t){case"title":i((e=>null!=e.title),(e=>{e.title={text:n}}));break;case"image":case"icon":const e="image"===t?o.oA.MAIN:o.oA.ICON;i((t=>null!=t.img&&t.img.type===e),(e=>{e.img={url:n}}));break;default:t in o.h0&&i((e=>null!=e.data&&e.data.type===o.jO[o.h0[t]]),(e=>{e.data={value:n}}))}})),n}function x(e,t){const n={},r=t?.assets||[];n.clickUrl=e.link?.url,n.privacyLink=e.privacy;for(const t of e?.assets||[]){const e=r.find((e=>t.id===e.id));t.title?n.title=t.title.text:t.img?n[e?.img?.type===o.oA.MAIN?"image":"icon"]={url:t.img.url,width:t.img.w,height:t.img.h}:t.data&&(n[f[p[e?.data?.type]]]=t.data.value)}n.impressionTrackers=[];let i=[];e.imptrackers&&n.impressionTrackers.push(...e.imptrackers);for(const t of e?.eventtrackers||[])t.event===c.OA&&t.method===c.Ni&&n.impressionTrackers.push(t.url),t.event===c.OA&&t.method===c.fR&&i.push(t.url);return i=i.map((e=>`<script async src="${e}"><\/script>`)),e?.jstracker&&i.push(e.jstracker),i.length&&(n.javascriptTrackers=i.join("\n")),n}function N(e){var t={};for(var n in e)t[e[n]]=n;return t}},1e3:(e,t,n)=>{n.d(t,{Cf:()=>a,S3:()=>i,Tb:()=>o,WR:()=>s,e4:()=>c,pS:()=>u,qN:()=>d,yB:()=>g,zt:()=>r});const r=["request","imp","bidResponse","response"],[i,o,s,a]=r,[d,c]=["default","pbs"],l=new Set(r);const{registerOrtbProcessor:u,getProcessors:g}=function(){const e={};return{registerOrtbProcessor(t){let{type:n,name:i,fn:o,priority:s=0,dialects:a=[d]}=t;if(!l.has(n))throw new Error(`ORTB processor type must be one of: ${r.join(", ")}`);a.forEach((t=>{e.hasOwnProperty(t)||(e[t]={}),e[t].hasOwnProperty(n)||(e[t][n]={}),e[t][n][i]={priority:s,fn:o}}))},getProcessors:t=>e[t]||{}}}()},77332:(e,t,n)=>{n.d(t,{WH:()=>X,Z:()=>Q,gH:()=>ee});var r=n(7873),i=n(91069),o=n(70433),s=n(63172),a=n(12449),d=n(78969),c=n(29075),l=n(46031);const{REQUEST:u,RESPONSE:g,NATIVE:f,EVENT:p}=d.nl,m={[u]:function(e,t,n){(0,c.bw)({renderFn(t){e(Object.assign({message:g,renderer:(0,l.kj)(n),rendererVersion:l.xh},t))},resizeFn:b(t.adId,n),options:t.options,adId:t.adId,bidResponse:n})},[p]:function(e,t,n){if(null==n)return void(0,i.logError)(`Cannot find ad '${t.adId}' for x-origin event request`);if(n.status!==d.tl.RENDERED)return void(0,i.logWarn)(`Received x-origin event request without corresponding render request for ad '${n.adId}'`);return(0,c.Uc)(t,n)}};function h(){window.addEventListener("message",(function(e){!function(e){var t=e.message?"message":"data",n={};try{n=JSON.parse(e[t])}catch(e){return}if(n&&n.adId&&n.message&&m.hasOwnProperty(n.message))(0,c.$A)(n.adId,n.message===d.nl.REQUEST).then((t=>{var r,o;m[n.message]((r=n.adId,o=function(e){return null==e.origin&&0===e.ports.length?function(){const e="Cannot post message to a frame with null origin. Please update creatives to use MessageChannel, see https://github.com/prebid/Prebid.js/issues/7870";throw(0,i.logError)(e),new Error(e)}:e.ports.length>0?function(t){e.ports[0].postMessage(JSON.stringify(t))}:function(t){e.source.postMessage(JSON.stringify(t),e.origin)}}(e),function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return o(Object.assign({},e,{adId:r}),...n)}),n,t)}))}(e)}),!1)}function b(e,t){return function(n,r){!function(e){let{instl:t,adId:n,adUnitCode:r,width:o,height:s}=e;if(t)return;function a(e){return e?e+"px":"100%"}function d(e){let t=c(n,r),i=document.getElementById(t);return i&&i.querySelector(e)}function c(e,t){return(0,i.isGptPubadsDefined)()?l(e):(0,i.isApnGetTagDefined)()?u(t):t}function l(e){const t=window.googletag.pubads().getSlots().find((t=>t.getTargetingKeys().find((n=>t.getTargeting(n).includes(e)))));return t?t.getSlotElementId():null}function u(e){let t=window.apntag.getTag(e);return t&&t.targetId}["div","iframe"].forEach((e=>{let t=d(e+':not([style*="display: none"])');if(t){let e=t.style;e.width=a(o),e.height=a(s)}else(0,i.logError)(`Unable to locate matching page element for adUnitCode ${r}.  Can't resize it to ad's dimensions.  Please review setup.`)}))}({...t,width:n,height:r,adId:e})}}Object.assign(m,{[f]:function(e,t,n){if(null==n)return void(0,i.logError)(`Cannot find ad for x-origin event request: '${t.adId}'`);switch(t.action){case"assetRequest":(0,c.Hh)(n,(()=>e((0,a.IX)(t,n))));break;case"allAssetRequest":(0,c.Hh)(n,(()=>e((0,a.yl)(t,n))));break;default:(0,c.vW)(t,n,{resizeFn:b(t.adId,n)}),(0,c.Pk)(n)}}});var y=n(38230),v=n(43272),E=n(67314),A=n(97779),I=n(16833),w=n(49164),T=n(93597),C=n(12938),S=n(11445),O=n(75023),B=n(16894),R=n(25555),k=n(70068),U=n(16916),D=n(12713),_=n(63895);const j=new Map([["format",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"object"==typeof e))],["w",i.isInteger],["h",i.isInteger],["btype",i.isArrayOfNums],["battr",i.isArrayOfNums],["pos",i.isInteger],["mimes",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"string"==typeof e))],["topframe",e=>[1,0].includes(e)],["expdir",i.isArrayOfNums],["api",i.isArrayOfNums],["id",i.isStr],["vcm",e=>[1,0].includes(e)]]);var $=n(71371);function x(e,t){return function(){if(document.prerendering&&e()){const e=this,n=Array.from(arguments);return new Promise((r=>{document.addEventListener("prerenderingchange",(()=>{(0,i.logInfo)("Auctions were suspended while page was prerendering"),r(t.apply(e,n))}),{once:!0})}))}return Promise.resolve(t.apply(this,arguments))}}var N=n(57377);const q=(0,r.m)(),{triggerUserSyncs:W}=y.zt,{ADD_AD_UNITS:P,REQUEST_BIDS:M,SET_TARGETING:G}=d.qY,L={bidWon:function(e){if(!E.n.getBidsRequested().map((e=>e.bids.map((e=>e.adUnitCode)))).reduce(i.flatten).filter(i.uniques).includes(e))return void(0,i.logError)('The "'+e+'" placement is not defined.');return!0}};function F(e,t){let n=[];return(0,i.isArray)(e)&&(t?e.length===t:e.length>0)&&(e.every((e=>(0,i.isArrayOfNums)(e,2)))?n=e:(0,i.isArrayOfNums)(e,2)&&n.push(e)),n}function z(e,t){const n=(0,o.A)(e,`ortb2Imp.${t}`),r=(0,o.A)(e,`mediaTypes.${t}`);if(!n&&!r)return;const a={[$.G_]:_.Zy,[$.D4]:j}[t];a&&[...a].forEach((n=>{let[r,a]=n;const d=(0,o.A)(e,`mediaTypes.${t}.${r}`),c=(0,o.A)(e,`ortb2Imp.${t}.${r}`);null==d&&null==c||(null==d?(0,s.J)(e,`mediaTypes.${t}.${r}`,c):null==c?(0,s.J)(e,`ortb2Imp.${t}.${r}`,d):((0,i.logWarn)(`adUnit ${e.code}: specifies conflicting ortb2Imp.${t}.${r} and mediaTypes.${t}.${r}, the latter will be ignored`,e),(0,s.J)(e,`mediaTypes.${t}.${r}`,c)))}))}function H(e){const t=(0,i.deepClone)(e),n=t.mediaTypes.banner,r=null==n.sizes?null:F(n.sizes),o=e.ortb2Imp?.banner?.format??n?.format;let a;if(null!=o){(0,s.J)(t,"ortb2Imp.banner.format",o),n.format=o;try{a=o.filter((t=>{let{w:n,h:r,wratio:o,hratio:s}=t;return null!=(n??r)&&null!=(o??s)?((0,i.logWarn)("Ad unit banner.format specifies both w/h and wratio/hratio",e),!1):null!=n&&null!=r||null!=o&&null!=s})).map((e=>{let{w:t,h:n,wratio:r,hratio:i}=e;return[t??r,n??i]}))}catch(t){(0,i.logError)(`Invalid format definition on ad unit ${e.code}`,o)}null==a||null==r||(0,i.deepEqual)(r,a)||(0,i.logWarn)(`Ad unit ${e.code} has conflicting sizes and format definitions`,e)}const d=a??r??[],c=e.ortb2Imp?.banner?.expdir??n.expdir;return null!=c&&(n.expdir=c,(0,s.J)(t,"ortb2Imp.banner.expdir",c)),d.length>0?(n.sizes=d,t.sizes=d):((0,i.logError)("Detected a mediaTypes.banner object without a proper sizes field.  Please ensure the sizes are listed like: [[300, 250], ...].  Removing invalid mediaTypes.banner object from request."),delete t.mediaTypes.banner),z(t,"banner"),t}function V(e){const t=(0,i.deepClone)(e),n=t.mediaTypes.video;if(n.playerSize){let e="number"==typeof n.playerSize[0]?2:1;const r=F(n.playerSize,e);r.length>0?(2===e&&(0,i.logInfo)("Transforming video.playerSize from [640,480] to [[640,480]] so it's in the proper format."),n.playerSize=r,t.sizes=r):((0,i.logError)("Detected incorrect configuration of mediaTypes.video.playerSize.  Please specify only one set of dimensions in a format like: [[640, 480]]. Removing invalid mediaTypes.video.playerSize property from request."),delete t.mediaTypes.video.playerSize)}return(0,_.aP)(t),z(t,"video"),t}function J(e){function t(t){return(0,i.logError)(`Error in adUnit "${e.code}": ${t}. Removing native request from ad unit`,e),delete r.mediaTypes.native,r}function n(e){for(const t of["sendTargetingKeys","types"])if(o.hasOwnProperty(t)){const n=e(t);if(n)return n}}const r=(0,i.deepClone)(e),o=r.mediaTypes.native;if(o.ortb){if(o.ortb.assets?.some((e=>!(0,i.isNumber)(e.id)||e.id<0||e.id%1!=0)))return t("native asset ID must be a nonnegative integer");if(n((e=>t(`ORTB native requests cannot specify "${e}"`))))return r;const e=Object.keys(d.x5).filter((e=>d.x5[e].includes("hb_native_"))),s=Object.keys(o).filter((t=>e.includes(t)));s.length>0&&((0,i.logError)(`when using native OpenRTB format, you cannot use legacy native properties. Deleting ${s} keys from request.`),s.forEach((e=>delete r.mediaTypes.native[e])))}else n((e=>`mediaTypes.native.${e} is deprecated, consider using native ORTB instead`));return o.image&&o.image.sizes&&!Array.isArray(o.image.sizes)&&((0,i.logError)("Please use an array of sizes for native.image.sizes field.  Removing invalid mediaTypes.native.image.sizes property from request."),delete r.mediaTypes.native.image.sizes),o.image&&o.image.aspect_ratios&&!Array.isArray(o.image.aspect_ratios)&&((0,i.logError)("Please use an array of sizes for native.image.aspect_ratios field.  Removing invalid mediaTypes.native.image.aspect_ratios property from request."),delete r.mediaTypes.native.image.aspect_ratios),o.icon&&o.icon.sizes&&!Array.isArray(o.icon.sizes)&&((0,i.logError)("Please use an array of sizes for native.icon.sizes field.  Removing invalid mediaTypes.native.icon.sizes property from request."),delete r.mediaTypes.native.icon.sizes),r}function K(e,t){let n=e?.mediaTypes?.[t]?.pos;if(!(0,i.isNumber)(n)||isNaN(n)||!isFinite(n)){let n=`Value of property 'pos' on ad unit ${e.code} should be of type: Number`;(0,i.logWarn)(n),delete e.mediaTypes[t].pos}return e}function Y(e){const t=t=>`adUnit.code '${e.code}' ${t}`,n=e.mediaTypes,r=e.bids;return null==r||(0,i.isArray)(r)?null==r&&null==e.ortb2Imp?((0,i.logError)(t("has no 'adUnit.bids' and no 'adUnit.ortb2Imp'. Removing adUnit from auction")),null):n&&0!==Object.keys(n).length?(null==e.ortb2Imp||null!=r&&0!==r.length||(e.bids=[{bidder:null}],(0,i.logMessage)(t("defines 'adUnit.ortb2Imp' with no 'adUnit.bids'; it will be seen only by S2S adapters"))),e):((0,i.logError)(t("does not define a 'mediaTypes' object.  This is a required field for the auction, so this adUnit has been removed.")),null):((0,i.logError)(t("defines 'adUnit.bids' that is not an array. Removing adUnit from auction")),null)}(0,w.L6)(),q.bidderSettings=q.bidderSettings||{},q.libLoaded=!0,q.version="v9.50.0",(0,i.logInfo)("Prebid.js v9.50.0 loaded"),q.installedModules=q.installedModules||[],q.adUnits=q.adUnits||[],q.triggerUserSyncs=W;const X={validateAdUnit:Y,validateBannerMediaType:H,validateSizes:F};Object.assign(X,{validateNativeMediaType:J}),Object.assign(X,{validateVideoMediaType:V});const Q=(0,I.A_)("sync",(function(e){const t=[];return e.forEach((e=>{if(null==(e=Y(e)))return;const n=e.mediaTypes;let r,i,o;n.banner&&(r=H(e),n.banner.hasOwnProperty("pos")&&(r=K(r,"banner"))),n.video&&(i=V(r||e),n.video.hasOwnProperty("pos")&&(i=K(i,"video"))),n.native&&(o=J(i||(r||e)));const s=Object.assign({},r,i,o);t.push(s)})),t}),"checkAdUnitSetup");function Z(e){const t=E.n[e]().filter((e=>E.n.getAdUnitCodes().includes(e.adUnitCode))),n=E.n.getLastAuctionId();return t.map((e=>e.adUnitCode)).filter(i.uniques).map((e=>t.filter((t=>t.auctionId===n&&t.adUnitCode===e)))).filter((e=>e&&e[0]&&e[0].adUnitCode)).map((e=>({[e[0].adUnitCode]:{bids:e}}))).reduce(((e,t)=>Object.assign(e,t)),{})}q.getAdserverTargetingForAdUnitCodeStr=function(e){if((0,i.logInfo)("Invoking pbjs.getAdserverTargetingForAdUnitCodeStr",arguments),e){var t=q.getAdserverTargetingForAdUnitCode(e);return(0,i.transformAdServerTargetingObj)(t)}(0,i.logMessage)("Need to call getAdserverTargetingForAdUnitCodeStr with adunitCode")},q.getHighestUnusedBidResponseForAdUnitCode=function(e){if(e){const t=E.n.getAllBidsForAdUnitCode(e).filter(A.Yl);return t.length?t.reduce(D.Vk):{}}(0,i.logMessage)("Need to call getHighestUnusedBidResponseForAdUnitCode with adunitCode")},q.getAdserverTargetingForAdUnitCode=function(e){return q.getAdserverTargeting(e)[e]},q.getAdserverTargeting=function(e){return(0,i.logInfo)("Invoking pbjs.getAdserverTargeting",arguments),A.iS.getAllTargeting(e)},q.getConsentMetadata=function(){return(0,i.logInfo)("Invoking pbjs.getConsentMetadata"),U.SL.getConsentMeta()},q.getNoBids=function(){return(0,i.logInfo)("Invoking pbjs.getNoBids",arguments),Z("getNoBids")},q.getNoBidsForAdUnitCode=function(e){return{bids:E.n.getNoBids().filter((t=>t.adUnitCode===e))}},q.getBidResponses=function(){return(0,i.logInfo)("Invoking pbjs.getBidResponses",arguments),Z("getBidsReceived")},q.getBidResponsesForAdUnitCode=function(e){return{bids:E.n.getBidsReceived().filter((t=>t.adUnitCode===e))}},q.setTargetingForGPTAsync=function(e,t){(0,i.logInfo)("Invoking pbjs.setTargetingForGPTAsync",arguments),(0,i.isGptPubadsDefined)()?A.iS.setTargetingForGPT(e,t):(0,i.logError)("window.googletag is not defined on the page")},q.setTargetingForAst=function(e){(0,i.logInfo)("Invoking pbjs.setTargetingForAn",arguments),A.iS.isApntagDefined()?(A.iS.setTargetingForAst(e),O.emit(G,A.iS.getAllTargeting())):(0,i.logError)("window.apntag is not defined on the page")},q.renderAd=(0,I.A_)("async",(function(e,t,n){(0,i.logInfo)("Invoking pbjs.renderAd",arguments),(0,i.logMessage)("Calling renderAd with adId :"+t),(0,c.BS)(e,t,n)})),q.removeAdUnit=function(e){if((0,i.logInfo)("Invoking pbjs.removeAdUnit",arguments),!e)return void(q.adUnits=[]);let t;t=(0,i.isArray)(e)?e:[e],t.forEach((e=>{for(let t=q.adUnits.length-1;t>=0;t--)q.adUnits[t].code===e&&q.adUnits.splice(t,1)}))},q.requestBids=function(){const e=(0,I.A_)("async",(function(){let{bidsBackHandler:e,timeout:t,adUnits:n,adUnitCodes:r,labels:o,auctionId:s,ttlBuffer:a,ortb2:d,metrics:c,defer:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};O.emit(M);const u=t||v.$W.getConfig("bidderTimeout");(0,i.logInfo)("Invoking pbjs.requestBids",arguments),null==r||Array.isArray(r)||(r=[r]),r&&r.length?n=n.filter((e=>r.includes(e.code))):r=n&&n.map((e=>e.code)),r=r.filter(i.uniques);const g={global:(0,i.mergeDeep)({},v.$W.getAnyConfig("ortb2")||{},d||{}),bidder:Object.fromEntries(Object.entries(v.$W.getBidderConfig()).map((e=>{let[t,n]=e;return[t,(0,i.deepClone)(n.ortb2)]})).filter((e=>{let[t,n]=e;return null!=n})))};return(0,k.w)(R.U9.resolve(g.global)).then((t=>(g.global=t,ee({bidsBackHandler:e,timeout:u,adUnits:n,adUnitCodes:r,labels:o,auctionId:s,ttlBuffer:a,ortb2Fragments:g,metrics:c,defer:l}))))}),"requestBids");return(0,I.Y6)(e,x((()=>!v.$W.getConfig("allowPrerendering")),(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.adUnits||q.adUnits;return t.adUnits=(0,i.isArray)(n)?n.slice():[n],t.metrics=(0,B.K7)(),t.metrics.checkpoint("requestBids"),t.defer=(0,R.v6)({promiseFactory:e=>new Promise(e)}),e.call(this,t),t.defer.promise})))}();const ee=(0,I.A_)("async",(function(){let{bidsBackHandler:e,timeout:t,adUnits:n,ttlBuffer:r,adUnitCodes:o,labels:a,auctionId:d,ortb2Fragments:c,metrics:l,defer:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const g=(0,S.pX)(v.$W.getConfig("s2sConfig")||[]);function f(t,n,r){if("function"==typeof e)try{e(t,n,r)}catch(e){(0,i.logError)("Error executing bidsBackHandler",null,e)}u.resolve({bids:t,timedOut:n,auctionId:r})}!function(e){e.forEach((e=>(0,_.V0)(e)))}(n),n=(0,B.BO)(l).measureTime("requestBids.validate",(()=>Q(n)));const p={};if(n.forEach((e=>{const t=Object.keys(e.mediaTypes||{banner:"banner"}),n=e.bids.map((e=>e.bidder)),o=S.Ay.bidderRegistry,s=n.filter((e=>!g.has(e)));e.adUnitId=(0,i.generateUUID)();const a=e.ortb2Imp?.ext?.tid;a&&(p.hasOwnProperty(e.code)?(0,i.logWarn)(`Multiple distinct ortb2Imp.ext.tid were provided for twin ad units '${e.code}'`):p[e.code]=a),null==r||e.hasOwnProperty("ttlBuffer")||(e.ttlBuffer=r),s.forEach((n=>{const r=o[n],s=r&&r.getSpec&&r.getSpec(),a=s&&s.supportedMediaTypes||["banner"];t.some((e=>a.includes(e)))||((0,i.logWarn)((0,i.unsupportedBidderMessage)(e,n)),e.bids=e.bids.filter((e=>e.bidder!==n)))}))})),n&&0!==n.length){n.forEach((e=>{const t=e.ortb2Imp?.ext?.tid||p[e.code]||(0,i.generateUUID)();p.hasOwnProperty(e.code)||(p[e.code]=t),e.transactionId=t,(0,s.J)(e,"ortb2Imp.ext.tid",t)}));const e=E.n.createAuction({adUnits:n,adUnitCodes:o,callback:f,cbTimeout:t,labels:a,auctionId:d,ortb2Fragments:c,metrics:l});let r=n.length;r>15&&(0,i.logInfo)(`Current auction ${e.getAuctionId()} contains ${r} adUnits.`,n),o.forEach((t=>A.iS.setLatestAuctionForAdUnit(t,e.getAuctionId()))),e.callBids()}else(0,i.logMessage)("No adUnits configured. No bids requested."),f()}),"startAuction");q.requestBids.before((function(e,t){function n(e){for(var t;t=e.shift();)t()}n(C.s0),n(te),e.call(this,t)}),49),q.addAdUnits=function(e){(0,i.logInfo)("Invoking pbjs.addAdUnits",arguments),q.adUnits.push.apply(q.adUnits,(0,i.isArray)(e)?e:[e]),O.emit(P)},q.onEvent=function(e,t,n){(0,i.logInfo)("Invoking pbjs.onEvent",arguments),(0,i.isFn)(t)?!n||L[e].call(null,n)?O.on(e,t,n):(0,i.logError)('The id provided is not valid for event "'+e+'" and no handler was set.'):(0,i.logError)('The event handler provided is not a function and was not set on event "'+e+'".')},q.offEvent=function(e,t,n){(0,i.logInfo)("Invoking pbjs.offEvent",arguments),n&&!L[e].call(null,n)||O.off(e,t,n)},q.getEvents=function(){return(0,i.logInfo)("Invoking pbjs.getEvents"),O.getEvents()},q.registerBidAdapter=function(e,t,n){(0,i.logInfo)("Invoking pbjs.registerBidAdapter",arguments);try{const r=n?(0,N.xb)(n):e();S.Ay.registerBidAdapter(r,t)}catch(e){(0,i.logError)("Error registering bidder adapter : "+e.message)}},q.registerAnalyticsAdapter=function(e){(0,i.logInfo)("Invoking pbjs.registerAnalyticsAdapter",arguments);try{S.Ay.registerAnalyticsAdapter(e)}catch(e){(0,i.logError)("Error registering analytics adapter : "+e.message)}},q.createBid=function(e){return(0,i.logInfo)("Invoking pbjs.createBid",arguments),(0,T.O)(e)};const te=[],ne=(0,I.A_)("async",(function(e){e&&!(0,i.isEmpty)(e)?((0,i.logInfo)("Invoking pbjs.enableAnalytics for: ",e),S.Ay.enableAnalytics(e)):(0,i.logError)("pbjs.enableAnalytics should be called with option {}")}),"enableAnalyticsCb");function re(e){if("function"==typeof e)try{e.call()}catch(e){(0,i.logError)("Error processing command :",e.message,e.stack)}else(0,i.logError)("Commands written into pbjs.cmd.push must be wrapped in a function")}function ie(e){e.forEach((function(e){if(void 0===e.called)try{e.call(),e.called=!0}catch(e){(0,i.logError)("Error processing command :","prebid.js",e)}}))}q.enableAnalytics=function(e){te.push(ne.bind(this,e))},q.aliasBidder=function(e,t,n){(0,i.logInfo)("Invoking pbjs.aliasBidder",arguments),e&&t?S.Ay.aliasBidAdapter(e,t,n):(0,i.logError)("bidderCode and alias must be passed as arguments","pbjs.aliasBidder")},q.aliasRegistry=S.Ay.aliasRegistry,v.$W.getConfig("aliasRegistry",(e=>{"private"===e.aliasRegistry&&delete q.aliasRegistry})),q.getAllWinningBids=function(){return E.n.getAllWinningBids()},q.getAllPrebidWinningBids=function(){return(0,i.logWarn)("getAllPrebidWinningBids may be removed or renamed in a future version. This function returns bids that have won in prebid and have had targeting set but have not (yet?) won in the ad server. It excludes bids that have been rendered."),E.n.getBidsReceived().filter((e=>e.status===d.tl.BID_TARGETING_SET))},q.getHighestCpmBids=function(e){return A.iS.getWinningBids(e)},q.clearAllAuctions=function(){E.n.clearAllAuctions()},q.markWinningBidAsUsed=function(e){let t,{adId:n,adUnitCode:r,analytics:o=!1,events:s=!1}=e;r&&null==n?t=A.iS.getWinningBids(r):n?t=E.n.getBidsReceived().filter((e=>e.adId===n)):(0,i.logWarn)("Improper use of markWinningBidAsUsed. It needs an adUnitCode or an adId to function."),t.length>0&&(o||s?(0,c.n6)(t[0]):E.n.addWinningBid(t[0]),(0,c.qn)(t[0]))},q.getConfig=v.$W.getAnyConfig,q.readConfig=v.$W.readAnyConfig,q.mergeConfig=v.$W.mergeConfig,q.mergeBidderConfig=v.$W.mergeBidderConfig,q.setConfig=v.$W.setConfig,q.setBidderConfig=v.$W.setBidderConfig,q.que.push((()=>h())),q.processQueue=x((()=>(0,r.m)().delayPrerendering),(function(){q.que.push=q.cmd.push=re,(0,c.XO)(),I.A_.ready(),ie(q.que),ie(q.cmd)})),q.triggerBilling=e=>{let{adId:t,adUnitCode:n}=e;E.n.getAllWinningBids().filter((e=>e.adId===t||null==t&&e.adUnitCode===n)).forEach((e=>{S.Ay.triggerBilling(e),(0,c.vB)(e)}))}},7873:(e,t,n)=>{n.d(t,{E:()=>s,m:()=>o});const r=window,i=r.pbjs=r.pbjs||{};function o(){return i}function s(e){i.installedModules.push(e)}i.cmd=i.cmd||[],i.que=i.que||[],r===window&&(r._pbjsGlobals=r._pbjsGlobals||[],r._pbjsGlobals.push("pbjs"))},27934:(e,t,n)=>{n.d(t,{EN:()=>d,gR:()=>s});var r=n(43272),i=n(91069);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;if(!e)return e;if(/\w+:\/\//.exec(e))return e;let n=t.location.protocol;try{n=t.top.location.protocol}catch(e){}return/^\/\//.exec(e)?n+e:`${n}//${e}`}function s(e){let{noLeadingWww:t=!1,noPort:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{e=new URL(o(e))}catch(e){return}return e=n?e.hostname:e.host,t&&e.startsWith("www.")&&(e=e.substring(4)),e}function a(e){try{const t=e.querySelector("link[rel='canonical']");if(null!==t)return t.href}catch(e){}return null}const d=function(e){let t,n,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return i.top!==i?e:function(){const o=a(i.document),s=i.location.href;return t===o&&s===n||(t=o,n=s,r=e()),r}}((c=window,function(){const e=[],t=function(e){try{if(!e.location.ancestorOrigins)return;return e.location.ancestorOrigins}catch(e){}}(c),n=r.$W.getConfig("maxNestedIframes");let d,l,u,g,f=!1,p=0,m=!1,h=!1,b=!1;do{const n=d,r=h;let o,s=!1,g=null;h=!1,d=d?d.parent:c;try{o=d.location.href||null}catch(e){s=!0}if(s)if(r){const e=n.context;try{g=e.sourceUrl,l=g,b=!0,m=!0,d===c.top&&(f=!0),e.canonicalUrl&&(u=e.canonicalUrl)}catch(e){}}else{(0,i.logWarn)("Trying to access cross domain iframe. Continuing without referrer and location");try{const e=n.document.referrer;e&&(g=e,d===c.top&&(f=!0))}catch(e){}!g&&t&&t[p-1]&&(g=t[p-1],d===c.top&&(b=!0)),g&&!m&&(l=g)}else{if(o&&(g=o,l=g,m=!1,d===c.top)){f=!0;const e=a(d.document);e&&(u=e)}d.context&&d.context.sourceUrl&&(h=!0)}e.push(g),p++}while(d!==c.top&&p<n);e.reverse();try{g=c.top.document.referrer}catch(e){}const y=f||b?l:null,v=r.$W.getConfig("pageUrl")||u||null;let E=r.$W.getConfig("pageUrl")||y||o(v,c);return y&&y.indexOf("?")>-1&&-1===E.indexOf("?")&&(E=`${E}${y.substring(y.indexOf("?"))}`),{reachedTop:f,isAmp:m,numIframes:p-1,stack:e,topmostLocation:l||null,location:y,canonicalUrl:v,page:E,domain:s(E)||null,ref:g||null,legacy:{reachedTop:f,isAmp:m,numIframes:p-1,stack:e,referer:l||null,canonicalUrl:v}}}));var c},12938:(e,t,n)=>{n.d(t,{CK:()=>b,X0:()=>f,qk:()=>g,s0:()=>p,vM:()=>h});var r=n(91069),i=n(12693),o=n(45569),s=n(95139),a=n(2604),d=n(76811),c=n(43272),l=n(11445),u=n(83441);const g="html5",f="cookie";let p=[];function m(){let{moduleName:e,moduleType:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isAllowed:n=s.io}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};function i(r,i){let s=e;const g=c.$W.getCurrentBidder();g&&t===o.tW&&l.Ay.aliasRegistry[g]===e&&(s=g);return r({valid:n(d.Ue,(0,u.s)(t,s,{[a.Zw]:i}))})}function m(e,t,n){if(!n||"function"!=typeof n)return i(e,t);p.push((function(){let r=i(e,t);n(r)}))}function h(e){const t=e.charAt(0).toUpperCase()+e.substring(1),n=()=>window[e],i=function(t){return m((function(t){if(t&&t.valid)try{return!!n()}catch(t){(0,r.logError)(`${e} api disabled`)}return!1}),g,t)};return{[`has${t}`]:i,[`${e}IsEnabled`]:e=>m((function(e){if(e&&e.valid)try{return n().setItem("prebid.cookieTest","1"),"1"===n().getItem("prebid.cookieTest")}catch(e){}finally{try{n().removeItem("prebid.cookieTest")}catch(e){}}return!1}),g,e),[`setDataIn${t}`]:(e,t,r)=>m((function(r){r&&r.valid&&i()&&n().setItem(e,t)}),g,r),[`getDataFrom${t}`]:(e,t)=>m((function(t){return t&&t.valid&&i()?n().getItem(e):null}),g,t),[`removeDataFrom${t}`]:(e,t)=>m((function(t){t&&t.valid&&i()&&n().removeItem(e)}),g,t)}}return{setCookie:function(e,t,n,r,i,o){return m((function(o){if(o&&o.valid){const o=i&&""!==i?` ;domain=${encodeURIComponent(i)}`:"",s=n&&""!==n?` ;expires=${n}`:"",a=null!=r&&"none"==r.toLowerCase()?"; Secure":"";document.cookie=`${e}=${encodeURIComponent(t)}${s}; path=/${o}${r?`; SameSite=${r}`:""}${a}`}}),f,o)},getCookie:function(e,t){return m((function(t){if(t&&t.valid){let t=window.document.cookie.match("(^|;)\\s*"+e+"\\s*=\\s*([^;]*)\\s*(;|$)");return t?decodeURIComponent(t[2]):null}return null}),f,t)},cookiesAreEnabled:function(e){return m((function(e){return!(!e||!e.valid)&&(0,r.checkCookieSupport)()}),f,e)},...h("localStorage"),...h("sessionStorage"),findSimilarCookies:function(e,t){return m((function(t){if(t&&t.valid){const t=[];if((0,r.hasDeviceAccess)()){const n=document.cookie.split(";");for(;n.length;){const r=n.pop();let i=r.indexOf("=");i=i<0?r.length:i;decodeURIComponent(r.slice(0,i).replace(/^\s+/,"")).indexOf(e)>=0&&t.push(decodeURIComponent(r.slice(i+1)))}}return t}}),f,t)}}}function h(){let{moduleType:e,moduleName:t,bidderCode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};function r(){throw new Error("Invalid invocation for getStorageManager: must set either bidderCode, or moduleType + moduleName")}return n?((e&&e!==o.tW||t)&&r(),e=o.tW,t=n):t&&e||r(),m({moduleType:e,moduleName:t})}function b(e){return m({moduleName:e,moduleType:o.tp})}(0,s.qB)(d.Ue,"deviceAccess config",(function(){if(!(0,r.hasDeviceAccess)())return{allow:!1}})),(0,s.qB)(d.Ue,"bidderSettings.*.storageAllowed",(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.u;if(e[a.Dk]!==o.tW)return;let n=t.get(e[a.q7],"storageAllowed");if(n&&!0!==n){const t=e[a.Zw];n=Array.isArray(n)?n.some((e=>e===t)):n===t}else n=!!n;return n?void 0:{allow:n}}))},97779:(e,t,n)=>{n.d(t,{Jp:()=>T,ME:()=>w,Yl:()=>I,iS:()=>S,m2:()=>C,uW:()=>A});var r=n(67314),i=n(27863),o=n(12693),s=n(43272),a=n(78969),d=n(75023),c=n(16833),l=n(71371),u=n(12449),g=n(91069),f=n(70433),p=n(12713),m=[];const h=20,b="targetingControls.allowTargetingKeys",y="targetingControls.addTargetingKeys",v=`Only one of "${b}" or "${y}" can be set`,E=Object.keys(a.xS).map((e=>a.xS[e]));let A={isActualBid:e=>e.getStatusCode()===a.XQ.GOOD,isBidNotExpired:e=>e.responseTimestamp+1e3*(0,i.cT)(e)>(0,g.timestamp)(),isUnusedBid:e=>e&&(e.status&&![a.tl.RENDERED].includes(e.status)||!e.status)};function I(e){return!Object.values(A).some((t=>!t(e)))}const w=(0,c.A_)("sync",(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:g.sortByHighestCpm;if(!r){const r=[],o=s.$W.getConfig("sendBidsControl.dealPrioritization");let a=(0,g.groupBy)(e,"adUnitCode");return Object.keys(a).forEach((e=>{let s=[],d=(0,g.groupBy)(a[e],"bidderCode");Object.keys(d).forEach((e=>{s.push(d[e].reduce(t))})),n?(s=o?s.sort(T(!0)):s.sort(((e,t)=>t.cpm-e.cpm)),r.push(...s.slice(0,n))):(s=s.sort(i),r.push(...s))})),r}return e}));function T(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){return void 0!==t.adserverTargeting.hb_deal&&void 0===n.adserverTargeting.hb_deal?-1:void 0===t.adserverTargeting.hb_deal&&void 0!==n.adserverTargeting.hb_deal?1:e?n.cpm-t.cpm:n.adserverTargeting.hb_pb-t.adserverTargeting.hb_pb}}function C(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>window.googletag.pubads().getSlots())().reduce(((e,n)=>{const r=(0,g.isFn)(t)&&t(n);return Object.keys(e).filter((0,g.isFn)(r)?r:(0,g.isAdUnitCodeMatchingSlot)(n)).forEach((t=>e[t].push(n))),e}),Object.fromEntries(e.map((e=>[e,[]]))))}const S=function(e){let t={},n={};function r(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=E.concat(u.Nh),i=s.$W.getConfig("targetingControls.allowSendAllBidsTargetingKeys"),o=i?i.map((e=>a.xS[e])):r;return e.reduce(((e,i)=>{if(t||n&&i.dealId){const t=function(e,t){return t.reduce(((t,n)=>(e.adserverTargeting[n]&&t.push({[`${n}_${e.bidderCode}`.substring(0,20)]:[e.adserverTargeting[n]]}),t)),[])}(i,r.filter((e=>void 0!==i.adserverTargeting[e]&&(n||-1!==o.indexOf(e)))));t&&e.push({[i.adUnitCode]:t})}return e}),[])}function i(t){return"string"==typeof t?[t]:(0,g.isArray)(t)?t:e.getAdUnitCodes()||[]}function A(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p.Bq,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=e.getBidsReceived().reduce(((e,t)=>{const r=s.$W.getConfig("useBidCache"),i=s.$W.getConfig("bidCacheFilterFunction"),o=n[t.adUnitCode]===t.auctionId,a=!(r&&!o&&"function"==typeof i)||!!i(t);return(r||o)&&a&&(0,f.A)(t,"video.context")!==l.LM&&I(t)&&(t.latestTargetedAuctionId=n[t.adUnitCode],e.push(t)),e}),[]);return w(i,t,void 0,void 0,void 0,r)}function S(e,n){let r=t.getWinningBids(n,e),i=O();return r=r.map((e=>({[e.adUnitCode]:Object.keys(e.adserverTargeting).filter((t=>void 0===e.sendStandardTargeting||e.sendStandardTargeting||-1===i.indexOf(t))).reduce(((t,n)=>{const r=[e.adserverTargeting[n]],i={[n.substring(0,h)]:r};if(n===a.xS.DEAL){const o=`${n}_${e.bidderCode}`.substring(0,h),s={[o]:r};return[...t,i,s]}return[...t,i]}),[])}))),r}function O(){return e.getStandardBidderAdServerTargeting().map((e=>e.key)).concat(E).filter(g.uniques)}return t.setLatestAuctionForAdUnit=function(e,t){n[e]=t},t.resetPresetTargeting=function(e,t){if((0,g.isGptPubadsDefined)()){const n=i(e);Object.values(C(n,t)).forEach((e=>{e.forEach((e=>{!function(e){m.forEach((t=>{e.getTargeting(t)&&e.clearTargeting(t)}))}(e)}))}))}},t.resetPresetTargetingAST=function(e){i(e).forEach((function(e){const t=window.apntag.getTag(e);if(t&&t.keywords){const n=Object.keys(t.keywords),r={};n.forEach((e=>{m.includes(e.toLowerCase())||(r[e]=t.keywords[e])})),window.apntag.modifyTag(e,{keywords:r})}}))},t.getAllTargeting=function(t,n,d){let c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:p.Vk,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:g.sortByHighestCpm;d||=A(c,l);const f=i(t),E=s.$W.getConfig("enableSendAllBids"),I=s.$W.getConfig("sendBidsControl.bidLimit"),C=E&&(n||I)||0,{customKeysByUnit:B,filteredBids:R}=function(e,t){const n=[],r={},i=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return t.forEach((t=>{const s=e.includes(t.adUnitCode),a=!0===o.u.get(t.bidderCode,"allowZeroCpmBids")?t.cpm>=0:t.cpm>0,d=i&&t.dealId;s&&(d||a)&&(n.push(t),Object.keys(t.adserverTargeting).filter(function(){let e=O();e=e.concat(u.Nh);return function(t){return-1===e.indexOf(t)}}()).forEach((e=>{const n=e.substring(0,h),i=r[t.adUnitCode]||{},o=[t.adserverTargeting[e]];i[n]?i[n]=i[n].concat(o).filter(g.uniques):i[n]=o,r[t.adUnitCode]=i})))})),{filteredBids:n,customKeysByUnit:r}}(f,d);let k=function(t,n,i){const o=!1!==s.$W.getConfig("targetingControls.allBidsCustomTargeting"),d=S(t,i).concat(function(e){const t=s.$W.getConfig("targetingControls.alwaysIncludeDeals");return r(e,s.$W.getConfig("enableSendAllBids"),t)}(t)).concat(function(t){function n(e){return e?.[a.iD.ADSERVER_TARGETING]}function r(e){const t=n(e);return Object.keys(t).map((function(e){return(0,g.isStr)(t[e])&&(t[e]=t[e].split(",").map((e=>e.trim()))),(0,g.isArray)(t[e])||(t[e]=[t[e]]),{[e]:t[e]}}))}return e.getAdUnits().filter((e=>t.includes(e.code)&&n(e))).reduce(((e,t)=>{const n=r(t);return n&&e.push({[t.code]:n}),e}),[])}(i));o&&d.push(...function(e,t){return e.reduce(((e,n)=>{const r=Object.assign({},n),i=t[r.adUnitCode],o=[];return i&&Object.keys(i).forEach((e=>{e&&i[e]&&o.push({[e]:i[e]})})),e.push({[r.adUnitCode]:o}),e}),[])}(t,n));return d.forEach((e=>{!function(e){Object.keys(e).forEach((t=>{e[t].forEach((e=>{const t=Object.keys(e);-1===m.indexOf(t[0])&&(m=t.concat(m))}))}))}(e)})),d}(w(R,c,C,void 0,l),B,f);const U=Object.keys(Object.assign({},a.Zh,a.x5));let D=s.$W.getConfig(b);const _=s.$W.getConfig(y);if(null!=_&&null!=D)throw new Error(v);D=null!=_?U.concat(_):D||U,Array.isArray(D)&&D.length>0&&(k=function(e,t){const n=Object.assign({},a.xS,a.x5),r=Object.keys(n),i={};(0,g.logInfo)(`allowTargetingKeys - allowed keys [ ${t.map((e=>n[e])).join(", ")} ]`),e.map((e=>{const o=Object.keys(e)[0],s=e[o].filter((e=>{const o=Object.keys(e)[0],s=0===r.filter((e=>0===o.indexOf(n[e]))).length||t.find((e=>{const t=n[e];return 0===o.indexOf(t)}));return i[o]=!s,s}));e[o]=s}));const o=Object.keys(i).filter((e=>i[e]));return(0,g.logInfo)(`allowTargetingKeys - removed keys [ ${o.join(", ")} ]`),e.filter((e=>e[Object.keys(e)[0]].length>0))}(k,D)),k=function(e){let t=e.map((e=>({[Object.keys(e)[0]]:e[Object.keys(e)[0]].map((e=>({[Object.keys(e)[0]]:e[Object.keys(e)[0]].join(",")}))).reduce(((e,t)=>Object.assign(t,e)),{})})));return t=t.reduce((function(e,t){var n=Object.keys(t)[0];return e[n]=Object.assign({},e[n],t[n]),e}),{}),t}(k);const j=s.$W.getConfig("targetingControls.auctionKeyMaxChars");return j&&((0,g.logInfo)(`Detected 'targetingControls.auctionKeyMaxChars' was active for this auction; set with a limit of ${j} characters.  Running checks on auction keys...`),k=function(e,t){let n=(0,g.deepClone)(e),r=Object.keys(n).map((e=>({adUnitCode:e,adserverTargeting:n[e]}))).sort(T());return r.reduce((function(e,r,i,o){let s=(a=r.adserverTargeting,Object.keys(a).reduce((function(e,t){return e+`${t}%3d${encodeURIComponent(a[t])}%26`}),""));var a;i+1===o.length&&(s=s.slice(0,-3));let d=r.adUnitCode,c=s.length;return c<=t?(t-=c,(0,g.logInfo)(`AdUnit '${d}' auction keys comprised of ${c} characters.  Deducted from running threshold; new limit is ${t}`,n[d]),e[d]=n[d]):(0,g.logWarn)(`The following keys for adUnitCode '${d}' exceeded the current limit of the 'auctionKeyMaxChars' setting.\nThe key-set size was ${c}, the current allotted amount was ${t}.\n`,n[d]),i+1===o.length&&0===Object.keys(e).length&&(0,g.logError)("No auction targeting keys were permitted due to the setting in setConfig(targetingControls.auctionKeyMaxChars).  Please review setup and consider adjusting."),e}),{})}(k,j)),f.forEach((e=>{k[e]||(k[e]={})})),k},s.$W.getConfig("targetingControls",(function(e){null!=(0,f.A)(e,b)&&null!=(0,f.A)(e,y)&&(0,g.logError)(v)})),t.setTargetingForGPT=(0,c.A_)("sync",(function(n,r){let i=t.getAllTargeting(n),o=Object.fromEntries(m.map((e=>[e,null])));Object.entries(C(Object.keys(i),r)).forEach((e=>{let[t,n]=e;n.length>1&&(0,g.logWarn)(`Multiple slots found matching: ${t}. Targeting will be set on all matching slots, which can lead to duplicate impressions if more than one are requested from GAM. To resolve this, ensure the arguments to setTargetingForGPTAsync resolve to a single slot by explicitly matching the desired slotElementID.`),n.forEach((e=>{Object.keys(i[t]).forEach((e=>{let n=i[t][e];"string"==typeof n&&-1!==n.indexOf(",")&&(n=n.split(",")),i[t][e]=n})),(0,g.logMessage)(`Attempting to set targeting-map for slot: ${e.getSlotElementId()} with targeting-map:`,i[t]),e.updateTargetingFromMap(Object.assign({},o,i[t]))}))})),Object.keys(i).forEach((t=>{Object.keys(i[t]).forEach((n=>{"hb_adid"===n&&e.setStatusForBids(i[t][n],a.tl.BID_TARGETING_SET)}))})),t.targetingDone(i),d.emit(a.qY.SET_TARGETING,i)}),"setTargetingForGPT"),t.targetingDone=(0,c.A_)("sync",(function(e){return e}),"targetingDone"),t.getWinningBids=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.Vk,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:g.sortByHighestCpm;const s=t||A(n,r),a=i(e);return s.filter((e=>a.includes(e.adUnitCode))).filter((e=>!0===o.u.get(e.bidderCode,"allowZeroCpmBids")?e.cpm>=0:e.cpm>0)).map((e=>e.adUnitCode)).filter(g.uniques).map((e=>s.filter((t=>t.adUnitCode===e?t:null)).reduce(p.Vk)))},t.setTargetingForAst=function(e){let n=t.getAllTargeting(e);try{t.resetPresetTargetingAST(e)}catch(e){(0,g.logError)("unable to reset targeting for AST"+e)}Object.keys(n).forEach((e=>Object.keys(n[e]).forEach((t=>{if((0,g.logMessage)(`Attempting to set targeting for targetId: ${e} key: ${t} value: ${n[e][t]}`),(0,g.isStr)(n[e][t])||(0,g.isArray)(n[e][t])){let r={},i=/pt[0-9]/;t.search(i)<0?r[t.toUpperCase()]=n[e][t]:r[t]=n[e][t],window.apntag.setKeywords(e,r,{overrideKeyValue:!0})}}))))},t.isApntagDefined=function(){if(window.apntag&&(0,g.isFn)(window.apntag.setKeywords))return!0},t}(r.n)},38230:(e,t,n)=>{n.d(t,{qh:()=>u,zt:()=>f});var r=n(91069),i=n(43272),o=n(12938),s=n(95139),a=n(76811),d=n(2604),c=n(45569),l=n(83441);const u={syncEnabled:!0,filterSettings:{image:{bidders:"*",filter:"include"}},syncsPerBidder:5,syncDelay:3e3,auctionDelay:500};i.$W.setDefaults({userSync:(0,r.deepClone)(u)});const g=(0,o.CK)("usersync");const f=function(e){let t={},n={image:[],iframe:[]},o=new Set,s={},u={image:!0,iframe:!1},g=e.config;function f(){if(g.syncEnabled&&e.browserSupportsCookies){try{!function(){if(!u.iframe)return;p(n.iframe,(e=>{let[t,i]=e;(0,r.logMessage)(`Invoking iframe user sync for bidder: ${t}`),(0,r.insertUserSyncIframe)(i),function(e,t){e.image=e.image.filter((e=>e[0]!==t))}(n,t)}))}(),function(){if(!u.image)return;p(n.image,(e=>{let[t,n]=e;(0,r.logMessage)(`Invoking image pixel user sync for bidder: ${t}`),(0,r.triggerPixel)(n)}))}()}catch(e){return(0,r.logError)("Error firing user syncs",e)}n={image:[],iframe:[]}}}function p(e,t){(0,r.shuffle)(e).forEach(t)}function m(e,t){let n=g.filterSettings;if(function(e,t){if(e.all&&e[t])return(0,r.logWarn)(`Detected presence of the "filterSettings.all" and "filterSettings.${t}" in userSync config.  You cannot mix "all" with "iframe/image" configs; they are mutually exclusive.`),!1;let n=e.all?e.all:e[t],i=e.all?"all":t;if(!n)return!1;let o=n.filter,s=n.bidders;if(o&&"include"!==o&&"exclude"!==o)return(0,r.logWarn)(`UserSync "filterSettings.${i}.filter" setting '${o}' is not a valid option; use either 'include' or 'exclude'.`),!1;if("*"!==s&&!(Array.isArray(s)&&s.length>0&&s.every((e=>(0,r.isStr)(e)&&"*"!==e))))return(0,r.logWarn)(`Detected an invalid setup in userSync "filterSettings.${i}.bidders"; use either '*' (to represent all bidders) or an array of bidders.`),!1;return!0}(n,e)){u[e]=!0;let r=n.all?n.all:n[e],i="*"===r.bidders?[t]:r.bidders;const o={include:(e,t)=>!e.includes(t),exclude:(e,t)=>e.includes(t)};return o[r.filter||"include"](i,t)}return!u[e]}return i.$W.getConfig("userSync",(e=>{if(e.userSync){let t=e.userSync.filterSettings;(0,r.isPlainObject)(t)&&(t.image||t.all||(e.userSync.filterSettings.image={bidders:"*",filter:"include"}))}g=Object.assign(g,e.userSync)})),e.regRule(a.Ml,"userSync config",(e=>{if(!g.syncEnabled)return{allow:!1,reason:"syncs are disabled"};if(e[d.Dk]===c.tW){const n=e[d.bt],r=e[d.iK];if(!t.canBidderRegisterSync(n,r))return{allow:!1,reason:`${n} syncs are not enabled for ${r}`}}})),t.registerSync=(t,i,u)=>o.has(i)?(0,r.logMessage)(`already fired syncs for "${i}", ignoring registerSync call`):g.syncEnabled&&(0,r.isArray)(n[t])?i?0!==g.syncsPerBidder&&Number(s[i])>=g.syncsPerBidder?(0,r.logWarn)(`Number of user syncs exceeded for "${i}"`):void(e.isAllowed(a.Ml,(0,l.s)(c.tW,i,{[d.bt]:t,[d.e3]:u}))&&(n[t].push([i,u]),s=function(e,t){return e[t]?e[t]+=1:e[t]=1,e}(s,i))):(0,r.logWarn)("Bidder is required for registering sync"):(0,r.logWarn)(`User sync type "${t}" not supported`),t.bidderDone=o.add.bind(o),t.syncUsers=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(e)return setTimeout(f,Number(e));f()},t.triggerUserSyncs=()=>{g.enableOverride&&t.syncUsers()},t.canBidderRegisterSync=(e,t)=>!g.filterSettings||!m(e,t),t}(Object.defineProperties({config:i.$W.getConfig("userSync"),isAllowed:s.io,regRule:s.qB},{browserSupportsCookies:{get:function(){return!(0,r.isSafariBrowser)()&&g.cookiesAreEnabled()}}}))},91069:(e,t,n)=>{n.r(t),n.d(t,{_each:()=>he,_map:()=>ye,_setEventEmitter:()=>T,binarySearch:()=>It,buildUrl:()=>ut,canAccessWindowTop:()=>K,checkCookieSupport:()=>Je,cleanObj:()=>ot,compareCodeAndSlot:()=>tt,compressDataWithGZip:()=>Bt,contains:()=>be,convertObjectToArray:()=>Et,createIframe:()=>ie,createInvisibleIframe:()=>oe,createTrackPixelHtml:()=>Te,createTrackPixelIframeHtml:()=>Se,cyrb53Hash:()=>mt,debugTurnedOn:()=>re,deepAccess:()=>d.A,deepClone:()=>xe,deepEqual:()=>gt,deepSetValue:()=>c.J,delayExecution:()=>Ke,encodeMacroURI:()=>Ce,extractDomainFromHost:()=>Ct,flatten:()=>Be,formatQS:()=>ct,generateUUID:()=>j,getBidIdParameter:()=>$,getBidRequest:()=>Re,getBidderCodes:()=>Ue,getDNT:()=>et,getDefinedParams:()=>Xe,getDocument:()=>J,getDomLoadingDuration:()=>He,getParameterByName:()=>se,getPerformanceNow:()=>ze,getPrebidInternal:()=>k,getSafeframeGeometry:()=>We,getUniqueIdentifierStr:()=>_,getUnixTimestampFromNow:()=>vt,getUserConfiguredParams:()=>Ze,getValue:()=>ke,getWinDimensions:()=>S,getWindowLocation:()=>V,getWindowSelf:()=>H,getWindowTop:()=>z,groupBy:()=>Ye,hasConsoleLogger:()=>ne,hasDeviceAccess:()=>Ve,hasNonSerializableProperty:()=>wt,inIframe:()=>Ne,insertElement:()=>ve,insertHtmlIntoIframe:()=>Ie,insertUserSyncIframe:()=>we,internal:()=>B,isA:()=>ae,isAdUnitCodeMatchingSlot:()=>nt,isApnGetTagDefined:()=>_e,isArray:()=>le,isArrayOfNums:()=>at,isBoolean:()=>fe,isEmpty:()=>pe,isEmptyStr:()=>me,isFn:()=>de,isGptPubadsDefined:()=>De,isGzipCompressionSupported:()=>Ot,isInteger:()=>it,isNumber:()=>ue,isPlainObject:()=>ge,isSafariBrowser:()=>Pe,isSafeFrameWindow:()=>qe,isStr:()=>ce,isValidMediaTypes:()=>Qe,logError:()=>Z,logInfo:()=>X,logMessage:()=>Y,logWarn:()=>Q,memoize:()=>yt,mergeDeep:()=>ft,parseGPTSingleSizeArray:()=>M,parseGPTSingleSizeArrayToRtbSize:()=>L,parseQS:()=>dt,parseQueryStringParameters:()=>x,parseSizesInput:()=>W,parseUrl:()=>lt,pick:()=>st,prefixLog:()=>ee,replaceAuctionPrice:()=>Ge,replaceClickThrough:()=>Le,replaceMacros:()=>Me,resetWinDimensions:()=>O,safeJSONEncode:()=>bt,safeJSONParse:()=>ht,setOnAny:()=>Tt,setScriptAttributes:()=>At,shuffle:()=>$e,sizeTupleToRtbSize:()=>G,sizeTupleToSizeString:()=>P,sizesToSizeTuples:()=>q,sortByHighestCpm:()=>je,timestamp:()=>Fe,transformAdServerTargetingObj:()=>N,triggerNurlWithCpm:()=>St,triggerPixel:()=>Ae,uniques:()=>Oe,unsupportedBidderMessage:()=>rt,waitForElementToLoad:()=>Ee});var r=n(43272),i=n(45751),o=n(78969),s=n(25555),a=n(7873),d=n(70433),c=n(63172),l="String",u="Function",g="Number",f="Object",p="Boolean",m=Object.prototype.toString;let h,b,y=Boolean(window.console),v=Boolean(y&&window.console.log),E=Boolean(y&&window.console.info),A=Boolean(y&&window.console.warn),I=Boolean(y&&window.console.error);const w=(0,a.m)();function T(e){h=e}function C(){null!=h&&h(...arguments)}const S=function(){let e;return()=>((!b||!e||Date.now()-e>20)&&(B.resetWinDimensions(),e=Date.now()),b)}();function O(){const e=K()?B.getWindowTop():B.getWindowSelf();b={screen:{width:e.screen?.width,height:e.screen?.height,availWidth:e.screen?.availWidth,availHeight:e.screen?.availHeight,colorDepth:e.screen?.colorDepth},innerHeight:e.innerHeight,innerWidth:e.innerWidth,outerWidth:e.outerWidth,outerHeight:e.outerHeight,visualViewport:{height:e.visualViewport?.height,width:e.visualViewport?.width},document:{documentElement:{clientWidth:e.document?.documentElement?.clientWidth,clientHeight:e.document?.documentElement?.clientHeight,scrollTop:e.document?.documentElement?.scrollTop,scrollLeft:e.document?.documentElement?.scrollLeft},body:{scrollTop:document.body?.scrollTop,scrollLeft:document.body?.scrollLeft,clientWidth:document.body?.clientWidth,clientHeight:document.body?.clientHeight}}}}const B={checkCookieSupport:Je,createTrackPixelIframeHtml:Se,getWindowSelf:H,getWindowTop:z,canAccessWindowTop:K,getWindowLocation:V,insertUserSyncIframe:we,insertElement:ve,isFn:de,triggerPixel:Ae,logError:Z,logWarn:Q,logMessage:Y,logInfo:X,parseQS:dt,formatQS:ct,deepEqual:gt,resetWinDimensions:O};let R={};function k(){return R}var U,D=(U=0,function(){return++U});function _(){return D()+Math.random().toString(16).substr(2)}function j(e){return e?(e^(window&&window.crypto&&window.crypto.getRandomValues?crypto.getRandomValues(new Uint8Array(1))[0]%16:16*Math.random())>>e/4).toString(16):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,j)}function $(e,t){return t?.[e]||""}function x(e){let t="";for(var n in e)e.hasOwnProperty(n)&&(t+=n+"="+encodeURIComponent(e[n])+"&");return t=t.replace(/&$/,""),t}function N(e){return e&&Object.getOwnPropertyNames(e).length>0?Object.keys(e).map((t=>`${t}=${encodeURIComponent(e[t])}`)).join("&"):""}function q(e){return"string"==typeof e?e.split(/\s*,\s*/).map((e=>e.match(/^(\d+)x(\d+)$/i))).filter((e=>e)).map((e=>{let[t,n,r]=e;return[parseInt(n,10),parseInt(r,10)]})):Array.isArray(e)?F(e)?[e]:e.filter(F):[]}function W(e){return q(e).map(P)}function P(e){return e[0]+"x"+e[1]}function M(e){if(F(e))return P(e)}function G(e){return{w:e[0],h:e[1]}}function L(e){if(F(e))return G(e)}function F(e){return le(e)&&2===e.length&&!isNaN(e[0])&&!isNaN(e[1])}function z(){return window.top}function H(){return window.self}function V(){return window.location}function J(){return document}function K(){try{if(B.getWindowTop().location.href)return!0}catch(e){return!1}}function Y(){re()&&v&&console.log.apply(console,te(arguments,"MESSAGE:"))}function X(){re()&&E&&console.info.apply(console,te(arguments,"INFO:"))}function Q(){re()&&A&&console.warn.apply(console,te(arguments,"WARNING:")),C(o.qY.AUCTION_DEBUG,{type:"WARNING",arguments})}function Z(){re()&&I&&console.error.apply(console,te(arguments,"ERROR:")),C(o.qY.AUCTION_DEBUG,{type:"ERROR",arguments})}function ee(e){function t(t){return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t(e,...r)}}return{logError:t(Z),logWarn:t(Q),logMessage:t(Y),logInfo:t(X)}}function te(e,t){e=[].slice.call(e);let n=r.$W.getCurrentBidder();return t&&e.unshift(t),n&&e.unshift(i("#aaa")),e.unshift(i("#3b88c3")),e.unshift("%cPrebid"+(n?`%c${n}`:"")),e;function i(e){return`display: inline-block; color: #fff; background: ${e}; padding: 1px 4px; border-radius: 3px;`}}function ne(){return v}function re(){return!!r.$W.getConfig("debug")}const ie=(()=>{const e={border:"0px",hspace:"0",vspace:"0",marginWidth:"0",marginHeight:"0",scrolling:"no",frameBorder:"0",allowtransparency:"true"};return function(t,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=t.createElement("iframe");return Object.assign(i,Object.assign({},e,n)),Object.assign(i.style,r),i}})();function oe(){return ie(document,{id:_(),width:0,height:0,src:"about:blank"},{display:"none",height:"0px",width:"0px",border:"0px"})}function se(e){return dt(V().search)[e]||""}function ae(e,t){return m.call(e)==="[object "+t+"]"}function de(e){return ae(e,u)}function ce(e){return ae(e,l)}const le=Array.isArray.bind(Array);function ue(e){return ae(e,g)}function ge(e){return ae(e,f)}function fe(e){return ae(e,p)}function pe(e){return!e||(le(e)||ce(e)?!(e.length>0):Object.keys(e).length<=0)}function me(e){return ce(e)&&(!e||0===e.length)}function he(e,t){if(de(e?.forEach))return e.forEach(t,this);Object.entries(e||{}).forEach((e=>{let[n,r]=e;return t.call(this,r,n)}))}function be(e,t){return de(e?.includes)&&e.includes(t)}function ye(e,t){return de(e?.map)?e.map(t):Object.entries(e||{}).map((n=>{let[r,i]=n;return t(i,r,e)}))}function ve(e,t,n,r){let i;t=t||document,i=n?t.getElementsByTagName(n):t.getElementsByTagName("head");try{if(i=i.length?i:t.getElementsByTagName("body"),i.length){i=i[0];let t=r?null:i.firstChild;return i.insertBefore(e,t)}}catch(e){}}function Ee(e,t){let n=null;return new s.U9((r=>{const i=function(){e.removeEventListener("load",i),e.removeEventListener("error",i),null!=n&&window.clearTimeout(n),r()};e.addEventListener("load",i),e.addEventListener("error",i),null!=t&&(n=window.setTimeout(i,t))}))}function Ae(e,t,n){const r=new Image;t&&B.isFn(t)&&Ee(r,n).then(t),r.src=e}function Ie(e){if(!e)return;const t=oe();var n;B.insertElement(t,document,"body"),(n=t.contentWindow.document).open(),n.write(e),n.close()}function we(e,t,n){let r=B.createTrackPixelIframeHtml(e,!1,"allow-scripts allow-same-origin"),i=document.createElement("div");i.innerHTML=r;let o=i.firstChild;t&&B.isFn(t)&&Ee(o,n).then(t),B.insertElement(o,document,"html",!0)}function Te(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:encodeURI;if(!e)return"";let n='<div style="position:absolute;left:0px;top:0px;visibility:hidden;">';return n+='<img src="'+t(e)+'"></div>',n}function Ce(e){return Array.from(e.matchAll(/\$({[^}]+})/g)).map((e=>e[1])).reduce(((e,t)=>e.replace("$"+encodeURIComponent(t),"$"+t)),encodeURI(e))}function Se(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return e?((!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(e=encodeURI(e)),t&&(t=`sandbox="${t}"`),`<iframe ${t} id="${_()}"\n      frameborder="0"\n      allowtransparency="true"\n      marginheight="0" marginwidth="0"\n      width="0" hspace="0" vspace="0" height="0"\n      style="height:0px;width:0px;display:none;"\n      scrolling="no"\n      src="${e}">\n    </iframe>`):""}function Oe(e,t,n){return n.indexOf(e)===t}function Be(e,t){return e.concat(t)}function Re(e,t){if(e)return t.flatMap((e=>e.bids)).find((t=>["bidId","adId","bid_id"].some((n=>t[n]===e))))}function ke(e,t){return e[t]}function Ue(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:w.adUnits).map((e=>e.bids.map((e=>e.bidder)).reduce(Be,[]))).reduce(Be,[]).filter((e=>void 0!==e)).filter(Oe)}function De(){if(window.googletag&&de(window.googletag.pubads)&&de(window.googletag.pubads().getSlots))return!0}function _e(){if(window.apntag&&de(window.apntag.getTag))return!0}const je=(e,t)=>t.cpm-e.cpm;function $e(e){let t=e.length;for(;t>0;){let n=Math.floor(Math.random()*t);t--;let r=e[t];e[t]=e[n],e[n]=r}return e}function xe(e){return(0,i.Q)(e)||{}}function Ne(){try{return B.getWindowSelf()!==B.getWindowTop()}catch(e){return!0}}function qe(){if(!Ne())return!1;const e=B.getWindowSelf();return!(!e.$sf||!e.$sf.ext)}function We(){try{const e=H();return"function"==typeof e.$sf.ext.geom?e.$sf.ext.geom():void 0}catch(e){return void Z("Error getting SafeFrame geometry",e)}}function Pe(){return/^((?!chrome|android|crios|fxios).)*safari/i.test(navigator.userAgent)}function Me(e,t){if(e)return Object.entries(t).reduce(((e,t)=>{let[n,r]=t;return e.replace(new RegExp("\\$\\{"+n+"\\}","g"),r||"")}),e)}function Ge(e,t){return Me(e,{AUCTION_PRICE:t})}function Le(e,t){if(e&&t&&"string"==typeof t)return e.replace(/\${CLICKTHROUGH}/g,t)}function Fe(){return(new Date).getTime()}function ze(){return window.performance&&window.performance.now&&window.performance.now()||0}function He(e){let t=-1;const n=(e=e||H()).performance;if(e.performance?.timing&&e.performance.timing.navigationStart>0){const e=n.timing.domLoading-n.timing.navigationStart;e>0&&(t=e)}return t}function Ve(){return!1!==r.$W.getConfig("deviceAccess")}function Je(){if(window.navigator.cookieEnabled||document.cookie.length)return!0}function Ke(e,t){if(t<1)throw new Error(`numRequiredCalls must be a positive number. Got ${t}`);let n=0;return function(){n++,n===t&&e.apply(this,arguments)}}function Ye(e,t){return e.reduce((function(e,n){return(e[n[t]]=e[n[t]]||[]).push(n),e}),{})}function Xe(e,t){return t.filter((t=>e[t])).reduce(((t,n)=>Object.assign(t,{[n]:e[n]})),{})}function Qe(e){const t=["banner","native","video"],n=["instream","outstream","adpod"];return!!Object.keys(e).every((e=>t.includes(e)))&&(!e.video||!e.video.context||n.includes(e.video.context))}function Ze(e,t,n){return e.filter((e=>e.code===t)).flatMap((e=>e.bids)).filter((e=>e.bidder===n)).map((e=>e.params||{}))}function et(){return"1"===navigator.doNotTrack||"1"===window.doNotTrack||"1"===navigator.msDoNotTrack||"yes"===navigator.doNotTrack}const tt=(e,t)=>e.getAdUnitPath()===t||e.getSlotElementId()===t;function nt(e){return t=>tt(e,t)}function rt(e,t){const n=Object.keys(e.mediaTypes||{banner:"banner"}).join(", ");return`\n    ${e.code} is a ${n} ad unit\n    containing bidders that don't support ${n}: ${t}.\n    This bidder won't fetch demand.\n  `}const it=Number.isInteger.bind(Number);function ot(e){return Object.fromEntries(Object.entries(e).filter((e=>{let[t,n]=e;return void 0!==n})))}function st(e,t){return"object"!=typeof e?{}:t.reduce(((n,r,i)=>{if("function"==typeof r)return n;let o=r,s=r.match(/^(.+?)\sas\s(.+?)$/i);s&&(r=s[1],o=s[2]);let a=e[r];return"function"==typeof t[i+1]&&(a=t[i+1](a,n)),void 0!==a&&(n[o]=a),n}),{})}function at(e,t){return le(e)&&(!t||e.length===t)&&e.every((e=>it(e)))}function dt(e){return e?e.replace(/^\?/,"").split("&").reduce(((e,t)=>{let[n,r]=t.split("=");return/\[\]$/.test(n)?(n=n.replace("[]",""),e[n]=e[n]||[],e[n].push(r)):e[n]=r||"",e}),{}):{}}function ct(e){return Object.keys(e).map((t=>Array.isArray(e[t])?e[t].map((e=>`${t}[]=${e}`)).join("&"):`${t}=${e[t]}`)).join("&")}function lt(e,t){let n=document.createElement("a");t&&"noDecodeWholeURL"in t&&t.noDecodeWholeURL?n.href=e:n.href=decodeURIComponent(e);let r=t&&"decodeSearchAsString"in t&&t.decodeSearchAsString;return{href:n.href,protocol:(n.protocol||"").replace(/:$/,""),hostname:n.hostname,port:+n.port,pathname:n.pathname.replace(/^(?!\/)/,"/"),search:r?n.search:B.parseQS(n.search||""),hash:(n.hash||"").replace(/^#/,""),host:n.host||window.location.host}}function ut(e){return(e.protocol||"http")+"://"+(e.host||e.hostname+(e.port?`:${e.port}`:""))+(e.pathname||"")+(e.search?`?${B.formatQS(e.search||"")}`:"")+(e.hash?`#${e.hash}`:"")}function gt(e,t){let{checkTypes:n=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e===t)return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;const r=Array.isArray(e),i=Array.isArray(t);if(r&&i){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!gt(e[r],t[r],{checkTypes:n}))return!1;return!0}if(r||i)return!1;if(n&&e.constructor!==t.constructor)return!1;const o=Object.keys(e),s=Object.keys(t);if(o.length!==s.length)return!1;for(const r of o){if(!Object.prototype.hasOwnProperty.call(t,r))return!1;if(!gt(e[r],t[r],{checkTypes:n}))return!1}return!0}function ft(e){for(let t=0;t<(arguments.length<=1?0:arguments.length-1);t++){const n=t+1<1||arguments.length<=t+1?void 0:arguments[t+1];ge(n)&&pt(e,n)}return e}function pt(e,t){if(!ge(e)||!ge(t))return;const n=Object.keys(t);for(let r=0;r<n.length;r++){const i=n[r];if("__proto__"===i||"constructor"===i)continue;const o=t[i];ge(o)?(e[i]||(e[i]={}),pt(e[i],o)):Array.isArray(o)?Array.isArray(e[i])?o.forEach((t=>{e[i].some((e=>gt(e,t)))||e[i].push(t)})):e[i]=[...o]:e[i]=o}}function mt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=function(e,t){if(de(Math.imul))return Math.imul(e,t);var n=(4194303&e)*(t|=0);return 4290772992&e&&(n+=(4290772992&e)*t|0),0|n},r=3735928559^t,i=1103547991^t;for(let t,o=0;o<e.length;o++)t=e.charCodeAt(o),r=n(r^t,2654435761),i=n(i^t,1597334677);return r=n(r^r>>>16,2246822507)^n(i^i>>>13,3266489909),i=n(i^i>>>16,2246822507)^n(r^r>>>13,3266489909),(4294967296*(2097151&i)+(r>>>0)).toString()}function ht(e){try{return JSON.parse(e)}catch(e){}}function bt(e){try{return JSON.stringify(e)}catch(e){return""}}function yt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};const n=new Map,r=function(){const r=t.apply(this,arguments);return n.has(r)||n.set(r,e.apply(this,arguments)),n.get(r)};return r.clear=n.clear.bind(n),r}function vt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"d";if(["m","d"].indexOf(t)<0)return Date.now();const n=e/("m"===t?1440:1);return Date.now()+(e&&e>0?864e5*n:0)}function Et(e){return Object.keys(e).map((t=>({[t]:e[t]})))}function At(e,t){Object.entries(t).forEach((t=>{let[n,r]=t;return e.setAttribute(n,r)}))}function It(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e=>e,r=0,i=e.length&&e.length-1;const o=n(t);for(;i-r>1;){const t=r+Math.round((i-r)/2);o>n(e[t])?r=t:i=t}for(;e.length>r&&o>n(e[r]);)r++;return r}function wt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;for(const n in e){const r=e[n],i=typeof r;if(void 0===r||"function"===i||"symbol"===i||r instanceof RegExp||r instanceof Map||r instanceof Set||r instanceof Date||null!==r&&"object"===i&&r.hasOwnProperty("toJSON"))return!0;if(null!==r&&"object"===i&&r.constructor===Object){if(t.has(r))return!0;if(t.add(r),wt(r,t))return!0}}return!1}function Tt(e,t){for(let n,r=0;r<e.length;r++)if(n=(0,d.A)(e[r],t),n)return n}function Ct(e){let t=null;try{let n=/[-\w]+\.([-\w]+|[-\w]{3,}|[-\w]{1,3}\.[-\w]{2})$/i.exec(e);if(null!=n&&n.length>0){t=n[0];for(let e=1;e<n.length;e++)n[e].length>t.length&&(t=n[e])}}catch(e){t=null}return t}function St(e,t){ce(e.nurl)&&""!==e.nurl&&(e.nurl=e.nurl.replace(/\${AUCTION_PRICE}/,t),Ae(e.nurl))}const Ot=function(){let e;return function(){if(void 0!==e)return e;try{void 0===window.CompressionStream?e=!1:(new window.CompressionStream("gzip"),e=!0)}catch(t){e=!1}return e}}();async function Bt(e){"string"!=typeof e&&(e=JSON.stringify(e));const t=(new TextEncoder).encode(e),n=new Blob([t]).stream().pipeThrough(new window.CompressionStream("gzip")),r=await new Response(n).blob(),i=await r.arrayBuffer();return new Uint8Array(i)}},57176:(e,t,n)=>{n.d(t,{y:()=>s});var r=n(67314),i=n(12693),o=n(91069);function s(e,t,n){let{index:s=r.n.index,bs:a=i.u}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};n=n||s.getBidRequest(t);const d=t?.adapterCode,c=t?.bidderCode||n?.bidder,l=a.get(t?.adapterCode,"adjustAlternateBids"),u=a.getOwn(c,"bidCpmAdjustment")||a.get(l?d:c,"bidCpmAdjustment");if(u&&"function"==typeof u)try{return u(e,Object.assign({},t),n)}catch(e){(0,o.logError)("Error during bid adjustment",e)}return e}},82621:(e,t,n)=>{function r(e){return!e?.gdprApplies||!0===e?.vendorData?.purpose?.consents?.[1]}n.d(t,{C:()=>r})},16894:(e,t,n)=>{n.d(t,{Ak:()=>h,BO:()=>f,K7:()=>p,NL:()=>b});var r=n(43272);const i="performanceMetrics",o=window.performance&&window.performance.now?()=>window.performance.now():()=>Date.now(),s=new WeakMap;function a(){let{now:e=o,mkNode:t=l,mkTimer:n=c,mkRenamer:r=(e=>e),nodes:i=s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(){return function o(s){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>({forEach(t){t(e)}});a=r(a);const d=(c="timestamps",function(e){return s.dfWalk({visit(t,n){const r=n[c];if(r.hasOwnProperty(e))return r[e]}})});var c;function l(e,t){const n=a(e);s.dfWalk({follow:(e,t)=>t.propagate&&(!e||!e.stopPropagation),visit(e,r){n.forEach((n=>{null==e?r.metrics[n]=t:(r.groups.hasOwnProperty(n)||(r.groups[n]=[]),r.groups[n].push(t))}))}})}function u(t){return n(e,(e=>l(t,e)))}function g(){let e={};return s.dfWalk({visit(t,n){e=Object.assign({},!t||t.includeGroups?n.groups:null,n.metrics,e)}}),e}const f={startTiming:u,measureTime:function(e,t){return u(e).stopAfter(t)()},measureHookTime:function(e,t,n){const r=u(e);return n(function(e){const t=r.stopBefore(e);return t.bail=e.bail&&r.stopBefore(e.bail),t.stopTiming=r,t.untimed=e,t}(t))},checkpoint:function(t){s.timestamps[t]=e()},timeSince:function(t,n){const r=d(t),i=null!=r?e()-r:null;return null!=n&&l(n,i),i},timeBetween:function(e,t,n){const r=d(e),i=d(t),o=null!=r&&null!=i?i-r:null;return null!=n&&l(n,o),o},setMetric:l,getMetrics:g,fork:function(){let{propagate:e=!0,stopPropagation:n=!1,includeGroups:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(t([[s,{propagate:e,stopPropagation:n,includeGroups:r}]]),a)},join:function(e){let{propagate:t=!0,stopPropagation:n=!1,includeGroups:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=i.get(e);null!=o&&o.addParent(s,{propagate:t,stopPropagation:n,includeGroups:r})},newMetrics:function(){return o(s.newSibling(),a)},renameWith:function(e){return o(s,e)},toJSON:()=>g()};return i.set(f,s),f}(t([]))}}function d(e,t,n){return function(){t&&t();try{return e.apply(this,arguments)}finally{n&&n()}}}function c(e,t){const n=e();let r=!1;function i(){r||(t(e()-n),r=!0)}return i.stopBefore=e=>d(e,i),i.stopAfter=e=>d(e,null,i),i}function l(e){return{metrics:{},timestamps:{},groups:{},addParent(t,n){e.push([t,n])},newSibling:()=>l(e.slice()),dfWalk(){let t,{visit:n,follow:r=(()=>!0),visited:i=new Set,inEdge:o}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!i.has(this)){if(i.add(this),t=n(o,this),null!=t)return t;for(const[s,a]of e)if(r(o,a)&&(t=s.dfWalk({visit:n,follow:r,visited:i,inEdge:a}),null!=t))return t}}}}const u=(()=>{const e=function(){},t=()=>({}),n={forEach:e},r=()=>null;r.stopBefore=e=>e,r.stopAfter=e=>e;const i=Object.defineProperties({dfWalk:e,newSibling:()=>i,addParent:e},Object.fromEntries(["metrics","timestamps","groups"].map((e=>[e,{get:t}]))));return a({now:()=>0,mkNode:()=>i,mkRenamer:()=>()=>n,mkTimer:()=>r,nodes:{get:e,set:e}})()})();let g=!0;function f(e){return g&&e||u}r.$W.getConfig(i,(e=>{g=!!e[i]}));const p=(()=>{const e=a();return function(){return g?e():u}})();function m(e,t){return function(n,r){return function(i){for(var o=arguments.length,s=new Array(o>1?o-1:0),a=1;a<o;a++)s[a-1]=arguments[a];const d=this;return f(t.apply(d,s)).measureHookTime(e+n,i,(function(e){return r.call(d,e,...s)}))}}}const h=m("requestBids.",(e=>e.metrics)),b=m("addBidResponse.",((e,t)=>t.metrics))},25555:(e,t,n)=>{n.d(t,{U9:()=>s,cb:()=>a,v6:()=>d});var r=n(30043),i=n(7873);const o=(0,i.m)().setTimeout??r.w,s=(0,i.m)().Promise??r.k;function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new s((t=>{o(t,e)}))}function d(){let e,t,{promiseFactory:n=(e=>new s(e))}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};function r(e){return t=>e(t)}return{promise:n(((n,r)=>{e=n,t=r})),resolve:r(e),reject:r(t)}}},12713:(e,t,n)=>{function r(e,t){return e===t?0:e<t?-1:1}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e=>e;return(t,n)=>r(e(t),e(n))}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return(t,n)=>-e(t,n)||0}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n){for(const r of t){const t=r(e,n);if(0!==t)return t}return 0}}function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;return(t,n)=>e(n,t)<0?n:t}function d(){return a(o(arguments.length>0&&void 0!==arguments[0]?arguments[0]:r))}n.d(t,{Bp:()=>a,Bq:()=>g,NV:()=>i,Ph:()=>d,Vk:()=>u});const c=i((e=>e.cpm)),l=i((e=>e.responseTimestamp)),u=d(s(c,o(i((e=>e.timeToRespond))))),g=d(s(c,o(l)));d(s(c,l))},76853:(e,t,n)=>{n.d(t,{H:()=>l});var r=n(25555),i=n(91069);let o=null,s=0,a=[];function d(){document.hidden?o=Date.now():(s+=Date.now()-(o??0),o=null,a.forEach((e=>{let{callback:t,startTime:n,setTimerId:r}=e;return r(c(t,s-n)())})),a=[])}function c(e,t){const n=s;let r=setTimeout((()=>{s===n&&null==o?e():null!=o?a.push({callback:e,startTime:n,setTimerId(e){r=e}}):r=c(e,s-n)()}),t);return()=>r}function l(){let{startTime:e=i.timestamp,ttl:t=(()=>null),monotonic:n=!1,slack:o=5e3}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const s=new Map,a=[],d=[],l=n?e=>d.push(e):e=>d.splice((0,i.binarySearch)(d,e,(e=>e.expiry)),0,e);let u,g;function f(){if(g&&clearTimeout(g),d.length>0){const e=(0,i.timestamp)();u=Math.max(e,d[0].expiry+o),g=c((()=>{const e=(0,i.timestamp)();let t=0;for(const n of d){if(n.expiry>e)break;a.forEach((e=>{try{e(n.item)}catch(e){(0,i.logError)(e)}})),s.delete(n.item),t++}d.splice(0,t),g=null,f()}),u-e)}else g=null}function p(n){const i={},s=m;let a;const[d,c]=Object.entries({start:e,delta:t}).map((e=>{let t,[d,c]=e;return function(){const e=t={};r.U9.resolve(c(n)).then((n=>{e===t&&(i[d]=n,s===m&&null!=i.start&&null!=i.delta&&(a=i.start+i.delta,l(p),(null==g||u>a+o)&&f()))}))}})),p={item:n,refresh:c,get expiry(){return a}};return d(),c(),p}let m={};return{[Symbol.iterator]:()=>s.keys(),add(e){!s.has(e)&&s.set(e,p(e))},clear(){d.length=0,f(),s.clear(),m={}},toArray:()=>Array.from(s.keys()),refresh(){d.length=0,f();for(const e of s.values())e.refresh()},onExpiry:e=>(a.push(e),()=>{const t=a.indexOf(e);t>=0&&a.splice(t,1)})}}document.addEventListener("visibilitychange",d)},63895:(e,t,n)=>{n.d(t,{E2:()=>f,H6:()=>a,V0:()=>l,Zy:()=>c,aP:()=>u,mn:()=>d,vk:()=>g});var r=n(91069),i=n(43272),o=n(16833),s=n(67314);const a="outstream",d="instream",c=new Map([["mimes",e=>Array.isArray(e)&&e.length>0&&e.every((e=>"string"==typeof e))],["minduration",r.isInteger],["maxduration",r.isInteger],["startdelay",r.isInteger],["maxseq",r.isInteger],["poddur",r.isInteger],["protocols",r.isArrayOfNums],["w",r.isInteger],["h",r.isInteger],["podid",r.isStr],["podseq",r.isInteger],["rqddurs",r.isArrayOfNums],["placement",r.isInteger],["plcmt",r.isInteger],["linearity",r.isInteger],["skip",e=>[1,0].includes(e)],["skipmin",r.isInteger],["skipafter",r.isInteger],["sequence",r.isInteger],["slotinpod",r.isInteger],["mincpmpersec",r.isNumber],["battr",r.isArrayOfNums],["maxextended",r.isInteger],["minbitrate",r.isInteger],["maxbitrate",r.isInteger],["boxingallowed",r.isInteger],["playbackmethod",r.isArrayOfNums],["playbackend",r.isInteger],["delivery",r.isArrayOfNums],["pos",r.isInteger],["api",r.isArrayOfNums],["companiontype",r.isArrayOfNums],["poddedupe",r.isArrayOfNums]]);function l(e){const t=e?.mediaTypes?.video;null!=t&&null==t.plcmt&&(t.context===a||[2,3,4].includes(t.placement)?t.plcmt=4:t.context!==a&&[2,6].includes(t.playbackmethod)&&(t.plcmt=2))}function u(e,t){const n=e?.mediaTypes?.video;(0,r.isPlainObject)(n)?null!=n&&Object.entries(n).forEach((i=>{let[o,s]=i;if(!c.has(o))return;c.get(o)(s)||("function"==typeof t?t(o,s,e):(delete n[o],(0,r.logWarn)(`Invalid prop in adUnit "${e.code}": Invalid value for mediaTypes.video.${o} ORTB property. The property has been removed.`)))})):(0,r.logWarn)("validateOrtbVideoFields: videoParams must be an object.")}function g(e){let{index:t=s.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=t.getMediaTypes(e)?.video,r=n&&n?.context,i=n&&n?.useCacheKey,o=t.getAdUnit(e);return f(e,o,n,r,i)}const f=(0,o.A_)("sync",(function(e,t,n,o,s){if(n&&(s||o!==a)){const{url:t,useLocal:n}=i.$W.getConfig("cache")||{};return t||n||!e.vastXml||e.vastUrl?!(!e.vastUrl&&!e.vastXml):((0,r.logError)('\n        This bid contains only vastXml and will not work when a prebid cache url is not specified.\n        Try enabling either prebid cache with pbjs.setConfig({ cache: {url: "..."} });\n        or local cache with pbjs.setConfig({ cache: { useLocal: true }});\n      '),!1)}return!(o===a&&!s)||!!(e.renderer||t&&t.renderer||n.renderer)}),"checkVideoBidSetup")},68693:(e,t,n)=>{n.d(t,{M_:()=>g,Sb:()=>f,X5:()=>E,kh:()=>c});var r=n(68044),i=n(43272),o=n(67314),s=n(91069),a=n(81657);const d=15,c=new Map;function l(e){let{index:t=o.n.index}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=u(e),r=t.getAuction(e);let s={type:"xml",value:n,ttlseconds:Number(e.ttl)+d};return i.$W.getConfig("cache.vasttrack")&&(s.bidder=e.bidder,s.bidid=e.requestId,s.aid=e.auctionId),null!=r&&(s.timestamp=r.getAuctionStart()),"string"==typeof e.customCacheKey&&""!==e.customCacheKey&&(s.key=e.customCacheKey),s}function u(e){return e.vastXml?e.vastXml:(t=e.vastUrl,n=e.vastImpUrl,`<VAST version="3.0">\n    <Ad>\n      <Wrapper>\n        <AdSystem>prebid.org wrapper</AdSystem>\n        <VASTAdTagURI><![CDATA[${t}]]></VASTAdTagURI>\n        ${(n=n&&(Array.isArray(n)?n:[n]))?n.map((e=>`<Impression><![CDATA[${e}]]></Impression>`)).join(""):""}\n        <Creatives></Creatives>\n      </Wrapper>\n    </Ad>\n  </VAST>`);var t,n}function g(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r.g4;const o={puts:e.map(l)};n(i.$W.getConfig("cache.timeout"))(i.$W.getConfig("cache.url"),function(e){return{success:function(t){let n;try{n=JSON.parse(t).responses}catch(t){return void e(t,[])}n?e(null,n):e(new Error("The cache server didn't respond with a responses property."),[])},error:function(t,n){e(new Error(`Error storing video ad in the cache: ${t}: ${JSON.stringify(n)}`),[])}}}(t),JSON.stringify(o),{contentType:"text/plain",withCredentials:!0})}const f=e=>{const t=u(e),n=URL.createObjectURL(new Blob([t],{type:"text/xml"}));p(e,n),c.set(e.videoCacheKey,n)},p=(e,t,n)=>{e.videoCacheKey=n||(0,s.generateUUID)(),e.vastUrl||(e.vastUrl=t)},m={store:g};function h(e){const t=e.map((e=>e.bidResponse));m.store(t,(function(n,r){var o;n?(o=n,(0,s.logError)(`Failed to save to the video cache: ${o}. Video bids will be discarded:`,t)):e.length!==r.length?(0,s.logError)(`expected ${e.length} cache IDs, got ${r.length} instead`):r.forEach(((t,n)=>{const{auctionInstance:r,bidResponse:o,afterBidAdded:d}=e[n];var c;""===t.uuid?(0,s.logWarn)("Supplied video cache key was already in use by Prebid Cache; caching attempt was rejected. Video bid must be discarded."):(p(o,(c=t.uuid,`${i.$W.getConfig("cache.url")}?uuid=${c}`),t.uuid),(0,a.v8)(r,o),d())}))}))}let b,y,v;i.$W.getConfig("cache",(e=>{let{cache:t}=e;b="number"==typeof t.batchSize&&t.batchSize>0?t.batchSize:1,y="number"==typeof t.batchTimeout&&t.batchTimeout>0?t.batchTimeout:0,t.useLocal&&!v&&(v=o.n.onExpiry((e=>{e.getBidsReceived().forEach((e=>{const t=c.get(e.videoCacheKey);t&&t.startsWith("blob")&&URL.revokeObjectURL(t),c.delete(e.videoCacheKey)}))})))}));const E=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:setTimeout,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h,n=[[]],r=!1;const i=e=>e();return function(o,s,a){const d=y>0?e:i;n[n.length-1].length>=b&&n.push([]),n[n.length-1].push({auctionInstance:o,bidResponse:s,afterBidAdded:a}),r||(r=!0,d((()=>{n.forEach(t),n=[[]],r=!1}),y))}}()}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[37769],{88944:(t,n,e)=>{e.d(n,{M:()=>o,g:()=>c});var i=e(91069);function c(){try{const t=(0,i.getWindowTop)();let{scrollY:n,scrollX:e}=t;const{height:c,width:h}=o();return{top:n,right:e+h,bottom:n+c,left:e}}catch(t){return{}}}function o(){const t=(0,i.getWinDimensions)();try{const n=t.innerHeight||t.document.documentElement.clientHeight||t.document.body.clientHeight||0;return{width:t.innerWidth||t.document.documentElement.clientWidth||t.document.body.clientWidth||0,height:n}}catch(t){return{}}}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[12139],{30043:(t,e,n)=>{n.d(e,{k:()=>c,w:()=>r});const s=0,l=1;class c{#t;#e;constructor(t){if("function"!=typeof t)throw new Error("resolver not a function");const e=[],n=[];let[c,r]=[s,l].map((t=>function(l){if(t===s&&"function"==typeof l?.then)l.then(c,r);else if(!e.length)for(e.push(t,l);n.length;)n.shift()()}));try{t(c,r)}catch(t){r(t)}this.#t=e,this.#e=n}then(t,e){const n=this.#t;return new this.constructor(((l,c)=>{const r=()=>{let r=n[1],[h,i]=n[0]===s?[t,l]:[e,c];if("function"==typeof h){try{r=h(r)}catch(t){return void c(t)}i=l}i(r)};n.length?r():this.#e.push(r)}))}catch(t){return this.then(null,t)}finally(t){let e;return this.then((n=>(e=n,t())),(n=>(e=this.constructor.reject(n),t()))).then((()=>e))}static#n(t,e,n){let s=t.length;function l(){e.apply(this,arguments),--s<=0&&n&&n()}0===t.length&&n?n():t.forEach(((t,e)=>this.resolve(t).then((t=>l(!0,t,e)),(t=>l(!1,t,e)))))}static race(t){return new this(((e,n)=>{this.#n(t,((t,s)=>t?e(s):n(s)))}))}static all(t){return new this(((e,n)=>{let s=[];this.#n(t,((t,e,l)=>t?s[l]=e:n(e)),(()=>e(s)))}))}static allSettled(t){return new this((e=>{let n=[];this.#n(t,((t,e,s)=>n[s]=t?{status:"fulfilled",value:e}:{status:"rejected",reason:e}),(()=>e(n)))}))}static resolve(t){return new this((e=>e(t)))}static reject(t){return new this(((e,n)=>n(t)))}}function r(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e>0)return setTimeout(t,e);t()}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[51085],{34595:(e,t,n)=>{n.d(t,{G:()=>s});const s='(()=>{"use strict";window.render=function({ad:e,adUrl:t,width:n,height:i,instl:d},{mkFrame:r},s){if(!e&&!t)throw{reason:"noAd",message:"Missing ad markup or URL"};{if(null==i){const e=s.document?.body;[e,e?.parentElement].filter((e=>null!=e?.style)).forEach((e=>e.style.height="100%"))}const h=s.document,o={width:n??"100%",height:i??"100%"};if(t&&!e?o.src=t:o.srcdoc=e,h.body.appendChild(r(h,o)),d&&s.frameElement){const e=s.frameElement.style;e.width=n?`${n}px`:"100vw",e.height=i?`${i}px`:"100vh"}}}})();'}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[44982],{29906:(e,l,s)=>{function t(e,l){let s=[];for(let t=0;t<Math.ceil(e.length/l);t++){let h=t*l,n=h+l;s.push(e.slice(h,n))}return s}s.d(l,{i:()=>t})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[97247],{28656:(r,e,a)=>{a.d(e,{D:()=>o});var n=a(73858),t=a(70433);const s=["user.keywords"].concat(n.Dy.flatMap((r=>["keywords","content.keywords"].map((e=>`${r}.${e}`)))));function o(r){for(var e=arguments.length,a=new Array(e>1?e-1:0),n=1;n<e;n++)a[n-1]=arguments[n];return function(){const r=new Set;for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return a.filter((r=>r)).flatMap((r=>Array.isArray(r)?r:r.split(","))).map((r=>r.replace(/^\s*/,"").replace(/\s*$/,""))).filter((r=>r)).forEach((e=>r.add(e))),Array.from(r.keys())}(...s.map((e=>(0,t.A)(r,e))),...a)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[95444],{554:(e,t,r)=>{r.d(t,{QF:()=>f,T_:()=>g,gg:()=>l});var n=r(91069),o=r(70433),i=r(28656),a=r(73858);const c={526:"1plusX",527:"1plusX",541:"captify_segments",540:"perid"},s=["user.data"].concat(a.Dy.map((e=>`${e}.content.data`)));function d(e,t,r){return null==t?r:(0,n.isStr)(t)?t:(0,n.isNumber)(t)?t.toString():void(0,n.logWarn)("Unsuported type for param: "+e+" required type: String")}function l(e){return(0,n.isStr)(e)&&""!==e?u(e.split(/\s*(?:,)\s*/)):{}}function u(e){const t={};return e.forEach((e=>{if(-1!==e.indexOf("=")){let r=e.split("="),n=r[0],o=r[1];t.hasOwnProperty(n)?t[n].push(o):t[n]=[o]}else t.hasOwnProperty(e)||(t[e]=[])})),t}function g(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"keywords";const r=[];return(0,n._each)(e,((e,o)=>{if((0,n.isArray)(e)){let r=[];(0,n._each)(e,(e=>{((e=d(t+"."+o,e))||""===e)&&r.push(e)})),e=r}else{if(e=d(t+"."+o,e),!(0,n.isStr)(e))return;e=[e]}e=e.filter((e=>""!==e));const i={key:o};e.length>0&&(i.value=e),r.push(i)})),r}((0,n.mergeDeep)(...t.map((e=>Object.fromEntries(Object.entries(e||{}).map((e=>{let[t,r]=e;return[t,(0,n.isNumber)(r)||(0,n.isStr)(r)?[r]:r]})))))))}function f(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return g(function(e){return u((0,i.D)(e))}(e),function(e){let t={};return s.forEach((r=>{((0,o.A)(e,r)||[]).forEach((e=>{const r=c[e?.ext?.segtax];r&&e.segment.forEach((e=>{t[r]?t[r].push(e.id):t[r]=[e.id]}))}))})),t}(e),...r)}},2349:(e,t,r)=>{r.d(t,{DX:()=>i,GS:()=>a,vk:()=>o});var n=r(91069);function o(e){return e.replace(/(?:^|\.?)([A-Z])/g,(function(e,t){return"_"+t.toLowerCase()})).replace(/^_/,"")}const i=[{code:"appnexusAst",gvlid:32},{code:"emxdigital",gvlid:183},{code:"emetriq",gvlid:213},{code:"pagescience",gvlid:32},{code:"gourmetads",gvlid:32},{code:"matomy",gvlid:32},{code:"featureforward",gvlid:32},{code:"oftmedia",gvlid:32},{code:"adasta",gvlid:32},{code:"beintoo",gvlid:618},{code:"projectagora",gvlid:1032},{code:"stailamedia",gvlid:32},{code:"uol",gvlid:32},{code:"adzymic",gvlid:723}];function a(e,t){let r=[];for(let o=0;o<t;o++){let t=(0,n.isPlainObject)(e)?(0,n.deepClone)(e):e;r.push(t)}return r}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[42698],{24673:(A,B,I)=>{I.d(B,{n:()=>s});const s={1:"IAB20-3",2:"IAB18-5",3:"IAB10-1",4:"IAB2-3",5:"IAB19-8",6:"IAB22-1",7:"IAB18-1",8:"IAB12-3",9:"IAB5-1",10:"IAB4-5",11:"IAB13-4",12:"IAB8-7",13:"IAB9-7",14:"IAB7-1",15:"IAB20-18",16:"IAB10-7",17:"IAB19-18",18:"IAB13-6",19:"IAB18-4",20:"IAB1-5",21:"IAB1-6",22:"IAB3-4",23:"IAB19-13",24:"IAB22-2",25:"IAB3-9",26:"IAB17-18",27:"IAB19-6",28:"IAB1-7",29:"IAB9-30",30:"IAB20-7",31:"IAB20-17",32:"IAB7-32",33:"IAB16-5",34:"IAB19-34",35:"IAB11-5",36:"IAB12-3",37:"IAB11-4",38:"IAB12-3",39:"IAB9-30",41:"IAB7-44",42:"IAB7-1",43:"IAB7-30",50:"IAB19-30",51:"IAB17-12",52:"IAB19-30",53:"IAB3-1",55:"IAB13-2",56:"IAB19-30",57:"IAB19-30",58:"IAB7-39",59:"IAB22-1",60:"IAB7-39",61:"IAB21-3",62:"IAB5-1",63:"IAB12-3",64:"IAB20-18",65:"IAB11-2",66:"IAB17-18",67:"IAB9-9",68:"IAB9-5",69:"IAB7-44",71:"IAB22-3",73:"IAB19-30",74:"IAB8-5",78:"IAB22-1",85:"IAB12-2",86:"IAB22-3",87:"IAB11-3",112:"IAB7-32",113:"IAB7-32",114:"IAB7-32",115:"IAB7-32",118:"IAB9-5",119:"IAB9-5",120:"IAB9-5",121:"IAB9-5",122:"IAB9-5",123:"IAB9-5",124:"IAB9-5",125:"IAB9-5",126:"IAB9-5",127:"IAB22-1",132:"IAB1-2",133:"IAB19-30",137:"IAB3-9",138:"IAB19-3",140:"IAB2-3",141:"IAB2-1",142:"IAB2-3",143:"IAB17-13",166:"IAB11-4",175:"IAB3-1",176:"IAB13-4",182:"IAB8-9",183:"IAB3-5"}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[12126],{25761:(e,n,r)=>{r.d(n,{$:()=>t});var s=r(91069);function t(e,n){return Object.keys(e).forEach((r=>{var t,u;n[r]&&((0,s.isFn)(e[r])?n[r]=e[r](n[r]):n[r]=(t=e[r],u=n[r],"string"===t?u&&u.toString():"number"===t?Number(u):u),isNaN(n[r])&&delete n.key)})),n}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[58498],{89766:(e,t,r)=>{r.d(t,{T:()=>l,A:()=>m});const n=new WeakMap;var i=r(91069),s=r(71371);var o=r(63895);var a=r(75561);var d=r(1e3),p=r(73858);const c={[d.S3]:{fpd:{priority:99,fn(e,t){(0,i.mergeDeep)(e,t.ortb2)}},onlyOneClient:{priority:-99,fn:(0,p.i8)("ORTB request")},props:{fn(e,t){Object.assign(e,{id:e.id||(0,i.generateUUID)(),test:e.test||0});const r=parseInt(t.timeout,10);isNaN(r)||(e.tmax=r)}}},[d.Tb]:{fpd:{priority:99,fn(e,t){(0,i.mergeDeep)(e,t.ortb2Imp)}},id:{fn(e,t){e.id=t.bidId}},banner:{fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.D4)return;const n=t?.mediaTypes?.banner;if(n){const r={topframe:!0===(0,i.inIframe)()?0:1};n.sizes&&null==t.ortb2Imp?.banner?.format&&(r.format=(0,i.sizesToSizeTuples)(n.sizes).map(i.sizeTupleToRtbSize)),n.hasOwnProperty("pos")&&(r.pos=n.pos),e.banner=(0,i.mergeDeep)(r,e.banner)}}},pbadslot:{fn(e){const t=e.ext?.data?.pbadslot;t&&"string"==typeof t||delete e.ext?.data?.pbadslot}},secure:{fn(e,t){e.secure=e.secure??1}}},[d.WR]:{mediaType:{priority:99,fn:a.K},banner:{fn:function(){let{createPixel:e=(e=>(0,i.createTrackPixelHtml)(decodeURIComponent(e),i.encodeMacroURI))}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return function(t,r){t.mediaType===s.D4&&(r.adm&&r.nurl?t.ad=e(r.nurl)+r.adm:r.adm?t.ad=r.adm:r.nurl&&(t.adUrl=r.nurl))}}()},props:{fn(e,t,r){Object.entries({requestId:r.bidRequest?.bidId,seatBidId:t.id,cpm:t.price,currency:r.ortbResponse.cur||r.currency,width:t.w,height:t.h,wratio:t.wratio,hratio:t.hratio,dealId:t.dealid,creative_id:t.crid,creativeId:t.crid,burl:t.burl,ttl:t.exp||r.ttl,netRevenue:r.netRevenue}).filter((e=>{let[t,r]=e;return void 0!==r})).forEach((t=>{let[r,n]=t;return e[r]=n})),e.meta||(e.meta={}),t.adomain&&(e.meta.advertiserDomains=t.adomain),t.ext?.dsa&&(e.meta.dsa=t.ext.dsa),t.cat&&(e.meta.primaryCatId=t.cat[0],e.meta.secondaryCatIds=t.cat.slice(1)),t.attr&&(e.meta.attr=t.attr),t.ext?.eventtrackers&&(e.eventtrackers=(e.eventtrackers??[]).concat(t.ext.eventtrackers))}}}};c[d.Tb].native={fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.s6)return;let n=t.nativeOrtbRequest;n&&(n=Object.assign({},r.nativeRequest,n),n.assets?.length?e.native=(0,i.mergeDeep)({},{request:JSON.stringify(n),ver:n.ver},e.native):(0,i.logWarn)("mediaTypes.native is set, but no assets were specified. Native request skipped.",t))}},c[d.WR].native={fn:function(e,t){if(e.mediaType===s.s6){let r;if(r="string"==typeof t.adm?JSON.parse(t.adm):t.adm,!(0,i.isPlainObject)(r)||!Array.isArray(r.assets))throw new Error("ORTB native response contained no assets");e.native={ortb:r}}}},c[d.Tb].video={fn:function(e,t,r){if(r.mediaType&&r.mediaType!==s.G_)return;const n=t?.mediaTypes?.video;if(!(0,i.isEmpty)(n)){const t=Object.fromEntries(Object.entries(n).filter((e=>{let[t]=e;return o.Zy.has(t)})));if(n.playerSize){const e=(0,i.sizesToSizeTuples)(n.playerSize).map(i.sizeTupleToRtbSize);e.length>1&&(0,i.logWarn)("video request specifies more than one playerSize; all but the first will be ignored"),Object.assign(t,e[0])}e.video=(0,i.mergeDeep)(t,e.video)}}},c[d.WR].video={fn:function(e,t,r){e.mediaType===s.G_&&(r?.imp?.video?.w&&r?.imp?.video?.h&&([e.playerWidth,e.playerHeight]=[r.imp.video.w,r.imp.video.h]),t.adm&&(e.vastXml=t.adm),t.nurl&&(e.vastUrl=t.nurl))}};var u=r(99466);function m(){let{context:e={},processors:t=l,overrides:r={},imp:s,request:o,bidResponse:a,response:p}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const c=new WeakMap;function u(e,i,s,o){let a;return function(){return null==a&&(a=function(){let a=s.bind(this,function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!n.has(e)){const t=Object.entries(e);t.sort(((e,t)=>(e=e[1].priority||0)===(t=t[1].priority||0)?0:e>t?-1:1)),n.set(e,t.map((e=>{let[t,r]=e;return[t,r.fn]})))}const r=n.get(e).filter((e=>{let[r]=e;return!t.hasOwnProperty(r)||t[r]})).map((function(e){let[r,n]=e;return t.hasOwnProperty(r)?t[r].bind(this,n):n}));return function(){const e=Array.from(arguments);r.forEach((t=>{t.apply(this,e)}))}}(t()[e]||{},r[e]||{}));return i&&(a=i.bind(this,a)),function(){try{return a.apply(this,arguments)}catch(e){o.call(this,e,...arguments)}}}()),a.apply(this,arguments)}}const m=u(d.Tb,s,(function(e,t,r){const n={};return e(n,t,r),n}),(function(e,t,r){(0,i.logError)("Error while converting bidRequest to ORTB imp; request skipped.",{error:e,bidRequest:t,context:r})})),f=u(d.S3,o,(function(e,t,r,n){const i={imp:t};return e(i,r,n),i}),(function(e,t,r,n){throw(0,i.logError)("Error while converting to ORTB request",{error:e,imps:t,bidderRequest:r,context:n}),e})),b=u(d.WR,a,(function(e,t,r){const n={};return e(n,t,r),n}),(function(e,t,r){(0,i.logError)("Error while converting ORTB seatbid.bid to bidResponse; bid skipped.",{error:e,bid:t,context:r})})),y=u(d.Cf,p,(function(e,t,r,n){const i={bids:t};return e(i,r,n),i}),(function(e,t,r,n){throw(0,i.logError)("Error while converting from ORTB response",{error:e,bidResponses:t,ortbResponse:r,context:n}),e}));return{toORTB(t){let{bidderRequest:r,bidRequests:n,context:s={}}=t;n=n||r.bids;const o={req:Object.assign({bidRequests:n},e,s),imp:{}};o.req.impContext=o.imp;const a=n.map((t=>{const n=Object.assign({bidderRequest:r,reqContext:o.req},e,s),a=m(t,n);if(null!=a){if(a.hasOwnProperty("id"))return Object.assign(n,{bidRequest:t,imp:a}),o.imp[a.id]=n,a;(0,i.logError)("Converted ORTB imp does not specify an id, ignoring bid request",t,a)}})).filter(Boolean),d=f(a,r,o.req);return o.req.bidderRequest=r,null!=d&&c.set(d,o),d},fromORTB(e){let{request:t,response:r}=e;const n=c.get(t);if(null==n)throw new Error("ortbRequest passed to `fromORTB` must be the same object returned by `toORTB`");function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.assign(e,{ortbRequest:t},r)}const o=Object.fromEntries((t.imp||[]).map((e=>[e.id,e]))),a=(r.seatbid||[]).flatMap((e=>(e.bid||[]).map((t=>{if(o.hasOwnProperty(t.impid)&&n.imp.hasOwnProperty(t.impid))return b(t,s(n.imp[t.impid],{imp:o[t.impid],seatbid:e,ortbResponse:r}));(0,i.logError)("ORTB response seatbid[].bid[].impid does not match any imp in request; ignoring bid",t)})))).filter(Boolean);return y(a,r,s(n.req))}}}const l=(0,i.memoize)((()=>(0,u.U)(c,(0,d.yB)(d.qN))))},99466:(e,t,r)=>{r.d(t,{U:()=>i});var n=r(1e3);function i(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const s=t.shift(),o=t.length>1?i(...t):t[0];return Object.fromEntries(n.zt.map((e=>[e,Object.assign({},s[e],o[e])])))}},75561:(e,t,r)=>{r.d(t,{K:()=>s,X:()=>i});var n=r(71371);const i={1:n.D4,2:n.G_,4:n.s6};function s(e,t,r){if(e.mediaType)return;const n=r.mediaType;if(!n&&!i.hasOwnProperty(t.mtype))throw new Error("Cannot determine mediaType for response");e.mediaType=n||i[t.mtype]}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[15889],{54406:(e,r,t)=>{t.d(r,{FE:()=>i});var n=t(70433),s=t(63172),o=t(91069);const u=Object.freeze([...["device.sua","source.schain","regs.gdpr","regs.us_privacy","regs.gpp","regs.gpp_sid","user.consent","user.eids"].map((e=>function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e,r)=>`${e}.ext.${r}`;const[t,o]=function(e){const r=e.split(".");return[r.slice(0,r.length-1).join("."),r[r.length-1]]}(e);return r=r(t,o),e=>{const u=(0,n.A)(e,t);if(null!=u?.[o])return(0,s.J)(e,r,u[o]),()=>delete u[o]}}(e))),...["app","content","site","user"].map((function(e){return r=>{const t=r[e]?.kwarray;if(null!=t){let n=(r[e].keywords||"").split(",");return Array.isArray(t)&&n.push(...t),r[e].keywords=n.join(","),()=>delete r[e].kwarray}}}))]);function i(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return function(t){return r.forEach((r=>{try{const n=r(t);"function"==typeof n&&e&&n()}catch(e){(0,o.logError)("Error translating request to ORTB 2.5",e)}})),t}}i()}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[33005],{51252:(t,n,o)=>{o.d(n,{Cn:()=>s,eu:()=>w,ho:()=>c,mw:()=>i,n9:()=>l,p:()=>u,ph:()=>a});var e=o(73858),g=o(91069),d=o(70433);function i(t){return n=>(0,g.compareCodeAndSlot)(n,t)}function a(t,n){if(!t||"string"!=typeof t)return!1;window.googletag=window.googletag||{cmd:[]},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push((()=>{window.googletag.pubads().setTargeting(t,n)}))}function l(t){let n;return(0,g.isGptPubadsDefined)()&&(n=window.googletag.pubads().getSlots().find(i(t))),n}function u(t){const n=l(t);return n?{gptSlot:n.getAdUnitPath(),divId:n.getSlotElementId()}:{}}const s=["IAB_AUDIENCE_1_1","IAB_CONTENT_2_2"];function w(t){return Object.entries({[s[0]]:r(t,["user.data"],4),[s[1]]:r(t,e.Dy.map((t=>`${t}.content.data`)),6)}).map((t=>{let[n,o]=t;return o.length?{taxonomy:n,values:o}:null})).filter((t=>t))}function r(t,n,o){return n.flatMap((n=>(0,d.A)(t,n)||[])).filter((t=>t.ext?.segtax===o)).flatMap((t=>t.segment?.map((t=>t.id)))).filter((t=>t)).filter(g.uniques)}function c(t){!function(t,n){const o=()=>window.googletag.pubads().addEventListener(t,n);(0,g.isGptPubadsDefined)()?o():(window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push(o))}("slotRenderEnded",t)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[47650],{25246:(t,n,e)=>{e.d(n,{Mu:()=>o,P8:()=>p,QS:()=>d});var s=e(16916);const o={env:"vp",gdfp_req:1,output:"vast",unviewed_position_start:1},p={protocol:"https",host:"securepubads.g.doubleclick.net",pathname:"/gampad/ads"};function d(){const t=s.mW.getConsentData(),n={};return t&&("boolean"==typeof t.gdprApplies&&(n.gdpr=Number(t.gdprApplies)),t.consentString&&(n.gdpr_consent=t.consentString),t.addtlConsent&&(n.addtl_consent=t.addtlConsent)),n}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[41],{74420:(e,n,r)=>{r.d(n,{A:()=>s});const i="application/xml";function s(){let e,n;return{parse:function(n){return(e||(e=new DOMParser),e).parseFromString(n,i)},serialize:function(e){return(n||(n=new XMLSerializer),n).serializeToString(e)}}}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[47618],{20981:(e,t,i)=>{i.d(t,{m:()=>m});var r=i(99466),d=i(1e3),n=i(70433),s=i(91069),a=i(25825),b=i(11445),o=i(43272),p=i(63172);var c=i(43323);var l=i(7873);var f=i(71371);var u=i(33005);const v={[d.S3]:{extPrebid:{fn:function(e,t){(0,p.J)(e,"ext.prebid",(0,s.mergeDeep)({auctiontimestamp:t.auctionStart,targeting:{includewinners:!0,includebidderkeys:!1}},e.ext?.prebid)),o.$W.getConfig("debug")&&(e.ext.prebid.debug=!0)}},extPrebidChannel:{fn:function(e){(0,p.J)(e,"ext.prebid.channel",Object.assign({name:"pbjs",version:(0,l.m)().version},e.ext?.prebid?.channel))}},extPrebidAliases:{fn:function(e,t,i){let{am:r=b.Ay}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(r.aliasRegistry[t.bidderCode]){const i=r.bidderRegistry[t.bidderCode];if(!i||!i.getSpec().skipPbsAliasing){(0,p.J)(e,`ext.prebid.aliases.${t.bidderCode}`,r.aliasRegistry[t.bidderCode]);const d=o.$W.getConfig(`gvlMapping.${t.bidderCode}`)||i?.getSpec?.().gvlid;d&&(0,p.J)(e,`ext.prebid.aliasgvlids.${t.bidderCode}`,d)}}}}},[d.Tb]:{params:{fn:c.W},adUnitCode:{fn:function(e,t){const i=t.adUnitCode;i&&(0,p.J)(e,"ext.prebid.adunitcode",i)}}},[d.WR]:{mediaType:{fn:a.o,priority:99},videoCache:{fn:function(e,t){if(e.mediaType===f.G_){let{cacheId:i,url:r}=(0,n.A)(t,"ext.prebid.cache.vastXml")||{};if(!i||!r){const{hb_uuid:e,hb_cache_host:d,hb_cache_path:s}=(0,n.A)(t,"ext.prebid.targeting")||{};e&&d&&s&&(i=e,r=`https://${d}${s}?uuid=${e}`)}i&&r&&Object.assign(e,{videoCacheKey:i,vastUrl:r})}},priority:-10},bidderCode:{fn(e,t,i){e.bidderCode=i.seatbid.seat,e.adapterCode=(0,n.A)(t,"ext.prebid.meta.adaptercode")||i.bidRequest?.bidder||e.bidderCode}},pbsBidId:{fn(e,t){const i=(0,n.A)(t,"ext.prebid.bidid");(0,s.isStr)(i)&&(e.pbsBidId=i)}},adserverTargeting:{fn(e,t){const i=(0,n.A)(t,"ext.prebid.targeting");(0,s.isPlainObject)(i)&&(e.adserverTargeting=i)}},extPrebidMeta:{fn(e,t){e.meta=(0,s.mergeDeep)({},(0,n.A)(t,"ext.prebid.meta"),e.meta)}},pbsWinTrackers:{fn:function(e,t){e.eventtrackers=e.eventtrackers||[],[[t.burl,u.OA],[t?.ext?.prebid?.events?.win,u.RO]].filter((t=>{let[i,r]=t;return i&&null==e.eventtrackers.find((e=>{let{method:t,event:d,url:n}=e;return d===r&&t===u.Ni&&n===i}))})).forEach((t=>{let[i,r]=t;e.eventtrackers.push({method:u.Ni,event:r,url:i})}))}}},[d.Cf]:{serverSideStats:{fn(e,t,i){Object.entries({errors:"serverErrors",responsetimemillis:"serverResponseTimeMs"}).forEach((e=>{let[r,d]=e;const s=(0,n.A)(t,`ext.${r}.${i.bidderRequest.bidderCode}`);s&&(i.bidderRequest[d]=s,i.bidRequests.forEach((e=>e[d]=s)))}))}}}};var g=i(89766);const m=(0,s.memoize)((()=>(0,r.U)((0,g.T)(),v,(0,d.yB)(d.e4))))},25825:(e,t,i)=>{i.d(t,{o:()=>s,s:()=>n});var r=i(71371),d=i(75561);const n={[r.D4]:"banner",[r.s6]:"native",[r.G_]:"video"};function s(e,t,i){let s=i.mediaType;s||(s=d.X.hasOwnProperty(t.mtype)?d.X[t.mtype]:t.ext?.prebid?.type,n.hasOwnProperty(s)||(s=r.D4)),e.mediaType=s}},43323:(e,t,i)=>{i.d(t,{W:()=>d});var r=i(63172);function d(e,t){let i=t.params;i&&(0,r.J)(e,`ext.prebid.bidder.${t.bidder}`,i)}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[46550],{8702:(p,n,e)=>{function t(p,n,e){let t={};return p&&("boolean"==typeof p.gdprApplies&&(t.gdpr=Number(p.gdprApplies)),"string"==typeof p.consentString&&(t.gdpr_consent=p.consentString)),n&&(t.us_privacy=encodeURIComponent(n)),e?.gppString&&(t.gpp=e.gppString,t.gpp_sid=e.applicableSections?.toString()),t}e.d(n,{d:()=>t})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[32316],{34278:(e,n,t)=>{t.d(n,{G:()=>l});var s=t(77332);const u=new Map;function l(e){let n;return u.has(e)?n=u.get(e):(n=e.getBoundingClientRect(),u.set(e,n)),n}s.gH.before(((e,n)=>{u.clear(),e(n)}))}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[19147],{29495:(e,r,n)=>{function u(e){return e?.ortb2?.ext?.prebid?.adServerCurrency}n.d(r,{b:()=>u})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[90467],{6036:(e,s,n)=>{n.d(s,{M:()=>a});var o=n(71371),r=n(91069),i=n(70433);const t=0;function a(e,s,n){const a=[];return(0,r.isFn)(e.getFloor)&&((0,i.A)(e,`mediaTypes.${n}.${n===o.G_?"playerSize":"sizes"}`)||[]).forEach((o=>{const r=e.getFloor({currency:s||"USD",mediaType:n,size:o}).floor;a.push(isNaN(r)?t:r)})),a.length?Math.min(...a):t}}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[26762],{74538:(t,n,o)=>{function e(){let t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;try{t=n.top.history.length}catch(n){t=void 0}return t}function i(){let t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;try{t=n.top.navigator.hardwareConcurrency}catch(n){t=void 0}return t}function r(){let t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;try{t=n.top.navigator.deviceMemory}catch(n){t=void 0}return t}o.d(n,{GA:()=>e,iz:()=>i,qM:()=>r})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[42809],{72285:(A,t,e)=>{e.d(t,{T:()=>n});let a=null;const n=()=>!1!==a;!function(){const A=navigator.userAgent.match(/iPhone OS (\d+)_(\d+)/);if(null!==A&&parseInt(A[1])<17&&!navigator.userAgent.includes("Safari"))return;const t=document.createElement("video");t.src="data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAADxtZGF0AAAAMGWIhAAV//73ye/Apuvb3rW/k89I/Cy3PsIqP39atohOSV14BYa1heKCYgALQC5K4QAAAwZtb292AAAAbG12aGQAAAAAAAAAAAAAAAAAAAPoAAAD6AABAAABAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAACMHRyYWsAAABcdGtoZAAAAAMAAAAAAAAAAAAAAAEAAAAAAAAD6AAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAEAAAAAAoAAAAFoAAAAAACRlZHRzAAAAHGVsc3QAAAAAAAAAAQAAA+gAAAAAAAEAAAAAAahtZGlhAAAAIG1kaGQAAAAAAAAAAAAAAAAAAEAAAABAAFXEAAAAAAAtaGRscgAAAAAAAAAAdmlkZQAAAAAAAAAAAAAAAFZpZGVvSGFuZGxlcgAAAAFTbWluZgAAABR2bWhkAAAAAQAAAAAAAAAAAAAAJGRpbmYAAAAcZHJlZgAAAAAAAAABAAAADHVybCAAAAABAAABE3N0YmwAAACvc3RzZAAAAAAAAAABAAAAn2F2YzEAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAoABaAEgAAABIAAAAAAAAAAEVTGF2YzYwLjMxLjEwMiBsaWJ4MjY0AAAAAAAAAAAAAAAY//8AAAA1YXZjQwFkAAr/4QAYZ2QACqzZQo35IQAAAwABAAADAAIPEiWWAQAGaOvjyyLA/fj4AAAAABRidHJ0AAAAAAAAAaAAAAGgAAAAGHN0dHMAAAAAAAAAAQAAAAEAAEAAAAAAHHN0c2MAAAAAAAAAAQAAAAEAAAABAAAAAQAAABRzdHN6AAAAAAAAADQAAAABAAAAFHN0Y28AAAAAAAAAAQAAADAAAABidWR0YQAAAFptZXRhAAAAAAAAACFoZGxyAAAAAAAAAABtZGlyYXBwbAAAAAAAAAAAAAAAAC1pbHN0AAAAJal0b28AAAAdZGF0YQAAAAEAAAAATGF2ZjYwLjE2LjEwMA==",t.setAttribute("playsinline","true"),t.muted=!0,t.play().then((()=>{a=!0,t.src=""})).catch((A=>{A instanceof DOMException&&"NotSupportedError"===A.name||(a=!1)}))}()}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[47639],{4122:(t,e,n)=>{function r(t){const e=t.performance||t.webkitPerformance||t.msPerformance||t.mozPerformance,n=e&&"function"==typeof e.getEntriesByType&&"[object Function]"===Object.prototype.toString.call(e.getEntriesByType)&&e.getEntriesByType("navigation")[0]&&e.getEntriesByType("navigation")[0].responseStart&&e.getEntriesByType("navigation")[0].requestStart&&e.getEntriesByType("navigation")[0].responseStart>0&&e.getEntriesByType("navigation")[0].requestStart>0&&Math.round(e.getEntriesByType("navigation")[0].responseStart-e.getEntriesByType("navigation")[0].requestStart);if(n)return n.toString();const r=e&&e.timing.responseStart&&e.timing.requestStart&&e.timing.responseStart>0&&e.timing.requestStart>0&&e.timing.responseStart-e.timing.requestStart;return r?r.toString():""}n.d(e,{v:()=>r})}}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[60041],{63399:(e,i,t)=>{var n=t(7873),a=t(91069),d=t(70433),o=t(81657),r=t(77332),u=t(63895),c=t(16833),l=t(68693),s=t(43272),g=t(71371),f=t(67314),h=t(78969);const p="hb_pb_cat_dur",v="hb_cache_id";let m=50,b=5,T=function(){let e={};function i(i){e[i]={},e[i].bidStorage=new Set,e[i].queueDispatcher=function(e){let i,t=1;return function(n,a,d,o){const r=this;var u=function(){y.call(r,n,a,d)};clearTimeout(i),o?t=1:t===b?(t=1,u()):(t++,i=setTimeout(u,e))}}(m),e[i].initialCacheKey=(0,a.generateUUID)()}return{addBid:function(t){e[t.auctionId]||i(t.auctionId),e[t.auctionId].bidStorage.add(t)},removeBid:function(i){e[i.auctionId].bidStorage.delete(i)},getBids:function(i){return e[i.auctionId]&&e[i.auctionId].bidStorage.values()},getQueueDispatcher:function(i){return e[i.auctionId]&&e[i.auctionId].queueDispatcher},setupInitialCacheKey:function(i){e[i.auctionId]||(e[i.auctionId]={},e[i.auctionId].initialCacheKey=(0,a.generateUUID)())},getInitialCacheKey:function(i){return e[i.auctionId]&&e[i.auctionId].initialCacheKey}}}();function C(e,i){let t=T.getInitialCacheKey(e),n=(0,d.A)(e,"video.durationBucket");const a=function(e){let i;if(s.$W.getConfig("adpod.prioritizeDeals")&&(0,d.A)(e,"video.dealTier")){const t=s.$W.getConfig(`adpod.dealTier.${e.bidderCode}.prefix`);i=t?t+(0,d.A)(e,"video.dealTier"):(0,d.A)(e,"video.dealTier")}else{const t=(0,o.mO)(e);i=(0,o.ZV)(t)(e)}return i}(e);let r;if(i){r=`${a}_${(0,d.A)(e,"meta.adServerCatId")}_${n}s`}else r=`${a}_${n}s`;e.adserverTargeting||(e.adserverTargeting={}),e.adserverTargeting[p]=r,e.adserverTargeting[v]=t,e.videoCacheKey=t,e.customCacheKey=`${r}_${t}`}function y(e,i,t){!function(e){for(let i=0;i<e.length;i++)T.removeBid(e[i])}(i),(0,l.M_)(i,(function(n,d){if(n)(0,a.logWarn)(`Failed to save to the video cache: ${n}. Video bid(s) must be discarded.`);else for(let n=0;n<d.length;n++)""!==d[n].uuid?(0,o.v8)(e,i[n]):(0,a.logInfo)(`Detected a bid was not cached because the custom key was already registered.  Attempted to use key: ${i[n].customCacheKey}. Bid was: `,i[n]),t()}))}function S(e,i,t,n,r){if(r&&r.context===g.LM){let e=s.$W.getConfig("adpod.brandCategoryExclusion");!(0,d.A)(t,"meta.adServerCatId")&&e?((0,a.logWarn)("Detected a bid without meta.adServerCatId while setConfig({adpod.brandCategoryExclusion}) was enabled.  This bid has been rejected:",t),n()):!1===s.$W.getConfig("adpod.deferCaching")?(T.addBid(t),C(t,e),function(e,i,t){let n=T.getBids(i);if(n){let a=Array.from(n),d=T.getQueueDispatcher(i),r=!(e.getAuctionStatus()===o.AA);d(e,a,t,r)}else(0,a.logWarn)("Attempted to cache a bid from an unknown auction. Bid:",i)}(i,t,n)):(T.setupInitialCacheKey(t),C(t,e),(0,o.v8)(i,t),n())}else e.call(this,i,t,n,r)}function A(e,i){let t=i.filter((e=>{let i=(0,d.A)(e,"mediaTypes"),t=(0,d.A)(i,"video");if(t&&t.context===g.LM){if(Object.keys(i).length>1)return(0,a.logWarn)(`Detected more than one mediaType in adUnitCode: ${e.code} while attempting to define an 'adpod' video adUnit.  'adpod' adUnits cannot be mixed with other mediaTypes.  This adUnit will be removed from the auction.`),!1;let n=`Detected missing or incorrectly setup fields for an adpod adUnit.  Please review the following fields of adUnitCode: ${e.code}.  This adUnit will be removed from the auction.`,d=!!(t.playerSize&&((0,a.isArrayOfNums)(t.playerSize,2)||(0,a.isArray)(t.playerSize)&&t.playerSize.every((e=>(0,a.isArrayOfNums)(e,2))))||t.sizeConfig),o=!!(t.adPodDurationSec&&(0,a.isNumber)(t.adPodDurationSec)&&t.adPodDurationSec>0),r=!!(t.durationRangeSec&&(0,a.isArrayOfNums)(t.durationRangeSec)&&t.durationRangeSec.every((e=>e>0)));if(!d||!o||!r)return n+=d?"":"\nmediaTypes.video.playerSize",n+=o?"":"\nmediaTypes.video.adPodDurationSec",n+=r?"":"\nmediaTypes.video.durationRangeSec",(0,a.logWarn)(n),!1}return!0}));i=t,e.call(this,i)}function D(e,i,t,n,o){if(o===g.LM){let t=!0;if(s.$W.getConfig("adpod.brandCategoryExclusion")&&!(0,d.A)(i,"meta.primaryCatId")&&(t=!1),(0,d.A)(i,"video"))if((0,d.A)(i,"video.context")&&i.video.context===g.LM||(t=!1),!(0,d.A)(i,"video.durationSeconds")||i.video.durationSeconds<=0)t=!1;else{let e=function(e,i){let t=(0,d.A)(i,"video.durationSeconds"),n=e.durationRangeSec;if(n.sort(((e,i)=>e-i)),e.requireExactDuration){if(!(n||[]).find((e=>e===t)))return(0,a.logWarn)("Detected a bid with a duration value not part of the list of accepted ranges specified in adUnit.mediaTypes.video.durationRangeSec.  Exact match durations must be used for this adUnit. Rejecting bid: ",i),!1;i.video.durationBucket=t}else{let e=Math.max(...n);if(!(t<=e+2))return(0,a.logWarn)("Detected a bid with a duration value outside the accepted ranges specified in adUnit.mediaTypes.video.durationRangeSec.  Rejecting bid: ",i),!1;{let e=(n||[]).find((e=>e+2>=t));i.video.durationBucket=e}}return!0}(n,i);e||(t=!1)}s.$W.getConfig("cache.url")||!i.vastXml||i.vastUrl||((0,a.logError)('\n        This bid contains only vastXml and will not work when a prebid cache url is not specified.\n        Try enabling prebid cache with pbjs.setConfig({ cache: {url: "..."} });\n      '),t=!1),e.bail(t)}else e.call(this,i,t,n,o)}function E(e,i){return e.adserverTargeting[h.xS.PRICE_BUCKET]/e.video.durationBucket<i.adserverTargeting[h.xS.PRICE_BUCKET]/i.video.durationBucket?1:e.adserverTargeting[h.xS.PRICE_BUCKET]/e.video.durationBucket>i.adserverTargeting[h.xS.PRICE_BUCKET]/i.video.durationBucket?-1:0}s.$W.getConfig("adpod",(e=>function(e){void 0!==e.bidQueueTimeDelay&&("number"==typeof e.bidQueueTimeDelay&&e.bidQueueTimeDelay>0?m=e.bidQueueTimeDelay:(0,a.logWarn)(`Detected invalid value for adpod.bidQueueTimeDelay in setConfig; must be a positive number.  Using default: ${m}`)),void 0!==e.bidQueueSizeLimit&&("number"==typeof e.bidQueueSizeLimit&&e.bidQueueSizeLimit>0?b=e.bidQueueSizeLimit:(0,a.logWarn)(`Detected invalid value for adpod.bidQueueSizeLimit in setConfig; must be a positive number.  Using default: ${b}`))}(e.adpod))),(0,c.pT)((0,c.Yn)("callPrebidCache"),S),(0,c.pT)(r.Z,A),(0,c.pT)(u.E2,D);const I={TARGETING_KEY_PB_CAT_DUR:p,TARGETING_KEY_CACHE_ID:v,getTargeting:function(){let{codes:e,callback:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!i)return void(0,a.logError)("No callback function was defined in the getTargeting call.  Aborting getTargeting().");e=e||[];const t=function(e){return f.n.getAdUnits().filter((e=>(0,d.A)(e,"mediaTypes.video.context")===g.LM)).filter((i=>!(e.length>0)||-1!=e.indexOf(i.code)))}(e),n=f.n.getBidsReceived(),o=s.$W.getConfig("adpod.brandCategoryExclusion"),r=s.$W.getConfig("adpod.deferCaching"),u="boolean"!=typeof r||r;let c=function(e,i){let t=i.map((e=>e.code));return e.filter((e=>-1!=t.indexOf(e.adUnitCode)&&e.video&&e.video.context===g.LM))}(n,t);if(c=o||u?function(e){let i=e.map((e=>Object.assign({},e,{[p]:e.adserverTargeting[p]})));i=(0,a.groupBy)(i,p);let t=[];return Object.keys(i).forEach((e=>{var n;i[e].sort((n="responseTimestamp",function(e,i){return e[n]<i[n]?1:e[n]>i[n]?-1:0})),t.push(i[e][0])})),t}(c):c,s.$W.getConfig("adpod.prioritizeDeals")){let[e,i]=c.reduce(((e,i)=>{let t=(0,d.A)(i,"video.dealTier"),n=s.$W.getConfig(`adpod.dealTier.${i.bidderCode}.minDealTier`);return n&&t?t>=n?e[1].push(i):e[0].push(i):t?e[1].push(i):e[0].push(i),e}),[[],[]]);i.sort(E),e.sort(E),c=i.concat(e)}else c.sort(E);let h={};if(!1===u)t.forEach((e=>{let i=[],t=(0,d.A)(e,"mediaTypes.video.adPodDurationSec");c.filter((i=>i.adUnitCode===e.code)).forEach(((e,n,a)=>{e.video.durationBucket<=t&&(i.push({[p]:e.adserverTargeting[p]}),t-=e.video.durationBucket),n===a.length-1&&i.length>0&&i.push({[v]:e.adserverTargeting[v]})})),h[e.code]=i})),i(null,h);else{let e=[];t.forEach((i=>{let t=(0,d.A)(i,"mediaTypes.video.adPodDurationSec");c.filter((e=>e.adUnitCode===i.code)).forEach((i=>{i.video.durationBucket<=t&&(e.push(i),t-=i.video.durationBucket)}))})),function(e,i){(0,l.M_)(e,(function(t,n){if(t)i(t,null);else{let t=[];for(let i=0;i<n.length;i++)""!==n[i]&&t.push(e[i]);i(null,t)}}))}(e,(function(e,t){if(e)i(e,null);else{let e=(0,a.groupBy)(t,"adUnitCode");Object.keys(e).forEach((i=>{let t=[];e[i].forEach(((e,i,n)=>{t.push({[p]:e.adserverTargeting[p]}),i===n.length-1&&t.length>0&&t.push({[v]:e.adserverTargeting[v]})})),h[i]=t})),i(null,h)}}))}return h}};Object.freeze(I),(0,c.xG)("adpod",(function(){(0,a.isPlainObject)(arguments.length<=0?void 0:arguments[0])?function(e,i){for(let t in i)e[t]=i[t]}(arguments.length<=0?void 0:arguments[0],I):(0,a.logError)("Adpod module needs plain object to share methods with submodule")})),(0,n.E)("adpod")}},e=>{e.O(0,[60802,37769,12139,51085],(()=>{return i=63399,e(e.s=i);var i}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[44599],{50791:(e,t,r)=>{r.d(t,{I:()=>P});var i=r(7873),a=r(91069),s=r(70433),n=r(95789),o=r(43272),d=r(57377),c=r(71371),p=r(63895),l=r(12938),u=r(12693),m=r(82621),g=r(12449),h=r(24673),_=r(554),f=r(2349),b=r(25761),y=r(29906);const v="appnexus",k="https://ib.adnxs.com/ut/v3/prebid",w="https://ib.adnxs-simple.com/ut/v3/prebid",x=["id","minduration","maxduration","skippable","playback_method","frameworks","context","skipoffset"],I=["minduration","maxduration","skip","skipafter","playbackmethod","api","startdelay","placement","plcmt"],C=["age","externalUid","external_uid","segments","gender","dnt","language"],A=["geo","device_id"],S=["enabled","dongle","member_id","debug_timeout"],T={apn_debug_dongle:"dongle",apn_debug_member_id:"member_id",apn_debug_timeout:"debug_timeout"},U={playback_method:{unknown:0,auto_play_sound_on:1,auto_play_sound_off:2,click_to_play:3,mouse_over:4,auto_play_sound_unknown:5},context:{unknown:0,pre_roll:1,mid_roll:2,post_roll:3,outstream:4,"in-banner":5,"in-feed":6,interstitial:7,accompanying_content_pre_roll:8,accompanying_content_mid_roll:9,accompanying_content_post_roll:10}},E={body:"description",body2:"desc2",cta:"ctatext",image:{serverName:"main_image",requiredParams:{required:!0}},icon:{serverName:"icon",requiredParams:{required:!0}},sponsoredBy:"sponsored_by",privacyLink:"privacy_link",salePrice:"saleprice",displayUrl:"displayurl"},O="<script",j=/\/\/cdn\.adnxs\.com\/v|\/\/cdn\.adnxs\-simple\.com\/v/,N="trk.js",D=(0,l.vM)({bidderCode:v}),R=new Map([[1,"Mobile/Tablet - General"],[2,"Personal Computer"],[3,"Connected TV"],[4,"Phone"],[5,"Tablet"],[6,"Connected Device"],[7,"Set Top Box"],[8,"OOH Device"]]),P={code:v,gvlid:32,aliases:f.DX,supportedMediaTypes:[c.D4,c.G_,c.s6],isBidRequestValid:function(e){return!!(e.params.placementId||e.params.placement_id||e.params.member&&(e.params.invCode||e.params.inv_code))},buildRequests:function(e,t){const r=(e=(0,g.Xj)(e)).map(q),i=(e||[]).find(z);let s={};!0===o.$W.getConfig("coppa")&&(s={coppa:!0}),i&&Object.keys(i.params.user).filter((e=>C.includes(e))).forEach((e=>{let t=(0,f.vk)(e);if("segments"===e&&(0,a.isArray)(i.params.user[e])){let r=[];i.params.user[e].forEach((e=>{(0,a.isNumber)(e)?r.push({id:e}):(0,a.isPlainObject)(e)&&r.push(e)})),s[t]=r}else"segments"!==e&&(s[t]=i.params.user[e])}));const n=(e||[]).find($);let d;n&&n.params&&n.params.app&&(d={},Object.keys(n.params.app).filter((e=>A.includes(e))).forEach((e=>d[e]=n.params.app[e])));const c=(e||[]).find(G);let p;c&&c.params&&n.params.app&&n.params.app.id&&(p={appid:c.params.app.id});let l={},u={};const h=D.getCookie("apn_prebid_debug")||null;if(h)try{l=JSON.parse(h)}catch(e){(0,a.logError)("AppNexus Debug Auction Cookie Error:\n\n"+e)}else{Object.keys(T).forEach((e=>{let t=(0,a.getParameterByName)(e);(0,a.isStr)(t)&&""!==t&&(l[T[e]]=t,l.enabled=!0)})),l=(0,b.$)({member_id:"number",debug_timeout:"number"},l);const t=(e||[]).find(L);t&&t.debug&&(l=t.debug)}l&&l.enabled&&Object.keys(l).filter((e=>S.includes(e))).forEach((e=>{u[e]=l[e]}));const v=(e||[]).find(B),x=v?parseInt(v.params.member,10):0,I=e[0].schain,U=(e||[]).find(H),E={tags:[...r],user:s,sdk:{source:"pbjs",version:"9.50.0"},schain:I};U&&(E.iab_support={omidpn:"Appnexus",omidpv:"9.50.0"}),x>0&&(E.member_id=x),n&&(E.device=d),c&&(E.app=p),t?.ortb2?.device&&(E.device=E.device||{},(0,a.mergeDeep)(E.device,function(e){const t={useragent:e.ua,devicetype:R.get(e.devicetype),make:e.make,model:e.model,os:e.os,os_version:e.osv,w:e.w,h:e.h,ppi:e.ppi,pxratio:e.pxratio};return Object.keys(t).reduce(((e,r)=>(t[r]&&(e[r]=t[r]),e)),{})}(t.ortb2.device)));let O=(0,a.deepClone)(t&&t.ortb2),j=(0,a.deepClone)(o.$W.getConfig("appnexusAuctionKeywords"))||{},N=(0,_.QF)(O,j);if(N.length>0&&(E.keywords=N),o.$W.getConfig("adpod.brandCategoryExclusion")&&(E.brand_category_uniqueness=!0),u.enabled&&(E.debug=u,(0,a.logInfo)("AppNexus Debug Auction Settings:\n\n"+JSON.stringify(u,null,4))),t&&t.gdprConsent&&(E.gdpr_consent={consent_string:t.gdprConsent.consentString,consent_required:t.gdprConsent.gdprApplies},t.gdprConsent.addtlConsent&&-1!==t.gdprConsent.addtlConsent.indexOf("~"))){let e=t.gdprConsent.addtlConsent,r=e.substring(e.indexOf("~")+1);E.gdpr_consent.addtl_consent=r.split(".").map((e=>parseInt(e,10)))}if(t&&t.uspConsent&&(E.us_privacy=t.uspConsent),t?.gppConsent?E.privacy={gpp:t.gppConsent.gppString,gpp_sid:t.gppConsent.applicableSections}:t?.ortb2?.regs?.gpp&&(E.privacy={gpp:t.ortb2.regs.gpp,gpp_sid:t.ortb2.regs.gpp_sid}),t&&t.refererInfo){let e={rd_ref:encodeURIComponent(t.refererInfo.topmostLocation),rd_top:t.refererInfo.reachedTop,rd_ifs:t.refererInfo.numIframes,rd_stk:t.refererInfo.stack.map((e=>encodeURIComponent(e))).join(",")},r=t.refererInfo.canonicalUrl;(0,a.isStr)(r)&&""!==r&&(e.rd_can=r),E.referrer_detection=e}(e||[]).find(W)&&e.filter(W).forEach((e=>{const t=function(e,t){const{durationRangeSec:r,requireExactDuration:i}=t.mediaTypes.video,a=function(e){const{adPodDurationSec:t,durationRangeSec:r,requireExactDuration:i}=e,a=Math.min(...r),s=Math.floor(t/a);return i?Math.max(s,r.length):s}(t.mediaTypes.video),s=Math.max(...r),n=e.filter((e=>e.uuid===t.bidId));let o=(0,f.GS)(...n,a);if(i){const e=Math.ceil(a/r.length),t=(0,y.i)(o,e);r.forEach(((e,r)=>{t[r].map((t=>{V(t,"minduration",e),V(t,"maxduration",e)}))}))}else o.map((e=>V(e,"maxduration",s)));return o}(r,e),i=E.tags.filter((t=>t.uuid!==e.bidId));E.tags=[...i,...t]}));if(e[0].userIdAsEids?.length>0){let t=[];e[0].userIdAsEids.forEach((e=>{!e||!e.uids||e.uids.length<1||e.uids.forEach((r=>{let i={source:e.source,id:r.id};"adserver.org"==e.source?i.rti_partner="TDID":"uidapi.com"==e.source&&(i.rti_partner="UID2"),t.push(i)}))})),t.length&&(E.eids=t)}if(t?.ortb2?.regs?.ext?.dsa){const e=t.ortb2.regs.ext.dsa,r={};if(["dsarequired","pubrender","datatopub"].forEach((t=>{(0,a.isNumber)(e[t])&&(r[t]=e[t])})),(0,a.isArray)(e.transparency)&&e.transparency.every((e=>(0,a.isPlainObject)(e)))){const t=[];e.transparency.forEach((e=>{(0,a.isStr)(e.domain)&&""!=e.domain&&(0,a.isArray)(e.dsaparams)&&e.dsaparams.every((e=>(0,a.isNumber)(e)))&&t.push(e)})),t.length>0&&(r.transparency=t)}(0,a.isEmpty)(r)||(E.dsa=r)}r[0].publisher_id&&(E.publisher_id=r[0].publisher_id);const P=function(e,t){let r=[],i={withCredentials:!0},s=k;(0,m.C)(t?.gdprConsent)||(s=w);"TRUE"!==(0,a.getParameterByName)("apn_test").toUpperCase()&&!0!==o.$W.getConfig("apn_test")||(i.customHeaders={"X-Is-Test":1});if(e.tags.length>15){const n=(0,a.deepClone)(e);(0,y.i)(e.tags,15).forEach((e=>{n.tags=e;const a=JSON.stringify(n);r.push({method:"POST",url:s,data:a,bidderRequest:t,options:i})}))}else{const a=JSON.stringify(e);r={method:"POST",url:s,data:a,bidderRequest:t,options:i}}return r}(E,t);return P},interpretResponse:function(e,t){let{bidderRequest:r}=t;e=e.body;const i=[];if(!e||e.error){let t=`in response for ${r.bidderCode} adapter`;return e&&e.error&&(t+=`: ${e.error}`),(0,a.logError)(t),i}if(e.tags&&e.tags.forEach((e=>{const t=(o=e)&&o.ads&&o.ads.length&&(o.ads||[]).find((e=>e.rtb));var o;if(t){if((!0===u.u.get(r.bidderCode,"allowZeroCpmBids")?t.cpm>=0:t.cpm>0)&&this.supportedMediaTypes.includes(t.ad_type)){const o=function(e,t,r){const i=(0,a.getBidRequest)(e.uuid,[r]),o=(0,a.getUniqueIdentifierStr)(),d={adId:o,requestId:e.uuid,cpm:t.cpm,creativeId:t.creative_id,dealId:t.deal_id,currency:"USD",netRevenue:!0,ttl:300,adUnitCode:i.adUnitCode,appnexus:{buyerMemberId:t.buyer_member_id,dealPriority:t.deal_priority,dealCode:t.deal_code}};t.adomain&&(d.meta=Object.assign({},d.meta,{advertiserDomains:[t.adomain]}));t.advertiser_id&&(d.meta=Object.assign({},d.meta,{advertiserId:t.advertiser_id}));t.dsa&&(d.meta=Object.assign({},d.meta,{dsa:t.dsa}));function l(e){return{ver:"1.0",complete:0,nodes:[{bsid:e.buyer_member_id.toString()}]}}t.buyer_member_id&&(d.meta=Object.assign({},d.meta,{dchain:l(t)}));t.brand_id&&(d.meta=Object.assign({},d.meta,{brandId:t.brand_id}));if(t.rtb.video){Object.assign(d,{width:t.rtb.video.player_width,height:t.rtb.video.player_height,vastImpUrl:t.notify_url,ttl:3600});switch((0,s.A)(i,"mediaTypes.video.context")){case c.LM:const i=h.n[t.brand_category_id]?h.n[t.brand_category_id]:null;d.meta=Object.assign({},d.meta,{primaryCatId:i});const o=t.deal_priority;d.video={context:c.LM,durationSeconds:Math.floor(t.rtb.video.duration_ms/1e3),dealTier:o},d.vastUrl=t.rtb.video.asset_url;break;case p.H6:if(d.adResponse=e,d.adResponse.ad=d.adResponse.ads[0],d.adResponse.ad.video=d.adResponse.ad.rtb.video,d.vastXml=t.rtb.video.content,t.renderer_url){const i=(r.bids||[]).find((t=>t.bidId===e.uuid));let o=(0,s.A)(i,"mediaTypes.video.renderer.options");o||(o=(0,s.A)(i,"renderer.options")),d.renderer=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n.A4.install({id:t.renderer_id,url:t.renderer_url,config:r,loaded:!1,adUnitCode:e});try{i.setRender(F)}catch(e){(0,a.logWarn)("Prebid Error calling setRender on renderer",e)}return i.setEventHandlers({impression:()=>(0,a.logMessage)("AppNexus outstream video impression event"),loaded:()=>(0,a.logMessage)("AppNexus outstream video loaded event"),ended:()=>{(0,a.logMessage)("AppNexus outstream renderer video event"),document.querySelector(`#${e}`).style.display="none"}}),i}(d.adUnitCode,t,o)}break;case p.mn:d.vastUrl=t.notify_url+"&redir="+encodeURIComponent(t.rtb.video.asset_url)}}else if(t.rtb[c.s6]){const e=t.rtb[c.s6];let r;if(function(e){if(!e||""===e)return!1;let t=e.match(j),r=null!=t&&t.length>=1,i=e.match(N),a=null!=i&&i.length>=1;return e.startsWith(O)&&a&&r}(t.viewability.config)){let e="pbjs_adid="+o+";pbjs_auc="+i.adUnitCode;r=t.viewability.config.replace("dom_id=%native_dom_id%",e)}let s=e.javascript_trackers;null==s?s=r:(0,a.isStr)(s)?s=[s,r]:s.push(r),d[c.s6]={title:e.title,body:e.desc,body2:e.desc2,cta:e.ctatext,rating:e.rating,sponsoredBy:e.sponsored,privacyLink:e.privacy_link,address:e.address,downloads:e.downloads,likes:e.likes,phone:e.phone,price:e.price,salePrice:e.saleprice,clickUrl:e.link.url,displayUrl:e.displayurl,clickTrackers:e.link.click_trackers,impressionTrackers:e.impression_trackers,video:e.video,javascriptTrackers:s},e.main_img&&(d[c.s6].image={url:e.main_img.url,height:e.main_img.height,width:e.main_img.width}),e.icon&&(d[c.s6].icon={url:e.icon.url,height:e.icon.height,width:e.icon.width}),d[c.s6].ext={video:e.video,customImage1:e.image1&&{url:e.image1.url,height:e.image1.height,width:e.image1.width},customImage2:e.image2&&{url:e.image2.url,height:e.image2.height,width:e.image2.width},customImage3:e.image3&&{url:e.image3.url,height:e.image3.height,width:e.image3.width},customImage4:e.image4&&{url:e.image4.url,height:e.image4.height,width:e.image4.width},customImage5:e.image5&&{url:e.image5.url,height:e.image5.height,width:e.image5.width},customIcon1:e.icon1&&{url:e.icon1.url,height:e.icon1.height,width:e.icon1.width},customIcon2:e.icon2&&{url:e.icon2.url,height:e.icon2.height,width:e.icon2.width},customIcon3:e.icon3&&{url:e.icon3.url,height:e.icon3.height,width:e.icon3.width},customIcon4:e.icon4&&{url:e.icon4.url,height:e.icon4.height,width:e.icon4.width},customIcon5:e.icon5&&{url:e.icon5.url,height:e.icon5.height,width:e.icon5.width},customSocialIcon1:e.socialicon1&&{url:e.socialicon1.url,height:e.socialicon1.height,width:e.socialicon1.width},customSocialIcon2:e.socialicon2&&{url:e.socialicon2.url,height:e.socialicon2.height,width:e.socialicon2.width},customSocialIcon3:e.socialicon3&&{url:e.socialicon3.url,height:e.socialicon3.height,width:e.socialicon3.width},customSocialIcon4:e.socialicon4&&{url:e.socialicon4.url,height:e.socialicon4.height,width:e.socialicon4.width},customSocialIcon5:e.socialicon5&&{url:e.socialicon5.url,height:e.socialicon5.height,width:e.socialicon5.width},customTitle1:e.title1,customTitle2:e.title2,customTitle3:e.title3,customTitle4:e.title4,customTitle5:e.title5,customBody1:e.body1,customBody2:e.body2,customBody3:e.body3,customBody4:e.body4,customBody5:e.body5,customCta1:e.ctatext1,customCta2:e.ctatext2,customCta3:e.ctatext3,customCta4:e.ctatext4,customCta5:e.ctatext5,customDisplayUrl1:e.displayurl1,customDisplayUrl2:e.displayurl2,customDisplayUrl3:e.displayurl3,customDisplayUrl4:e.displayurl4,customDisplayUrl5:e.displayurl5,customSocialUrl1:e.socialurl1,customSocialUrl2:e.socialurl2,customSocialUrl3:e.socialurl3,customSocialUrl4:e.socialurl4,customSocialUrl5:e.socialurl5}}else{Object.assign(d,{width:t.rtb.banner.width,height:t.rtb.banner.height,ad:t.rtb.banner.content});try{if(t.rtb.trackers)for(let e=0;e<t.rtb.trackers[0].impression_urls.length;e++){const r=t.rtb.trackers[0].impression_urls[e],i=(0,a.createTrackPixelHtml)(r);d.ad+=i}}catch(e){(0,a.logError)("Error appending tracking pixel",e)}}return d}(e,t,r);o.mediaType=function(e){const t=e.ad_type;return t===c.G_?c.G_:t===c.s6?c.s6:c.D4}(t),i.push(o)}}})),e.debug&&e.debug.debug_info){let t="AppNexus Debug Auction for Prebid\n\n"+e.debug.debug_info;t=t.replace(/(<td>|<th>)/gm,"\t").replace(/(<\/td>|<\/th>)/gm,"\n").replace(/^<br>/gm,"").replace(/(<br>\n|<br>)/gm,"\n").replace(/<h1>(.*)<\/h1>/gm,"\n\n===== $1 =====\n\n").replace(/<h[2-6]>(.*)<\/h[2-6]>/gm,"\n\n*** $1 ***\n\n").replace(/(<([^>]+)>)/gim,""),(0,a.logMessage)("https://console.appnexus.com/docs/understanding-the-debug-auction"),(0,a.logMessage)(t)}return i},getUserSyncs:function(e,t,r,i,a){if(e.iframeEnabled&&(0,m.C)(r))return[{type:"iframe",url:"https://acdn.adnxs.com/dmp/async_usersync.html"}];if(e.pixelEnabled){return["https://px.ads.linkedin.com/setuid?partner=appNexus"].map((e=>({type:"image",url:e})))}}};function q(e){const t={};Object.keys(e.params).forEach((t=>{let r=(0,f.vk)(t);r!==t&&(e.params[r]=e.params[t],delete e.params[t])})),t.sizes=M(e.sizes),t.primary_size=t.sizes[0],t.ad_types=[],t.uuid=e.bidId,e.params.placement_id?t.id=parseInt(e.params.placement_id,10):t.code=e.params.inv_code;const r=(0,a.getParameterByName)("ast_override_div");if((0,a.isStr)(r)&&""!==r){const i=decodeURIComponent(r).split(",").find((t=>t.startsWith(`${e.adUnitCode}:`)));if(i){const e=i.split(":")[1];e&&(t.force_creative_id=parseInt(e,10))}}t.allow_smaller_sizes=e.params.allow_smaller_sizes||!1,t.use_pmt_rule="boolean"==typeof e.params.use_payment_rule?e.params.use_payment_rule:"boolean"==typeof e.params.use_pmt_rule&&e.params.use_pmt_rule,t.prebid=!0,t.disable_psa=!0;let i=function(e){if(!(0,a.isFn)(e.getFloor))return e.params.reserve?e.params.reserve:null;let t=e.getFloor({currency:"USD",mediaType:"*",size:"*"});if((0,a.isPlainObject)(t)&&!isNaN(t.floor)&&"USD"===t.currency)return t.floor;return null}(e);if(i&&(t.reserve=i),e.params.position)t.position={above:1,below:2}[e.params.position]||0;else{let r=(0,s.A)(e,"mediaTypes.banner.pos")||(0,s.A)(e,"mediaTypes.video.pos");0!==r&&1!==r&&3!==r||(t.position=3===r?2:r)}e.params.traffic_source_code&&(t.traffic_source_code=e.params.traffic_source_code),e.params.private_sizes&&(t.private_sizes=M(e.params.private_sizes)),e.params.supply_type&&(t.supply_type=e.params.supply_type),e.params.pub_click&&(t.pubclick=e.params.pub_click),e.params.ext_inv_code&&(t.ext_inv_code=e.params.ext_inv_code),e.params.publisher_id&&(t.publisher_id=parseInt(e.params.publisher_id,10)),e.params.external_imp_id&&(t.external_imp_id=e.params.external_imp_id);const n=(0,_.T_)((0,_.gg)((0,s.A)(e,"ortb2Imp.ext.data.keywords")),e.params?.keywords);n.length>0&&(t.keywords=n);let o=(0,s.A)(e,"ortb2Imp.ext.gpid")||(0,s.A)(e,"ortb2Imp.ext.data.pbadslot");if(o&&(t.gpid=o),(e.mediaType===c.s6||(0,s.A)(e,`mediaTypes.${c.s6}`))&&(t.ad_types.push(c.s6),0===t.sizes.length&&(t.sizes=M([1,1])),e.nativeParams)){const r=function(e){const t={};return Object.keys(e).forEach((r=>{const i=E[r]&&E[r].serverName||E[r]||r,s=E[r]&&E[r].requiredParams;t[i]=Object.assign({},s,e[r]);if(!(i!==E.image.serverName&&i!==E.icon.serverName)&&t[i].sizes){let e=t[i].sizes;((0,a.isArrayOfNums)(e)||(0,a.isArray)(e)&&e.length>0&&e.every((e=>(0,a.isArrayOfNums)(e))))&&(t[i].sizes=M(t[i].sizes))}i===E.privacyLink&&(t.privacy_supported=!0)})),t}(e.nativeParams);t[c.s6]={layouts:[r]}}{const r=(0,s.A)(e,`mediaTypes.${c.G_}`),i=(0,s.A)(e,"mediaTypes.video.context");t.hb_source=r&&"adpod"===i?7:1,(e.mediaType===c.G_||r)&&t.ad_types.push(c.G_),(e.mediaType===c.G_||r&&"outstream"!==i)&&(t.require_asset_url=!0),e.params.video&&(t.video={},Object.keys(e.params.video).filter((e=>x.includes(e))).forEach((r=>{switch(r){case"context":case"playback_method":let i=e.params.video[r];i=(0,a.isArray)(i)?i[0]:i,t.video[r]=U[r][i];break;case"frameworks":break;default:t.video[r]=e.params.video[r]}})),e.params.video.frameworks&&(0,a.isArray)(e.params.video.frameworks)&&(t.video_frameworks=e.params.video.frameworks)),r&&(t.video=t.video||{},Object.keys(r).filter((e=>I.includes(e))).forEach((e=>{switch(e){case"minduration":case"maxduration":"number"!=typeof t.video[e]&&(t.video[e]=r[e]);break;case"skip":"boolean"!=typeof t.video.skippable&&(t.video.skippable=1===r[e]);break;case"skipafter":"number"!=typeof t.video.skipoffset&&(t.video.skippoffset=r[e]);break;case"playbackmethod":if("number"!=typeof t.video.playback_method){let i=r[e];i=(0,a.isArray)(i)?i[0]:i,i>=1&&i<=4&&(t.video.playback_method=i)}break;case"api":if(!t.video_frameworks&&(0,a.isArray)(r[e])){let i=r[e].map((e=>{let t=4===e?5:5===e?4:e;if(t>=1&&t<=5)return t})).filter((e=>e));t.video_frameworks=i}break;case"startdelay":case"plcmt":case"placement":if("number"!=typeof t.video.context){const e=r.plcmt,i=r.placement,a=r.startdelay,s=function(e,t){if(!e)return;if(2===e){if(void 0===t)return;if(0===t)return"accompanying_content_pre_roll";if(-1===t)return"accompanying_content_mid_roll";if(-2===t)return"accompanying_content_post_roll"}else{if(3===e)return"interstitial";if(4===e)return"outstream"}}(e,a)||function(e){if(!e)return;if(2===e)return"in-banner";if(3===e)return"outstream";if(4===e)return"in-feed";if(5===e)return"intersitial"}(i)||function(e){if(void 0===e)return;if(0===e)return"pre_roll";if(-1===e)return"mid_roll";if(-2===e)return"post_roll"}(a);t.video.context=U.context[s]}}}))),e.renderer&&(t.video=Object.assign({},t.video,{custom_renderer_present:!0}))}return e.params.frameworks&&(0,a.isArray)(e.params.frameworks)&&(t.banner_frameworks=e.params.frameworks),(0,s.A)(e,`mediaTypes.${c.D4}`)&&t.ad_types.push(c.D4),0===t.ad_types.length&&delete t.ad_types,t}function M(e){let t=[],r={};if((0,a.isArray)(e)&&2===e.length&&!(0,a.isArray)(e[0]))r.width=parseInt(e[0],10),r.height=parseInt(e[1],10),t.push(r);else if("object"==typeof e)for(let i=0;i<e.length;i++){let a=e[i];r={},r.width=parseInt(a[0],10),r.height=parseInt(a[1],10),t.push(r)}return t}function z(e){return!!e.params.user}function B(e){return!!parseInt(e.params.member,10)}function $(e){if(e.params)return!!e.params.app}function G(e){return e.params&&e.params.app?!!e.params.app.id:!!e.params.app}function L(e){return!!e.debug}function W(e){return e.mediaTypes&&e.mediaTypes.video&&e.mediaTypes.video.context===c.LM}function H(e){let t=!1;const r=e.params,i=e.params.video;return r.frameworks&&(0,a.isArray)(r.frameworks)&&(t=e.params.frameworks.includes(6)),!t&&i&&i.frameworks&&(0,a.isArray)(i.frameworks)&&(t=e.params.video.frameworks.includes(6)),t}function V(e,t,r){(0,a.isEmpty)(e.video)&&(e.video={}),e.video[t]=r}function F(e,t){!function(e){try{const t=document.getElementById(e).querySelectorAll("div[id^='google_ads']");t[0]&&t[0].style.setProperty("display","none")}catch(e){}}(e.adUnitCode),function(e){try{const t=document.getElementById(e).querySelectorAll("script[id^='sas_script']");t[0].nextSibling&&"iframe"===t[0].nextSibling.localName&&t[0].nextSibling.style.setProperty("display","none")}catch(e){}}(e.adUnitCode),e.renderer.push((()=>{(t?.defaultView||window).ANOutstreamVideo.renderAd({tagId:e.adResponse.tag_id,sizes:[e.getSize().split("x")],targetId:e.adUnitCode,uuid:e.adResponse.uuid,adResponse:e.adResponse,rendererOptions:e.renderer.getConfig()},J.bind(null,e))}))}function J(e,t,r){e.renderer.handleVideoEvent({id:t,eventName:r})}(0,d.a$)(P),(0,i.E)("appnexusBidAdapter")}},e=>{e.O(0,[60802,44982,97247,95444,42698,12126,37769,12139,51085],(()=>{return t=50791,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[21829],{91493:(e,t,i)=>{var a=i(7873),r=i(63172),n=i(91069),o=i(57377),s=i(71371),d=i(12938),p=i(27934),l=i(82621),c=i(95789),u=i(63895),m=i(68044),v=i(89766),g=i(54406);const y="criteo",f="https://grid-bidder.criteo.com/openrtb_2_5/pbjs/auction/request",b=(0,d.vM)({bidderCode:y}),h="Criteo: ",T=(0,g.FE)(),I="https://static.criteo.net/js/ld/publishertag.renderer.js",k="cto_optout",x="cto_bundle",P=(0,v.A)({context:{netRevenue:!0,ttl:60},imp:function(e,t,i){let a=e(t,i);const n=t.params;a.tagid=t.adUnitCode,(0,r.J)(a,"ext",{...t.params.ext,...a.ext,rwdd:a.rwdd,floors:U(t),bidder:{publishersubid:n?.publisherSubId,zoneid:n?.zoneId,uid:n?.uid}}),delete a.rwdd,!i.fledgeEnabled&&a.ext.igs?.ae&&delete a.ext.igs.ae;if(w(t)){const e=t.params.video;void 0!==e&&(0,r.J)(a,"video",{...a.video,skip:a.video.skip||e.skip||0,placement:a.video.placement||e.placement,minduration:a.video.minduration||e.minduration,playbackmethod:a.video.playbackmethod||e.playbackmethod,startdelay:a.video.startdelay||e.startdelay||0}),(0,r.J)(a,"video.ext",{context:t.mediaTypes.video.context,playersizes:E(t?.mediaTypes?.video?.playerSize,R),plcmt:t.mediaTypes.video.plcmt,poddur:t.mediaTypes.video.adPodDurationSec,rqddurs:t.mediaTypes.video.durationRangeSec})}if(a.native&&void 0!==a.native.request){let e=JSON.parse(a.native.request);e.assets&&(1!==e.assets.length||Object.keys(e.assets[0]).length)||delete e.assets,(0,r.J)(a,"native.request_native",e),delete a.native.request}return a},request:function(e,t,i,a){let n=e(t,i,a);void 0!==a.publisherId&&(void 0!==n.app?(0,r.J)(n,"app.publisher.id",a.publisherId):(0,r.J)(n,"site.publisher.id",a.publisherId));i&&i.gdprConsent&&(0,r.J)(n,"regs.ext.gdprversion",i.gdprConsent.apiVersion);return n=T(n),n},bidResponse:function(e,t,i){i.mediaType=t?.ext?.mediatype,i.mediaType===s.s6&&void 0!==t.adm_native&&(t.adm=t.adm_native,delete t.adm_native);let a=e(t,i);const{bidRequest:n}=i;a.currency=t?.ext?.cur,void 0!==t?.ext?.meta&&(0,r.J)(a,"meta",{...a.meta,...t.ext.meta});void 0!==t?.ext?.paf?.content_id&&(0,r.J)(a,"meta.paf.content_id",t.ext.paf.content_id);a.mediaType===s.G_&&(a.vastUrl=t.ext?.displayurl,n?.mediaTypes?.video?.context===u.H6&&(a.renderer=function(e){if(void 0===e.ext?.videoPlayerConfig||void 0===e.ext?.videoPlayerType)return;const t={documentResolver:(e,t,i)=>i??t},i=(t,i)=>{let a={slotid:e.id,vastUrl:e.ext?.displayurl,vastXml:e.adm,documentContext:i},r=e.ext.videoPlayerConfig;window.CriteoOutStream[e.ext.videoPlayerType].play(a,r)},a=c.A4.install({url:I,config:t});return a.setRender(i),a}(t)));return a},response:function(e,t,i,a){let n=e(t,i,a);const o=i?.ext?.paf?.transmission;return n.bids.forEach((e=>{void 0!==o&&void 0!==e?.meta?.paf?.content_id?(0,r.J)(e,"meta.paf.transmission",o):delete e.meta.paf})),n}});const S={code:y,gvlid:91,supportedMediaTypes:[s.D4,s.G_,s.s6],getUserSyncs:function(e,t,i,a){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},{gppString:o="",applicableSections:s=[]}=r;const d=(0,p.EN)(),c="criteoPrebidAdapter";if(e.iframeEnabled&&(0,l.C)(i)){const e=[];if(e.push(`origin=${c}`),e.push(`topUrl=${d.domain}`),i&&(i.gdprApplies&&e.push("gdpr="+(1==i.gdprApplies?1:0)),i.consentString&&e.push(`gdpr_consent=${i.consentString}`)),a&&e.push(`us_privacy=${a}`),e.push(`gpp=${o}`),Array.isArray(s))for(const t of s)e.push(`gpp_sid=${t}`);const t=Math.random().toString(),r={bundle:C(x),cw:b.cookiesAreEnabled(),lsw:b.localStorageIsEnabled(),optoutCookie:C(k),origin:c,requestId:t,tld:d.domain,topUrl:d.domain,version:"9.50.0".replace(/\./g,"_")};window.addEventListener("message",(function e(i){if(!i.data||"https://gum.criteo.com"!=i.origin)return;if(i.data.requestId!==t)return;this.removeEventListener("message",e),i.stopImmediatePropagation();const a=i.data;a.optout?(_(x),q(k,!0,43200,d.domain)):(a.bundle&&q(x,a.bundle,9360,d.domain),a?.callbacks?.forEach?.(n.triggerPixel))}),!0);const p=JSON.stringify(r).replace(/"/g,"%22");return[{type:"iframe",url:`https://gum.criteo.com/syncframe?${e.join("&")}#${p}`}]}if(e.pixelEnabled&&(0,l.C)(i)){const e=[];if(e.push("profile=207"),i&&(!0===i.gdprApplies&&e.push("gdprapplies=true"),i.consentString&&e.push(`gdpr=${i.consentString}`)),a&&e.push(`ccpa=${a}`),e.push(`gpp=${o}`),Array.isArray(s))for(const t of s)e.push(`gpp_sid=${t}`);return[{type:"image",url:`https://ssp-sync.criteo.com/user-sync/redirect?${e.join("&")}`}]}return[]},isBidRequestValid:e=>!(!e||!e.params||!e.params.zoneId&&!e.params.networkId)&&!(w(e)&&!function(e){let t=!0;return["mimes","playerSize","maxduration","protocols","api","skip","placement","playbackmethod"].forEach((function(i){"placement"===i?void 0===e?.mediaTypes?.video?.[i]&&void 0===e?.params?.video?.[i]&&void 0===e?.mediaTypes?.video?.plcmt&&void 0===e?.params?.video?.plcmt&&(t=!1,(0,n.logError)("Criteo Bid Adapter: mediaTypes.video."+i+" or mediaTypes.video.plcmt is required")):void 0===e?.mediaTypes?.video?.[i]&&void 0===e?.params?.video?.[i]&&(t=!1,(0,n.logError)("Criteo Bid Adapter: mediaTypes.video."+i+" is required"))})),t}(e)),buildRequests:(e,t)=>{e.forEach((e=>{(function(e){return void 0!==e?.mediaTypes?.native})(e)&&(function(e){return!(e.nativeParams&&(e.nativeParams.image&&(!0!==e.nativeParams.image.sendId||!0===e.nativeParams.image.sendTargetingKeys)||e.nativeParams.icon&&(!0!==e.nativeParams.icon.sendId||!0===e.nativeParams.icon.sendTargetingKeys)||e.nativeParams.clickUrl&&(!0!==e.nativeParams.clickUrl.sendId||!0===e.nativeParams.clickUrl.sendTargetingKeys)||e.nativeParams.displayUrl&&(!0!==e.nativeParams.displayUrl.sendId||!0===e.nativeParams.displayUrl.sendTargetingKeys)||e.nativeParams.privacyLink&&(!0!==e.nativeParams.privacyLink.sendId||!0===e.nativeParams.privacyLink.sendTargetingKeys)||e.nativeParams.privacyIcon&&(!0!==e.nativeParams.privacyIcon.sendId||!0===e.nativeParams.privacyIcon.sendTargetingKeys)))}(e)||(0,n.logWarn)(h+"all native assets containing URL should be sent as placeholders with sendId(icon, image, clickUrl, displayUrl, privacyLink, privacyIcon)"),null==e?.nativeOrtbRequest?.assets&&((0,n.logWarn)(h+"native asset requirements are missing"),(0,r.J)(e,"nativeOrtbRequest.assets",[{}])))}));const i=function(e,t){const i=(0,n.parseUrl)(t?.refererInfo?.topmostLocation).search;return{url:t?.refererInfo?.page||"",debug:"1"===i.pbt_debug,noLog:"1"===i.pbt_nolog,fledgeEnabled:t.paapi?.enabled,amp:e.some((e=>"amp"===e.params.integrationMode)),networkId:e.find((e=>e.params?.networkId))?.params.networkId,publisherId:e.find((e=>e.params?.pubid))?.params.pubid}}(e,t),a=function(e){let t=f;t+="?profileId=207",t+="&av="+String(37),t+="&wv="+encodeURIComponent("9.50.0"),t+="&cb="+String(Math.floor(99999999999*Math.random())),b.localStorageIsEnabled()?t+="&lsavail=1":t+="&lsavail=0";e.amp&&(t+="&im=1");e.debug&&(t+="&debug=1");e.noLog&&(t+="&nolog=1");const i=C(x);i&&(t+=`&bundle=${i}`);C(k)&&(t+="&optout=1");e.networkId&&(t+="&networkId="+e.networkId);return t}(i),o=P.toORTB({bidderRequest:t,bidRequests:e,context:i});if(o)return{method:"POST",url:a,data:o,bidRequests:e}},interpretResponse:(e,t)=>{if(void 0===e?.body)return[];const i=P.fromORTB({response:e.body,request:t.data}).bids||[],a=e.body?.ext?.igi?.filter((e=>(0,n.isArray)(e?.igs))).flatMap((e=>e.igs));return a?.length?{bids:i,paapi:a}:i},onDataDeletionRequest:e=>{const t=C(x);t&&(_(x),(0,m.RD)("https://privacy.criteo.com/api/privacy/datadeletionrequest",null,JSON.stringify({publisherUserId:t}),{contentType:"application/json",method:"POST"}))}};function C(e){const t=b.getCookie(e),i=b.getDataFromLocalStorage(e);return t||i||void 0}function q(e,t,i,a){const r=new Date;r.setTime(r.getTime()+60*i*60*1e3);const n=`expires=${r.toUTCString()}`,o=a.split(".");for(let i=0;i<o.length;++i){const a=o.slice(o.length-i-1,o.length).join(".");try{b.setCookie(e,t,n,null,"."+a);const i=b.getCookie(e);if(i&&i===t)break}catch(e){}}b.setDataInLocalStorage(e,t)}function _(e){b.setCookie(e,"",0),b.removeDataFromLocalStorage(e)}function E(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return null==e?[]:Array.isArray(e[0])?e.map((e=>t(e))):[t(e)]}function R(e){return e[0]+"x"+e[1]}function w(e){return void 0!==e?.mediaTypes?.video}function U(e){try{const t={},i=function(e){if(e.getFloor)return e.getFloor;if(e.params.bidFloor&&e.params.bidFloorCur)try{const t=parseFloat(e.params.bidFloor);return()=>({currency:e.params.bidFloorCur,floor:t})}catch{}}(e);if(i){if(e.mediaTypes?.banner){t.banner={};E(e?.mediaTypes?.banner?.sizes).forEach((a=>t.banner[R(a).toString()]=i.call(e,{size:a,mediaType:s.D4})))}if(e.mediaTypes?.video){t.video={};E(e?.mediaTypes?.video?.playerSize).forEach((a=>t.video[R(a).toString()]=i.call(e,{size:a,mediaType:s.G_})))}return e.mediaTypes?.native&&(t.native={},t.native["*"]=i.call(e,{size:"*",mediaType:s.s6})),t}}catch(e){(0,n.logError)("Could not parse floors from Prebid: "+e)}}(0,o.a$)(S),(0,a.E)("criteoBidAdapter")}},e=>{e.O(0,[60802,58498,15889,37769,12139,51085],(()=>{return t=91493,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[70931],{32843:(e,t,o)=>{var n=o(7873),r=o(91069),i=o(68044),a=o(27934),c=o(16833),s=o(12938),d=o(45569),l=o(16916);const p="criteo",g=(0,s.vM)({moduleType:d.fW,moduleName:p}),u="cto_bidid",m="cto_bundle",b="html5",I="cookie",y=new Date(0).toString(),f=new Date((0,r.timestamp)()+33696e6).toString();function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=(0,r.parseUrl)(e,{noDecodeWholeURL:!0});return t?`${o.hostname}`:`${o.protocol}://${o.hostname}${o.port?":"+o.port:""}/`}function h(e,t){return e?.storage?.type===b?g.getDataFromLocalStorage(t):e?.storage?.type===I?g.getCookie(t):g.getCookie(t)||g.getDataFromLocalStorage(t)}function C(e,t,o,n){t&&o&&(e?.storage?.type===b?g.setDataInLocalStorage(t,o):(e?.storage?.type===I||g.setDataInLocalStorage(t,o),U(t,o,f,n,!0)))}function U(e,t,o,n,r){const i=n.split(".");for(let n=0;n<i.length;++n){const a=i.slice(i.length-n-1,i.length).join(".");try{if(g.setCookie(e,t,o,null,"."+a),r){const o=g.getCookie(e);if(o&&o===t)break}}catch(e){}}}function v(e,t,o){const n=(void 0===e?.storage?.type||e?.storage?.type===I)&&g.cookiesAreEnabled(),c=(void 0===e?.storage?.type||e?.storage?.type===b)&&g.localStorageIsEnabled(),s=S((0,a.EN)().page),d=S(document.location.href,!0),p="undefined"!=typeof criteo_pubtag,f=function(e,t,o,n,r,i,a){let c="https://gum.criteo.com/sid/json?origin=prebid"+(e?"&topUrl="+encodeURIComponent(e):"")+(t?"&domain="+encodeURIComponent(t):"")+(o?"&bundle="+encodeURIComponent(o):"")+(n?"&info="+encodeURIComponent(n):"")+(r?"&cw=1":"")+(a?"&pbt=1":"")+(i?"&lsw=1":"");const s=l.t6.getConsentData();s&&(c+=`&us_privacy=${encodeURIComponent(s)}`);const d=l.mW.getConsentData();d&&(c=c+""+(d.consentString?"&gdprString="+encodeURIComponent(d.consentString):""),c=c+"&gdpr="+(!0===d.gdprApplies?1:0));const p=l.ad.getConsentData();return p&&(c=c+""+(p.gppString?"&gpp="+encodeURIComponent(p.gppString):""),c=c+""+(p.applicableSections?"&gpp_sid="+encodeURIComponent(p.applicableSections):"")),c}(s,d,t.bundle,t.dnaBundle,n,c,p),h={success:t=>{const n=JSON.parse(t);if(n.pixels&&n.pixels.forEach((t=>function(e,t,o){o.writeBundleInStorage&&o.bundlePropertyName&&o.storageKeyName?(0,i.RD)(o.pixelUrl,{success:n=>{if(n){const r=JSON.parse(n);r&&r[o.bundlePropertyName]&&C(e,o.storageKeyName,r[o.bundlePropertyName],t)}},error:e=>{(0,r.logError)("criteoIdSystem: unable to sync user id",e)}},void 0,{method:"GET",withCredentials:!0}):(0,r.triggerPixel)(o.pixelUrl)}(e,d,t))),n.acwsUrl){("string"==typeof n.acwsUrl?[n.acwsUrl]:n.acwsUrl).forEach((e=>(0,r.triggerPixel)(e)))}else n.bundle&&C(e,m,n.bundle,d);if(n.bidId){C(e,u,n.bidId,d);const t={criteoId:n.bidId};o(t)}else U(a=u,"",y,d,!0),g.removeDataFromLocalStorage(a),o();var a},error:e=>{(0,r.logError)("criteoIdSystem: unable to sync user id",e),o()}};(0,i.RD)(f,h,void 0,{method:"GET",contentType:"application/json",withCredentials:!0})}const D={name:p,gvlid:91,decode:e=>e,getId(e){let t=function(e){return{bundle:h(e,m),dnaBundle:h(e,"cto_dna_bundle"),bidId:h(e,u)}}(e);return{id:t.bidId?{criteoId:t.bidId}:void 0,callback:o=>v(e,t,o)}},eids:{criteoId:{source:"criteo.com",atype:1}}};(0,c.bz)("userId",D),(0,n.E)("criteoIdSystem")}},e=>{e.O(0,[60802,37769,12139,51085],(()=>{return t=32843,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[36050],{96480:(e,t,r)=>{var n=r(7873),a=r(51252),o=r(10201),i=r(51692),s=r(67314),c=r(43272),l=r(78969),d=r(75023),u=r(16833),p=r(27934),m=r(97779),f=r(91069),b=r(25246),h=r(68693),g=r(68044),y=r(74420);const A={ri:p.EN},v="VASTAdTagURI";function U(e){if(!e.params&&!e.url)return void(0,f.logError)("A params object or a url is required to use pbjs.adServers.dfp.buildVideoUrl");const t=e.adUnit,r=e.bid||m.iS.getWinningBids(t.code)[0];let n={};if(e.url&&(n=(0,f.parseUrl)(e.url,{noDecodeWholeURL:!0}),(0,f.isEmpty)(e.params)))return function(e,t,r){const n=_(t,e,"search");n&&(e.search.description_url=n);return e.search.cust_params=x(t,r,e.search.cust_params),(0,f.buildUrl)(e)}(n,r,e);const o={correlator:Date.now(),sz:(0,f.parseSizesInput)(t?.mediaTypes?.video?.playerSize).join("|"),url:encodeURIComponent(location.href)},c=n.search,l=c&&c.sz;l&&(o.sz=l+"|"+o.sz);let d=x(r,e,c&&c.cust_params);const u=Object.assign({},b.Mu,n.search,o,e.params,{cust_params:d},(0,b.QS)()),p=_(r,e,"params");if(p&&(u.description_url=p),!u.ppid){const e=(0,i.Q)();null!=e&&(u.ppid=e)}const h=e.adUnit?.mediaTypes?.video;Object.entries({plcmt:()=>h?.plcmt,min_ad_duration:()=>(0,f.isNumber)(h?.minduration)?1e3*h.minduration:null,max_ad_duration:()=>(0,f.isNumber)(h?.maxduration)?1e3*h.maxduration:null,vpos(){const e=h?.startdelay;if((0,f.isNumber)(e))return-2===e?"postroll":-1===e||e>0?"midroll":"preroll"},vconp:()=>Array.isArray(h?.playbackmethod)&&h.playbackmethod.some((e=>7===e))?"2":void 0,vpa(){if(Array.isArray(h?.playbackmethod)){const e=h.playbackmethod.some((e=>3===e)),t=h.playbackmethod.some((e=>[1,2,4,5,6].includes(e)));if(e&&!t)return"click";if(t&&!e)return"auto"}},vpmute(){if(Array.isArray(h?.playbackmethod)){const e=h.playbackmethod.some((e=>[2,6].includes(e))),t=h.playbackmethod.some((e=>[1,3,4,5].includes(e)));if(e&&!t)return"1";if(t&&!e)return"0"}}}).forEach((e=>{let[t,r]=e;if(!u.hasOwnProperty(t)){const e=r();null!=e&&(u[t]=e)}}));const g=s.n.index.getBidRequest(e.bid||{})?.ortb2??s.n.index.getAuction(e.bid||{})?.getFPD()?.global,y=(0,a.eu)(g);return y.length&&(u.ppsj=btoa(JSON.stringify({PublisherProvidedTaxonomySignals:y}))),(0,f.buildUrl)(Object.assign({},b.P8,n,{search:u}))}function _(e,t,r){return t?.[r]?.description_url||encodeURIComponent(A.ri().page)}function x(e,t,r){const n=e&&e.adserverTargeting||{};let a={};const o=t&&t.adUnit;if(o){let e=m.iS.getAllTargeting(o.code);a=e?e[o.code]:{}}const i=Object.assign({},{hb_uuid:e&&e.videoCacheKey},{hb_cache_id:e&&e.videoCacheKey},a,n);d.emit(l.qY.SET_TARGETING,{[o.code]:i});const s=t?.params?.cust_params,c=Object.assign({},i,s);let u=encodeURIComponent((0,f.formatQS)(c));return r&&(u=r+"%26"+u),u}async function C(e,t){try{const r=(0,y.A)(),n=r.parse(e),a=n.querySelectorAll(v)[0];if(!a||!a.textContent)return e;const o=new RegExp("[A-Fa-f0-9]{8}-(?:[A-Fa-f0-9]{4}-){3}[A-Fa-f0-9]{12}","gi"),i=Array.from(a.textContent.matchAll(o)).map((e=>{let[t]=e;return t})).filter((e=>t.has(e)));if(1!=i.length)return(0,f.logWarn)(`Unable to determine unique uuid in ${v}`),e;const s=i[0],c=t.get(s),l=await async function(e){const t=await(0,g.hd)(e);if(!t.ok)throw(0,f.logError)("Unable to fetch blob"),new Error("Blob not found");const r=await t.text();return`data://text/xml;base64,${btoa(r)}`}(c),d=n.createCDATASection(l);return a.textContent="",a.appendChild(d),r.serialize(n)}catch(t){return(0,f.logWarn)("Unable to process xml",t),e}}c.$W.getConfig("brandCategoryTranslation.translationFile")&&(0,u.Yn)("registerAdserver").before((function(e){e.call(this,"dfp")})),(0,o.U)("dfp",{buildVideoUrl:U,getVastXml:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h.kh;const r=U(e),n=await(0,g.hd)(r);if(!n.ok)throw new Error("Unable to fetch GAM VAST wrapper");const a=await n.text();if(c.$W.getConfig("cache.useLocal")){return await C(a,t)}return a}}),(0,n.E)("dfpAdServerVideo")}},e=>{e.O(0,[60802,33005,47650,41,37769,12139,51085],(()=>{return t=96480,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[4584],{84232:(t,e,o)=>{var n=o(7873),a=o(51252),d=o(67314),s=o(43272),r=o(78969),i=o(16833),l=o(91069),c=o(63172),u=o(70433);let f={},p=!1;function g(t){return(0,a.eu)(t)}const b=t=>(s.$W.getConfig("gptPreAuction")||{}).mcmEnabled?t.replace(/(^\/\d*),\d*\//,"$1/"):t;function m(t){(0,l.logWarn)("pbadslot is deprecated and will soon be removed, use gpid instead",t)}const h=function(t,e){const o=(t=>{const{customGptSlotMatching:e}=f;if(!(0,l.isGptPubadsDefined)())return;const o=t.reduce(((t,e)=>(t[e.code]=t[e.code]||[],t[e.code].push(e),t)),{}),n={};return window.googletag.pubads().getSlots().forEach((t=>{const a=Object.keys(o).find(e?e(t):(0,l.isAdUnitCodeMatchingSlot)(t));if(a){const e=n[a]=t.getAdUnitPath(),d={name:"gam",adslot:b(e)};o[a].forEach((t=>{(0,c.J)(t,"ortb2Imp.ext.data.adserver",Object.assign({},t.ortb2Imp?.ext?.data?.adserver,d))}))}})),n})(e),{useDefaultPreAuction:n,customPreAuction:a}=f;e.forEach((t=>{t.ortb2Imp=t.ortb2Imp||{},t.ortb2Imp.ext=t.ortb2Imp.ext||{},t.ortb2Imp.ext.data=t.ortb2Imp.ext.data||{};const e=t.ortb2Imp.ext;if(a||n){e.data?.pbadslot&&m(t);let d,s=(0,u.A)(e,"data.adserver.adslot");a?d=a(t,s,o?.[t.code]):n&&(d=((t,e,o)=>{const n=t.ortb2Imp.ext.data;if(n.pbadslot)return n.pbadslot;if((0,l.isGptPubadsDefined)()){var a=window.googletag.pubads().getSlots().filter((t=>t.getAdUnitPath()===o));if(0!==a.length)return 1===a.length?e:`${e}#${t.code}`}})(t,s,o?.[t.code])),d&&(e.gpid=e.data.pbadslot=d)}else{m(t);const o=(t=>{const e=t.ortb2Imp.ext.data,{customPbAdSlot:o}=f;if(!e.pbadslot)if(o)e.pbadslot=o(t.code,(0,u.A)(e,"adserver.adslot"));else{try{const o=document.getElementById(t.code);if(o.dataset.adslotid)return void(e.pbadslot=o.dataset.adslotid)}catch(t){}if(!(0,u.A)(e,"adserver.adslot"))return e.pbadslot=t.code,!0;e.pbadslot=e.adserver.adslot}})(t);e.gpid||o||(e.gpid=e.data.pbadslot)}}));for(var d=arguments.length,s=new Array(d>2?d-2:0),r=2;r<d;r++)s[r-2]=arguments[r];return t.call(undefined,e,...s)},A=(t,e)=>{const o=function(t){const e={};return a.Cn.forEach((o=>{const n=t.flatMap((t=>t)).filter((t=>t.taxonomy===o)).map((t=>t.values));e[o]=n.length?n.reduce(((t,e)=>t.filter((t=>e.includes(t))))):[],e[o]={values:e[o]}})),e}(function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d.n.index;return t.map((t=>e.getAuction({auctionId:t})?.getFPD()?.global)).map(g).filter((t=>t))}(function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d.n;return Object.values(t).flatMap((t=>Object.entries(t))).filter((t=>t[0]===r.xS.AD_ID||t[0].startsWith(r.xS.AD_ID+"_"))).flatMap((t=>t[1])).map((t=>e.findBidByAdId(t)?.auctionId)).filter((t=>null!=t)).filter(l.uniques)}(e)));window.googletag.setConfig&&window.googletag.setConfig({pps:{taxonomies:o}}),t(e)},v=t=>{f=(0,l.pick)(t,["enabled",t=>!1!==t,"customGptSlotMatching",t=>"function"==typeof t&&t,"customPbAdSlot",t=>"function"==typeof t&&t,"customPreAuction",t=>"function"==typeof t&&t,"useDefaultPreAuction",t=>t??!0]),f.enabled?p||((0,i.Yn)("makeBidRequests").before(h),(0,i.Yn)("targetingDone").after(A),p=!0):((0,l.logInfo)("GPT Pre-Auction: Turning off module"),f={},(0,i.Yn)("makeBidRequests").getHooks({hook:h}).remove(),(0,i.Yn)("targetingDone").getHooks({hook:A}).remove(),p=!1)};s.$W.getConfig("gptPreAuction",(t=>v(t.gptPreAuction))),v({}),(0,n.E)("gptPreAuction")}},t=>{t.O(0,[33005,60802,37769,12139,51085],(()=>{return e=84232,t(t.s=e);var e}));t.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[55153],{13557:(e,t,i)=>{var n=i(7873),o=i(91069),r=i(68044),l=i(16833),p=i(12938),s=i(45569);const d="identityLink",a=(0,p.vM)({moduleType:s.fW,moduleName:d}),c="_lr_env",g={name:d,gvlid:97,decode:e=>({idl_env:e}),getId(e,t){const i=e&&e.params||{};if(!i||"string"!=typeof i.pid)return void o.logError("identityLink: requires partner id to be defined");const{gdpr:n,gpp:r}=t??{},l=n&&"boolean"==typeof n.gdprApplies&&n.gdprApplies?1:0,p=l?n.consentString:"";if(l&&(!p||""===p))return void o.logInfo("identityLink: Consent string is required to call envelope API.");const s=!(!r||!r.gppString)&&r.gppString,d=!!(r&&r.gppString&&r.applicableSections.length>0&&-1!==r.applicableSections[0])&&r.applicableSections[0],g=s&&d,u=`https://api.rlcdn.com/api/identity/envelope?pid=${i.pid}${l?"&ct=4&cv="+p:""}${g?"&gpp="+s+"&gpp_sid="+d:""}`;let y;return y=function(e){if(window.ats&&window.ats.retrieveEnvelope)o.logInfo("identityLink: ATS exists!"),window.ats.retrieveEnvelope((function(t){t?(o.logInfo("identityLink: An envelope can be retrieved from ATS!"),f(!0),e(JSON.parse(t).envelope)):v(u,e,i)}));else{let t=function(){let e=a.getCookie(c)||a.getDataFromLocalStorage(c);return e?window.atob(e):void 0}();t?(o.logInfo("identityLink: LiveRamp envelope successfully retrieved from storage!"),e(JSON.parse(t).envelope)):v(u,e,i)}},{callback:y}},eids:{idl_env:{source:"liveramp.com",atype:3}}};function v(e,t,i){const n={success:e=>{let i;if(e)try{i=JSON.parse(e)}catch(e){o.logInfo(e)}t(i&&i.envelope?i.envelope:"")},error:e=>{o.logInfo("identityLink: identityLink: ID fetch encountered an error",e),t()}};i.notUse3P||a.getCookie("_lr_retry_request")?t():(!function(){let e=new Date;e.setTime(e.getTime()+36e5),a.setCookie("_lr_retry_request","true",e.toUTCString())}(),o.logInfo("identityLink: A 3P retrieval is attempted!"),f(!1),(0,r.RD)(e,n,void 0,{method:"GET",withCredentials:!0}))}function f(e){let t=new Date;t.setTime(t.getTime()+2592e6),a.setCookie("_lr_env_src_ats",e,t.toUTCString())}(0,l.bz)("userId",g),(0,n.E)("identityLinkIdSystem")}},e=>{e.O(0,[60802,37769,12139,51085],(()=>{return t=13557,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[99831],{39739:(e,o,r)=>{var a=r(7873),t=r(16833),n=r(12938),i=r(91069),l=r(45569);const s="pairId",d="pairId",c=(0,n.vM)({moduleType:l.fW,moduleName:s});function p(e){return c.localStorageIsEnabled()?c.getDataFromLocalStorage(e):null}function f(e){return c.cookiesAreEnabled()?c.getCookie(e):null}const g={name:s,gvlid:755,decode:e=>e&&Array.isArray(e)?{pairId:e}:void 0,getId(e){const o=p(d)||f(d);let r=[];if(o&&"string"==typeof o)try{r=r.concat(JSON.parse(atob(o)))}catch(e){(0,i.logInfo)(e)}const a=e&&e.params||{};if(a&&a.liveramp){let e=a.liveramp.storageKey||"_lr_pairId";const o=p(e)||f(e);if(o)try{const e=atob(o);if(e){const o=JSON.parse(e);o&&"object"==typeof o&&o.envelope?r=r.concat(o.envelope):(0,i.logInfo)("Pairid: Parsed object is not valid or does not contain envelope")}else(0,i.logInfo)("Pairid: Decoded value is empty")}catch(e){(0,i.logInfo)("Pairid: Error parsing JSON: ",e)}else(0,i.logInfo)("Pairid: liverampValue for pairId from storage is empty or null")}if(0!=r.length)return{id:r};(0,i.logInfo)("PairId not found.")},eids:{pairId:{source:"google.com",atype:571187}}};(0,t.bz)("userId",g),(0,a.E)("pairIdSystem")}},e=>{e.O(0,[60802,37769,12139,51085],(()=>{return o=39739,e(e.s=o);var o}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[62711],{99293:(e,t,r)=>{var i=r(7873),n=r(89766),o=r(20981),s=r(57377),a=r(43272),d=r(71371),c=r(95789),p=r(91069),u=r(63172),l=r(70433),m=r(28656),b=r(8702);const g="https://video-outstream.rubiconproject.com/apex-2.2.1.js";let x=a.$W.getConfig("rubicon")||{};a.$W.getConfig("rubicon",(e=>{(0,p.mergeDeep)(x,e.rubicon)}));let f={};var y={1:"468x60",2:"728x90",5:"120x90",7:"125x125",8:"120x600",9:"160x600",10:"300x600",13:"200x200",14:"250x250",15:"300x250",16:"336x280",17:"240x400",19:"300x100",31:"980x120",32:"250x360",33:"180x500",35:"980x150",37:"468x400",38:"930x180",39:"750x100",40:"750x200",41:"750x300",42:"2x4",43:"320x50",44:"300x50",48:"300x300",53:"1024x768",54:"300x1050",55:"970x90",57:"970x250",58:"1000x90",59:"320x80",60:"320x150",61:"1000x1000",64:"580x500",65:"640x480",66:"930x600",67:"320x480",68:"1800x1000",72:"320x320",73:"320x160",78:"980x240",79:"980x300",80:"980x400",83:"480x300",85:"300x120",90:"548x150",94:"970x310",95:"970x100",96:"970x210",101:"480x320",102:"768x1024",103:"480x280",105:"250x800",108:"320x240",113:"1000x300",117:"320x100",125:"800x250",126:"200x600",144:"980x600",145:"980x150",152:"1000x250",156:"640x320",159:"320x250",179:"250x600",195:"600x300",198:"640x360",199:"640x200",213:"1030x590",214:"980x360",221:"1x1",229:"320x180",230:"2000x1400",232:"580x400",234:"6x6",251:"2x2",256:"480x820",257:"400x600",258:"500x200",259:"998x200",261:"480x480",264:"970x1000",265:"1920x1080",274:"1800x200",278:"320x500",282:"320x400",288:"640x380",484:"720x1280",524:"1x2",548:"500x1000",550:"980x480",552:"300x200",558:"640x640",562:"300x431",564:"320x431",566:"320x300",568:"300x150",570:"300x125",572:"250x350",574:"620x891",576:"610x877",578:"980x552",580:"505x656",622:"192x160",632:"1200x450",634:"340x450",680:"970x570",682:"300x240",684:"970x550",686:"300x210",688:"300x220",690:"970x170",710:"600x250",712:"340x430"};(0,p._each)(y,((e,t)=>y[e]=t));const h=(0,n.A)({request(e,t,r,n){const{bidRequests:o}=n,s=e(t,r,n);var d;s.cur=["USD"],s.test=a.$W.getConfig("debug")?1:0,(0,u.J)(s,"ext.prebid.cache",{vastxml:{returnCreative:!0===x.returnVast}}),(0,u.J)(s,"ext.prebid.bidders",{rubicon:{integration:x.int_type||"pbjs"}}),(0,u.J)(s,"ext.prebid.targeting.pricegranularity",{ranges:{low:[{max:5,increment:.5}],medium:[{max:20,increment:.1}],high:[{max:20,increment:.01}],auto:[{max:5,increment:.05},{min:5,max:10,increment:.1},{min:10,max:20,increment:.5}],dense:[{max:3,increment:.01},{min:3,max:8,increment:.05},{min:8,max:20,increment:.5}],custom:(d=a.$W).getConfig("customPriceBucket")&&d.getConfig("customPriceBucket").buckets}[d.getConfig("priceGranularity")]});let c=(0,i.m)().installedModules;!c||c.length&&-1===c.indexOf("rubiconAnalyticsAdapter")||(0,u.J)(s,"ext.prebid.analytics",{rubicon:{"client-analytics":!0}}),function(e,t,r){let i={};const n=(0,m.D)(r,...t.map((e=>e.params.keywords)));t.forEach((t=>{const r={user:{ext:{data:{...t.params.visitor}}},site:{ext:{data:{...t.params.inventory}}}},n=e.imp.find((e=>e.ext?.prebid?.bidder?.rubicon?.video?.language));n&&(r.site.content={language:n.ext?.prebid?.bidder?.rubicon?.video?.language}),i=(0,p.mergeDeep)(i,t.ortb2||{},r);const o=a.$W.getConfig("user.id");i.user.id=i.user.id||o})),(0,p.mergeDeep)(e,i),n&&n.length&&(0,u.J)(e,"site.keywords",n.join(","));delete e?.ext?.prebid?.storedrequest}(s,o,r.ortb2),delete s?.ext?.prebid?.storedrequest,!0===x.disableFloors&&delete s.ext.prebid.floors;return o.filter((e=>"object"==typeof e.floorData)).length>0&&(s.ext.prebid.floors={enabled:!1}),s},imp(e,t,r){const i=S(t);if(i.includes(d.D4)&&1==i.length)return;const n=e(t,r);return n.id=t.adUnitCode,delete n.banner,"atf"===t.params.position&&n.video&&(n.video.pos=1),"btf"===t.params.position&&n.video&&(n.video.pos=3),delete n.ext?.prebid?.storedrequest,!0===t.params.bidonmultiformat&&i.length>1&&(0,u.J)(n,"ext.prebid.bidder.rubicon.formats",i),function(e,t){"USD"!=t.bidfloorcur&&(delete t.bidfloor,delete t.bidfloorcur);if(!t.bidfloor){let r=parseFloat((0,l.A)(e,"params.floor"));isNaN(r)||(t.bidfloor=r,t.bidfloorcur="USD")}}(t,n),n.id=f[n.id]?n.id+f[n.id]++:(f[n.id]=2,n.id),n},bidResponse(e,t,r){const i=e(t,r);i.meta.mediaType=(0,l.A)(t,"ext.prebid.type");const{bidRequest:n}=r;let[o,s]="outstream"===n.mediaTypes.video?.context?j(n,d.G_):[void 0,void 0];return i.width=t.w||o||i.playerWidth||0,i.height=t.h||s||i.playerHeight||0,i.mediaType===d.G_&&"outstream"===n.mediaTypes.video.context&&(i.renderer=function(e){const t=c.A4.install({id:e.adId,url:x.rendererUrl||g,config:x.rendererConfig||{},loaded:!1,adUnitCode:e.adUnitCode});try{t.setRender(A)}catch(e){(0,p.logWarn)("Prebid Error calling setRender on renderer",e)}return t}(i)),(0,l.A)(t,"ext.bidder.rp.advid")&&(0,u.J)(i,"meta.advertiserId",t.ext.bidder.rp.advid),i},context:{netRevenue:!1!==x.netRevenue,ttl:360},processors:o.m}),_={code:"rubicon",gvlid:52,supportedMediaTypes:[d.D4,d.G_,d.s6],isBidRequestValid:function(e){let t=!0;if("object"!=typeof e.params)return!1;for(let t=0,r=["accountId","siteId","zoneId"];t<r.length;t++)if(e.params[r[t]]=parseInt(e.params[r[t]]),isNaN(e.params[r[t]]))return(0,p.logError)("Rubicon: wrong format of accountId or siteId or zoneId."),!1;let r=S(e,!0);if(!r.length)return!1;r.includes(d.G_)&&(t=function(e){let t=!0,r=Object.prototype.toString.call([]),i=Object.prototype.toString.call(0);var n={mimes:r,protocols:r,linearity:i};return Object.keys(n).forEach((function(r){Object.prototype.toString.call((0,l.A)(e,"mediaTypes.video."+r))!==n[r]&&(t=!1,(0,p.logError)("Rubicon: mediaTypes.video."+r+" is required and must be of type: "+n[r]))})),t}(e));const i=[d.D4,d.s6].filter((e=>r.includes(e))).length>0;return i?t&&i:t},buildRequests:function(e,t){let r,i=[],n=[];if(r=e.filter((e=>{const t=S(e)||[],{length:r}=t,{bidonmultiformat:i,video:n}=e.params||{};return 1===r&&(t.includes(d.G_)||t.includes(d.s6))||2===r&&!t.includes(d.D4)||n&&t.includes(d.G_)||i&&(t.includes(d.G_)||t.includes(d.s6))})),r&&r.length){const e=h.toORTB({bidRequests:r,bidderRequest:t});R(),n.push({method:"POST",url:`https://${x.videoHost||"prebid-server"}.rubiconproject.com/openrtb2/auction`,data:e,bidRequest:r})}const o=e.filter((e=>{const t=S(e)||[],{bidonmultiformat:r,video:i}=e.params||{};return t.includes(d.D4)&&(1===t.length||r||!r&&!i||!r&&i&&!t.includes(d.G_))}));if(!0!==x.singleRequest)i=n.concat(o.map((e=>{const r=_.createSlotParams(e,t);return{method:"GET",url:`https://${x.bannerHost||"fastlane"}.rubiconproject.com/a/api/fastlane.json`,data:_.getOrderedParams(r).reduce(((e,t)=>{const i=r[t];return(0,p.isStr)(i)&&""!==i||(0,p.isNumber)(i)?`${e}${I(t,i)}&`:e}),"")+`slots=1&rand=${Math.random()}`,bidRequest:e}})));else{const e=o.reduce(((e,t)=>((e[t.params.siteId]=e[t.params.siteId]||[]).push(t),e)),{}),r=10;i=n.concat(Object.keys(e).reduce(((i,n)=>{var o,s;return(o=e[n],s=r,o.map(((e,t)=>t%s==0?o.slice(t,t+s):null)).filter((e=>e))).forEach((e=>{const r=_.combineSlotUrlParams(e.map((e=>_.createSlotParams(e,t))));i.push({method:"GET",url:`https://${x.bannerHost||"fastlane"}.rubiconproject.com/a/api/fastlane.json`,data:_.getOrderedParams(r).reduce(((e,t)=>{const i=r[t];return(0,p.isStr)(i)&&""!==i||(0,p.isNumber)(i)?`${e}${I(t,i)}&`:e}),"")+`slots=${e.length}&rand=${Math.random()}`,bidRequest:e})})),i}),[]))}return i},getOrderedParams:function(e){const t=/^tg_v/,r=/^tg_i/,i=/^eid_|^tpid_/,n=["account_id","site_id","zone_id","size_id","alt_size_ids","p_pos","gdpr","gdpr_consent","us_privacy","gpp","gpp_sid","rp_schain"].concat(Object.keys(e).filter((e=>i.test(e)))).concat(["x_liverampidl","ppuid","rf","p_geo.latitude","p_geo.longitude","kw"]).concat(Object.keys(e).filter((e=>t.test(e)))).concat(Object.keys(e).filter((e=>r.test(e)))).concat(["tk_flint","x_source.tid","l_pb_bid_id","p_screen_res","o_ae","o_cdep","rp_floor","rp_secure","tk_user_key"]);return n.concat(Object.keys(e).filter((e=>-1===n.indexOf(e))))},combineSlotUrlParams:function(e){if(1===e.length)return e[0];const t=e.reduce((function(t,r,i){return Object.keys(r).forEach((function(n){t.hasOwnProperty(n)||(t[n]=new Array(e.length)),t[n].splice(i,1,r[n])})),t}),{}),r=new RegExp("^([^;]*)(;\\1)+$");return Object.keys(t).forEach((function(e){const i=t[e].join(";"),n=i.match(r);t[e]=n?n[1]:i})),t},createSlotParams:function(e,t){e.startTime=(new Date).getTime();const r=e.params,i=j(e,"banner"),[n,o]=r.latLong||[],s={account_id:r.accountId,site_id:r.siteId,zone_id:r.zoneId,size_id:i[0],alt_size_ids:i.slice(1).join(",")||void 0,rp_floor:(r.floor=parseFloat(r.floor))>=.01?r.floor:void 0,rp_secure:"1",tk_flint:`${x.int_type||"pbjs_lite"}_v9.50.0`,"x_source.tid":t.ortb2?.source?.tid,"x_imp.ext.tid":e.ortb2Imp?.ext?.tid,l_pb_bid_id:e.bidId,o_cdep:e.ortb2?.device?.ext?.cdep,ip:e.ortb2?.device?.ip,ipv6:e.ortb2?.device?.ipv6,p_screen_res:[window.screen.width,window.screen.height].join("x"),tk_user_key:r.userId,"p_geo.latitude":isNaN(parseFloat(n))?void 0:parseFloat(n).toFixed(4),"p_geo.longitude":isNaN(parseFloat(o))?void 0:parseFloat(o).toFixed(4),"tg_fl.eid":e.code,rf:v(e,t)};if("function"==typeof e.getFloor&&!x.disableFloors){let t;try{t=e.getFloor({currency:"USD",mediaType:"banner",size:"*"})}catch(e){(0,p.logError)("Rubicon: getFloor threw an error: ",e)}s.rp_hard_floor=(0,p.isPlainObject)(t)&&"USD"===t.currency&&!isNaN(parseInt(t.floor))?t.floor:void 0}!0===r.bidonmultiformat&&(0,l.A)(e,"mediaTypes")&&Object.keys(e.mediaTypes).length>1&&(s.p_formats=Object.keys(e.mediaTypes).join(","));let c={1:"atf",3:"btf"}[(0,l.A)(e,"mediaTypes.banner.pos")]||"";s.p_pos="atf"===r.position||"btf"===r.position?r.position:c;const u=a.$W.getConfig("user.id");return u&&(s.ppuid=u),e?.ortb2Imp?.ext?.ae&&(s.o_ae=1),"number"==typeof e?.ortb2?.site?.mobile&&(s["p_site.mobile"]=e.ortb2.site.mobile),function(e,t){if(!1===x.readTopics)return;let r=[1,2,5,6,7,507].concat(x.sendSiteSegtax?.map((e=>Number(e)))||[]),i=[4,508].concat(x.sendUserSegtax?.map((e=>Number(e)))||[]),n=e.ortb2?.user?.data||[],o=e.ortb2?.site?.content?.data||[];n.forEach($(t,"v",i)),o.forEach($(t,"i",r))}(t,s),e?.ortb2?.user?.ext?.eids&&e.ortb2.user.ext.eids.forEach((e=>{let{source:t,uids:r=[],inserter:i,matcher:n,mm:o,ext:a={}}=e;try{const e=r[0];if(!e)return;const a=e=>[e.id,e.atype||"","",i||"",n||"",o||"",e?.ext?.rtiPartner||e?.ext?.rtipartner||""].join("^"),d=a(e);if(s[`eid_${t}`]=d,!s.ppuid){const e=r.find((e=>"ppuid"===e.ext?.stype));e?.id&&(s.ppuid=e.id)}}catch(e){(0,p.logWarn)("Rubicon: error reading eid:",{source:t,uids:r},e)}})),t.gdprConsent&&("boolean"==typeof t.gdprConsent.gdprApplies&&(s.gdpr=Number(t.gdprConsent.gdprApplies)),s.gdpr_consent=t.gdprConsent.consentString),t.uspConsent&&(s.us_privacy=encodeURIComponent(t.uspConsent)),t.gppConsent?.gppString&&(s.gpp=t.gppConsent.gppString,s.gpp_sid=t.gppConsent?.applicableSections?.toString()),s.rp_maxbids=t.bidLimit||1,function(e,t,r){const i={user:{ext:{data:{...e.params.visitor}}},site:{ext:{data:{...e.params.inventory}}}};e.params.keywords&&(i.site.keywords=(0,p.isArray)(e.params.keywords)?e.params.keywords.join(","):e.params.keywords);let n=(0,p.mergeDeep)({},e.ortb2||{},i),o=(0,l.A)(e.ortb2Imp,"ext")||{},s=(0,l.A)(e.ortb2Imp,"ext.data")||{};const a=(0,l.A)(e,"ortb2Imp.ext.gpid"),c=(0,l.A)(n,"regs.ext.dsa"),u={user:[4],site:[1,2,5,6,7]},m={user:"tg_v.",site:"tg_i.",adserver:"tg_i.dfp_ad_unit_code",pbadslot:"tg_i.pbadslot",keywords:"kw"},b=function(e,t,r){return"data"===t&&Array.isArray(e)?e.filter((e=>e.segment&&(0,l.A)(e,"ext.segtax")&&u[r]&&-1!==u[r].indexOf((0,l.A)(e,"ext.segtax")))).map((e=>{let t=e.segment.filter((e=>e.id)).reduce(((e,t)=>(e.push(t.id),e)),[]);if(t.length>0)return t.toString()})).toString():("object"!=typeof e||Array.isArray(e))&&void 0!==e?Array.isArray(e)?e.filter((e=>{if("object"!=typeof e&&void 0!==e)return e.toString();(0,p.logWarn)("Rubicon: Filtered value: ",e,"for key",t,": Expected value to be string, integer, or an array of strings/ints")})).toString():e.toString():void 0},g=function(e,t,i){let n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=b(e,i,t),s=m[i]&&n?`${m[i]}`:"data"===i?`${m[t]}iab`:`${m[t]}${i}`;r[s]=r[s]?r[s].concat(",",o):o};if(t===d.D4){["site","user"].forEach((e=>{Object.keys(n[e]).forEach((t=>{"site"===e&&"content"===t&&n[e][t].data?g(n[e][t].data,e,"data"):"ext"!==t?g(n[e][t],e,t):n[e][t].data&&Object.keys(n[e].ext.data).forEach((t=>{g(n[e].ext.data[t],e,t,!1)}))}))})),Object.keys(s).forEach((e=>{"adserver"!==e?g(s[e],"site",e):"gam"===s[e].name&&g(s[e].adslot,name,e)})),a&&(r.p_gpid=a),c&&Object.keys(c).length&&(0,p.pick)(c,["dsainfo",e=>r.dsainfo=e,"dsarequired",e=>r.dsarequired=e,"pubrender",e=>r.dsapubrender=e,"datatopub",e=>r.dsadatatopubs=e,"transparency",e=>{Array.isArray(e)&&e.length&&(r.dsatransparency=e.reduce(((e,t)=>{const r=t.domain||"";if(!r)return e;const i=t.dsaparams||t.params;return Array.isArray(i)&&0!==i.length?(e&&(e+="~~"),e+`${r}~${i.join("_")}`):e}),""))}]),r["tg_i.pbadslot"]&&delete r["tg_i.dfp_ad_unit_code"];const e=(0,l.A)(n,"device.sua");e&&!1!==x.chEnabled&&(0,p.pick)(e,["architecture",e=>r.m_ch_arch=e,"bitness",e=>r.m_ch_bitness=e,"browsers",e=>{if(!Array.isArray(e))return;const[t,i]=e.reduce(((e,t)=>(e[0].push(`"${t?.brand}"|v="${t?.version?.[0]}"`),t?.version?.length>1&&e[1].push(`"${t?.brand}"|v="${t?.version?.join?.(".")}"`),e)),[[],[]]);r.m_ch_ua=t?.join?.(","),r.m_ch_full_ver=i?.join?.(",")},"mobile",e=>r.m_ch_mobile=`?${e}`,"model",e=>r.m_ch_model=e,"platform",e=>{r.m_ch_platform=e?.brand,r.m_ch_platform_ver=e?.version?.join?.(".")}])}else Object.keys(o).length&&(0,p.mergeDeep)(r.imp[0].ext,o),a&&(r.imp[0].ext.gpid=a),(0,p.mergeDeep)(r,n)}(e,d.D4,s),!0===a.$W.getConfig("coppa")&&(s.coppa=1),e.schain&&C(e.schain)&&(s.rp_schain=_.serializeSupplyChain(e.schain)),s},serializeSupplyChain:function(e){if(!C(e))return"";const{ver:t,complete:r,nodes:i}=e;return`${t},${r}!${_.serializeSupplyChainNodes(i)}`},serializeSupplyChainNodes:function(e){const t=["asi","sid","hp","rid","name","domain"];return e.map((e=>t.map((t=>encodeURIComponent(e[t]||""))).join(","))).join("!")},interpretResponse:function(e,t){e=e.body;const{data:r}=t;if(!e||"object"!=typeof e)return[];if(e.seatbid){const t=(0,l.A)(e,"ext.errors.rubicon");Array.isArray(t)&&t.length>0&&(0,p.logWarn)("Rubicon: Error in video response");return h.fromORTB({request:r,response:e}).bids}let i,n=e.ads,o=0;const{bidRequest:s}=t;if("object"==typeof s&&!Array.isArray(s)&&S(s).includes(d.G_)&&"object"==typeof n&&(n=n[s.adUnitCode]),!Array.isArray(n)||n.length<1)return[];let a=n.reduce(((t,r,n)=>{if(r.impression_id&&i===r.impression_id?o++:i=r.impression_id,"ok"!==r.status)return t;const a=Array.isArray(s)?s[n-o]:s;if(a&&"object"==typeof a){let e={requestId:a.bidId,currency:"USD",creativeId:r.creative_id||`${r.network||""}-${r.advertiser||""}`,cpm:r.cpm||0,dealId:r.deal,ttl:360,netRevenue:!1!==x.netRevenue,rubicon:{advertiserId:r.advertiser,networkId:r.network},meta:{advertiserId:r.advertiser,networkId:r.network,mediaType:d.D4}};r.creative_type&&(e.mediaType=r.creative_type),r.dsa&&Object.keys(r.dsa).length&&(e.meta.dsa=r.dsa),r.adomain&&(e.meta.advertiserDomains=Array.isArray(r.adomain)?r.adomain:[r.adomain]),r.emulated_format&&(e.meta.mediaType=r.emulated_format),r.creative_type===d.G_?(e.width=a.params.video.playerWidth,e.height=a.params.video.playerHeight,e.vastUrl=r.creative_depot_url,e.impression_id=r.impression_id,e.videoCacheKey=r.impression_id):(e.ad=(c=r.script,`<html>\n<head><script type='text/javascript'>inDapIF=true;<\/script></head>\n<body style='margin : 0; padding: 0;'>\n\x3c!-- Rubicon Project Ad Tag --\x3e\n<div data-rp-impression-id='${r.impression_id}'>\n<script type='text/javascript'>${c}<\/script>\n</div>\n</body>\n</html>`),[e.width,e.height]=y[r.size_id].split("x").map((e=>Number(e)))),e.rubiconTargeting=(Array.isArray(r.targeting)?r.targeting:[]).reduce(((e,t)=>(e[t.key]=t.values[0],e)),{rpfl_elemid:a.adUnitCode}),t.push(e)}else(0,p.logError)(`Rubicon: bidRequest undefined at index position:${n}`,s,e);var c;return t}),[]).sort(((e,t)=>(t.cpm||0)-(e.cpm||0))),c=e.component_auction_config?.map((e=>({config:e,bidId:e.bidId})));return c?{bids:a,paapi:c}:a},getUserSyncs:function(e,t,r,i,n){if(!w&&e.iframeEnabled){let e=(0,b.d)(r,i,n);return e=Object.keys(e).length?`?${(0,p.formatQS)(e)}`:"",w=!0,{type:"iframe",url:`https://${x.syncHost||"eus"}.rubiconproject.com/usync.html`+e}}}};function v(e,t){let r;return r=e.params.referrer?e.params.referrer:t.refererInfo.page,e.params.secure?r.replace(/^http:/i,"https:"):r}function A(e){const t=document.getElementById(e.adUnitCode);!function(e){const t=e.querySelector("div[id^='google_ads']");t&&t.style.setProperty("display","none")}(t),function(e){const t=e.querySelector("script[id^='sas_script']"),r=t&&t.nextSibling;r&&"iframe"===r.localName&&r.style.setProperty("display","none")}(t);const r={...{align:"center",position:"append",closeButton:!1,label:void 0,collapse:!0},...e.renderer.getConfig()};e.renderer.push((()=>{window.MagniteApex.renderAd({width:e.width,height:e.height,vastUrl:e.vastUrl,placement:{attachTo:`#${e.adUnitCode}`,align:r.align,position:r.position},closeButton:r.closeButton,label:r.label,collapse:r.collapse})}))}function j(e,t){let r=e.params;if(t===d.G_){let t=[];return r.video&&r.video.playerWidth&&r.video.playerHeight?t=[r.video.playerWidth,r.video.playerHeight]:Array.isArray((0,l.A)(e,"mediaTypes.video.playerSize"))&&1===e.mediaTypes.video.playerSize.length?t=e.mediaTypes.video.playerSize[0]:Array.isArray(e.sizes)&&e.sizes.length>0&&Array.isArray(e.sizes[0])&&e.sizes[0].length>1&&(t=e.sizes[0]),t}let i=[];return Array.isArray(r.sizes)?i=r.sizes:void 0!==(0,l.A)(e,"mediaTypes.banner.sizes")?i=k(e.mediaTypes.banner.sizes):Array.isArray(e.sizes)&&e.sizes.length>0?i=k(e.sizes):(0,p.logWarn)("Rubicon: no sizes are setup or found"),function(e){const t=[15,2,9];return e.sort(((e,r)=>{const i=t.indexOf(e),n=t.indexOf(r);return i>-1||n>-1?-1===i?1:-1===n?-1:i-n:e-r}))}(i)}function $(e,t,r){return i=>{const n=Number(i.ext?.segtax);r.includes(n)&&(e[`tg_${t}.tax${n}`]=i.segment?.map((e=>e.id)).join(","))}}function k(e){return(0,p.parseSizesInput)(e).reduce(((e,t)=>{let r=parseInt(y[t],10);return r&&e.push(r),e}),[])}function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=[];if(function(e){let t=void 0!==(0,l.A)(e,`mediaTypes.${d.G_}`),r=void 0!==(0,l.A)(e,`mediaTypes.${d.D4}`),i=void 0!==(0,l.A)(e,"params.bidonmultiformat"),n="object"!=typeof(0,l.A)(e,"params.video");return!(!t||!i)||(r&&n&&(t=!1),t&&n&&(0,u.J)(e,"params.video",{}),t)}(e)){if(-1===["outstream","instream"].indexOf((0,l.A)(e,`mediaTypes.${d.G_}.context`)))return t&&(0,p.logError)("Rubicon: mediaTypes.video.context must be outstream or instream"),r;if(j(e,d.G_).length<2)return t&&(0,p.logError)("Rubicon: could not determine the playerSize of the video"),r;t&&(0,p.logMessage)("Rubicon: making video request for adUnit",e.adUnitCode),r.push(d.G_)}if(void 0!==(0,l.A)(e,`mediaTypes.${d.s6}`)&&r.push(d.s6),void 0!==(0,l.A)(e,`mediaTypes.${d.D4}`)){if(0===j(e,d.D4).length)return t&&(0,p.logError)("Rubicon: could not determine the sizes for banner request"),r;t&&(0,p.logMessage)("Rubicon: making banner request for adUnit",e.adUnitCode),r.push(d.D4)}return r}const R=()=>f={};function C(e){let t=!1;const r=["asi","sid","hp"];return e.nodes?(t=e.nodes.reduce(((e,t)=>e?r.every((e=>t.hasOwnProperty(e))):e),!0),t||(0,p.logError)("Rubicon: required schain params missing"),t):t}function I(e,t){return"rp_schain"===e?`rp_schain=${t}`:`${e}=${encodeURIComponent(t)}`}var w=!1;(0,s.a$)(_),(0,i.E)("rubiconBidAdapter")}},e=>{e.O(0,[60802,58498,97247,47618,46550,37769,12139,51085],(()=>{return t=99293,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[11613],{91473:(n,i,e)=>{var s=e(7873),o=e(43272),t=e(11445),r=e(91069),a=e(70433),c=e(63172),h=e(1e3);const f="Invalid schain object found: ",d=" should be a string",l=" should be an Integer",u=" should be an object",g=" should be an Array",b={STRICT:"strict",RELAXED:"relaxed",OFF:"off"},p=[];function m(n,i){let e=b.STRICT;if(function(n){return!(void 0===n||!(0,r.isPlainObject)(n)&&((0,r.logError)(f+"the following schain config will not be used as schain is not an object.",n),1))}(n)){if((0,r.isStr)(n.validation)&&-1!=p.indexOf(n.validation)&&(e=n.validation),e===b.OFF)return n.config;if(function(n,i){let e="Detected something wrong within an schain config:",s="";function o(n){s+="\n"+n}function t(){!0===i?(0,r.logError)(e,n,s):(0,r.logWarn)(e,n,s)}if(!(0,r.isPlainObject)(n)&&(o("schain.config"+u),t(),i))return!1;if((0,r.isNumber)(n.complete)&&(0,r.isInteger)(n.complete)||o("schain.config.complete"+l),(0,r.isStr)(n.ver)||o("schain.config.ver"+d),n.hasOwnProperty("ext")&&((0,r.isPlainObject)(n.ext)||o("schain.config.ext"+u)),(0,r.isArray)(n.nodes))n.nodes.forEach(((n,i)=>{(0,r.isStr)(n.asi)||o(`schain.config.nodes[${i}].asi`+d),(0,r.isStr)(n.sid)||o(`schain.config.nodes[${i}].sid`+d),(0,r.isNumber)(n.hp)&&(0,r.isInteger)(n.hp)||o(`schain.config.nodes[${i}].hp`+l),n.hasOwnProperty("rid")&&((0,r.isStr)(n.rid)||o(`schain.config.nodes[${i}].rid`+d)),n.hasOwnProperty("name")&&((0,r.isStr)(n.name)||o(`schain.config.nodes[${i}].name`+d)),n.hasOwnProperty("domain")&&((0,r.isStr)(n.domain)||o(`schain.config.nodes[${i}].domain`+d)),n.hasOwnProperty("ext")&&((0,r.isPlainObject)(n.ext)||o(`schain.config.nodes[${i}].ext`+u))}));else if(o("schain.config.nodes"+g),t(),i)return!1;return!(s.length>0&&(t(),i))}(n.config,!(e!==b.STRICT)))return n.config;(0,r.logError)(f+`due to the 'strict' validation setting, this schain config will not be passed to bidder '${i}'.  See above error for details.`)}return null}function O(n,i){const e=o.$W.getConfig("schain"),s=o.$W.getBidderConfig();i.forEach((n=>{let i=n.bidderCode,o=function(n){return s[n]&&s[n].schain||e}(i);n.bids.forEach((n=>{let e=m(o,i);e&&(n.schain=(0,r.deepClone)(e))}))})),n(i)}(0,r._each)(b,(n=>p.push(n))),t.Ay.makeBidRequests.after(O),(0,h.pS)({type:h.S3,name:"sourceExtSchain",fn:function(n,i,e){if(!(0,a.A)(n,"source.ext.schain")){const i=(0,a.A)(e,"bidRequests.0.schain");i&&(0,c.J)(n,"source.ext.schain",i)}}}),(0,s.E)("schain")}},n=>{n.O(0,[60802,37769,12139,51085],(()=>{return i=91473,n(n.s=i);var i}));n.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[84258],{95282:(t,e,n)=>{var i=n(7873),r=n(34278),s=n(57377),o=n(43272),a=n(71371),p=n(91069),c=n(88944);const d={[a.D4]:"display",[a.G_]:"video"},u="fixed",g="mobile",l="unknown",m="USD";const b=()=>{const t=navigator.connection||navigator.mozConnection||navigator.webkitConnection||{};switch(t.type||t.effectiveType){case"wifi":case"ethernet":return u;case"cellular":case"wimax":return g;default:return/iPad|iPhone|iPod/.test(navigator.userAgent)||/android/i.test(navigator.userAgent)?l:u}};function h(t){return!!t.mediaTypes&&!!t.mediaTypes.video}function y(t){const e=t.params;return!!e.publisherId&&!!e.adUnitId}function f(t){const e=S(t);return!!t.params.publisherId&&!!t.params.adUnitId&&h(t)&&!!e.playerSize&&(0,p.isArray)(e.playerSize)&&e.playerSize.length>0}function v(t){const e=t.params,n=(0,p._map)(Object.keys(t.mediaTypes),(function(t){return d[t]})),i={id:t.bidId,transactionId:t.ortb2Imp?.ext?.tid,gpid:t.ortb2Imp?.ext?.gpid,sizes:t.sizes,supplyTypes:n,adUnitId:e.adUnitId,adUnitCode:t.adUnitCode,geom:T(t.adUnitCode),placement:e.placement,requestCount:t.bidderRequestsCount||1};h(t)&&(i.videoParams=S(t));const r=function(t){let e={};return"function"==typeof t.getFloor&&(e=t.getFloor({currency:m,mediaType:"*",size:"*"})),e?.floor}(t);return r&&(i.bidFloor=r),i}function S(t){const e=t.mediaTypes.video||{};return e.playerSize&&(e.w=e.playerSize[0][0],e.h=e.playerSize[0][1]),e}function I(t){const e="display"===(n=t.mediaType)?a.D4:"video"===n?a.G_:n;var n;const i={requestId:t.bidId,cpm:t.price,width:t.width,height:t.height,creativeId:t.creativeId,currency:t.currency,netRevenue:!0,mediaType:e,ttl:t.ttl,nurl:t.nurl,meta:{advertiserDomains:t&&t.adomain&&t.adomain.length>0?t.adomain:[],mediaType:t.realMediaType}};return e===a.G_?i.vastXml=t.content:i.ad=t.content,i}function C(){const t=(()=>{try{const t=performance.getEntriesByType("navigation")[0];return Math.round(t.responseStart-t.startTime)}catch(t){try{const t=performance.timing;return Math.round(t.responseStart-t.fetchStart)}catch(t){return 0}}})();return t>=0&&t<=performance.now()?t:0}function T(t){const e=document.getElementById(t);if(e){const{top:t,left:n,width:i,height:s}=(0,r.G)(e),o={width:(0,p.getWinDimensions)().innerWidth,height:(0,p.getWinDimensions)().innerHeight};return{scrollY:(0,c.g)().top||0,top:t,left:n,width:i,height:s,viewport:o}}}const w={code:"seedtag",gvlid:157,aliases:["st"],supportedMediaTypes:[a.D4,a.G_],isBidRequestValid(t){const e=h(t),n=function(t){return!!t.mediaTypes&&!!t.mediaTypes.banner}(t);return e&&n?f(t)&&"outstream"===S(t).context&&y(t):e?f(t):!!n&&y(t)},buildRequests(t,e){const n={url:e.refererInfo.page,publisherToken:t[0].params.publisherId,cmp:!!e.gdprConsent,timeout:e.timeout,version:"9.50.0",connectionType:b(),auctionStart:e.auctionStart||Date.now(),ttfb:C(),bidRequests:(0,p._map)(t,v),user:{topics:[],eids:[]},site:{}};if(n.cmp){const t=e.gdprConsent.gdprApplies;void 0!==t&&(n.ga=t),n.cd=e.gdprConsent.consentString}e.uspConsent&&(n.uspConsent=e.uspConsent),t[0].schain&&(n.schain=t[0].schain);let i=o.$W.getConfig("coppa");i&&(n.coppa=i),e.gppConsent?n.gppConsent={gppString:e.gppConsent.gppString,applicableSections:e.gppConsent.applicableSections}:e.ortb2?.regs?.gpp&&(n.gppConsent={gppString:e.ortb2.regs.gpp,applicableSections:e.ortb2.regs.gpp_sid}),e.ortb2?.user?.data&&(n.user.topics=e.ortb2.user.data),t[0]&&t[0].userIdAsEids&&(n.user.eids=t[0].userIdAsEids),e.ortb2?.bcat&&(n.bcat=e.ortb2?.bcat),e.ortb2?.badv&&(n.badv=e.ortb2?.badv),e.ortb2?.device?.sua&&(n.sua=e.ortb2.device.sua),e.ortb2?.site?.cat&&(n.site.cat=e.ortb2.site.cat),e.ortb2?.site?.cattax&&(n.site.cattax=e.ortb2.site.cattax),e.ortb2?.site?.pagecat&&(n.site.pagecat=e.ortb2.site.pagecat);return{method:"POST",url:"https://s.seedtag.com/c/hb/bid",data:JSON.stringify(n)}},interpretResponse:function(t){const e=t.body;return e&&e.bids&&(0,p.isArray)(e.bids)?(0,p._map)(e.bids,(function(t){return I(t)})):[]},getUserSyncs(t,e){const n=e[0];if(t.iframeEnabled&&n){const t=n.body.cookieSync;return t?[{type:"iframe",url:t}]:[]}return[]},onTimeout(t){const e=function(t){let e="";if((0,p.isArray)(t)&&t[0]&&(0,p.isArray)(t[0].params)&&t[0].params[0]){const n=t[0].params[0],i=t[0].timeout;e="?publisherToken="+n.publisherId+"&adUnitId="+n.adUnitId+"&timeout="+i}return"https://s.seedtag.com/se/hb/timeout"+e}(t);(0,p.triggerPixel)(e)},onBidWon:function(t){t&&t.nurl&&(0,p.triggerPixel)(t.nurl)}};(0,s.a$)(w),(0,i.E)("seedtagBidAdapter")}},t=>{t.O(0,[32316,60802,37769,12139,51085],(()=>{return e=95282,t(t.s=e);var e}));t.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[54026],{88518:(e,t,a)=>{var r=a(7873),s=a(91069),i=a(70433),o=a(71371),d=a(43272),n=a(6036),p=a(57377),l=a(29495);const m={code:"smartadserver",gvlid:45,aliases:["smart"],supportedMediaTypes:[o.D4,o.G_],isBidRequestValid:function(e){return!!(e.params&&e.params.siteId&&e.params.pageId&&e.params.formatId)},serializeSupplyChain:function(e){if(!e||!e.nodes)return null;const t=["asi","sid","hp","rid","name","domain"];return`${e.ver},${e.complete}!`+e.nodes.map((e=>t.map((t=>e[t]?encodeURIComponent(e[t]):"")).join(","))).join("!")},adaptBannerSizes:function(e){return e.map((e=>({w:e[0],h:e[1]})))},fillPayloadForVideoBidRequest:function(e,t,a){const r=t.playerSize[0],i={maxbitrate:"vbrmax",maxduration:"vdmax",minbitrate:"vbrmin",minduration:"vdmin",placement:"vpt",plcmt:"vplcmt",skip:"skip"};e.mediaType=o.G_,e.isVideo="instream"===t.context,e.videoData={};for(const[a,r]of Object.entries(i))e.videoData={...e.videoData,...this.getValuableProperty(r,t[a])};e.videoData={...e.videoData,...this.getValuableProperty("playerWidth",r[0]),...this.getValuableProperty("playerHeight",r[1]),...this.getValuableProperty("adBreak",this.getStartDelayForVideoBidRequest(t,a)),...this.getValuableProperty("videoProtocol",this.getProtocolForVideoBidRequest(t,a)),...(0,s.isArrayOfNums)(t.api)&&t.api.length?{iabframeworks:t.api.toString()}:{},...(0,s.isArrayOfNums)(t.playbackmethod)&&t.playbackmethod.length?{vpmt:t.playbackmethod}:{}}},getValuableProperty:function(e,t){return"string"==typeof e&&(0,s.isInteger)(t)&&t?{[e]:t}:{}},getProtocolForVideoBidRequest:function(e,t){return void 0!==t&&t.protocol?t.protocol:void 0!==e&&Array.isArray(e.protocols)?Math.max.apply(Math,e.protocols):null},getStartDelayForVideoBidRequest:function(e,t){if(t?.startDelay)return t.startDelay;if(e?.startdelay){if(e.startdelay>0||-1==e.startdelay)return 2;if(-2==e.startdelay)return 3}return 1},createServerRequest:function(e,t){return{method:"POST",url:(void 0!==t?t:"https://prg.smartadserver.com")+"/prebid/v1",data:JSON.stringify(e),options:{browsingTopics:!1}}},buildRequests:function(e,t){const a=(0,l.b)(t),r=(0,i.A)(t,"ortb2.user.data",d.$W.getAnyConfig("ortb2.user.data")),p=(0,i.A)(t,"ortb2.site.content.data",d.$W.getAnyConfig("ortb2.site.content.data"));return e.reduce(((e,l)=>{let u={siteid:l.params.siteId,pageid:l.params.pageId,formatid:l.params.formatId,currencyCode:a,targeting:l.params.target&&""!==l.params.target?l.params.target:void 0,buid:l.params.buId&&""!==l.params.buId?l.params.buId:void 0,appname:l.params.appName&&""!==l.params.appName?l.params.appName:void 0,ckid:l.params.ckId||0,tagId:l.adUnitCode,pageDomain:t&&t.refererInfo&&t.refererInfo.page?t.refererInfo.page:void 0,transactionId:l.ortb2Imp?.ext?.tid,timeout:d.$W.getConfig("bidderTimeout"),bidId:l.bidId,prebidVersion:"9.50.0",schain:m.serializeSupplyChain(l.schain),sda:r,sdc:p};const c=(0,i.A)(l,"ortb2Imp.ext.gpid")||(0,i.A)(l,"ortb2Imp.ext.data.pbadslot");c&&(u.gpid=c);const g=(0,i.A)(l,"ortb2.regs.ext.dsa");g&&(u.dsa=g),t&&(t.gdprConsent&&(u.addtl_consent=t.gdprConsent.addtlConsent,u.gdpr_consent=t.gdprConsent.consentString,u.gdpr=t.gdprConsent.gdprApplies),t.gppConsent&&(u.gpp=t.gppConsent.gppString,u.gpp_sid=t.gppConsent.applicableSections),t.uspConsent&&(u.us_privacy=t.uspConsent)),l&&l.userIdAsEids&&(u.eids=l.userIdAsEids),t&&t.uspConsent&&(u.us_privacy=t.uspConsent);const y=(0,i.A)(l,"mediaTypes.banner"),b=(0,i.A)(l,"mediaTypes.video"),f=b&&("instream"===b.context||"outstream"===b.context);if(y||f){let t;if(y){if(t=o.D4,u.sizes=m.adaptBannerSizes(y.sizes),f){let t=(0,s.deepClone)(u);m.fillPayloadForVideoBidRequest(t,b,l.params.video),t.bidfloor=l.params.bidfloor||(0,n.M)(l,a,o.G_),e.push(m.createServerRequest(t,l.params.domain))}}else t=o.G_,m.fillPayloadForVideoBidRequest(u,b,l.params.video);u.bidfloor=l.params.bidfloor||(0,n.M)(l,a,t),e.push(m.createServerRequest(u,l.params.domain))}else e.push({});return e}),[])},interpretResponse:function(e,t){const a=[];let r=e.body;try{if(r&&!r.isNoAd&&(r.ad||r.adUrl)){const e=JSON.parse(t.data);let i={requestId:e.bidId,cpm:r.cpm,width:r.width,height:r.height,creativeId:r.creativeId,dealId:r.dealId,currency:r.currency,netRevenue:r.isNetCpm,ttl:r.ttl,dspPixels:r.dspPixels,meta:{...(0,s.isArray)(r.adomain)&&!(0,s.isEmpty)(r.adomain)?{advertiserDomains:r.adomain}:{},...(0,s.isEmpty)(r.dsa)?{}:{dsa:r.dsa}}};e.mediaType===o.G_?(i.mediaType=o.G_,i.vastUrl=r.adUrl,i.vastXml=r.ad,i.content=r.ad):(i.adUrl=r.adUrl,i.ad=r.ad),a.push(i)}}catch(e){(0,s.logError)("Error while parsing smart server response",e)}return a},getUserSyncs:function(e,t){const a=[];return e.iframeEnabled&&t.length>0&&null!=t[0].body.cSyncUrl?a.push({type:"iframe",url:t[0].body.cSyncUrl}):e.pixelEnabled&&t.length>0&&void 0!==t[0].body.dspPixels&&t[0].body.dspPixels.forEach((function(e){a.push({type:"image",url:e})})),a}};(0,p.a$)(m),(0,r.E)("smartadserverBidAdapter")}},e=>{e.O(0,[19147,90467,60802,37769,12139,51085],(()=>{return t=88518,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[99097],{45011:(e,r,t)=>{var o=t(7873),i=t(57377),n=t(71371),a=t(43272),s=t(91069),d=t(63172),u=t(12938),p=t(68044),g=t(89766);const l="taboola",c="USD",b="user-id",f=`taboola global:${b}`,m="https://beacon.bidder.taboola.com",y={storageManager:(0,u.vM)({bidderCode:l}),getUserId:()=>{const{getFromLocalStorage:e,getFromCookie:r,getFromTRC:t}=y;try{return e()||r()||t()}catch(e){return 0}},getFromCookie(){const{cookiesAreEnabled:e,getCookie:r}=y.storageManager;if(e()){const e=r("trc_cookie_storage");let t;if(e&&(t=y.getCookieDataByKey(e,b)),t)return t;if(t=r("t_gid"),t)return t;if(t=r("t_pt_gid"),t)return t;const o=r("tbla_id");if(o)return o}},getCookieDataByKey(e,r){if(!e)return;const[,t=""]=e.split(`${r}=`);return t},getFromLocalStorage(){const{hasLocalStorage:e,localStorageIsEnabled:r,getDataFromLocalStorage:t}=y.storageManager;if(e()&&r())return t(f)},getFromTRC:()=>window.TRC?window.TRC.user_id:0},h={getPageUrl:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e?.page||(0,s.getWindowSelf)().location.href},getReferrer:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e?.ref||(0,s.getWindowSelf)().document.referrer}},R=(0,g.A)({context:{netRevenue:!0,mediaType:n.D4,ttl:300},imp(e,r,t){let o=e(r,t);return function(e,r){const{tagId:t,position:o}=e.params;if(r.banner=function(e,r){return{...x(e.sizes),pos:r}}(e,o),r.tagid=t,"function"==typeof e.getFloor){const t=e.getFloor({currency:c,size:"*"});(0,s.isPlainObject)(t)&&t.currency===c&&!isNaN(parseFloat(t.floor))&&(r.bidfloor=parseFloat(t.floor),r.bidfloorcur=c)}else{const{bidfloor:t=null,bidfloorcur:o=c}=e.params;r.bidfloor=t,r.bidfloorcur=o}(0,d.J)(r,"ext.gpid",e?.ortb2Imp?.ext?.gpid)}(r,o),o},request(e,r,t,o){const i=e(r,t,o);return function(e,r,t){const{refererInfo:o,gdprConsent:i={},uspConsent:n}=e,s=function(e,r,t){let{publisherId:o}=e;const{getPageUrl:i,getReferrer:n}=h;return{id:o,name:o,domain:t?.site?.domain||r?.domain||window.location?.host,page:t?.site?.page||i(r),ref:t?.site?.ref||n(r),publisher:{id:o},content:{language:navigator.language}}}(r.params,o,e.ortb2);(0,d.J)(t,"device",e?.ortb2?.device);const u=y.getUserId(i,n);null==t.user&&(t.user={buyeruid:0,ext:{}});u&&0!==u&&(0,d.J)(t,"user.buyeruid",u);null==t.regs?.ext&&(t.regs={ext:{}});(0,d.J)(t,"regs.coppa",0),i.gdprApplies&&((0,d.J)(t,"user.ext.consent",e.gdprConsent.consentString),(0,d.J)(t,"regs.ext.gdpr",1));n&&(0,d.J)(t,"regs.ext.us_privacy",n);e.ortb2?.regs?.gpp&&((0,d.J)(t,"regs.ext.gpp",e.ortb2.regs.gpp),(0,d.J)(t,"regs.ext.gpp_sid",e.ortb2.regs.gpp_sid));a.$W.getConfig("coppa")&&(0,d.J)(t,"regs.coppa",1);const p=e.ortb2||{bcat:[],badv:[],wlang:[]};(0,d.J)(t,"source.fd",1),t.id=e.bidderRequestId,t.site=s,t.tmax=null==e.timeout?void 0:parseInt(e.timeout),t.bcat=p.bcat||r.params.bcat||[],t.badv=p.badv||r.params.badv||[],t.wlang=p.wlang||r.params.wlang||[],(0,d.J)(t,"ext.pageType",p?.ext?.data?.pageType||p?.ext?.data?.section||r.params.pageType),(0,d.J)(t,"ext.prebid.version","9.50.0")}(t,o.bidRequests[0],i),i},bidResponse(e,r,t){const o=e(r,t);return o.nurl=r.nurl,o.ad=(0,s.replaceAuctionPrice)(r.adm,r.price),r.ext&&r.ext.dchain&&(0,d.J)(o,"meta.dchain",r.ext.dchain),o}}),S={supportedMediaTypes:[n.D4],gvlid:42,code:l,isBidRequestValid:e=>!!(e.sizes&&e.params&&e.params.publisherId&&e.params.tagId),buildRequests:(e,r)=>{const[t]=e,o=R.toORTB({bidderRequest:r,bidRequests:e}),{publisherId:i}=t.params;return{url:"https://display.bidder.taboola.com/OpenRTB/TaboolaHB/auction?publisher="+i,method:"POST",data:o,bids:e,options:{withCredentials:!1}}},interpretResponse:(e,r)=>{if(!r||!r.bids||!r.data)return[];if(!e||!e.body)return[];const t=[],o=[];if(e.body.seatbid&&e.body.seatbid.length&&e.body.seatbid[0].bid&&e.body.seatbid[0].bid.length)t.push(...R.fromORTB({response:e.body,request:r.data}).bids);else if(!e.body.ext||!e.body.ext.igbid||!e.body.ext.igbid.length)return[];return(0,s.isArray)(e.body.ext?.igbid)&&e.body.ext.igbid.forEach((e=>{if(!(e&&e.igbuyer&&e.igbuyer.length&&e.igbuyer[0].buyerdata))return;let r=(0,s.safeJSONParse)(e.igbuyer[0]?.buyerdata);if(!r)return;const t={};e.igbuyer.forEach((e=>{if(!e||!e.buyerdata||!e.origin)return;let r=(0,s.safeJSONParse)(e.buyerdata);r&&r.perBuyerSignals&&e.origin in r.perBuyerSignals&&(t[e.origin]=r.perBuyerSignals[e.origin])}));const i=e?.impid;o.push({impId:i,config:{seller:r?.seller,resolveToConfig:r?.resolveToConfig,sellerSignals:{},sellerTimeout:r?.sellerTimeout,perBuyerSignals:t,auctionSignals:{},decisionLogicUrl:r?.decisionLogicUrl,interestGroupBuyers:r?.interestGroupBuyers,perBuyerTimeouts:r?.perBuyerTimeouts}})})),o.length?{bids:t,paapi:o}:t},onBidWon:e=>{if(e.nurl){const r=(0,s.replaceAuctionPrice)(e.nurl,e.originalCpm);(0,p.RD)(r)}},getUserSyncs:function(e,r,t,o,i){const n=[],a=[];return t&&a.push(`gdpr=${Number(t.gdprApplies&&1)}&gdpr_consent=${encodeURIComponent(t.consentString||"")}`),o&&a.push("us_privacy="+encodeURIComponent(o)),i&&a.push("gpp="+encodeURIComponent(i.gppString||"")+"&gpp_sid="+encodeURIComponent((i.applicableSections||[]).join(","))),e.iframeEnabled&&n.push({type:"iframe",url:"https://cdn.taboola.com/scripts/prebid_iframe_sync.html"+(a.length?"?"+a.join("&"):"")}),e.pixelEnabled&&n.push({type:"image",url:"https://trc.taboola.com/sg/prebidJS/1/cm"+(a.length?"?"+a.join("&"):"")}),n},onTimeout:e=>{(0,p.RD)(m+"/timeout",null,JSON.stringify(e),{method:"POST"})},onBidderError:e=>{let{error:r,bidderRequest:t}=e;(0,p.RD)(m+"/bidError",null,JSON.stringify({error:r,bidderRequest:t}),{method:"POST"})}};function x(e){return{format:e.map((e=>({w:e[0],h:e[1]})))}}(0,i.a$)(S),(0,o.E)("taboolaBidAdapter")}},e=>{e.O(0,[60802,58498,37769,12139,51085],(()=>{return r=45011,e(e.s=r);var r}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[28746],{27454:(e,t,n)=>{var r=n(7873),i=n(91069),o=n(70433),d=n(57377),a=n(12938),s=n(72285),c=n(74538),p=n(4122);const u="teads",l=12,m=11,I=0,g=22,f=(0,a.vM)({bidderCode:u}),y={code:u,gvlid:132,supportedMediaTypes:["video","banner"],isBidRequestValid:function(e){let t=!1;if(void 0!==e.params){let n=q(e.params.placementId),r=q(e.params.pageId);t=n&&r}return t||(0,i.logError)("Teads placementId and pageId parameters are required. Bid aborted."),t},buildRequests:function(e,t){const n=e.map(k),r=window.top,o={referrer:h(t),pageReferrer:document.referrer,pageTitle:b().slice(0,300),pageDescription:w().slice(0,300),networkBandwidth:(d=window.navigator,d&&d.connection&&d.connection.downlink>=0?d.connection.downlink.toString():""),networkQuality:S(window.navigator),timeToFirstByte:(0,p.v)(window),data:n,domComplexity:C(document),device:t?.ortb2?.device||{},deviceWidth:screen.width,deviceHeight:screen.height,devicePixelRatio:r.devicePixelRatio,screenOrientation:screen.orientation?.type,historyLength:(0,c.GA)(),viewportHeight:(0,i.getWinDimensions)().visualViewport.height,viewportWidth:(0,i.getWinDimensions)().visualViewport.width,hardwareConcurrency:(0,c.iz)(),deviceMemory:(0,c.qM)(),hb_version:"9.50.0",...v(e),outbrainId:f.getDataFromLocalStorage("OB-USER-TOKEN"),...A(e)};var d;const a=e[0];a.schain&&(o.schain=a.schain);let s=t.gppConsent;if(t&&s){let e="string"==typeof s.gppString,t=Array.isArray(s.applicableSections)&&s.applicableSections.every((e=>"number"==typeof e));o.gpp={consentString:e?s.gppString:"",applicableSectionIds:t?s.applicableSections:[]}}let u=t.gdprConsent;if(t&&u){let e="boolean"==typeof u.gdprApplies,t="string"==typeof u.consentString,n=e?function(e,t){let n=l;e?t&&!t.isServiceSpecific&&(n=m):n=I;return n}(u.gdprApplies,u.vendorData):g;o.gdpr_iab={consent:t?u.consentString:"",status:n,apiVersion:u.apiVersion}}t&&t.uspConsent&&(o.us_privacy=t.uspConsent);const y=a?.ortb2?.device?.sua;y&&(o.userAgentClientHints=y);const q=t?.ortb2?.regs?.ext?.dsa;q&&(o.dsa=q);return{method:"POST",url:"https://a.teads.tv/hb/bid-request",data:JSON.stringify(o)}},interpretResponse:function(e,t){if(!(e=e.body).responses)return[];const n=(0,s.T)();return e.responses.filter((e=>!e.needAutoplay||n)).map((e=>{const t={cpm:e.cpm,width:e.width,height:e.height,currency:e.currency,netRevenue:!0,ttl:e.ttl,meta:{advertiserDomains:e&&e.adomain?e.adomain:[]},ad:e.ad,requestId:e.bidId,creativeId:e.creativeId,placementId:e.placementId};return e.dealId&&(t.dealId=e.dealId),e?.ext?.dsa&&(t.meta.dsa=e.ext.dsa),t}))}};function v(e){const t={unifiedId2:"uid2.id",liveRampId:"idl_env",lotamePanoramaId:"lotamePanoramaId",id5Id:"id5id.uid",criteoId:"criteoId",yahooConnectId:"connectId",quantcastId:"quantcastId",epsilonPublisherLinkId:"publinkId",publisherFirstPartyViewerId:"pubcid",merkleId:"merkleId.id",kinessoId:"kpuid"};let n={};for(const r in t){const i=t[r],d=(0,o.A)(e,`0.userId.${i}`);d&&(n[r]=d)}return n}function h(e){let t="";return e&&e.refererInfo&&e.refererInfo.page&&(t=e.refererInfo.page),t}function b(){try{const e=window.top.document.querySelector('meta[property="og:title"]');return window.top.document.title||e&&e.content||""}catch(e){const t=document.querySelector('meta[property="og:title"]');return document.title||t&&t.content||""}}function w(){let e;try{e=window.top.document.querySelector('meta[name="description"]')||window.top.document.querySelector('meta[property="og:description"]')}catch(t){e=document.querySelector('meta[name="description"]')||document.querySelector('meta[property="og:description"]')}return e&&e.content||""}function S(e){const t=e.connection||e.mozConnection||e.webkitConnection;return t?.effectiveType??""}function C(e){return e?.querySelectorAll("*")?.length??-1}function k(e){const t={};let n=e.params.placementId,r=e.params.pageId;const o=e?.ortb2Imp?.ext?.gpid,d=e?.mediaTypes?.video?.plcmt;return t.sizes=function(e){return(0,i.parseSizesInput)(function(e){let t=e?.mediaTypes?.video?.playerSize,n=e?.mediaTypes?.video?.sizes,r=e?.mediaTypes?.banner?.sizes;if((0,i.isArray)(r)||(0,i.isArray)(t)||(0,i.isArray)(n)){return[r,n,t].reduce((function(e,t){return(0,i.isArray)(t)&&((0,i.isArray)(t[0])?t.forEach((function(t){e.push(t)})):e.push(t)),e}),[])}return e.sizes}(e))}(e),t.bidId=(0,i.getBidIdParameter)("bidId",e),t.bidderRequestId=(0,i.getBidIdParameter)("bidderRequestId",e),t.placementId=parseInt(n,10),t.pageId=parseInt(r,10),t.adUnitCode=(0,i.getBidIdParameter)("adUnitCode",e),t.transactionId=e.ortb2Imp?.ext?.tid||"",o&&(t.gpid=o),d&&(t.videoPlcmt=d),t}function q(e){return parseInt(e)>0}function A(e){const t=e?.[0]?.userId?.teadsId;if(t)return{firstPartyCookieTeadsId:t};if(f.cookiesAreEnabled(null)){const e=f.getCookie("_tfpvi",null);if(e)return{firstPartyCookieTeadsId:e}}return{}}(0,d.a$)(y),(0,r.E)("teadsBidAdapter")}},e=>{e.O(0,[26762,42809,47639,60802,37769,12139,51085],(()=>{return t=27454,e(e.s=t);var t}));e.O()}]);
(self.pbjsChunk=self.pbjsChunk||[]).push([[73469],{34836:(e,t,n)=>{var r=n(7873),o=n(43272),s=n(75023),i=n(11445),a=n(78969),l=n(16833),c=n(91069);const u=new Map;function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;const n={};function r(e){const t=JSON.stringify([e.source?.toLowerCase(),...Object.keys(e).filter((e=>!["uids","source"].includes(e))).sort().map((t=>e[t]))]);n.hasOwnProperty(t)?n[t].uids.push(...e.uids):n[t]=e}return Object.entries(e).forEach((e=>{let[n,o]=e;o=Array.isArray(o)?o:[o];const s=t.get(n);let i;if("pubProvidedId"===n)i=(0,c.deepClone)(o);else if("function"==typeof s)try{i=s(o),Array.isArray(i)||(i=[i]),i.forEach((e=>e.uids=e.uids.filter((e=>{let{id:t}=e;return(0,c.isStr)(t)})))),i=i.filter((e=>{let{uids:t}=e;return t?.length>0}))}catch(e){(0,c.logError)(`Could not generate EID for "${n}"`,e)}else i=o.map((e=>function(e,t,n){if(n&&e){let t={};t.source=(0,c.isFn)(n.getSource)?n.getSource(e):n.source;const r=(0,c.isFn)(n.getValue)?n.getValue(e):e;if((0,c.isStr)(r)){const o={id:r,atype:n.atype};if((0,c.isFn)(n.getUidExt)){const t=n.getUidExt(e);t&&(o.ext=t)}if(t.uids=[o],n.inserter||(0,c.isFn)(n.getInserter)){const r=(0,c.isFn)(n.getInserter)?n.getInserter(e):n.inserter;null!=r&&(t.inserter=r)}if(n.matcher||(0,c.isFn)(n.getMatcher)){const r=(0,c.isFn)(n.getMatcher)?n.getMatcher(e):n.matcher;null!=r&&(t.matcher=r)}if(null!=n.mm&&(t.mm=n.mm),(0,c.isFn)(n.getEidExt)){const r=n.getEidExt(e);r&&(t.ext=r)}return t}}return null}(e,0,s)));Array.isArray(i)&&i.filter((e=>null!=e)).forEach(r)})),Object.values(n)}function f(e){const t=new Map,n={};return Object.entries(e).forEach((e=>{let[r,o]=e;const s=o();if(s){n[r]=s.idObj[r];let e=s.submodule.eids?.[r];"function"==typeof e&&(i=e,e=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i(...t,s.config)}),t.set(r,e)}var i})),d(n,t)}var g=n(12938),m=n(63172),b=n(51692),h=n(25555),p=n(16894),y=n(5973),I=n(16916),w=n(45569),S=n(95139),E=n(76811),D=n(83441),v=n(38230),O=n(77332);const k="User ID",j=g.X0,$=g.qk,A="_pbjs_id_optout",C=(0,g.CK)("userId"),T={isAllowed:S.io};let U,x,N,F,P,L,_=[],M=[],W={},q=[];const G=(()=>{let e;return()=>(null==e&&(e=(0,p.K7)()),e)})();function J(e){return G().fork().renameWith((t=>[`userId.mod.${t}`,`userId.mods.${e}.${t}`]))}function R(e,t){t=t||e.storageMgr;const n="function"==typeof e.submodule.domainOverride?e.submodule.domainOverride():null,r=e.config.storage.name;return function(e,o,s){t.setCookie(r+(e||""),o,s,"Lax",n)}}function H(e,t){const n=e.config.storage;try{const r=new Date(Date.now()+864e5*n.expires).toUTCString(),o=(0,c.isPlainObject)(t)?JSON.stringify(t):t;e.enabledStorageTypes.forEach((t=>{switch(t){case j:!function(e,t,n){const r=e.config.storage,o=R(e);o(null,t,n),o("_cst",me(),n),"number"==typeof r.refreshInSeconds&&o("_last",(new Date).toUTCString(),n)}(e,o,r);break;case $:!function(e,t,n){const r=e.config.storage,o=e.storageMgr;o.setDataInLocalStorage(`${r.name}_exp`,n),o.setDataInLocalStorage(`${r.name}_cst`,me()),o.setDataInLocalStorage(r.name,encodeURIComponent(t)),"number"==typeof r.refreshInSeconds&&o.setDataInLocalStorage(`${r.name}_last`,(new Date).toUTCString())}(e,o,r)}}))}catch(e){(0,c.logError)(e)}}function B(e){Se(e),e.enabledStorageTypes.forEach((t=>{switch(t){case j:!function(e){const t=R(e,C),n=new Date(Date.now()-864e5).toUTCString();["","_last","_cst"].forEach((e=>{try{t(e,"",n)}catch(e){(0,c.logError)(e)}}))}(e);break;case $:!function(e){["","_last","_exp","_cst"].forEach((t=>{try{C.removeDataFromLocalStorage(e.config.storage.name+t)}catch(e){(0,c.logError)(e)}}))}(e)}}))}function K(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;const n=e.config.storage,r=t?`${n.name}_${t}`:n.name;let o;try{e.enabledStorageTypes.find((t=>{switch(t){case j:o=function(e,t){return e.storageMgr.getCookie(t)}(e,r);break;case $:o=function(e,t){const n=e.storageMgr,r=e.config.storage,o=n.getDataFromLocalStorage(`${r.name}_exp`);return""===o?n.getDataFromLocalStorage(t):o&&new Date(o).getTime()-Date.now()>0?decodeURIComponent(n.getDataFromLocalStorage(t)):void 0}(e,r)}return!!o})),"string"==typeof o&&"{"===o.trim().charAt(0)&&(o=JSON.parse(o))}catch(e){(0,c.logError)(e)}return o}function V(e,t,n){t=G().fork().startTiming("userId.callbacks.total").stopBefore(t);const r=(0,c.delayExecution)((()=>{clearTimeout(x),t()}),e.length);e.forEach((function(e){const t=J(e.submodule.name).startTiming("callback").stopBefore(r);try{e.callback((function(r){r?(e.config.storage&&H(e,r),e.idObj=e.submodule.decode(r,e.config),n.refresh(),he(n)):(0,c.logInfo)(`${k}: ${e.submodule.name} - request id responded with an empty value`),t()}),K.bind(null,e))}catch(n){(0,c.logError)(`Error in userID module '${e.submodule.name}':`,n),t()}e.callback=void 0}))}function Y(e){return Object.fromEntries(Object.entries(e).map((e=>{let[t,n]=e;return[t,n()?.idObj?.[t]]})).filter((e=>{let[t,n]=e;return null!=n})))}function z(e,t,n){const r={};return e.forEach((e=>{const o=n(e),s=function(e){if(e.primaryIds)return e.primaryIds;const t=Object.keys(e.eids??{});if(t.length>1)throw new Error(`ID submodule ${e.name} can provide multiple IDs, but does not specify 'primaryIds'`);return t}(o);t(e).forEach((t=>{const n=r[t]=r[t]??[],i=W[t]?.indexOf(o.name)??(s.includes(t)?0:-1),a=n.findIndex((e=>{let[t]=e;return t<i}));n.splice(-1===a?n.length:a,0,[i,e])}))})),Object.fromEntries(Object.entries(r).map((e=>{let[t,n]=e;return[t,n.map((e=>{let[t,n]=e;return n}))]})))}function Q(){const e={submodules:[],global:{},bidder:{},combined:{},refresh(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const n=new Set(t.map((e=>e.submodule)));e.submodules=e.submodules.filter((e=>!n.has(e.submodule))).concat(t),function(){const t=z(e.submodules,(e=>Object.keys(e.idObj??{})),(e=>e.submodule)),n={},r={};function o(e,t,n){return function(){for(const{allowed:r,bidders:o,module:s}of n){if(!T.isAllowed(E.yl,(0,D.s)(w.fW,s?.config?.name,{init:!1})))continue;const i=s.idObj?.[e];if(null!=i){if(r)return s;if(t){const t=e=>e.map((e=>e.module.submodule.name)).join(", ");return(0,c.logWarn)(`userID modules ${t(n)} provide the same ID ('${e}'); ${s.submodule.name} is the preferred source, but it's configured only for some bidders, unlike ${t(n.filter((e=>null==e.bidders)))}. Other bidders will not see the "${e}" ID.`),null}if(null==o)return null}}return null}}Object.entries(t).forEach((e=>{let[t,s]=e,i=!0;const a=new Set;s=s.map((e=>{let t=null;return Array.isArray(e.config.bidders)&&e.config.bidders.length>0?(t=e.config.bidders,t.forEach((e=>a.add(e)))):i=!1,{module:e,bidders:t}})),i||(n[t]=o(t,!0,s.map((e=>{let{bidders:t,module:n}=e;return{allowed:null==t,bidders:t,module:n}})))),a.forEach((e=>{r[e]=r[e]??{},r[e][t]=o(t,!1,s.map((t=>{let{bidders:n,module:r}=t;return{allowed:n?.includes(e),bidders:n,module:r}})))}))}));const s=Object.values(r).concat([n]).reduce(((e,t)=>Object.assign(e,t)),{});Object.assign(e,{global:n,bidder:r,combined:s})}()}};return e}function X(e){let{adUnits:t,ortb2Fragments:n}=e;if(n=n??{global:{},bidder:{}},function(e){const{global:t,bidder:n}=e,{global:r,bidder:o}=U,s=f(r);s.length>0&&(0,m.J)(t,"user.ext.eids",(t.user?.ext?.eids??[]).concat(s)),Object.entries(o).forEach((e=>{let[t,r]=e;const o=f(r);o.length>0&&(0,m.J)(n,`${t}.user.ext.eids`,(n[t]?.user?.ext?.eids??[]).concat(o))}))}(n),[t].some((e=>!Array.isArray(e)||!e.length)))return;const r=Y(U.global),o=n.global.user?.ext?.eids||[];t.forEach((e=>{e.bids&&(0,c.isArray)(e.bids)&&e.bids.forEach((e=>{const t=Object.assign({},r,Y(U.bidder[e.bidder]??{})),s=o.concat(n.bidder?.[e.bidder]?.user?.ext?.eids||[]);Object.keys(t).length>0&&(e.userId=t),s.length>0&&(e.userIdAsEids=s)}))}))}const Z={};let ee;function te(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ie()||[];const t=P&&e.find((e=>e.source===P));if(t&&"string"==typeof t?.uids?.[0]?.id){const e=t.uids[0].id.replace(/[\W_]/g,"");if(e.length>=32&&e.length<=150)return e;(0,c.logWarn)(`User ID - Googletag Publisher Provided ID for ${P} is not between 32 and 150 characters - ${e}`)}}const ne=(0,p.Ak)("userId",(function(e,t){let{mkDelay:n=h.cb,getIds:r=ge}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};h.U9.race([r().catch((()=>null)),n(F)]).then((()=>{X(t),G().join((0,p.BO)(t.metrics),{propagate:!1,includeGroups:!0}),e.call(this,t)}))})),re=(0,p.Ak)("userId",(function(e,t){X(t),e.call(this,t)}));function oe(){return!!O.gH.getHooks({hook:ne}).length}function se(){return Y(U.combined)}function ie(){return f(U.combined)}function ae(e){return ie().filter((t=>t.source===e))[0]}function le(e,t,n){return de().then((()=>{let r={};if((0,c.isFn)(n)){(0,c.logInfo)(`${k} - Getting encrypted signal from custom function : ${n.name} & source : ${e} `);const t=n(e);r[e]=t?ce(t):null}else{const n=ae(e);(0,c.logInfo)(`${k} - Getting encrypted signal for eids :${JSON.stringify(n)}`),(0,c.isEmpty)(n)||(r[n.source]=!0===t?ce(n):n.uids[0].id)}return(0,c.logInfo)(`${k} - Fetching encrypted eids: ${r[e]}`),r[e]}))}function ce(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n="";if(1===t)n="object"==typeof e?window.btoa(JSON.stringify(e)):window.btoa(e);return`${t}||${n}`}function ue(){if(!(0,c.isGptPubadsDefined)())return;window.googletag.secureSignalProviders=window.googletag.secureSignalProviders||[];const e=o.$W.getConfig("userSync.encryptedSignalSources");if(e){const t=e.registerDelay||0;setTimeout((()=>{e.sources&&e.sources.forEach((e=>{let{source:t,encrypt:n,customFunc:r}=e;t.forEach((e=>{window.googletag.secureSignalProviders.push({id:e,collectorFunction:()=>le(e,n,r)})}))}))}),t)}else(0,c.logWarn)(`${k} - ESP : encryptedSignalSources config not defined under userSync Object`)}function de(e){return ee(e).then((()=>se()),(e=>e===Z?Promise.resolve().then(ge):((0,c.logError)("Error initializing userId",e),h.U9.reject(e))))}function fe(){let{submoduleNames:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return de({refresh:!0,submoduleNames:e}).then((e=>(t&&(0,c.isFn)(t)&&t(),e)))}function ge(){return de()}function me(){let e=Number(I.SL.hash);const t=[];for(;e>0;)t.push(String.fromCharCode(255&e)),e>>>=8;return btoa(t.join())}function be(e,t){const n=I.SL.getConsentData();if(e.config.storage){let r,o=K(e),s=!1;if("number"==typeof e.config.storage.refreshInSeconds){const t=new Date(K(e,"last"));s=t&&Date.now()-t.getTime()>1e3*e.config.storage.refreshInSeconds}if(!o||s||t||function(e){const t=K(e,"cst");return!t||t!==me()}(e)){const t=Object.assign({enabledStorageTypes:e.enabledStorageTypes},e.config);r=e.submodule.getId(t,n,o)}else"function"==typeof e.submodule.extendId&&(r=e.submodule.extendId(e.config,n,o));(0,c.isPlainObject)(r)&&(r.id&&(H(e,r.id),o=r.id),"function"==typeof r.callback&&(e.callback=r.callback)),o&&(e.idObj=e.submodule.decode(o,e.config))}else if(e.config.value)e.idObj=e.config.value;else{const t=e.submodule.getId(e.config,n);(0,c.isPlainObject)(t)&&("function"==typeof t.callback&&(e.callback=t.callback),t.id&&(e.idObj=e.submodule.decode(t.id,e.config)))}}function he(e){const t=f(e.combined);if(t.length&&P){const e=te(t);e&&((0,c.isGptPubadsDefined)()?window.googletag.pubads().setPublisherProvidedId(e):(window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[],window.googletag.cmd.push((function(){window.googletag.pubads().setPublisherProvidedId(e)}))))}}function pe(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return G().fork().measureTime("userId.init.modules",(function(){if(!t.length)return[];if(t.forEach((e=>Se(e))),!(t=t.filter((e=>(!e.config.storage||!!e.enabledStorageTypes.length)&&T.isAllowed(E.yl,(0,D.s)(w.fW,e.config.name))))).length)return(0,c.logWarn)(`${k} - no ID module configured`),[];const r=t.reduce(((e,t)=>J(t.submodule.name).measureTime("init",(()=>{try{be(t,n),e.push(t)}catch(e){(0,c.logError)(`Error in userID module '${t.submodule.name}':`,e)}return e}))),[]);return e.refresh(r),he(e),r}))}function ye(e){return e?.storage?.type?.trim().split(/\s*&\s*/)||[]}function Ie(e){function t(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];(0,c.logWarn)(`Invalid userSync.userId config: ${e}`,...n)}return Array.isArray(e)?e.filter((e=>{if(!e?.name)return t('must specify "name"',e);if(e.storage){if(!e.storage.name||!e.storage.type)return t('must specify "storage.name" and "storage.type"',e);if(!function(e){return ye(e).every((e=>we.has(e)))}(e))return t('invalid "storage.type"',e);["expires","refreshInSeconds"].forEach((n=>{let r=e.storage[n];null!=r&&"number"!=typeof r&&(r=Number(r),isNaN(r)?(t(`storage.${n} must be a number and will be ignored`,e),delete e.storage[n]):e.storage[n]=r)}))}return!0})):(null!=e&&t("must be an array",e),[])}const we=new Set([$,j]);function Se(e){if(e.enabledStorageTypes)return;const t=ye(e.config);e.enabledStorageTypes=t.filter((t=>{switch(t){case $:return function(e){return!(!e.storageMgr.localStorageIsEnabled()||C.getDataFromLocalStorage(A)&&((0,c.logInfo)(`${k} - opt-out localStorage found, storage disabled`),1))}(e);case j:return function(e){return!(!e.storageMgr.cookiesAreEnabled()||C.getCookie(A)&&((0,c.logInfo)(`${k} - opt-out cookie found, storage disabled`),1))}(e)}return!1}))}function Ee(e){u.clear(),Object.entries(z(e,(e=>Object.keys(e.eids||{})),(e=>e))).forEach((e=>{let[t,n]=e;return u.set(t,n[0].eids[t])}))}function De(){Ee(q);const e=Ie(M);if(!e.length)return;const t=q.filter((e=>!(_||[]).find((t=>t.name===e.name))));_.splice(0,_.length),t.map((t=>{const n=(e||[]).find((e=>e.name&&(e.name.toLowerCase()===t.name.toLowerCase()||t.aliasName&&e.name.toLowerCase()===t.aliasName.toLowerCase())));return n&&t.name!==n.name&&(n.name=t.name),n?{submodule:t,config:n,callback:void 0,idObj:void 0,storageMgr:(0,g.vM)({moduleType:w.fW,moduleName:n.name})}:null})).filter((e=>null!==e)).forEach((e=>_.push(e))),_.length&&(oe()||(O.gH.getHooks({hook:re}).remove(),O.gH.before(ne,100),i.Ay.callDataDeletionRequest.before(ve),b.Q.after((e=>e(te())))),(0,c.logInfo)(`${k} - usersync config updated for ${_.length} submodules: `,_.map((e=>e.submodule.name))))}function ve(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];(0,c.logInfo)("UserID: received data deletion request; deleting all stored IDs..."),_.forEach((e=>{if("function"==typeof e.submodule.onDataDeletionRequest)try{e.submodule.onDataDeletionRequest(e.config,e.idObj,...n)}catch(t){(0,c.logError)(`Error calling onDataDeletionRequest for ID submodule ${e.submodule.name}`,t)}B(e)})),e.apply(this,n)}function Oe(e){return function(){return Promise.resolve(e.apply(this,arguments))}}function ke(e){let{mkDelay:t=h.cb}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};P=void 0,_=[],M=[],U=Q(),ee=function(){let{mkDelay:e=h.cb}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,h.v6)(),n=(0,h.v6)();let r,o,i=!1;function u(e){return o=G().fork(),null!=r&&r.reject(Z),r=(0,h.v6)(),h.U9.race([e,r.promise]).finally(o.startTiming("userId.total"))}let d=U,f=_;function g(e){return function(){if(d===U&&f===_)return e(...arguments)}}function m(){return I.SL.promise.finally(o.startTiming("userId.init.consent"))}let b=u(h.U9.all([l.Gc,t.promise]).then(m).then(g((()=>{pe(d,f)}))).then((()=>n.promise.finally(o.startTiming("userId.callbacks.pending")))).then(g((()=>{const e=d.submodules.filter((e=>(0,c.isFn)(e.callback)));if(e.length)return new h.U9((t=>V(e,t,d)))}))));return function(){let{refresh:r=!1,submoduleNames:o=null,ready:l=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return l&&!i&&(i=!0,t.resolve(),F>0?n.resolve():s.on(a.qY.AUCTION_END,(function t(){s.off(a.qY.AUCTION_END,t),e(N).then(n.resolve)}))),r&&i&&(b=u(b.catch((()=>null)).then(m).then(g((()=>{const e=pe(d,f.filter((e=>null==o||o.includes(e.submodule.name))),!0).filter((e=>null!=e.callback));if(e.length)return new h.U9((t=>V(e,t,d)))}))))),b}}({mkDelay:t}),null!=L&&L(),q=[],L=e.getConfig("userSync",(e=>{const t=e.userSync;t&&(P=t.ppid,t.userIds&&(M=t.userIds,N=(0,c.isNumber)(t.syncDelay)?t.syncDelay:v.qh.syncDelay,F=(0,c.isNumber)(t.auctionDelay)?t.auctionDelay:v.qh.auctionDelay,De(),function(e,t){if(e){const n={},r=new Map(t.map((e=>e.aliasName?[e.aliasName,e.name]:[])));Object.keys(e).forEach((t=>{const o=(0,c.isArray)(e[t])?[...e[t]].reverse():[];n[t]=o.map((e=>r.has(e)?r.get(e):e))})),W=n}else W={};U.refresh(),Ee(t)}(t.idPriority,q),ee({ready:!0})))})),(0,r.m)().getUserIds=se,(0,r.m)().getUserIdsAsEids=ie,(0,r.m)().getEncryptedEidsForSource=Oe(le),(0,r.m)().registerSignalSources=ue,(0,r.m)().refreshUserIds=Oe(fe),(0,r.m)().getUserIdsAsync=Oe(ge),(0,r.m)().getUserIdsAsEidBySource=ae,oe()||O.gH.before(re,100)}ke(o.$W),(0,l.xG)("userId",(function(e){e.findRootDomain=y.S,(q||[]).find((t=>t.name===e.name))||(q.push(e),I.o2.register(w.fW,e.name,e.gvlid),De(),ee({refresh:!0,submoduleNames:[e.name]}))}),{postInstallAllowed:!0}),(0,r.E)("userId")}},e=>{e.O(0,[60802,37769,12139,51085],(()=>{return t=34836,e(e.s=t);var t}));e.O()}]);
})(),pbjs.processQueue();
/* jshint ignore:end */
/* build date: 2025-06-16T21:17:53.365Z */

/*! zaz-cerebro - v1.7.6 - 15/08/2025 -- 1:17pm */
if(!window.zaz&&window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("ZAZ_CEREBRO_DEFINED")}catch(perfException){window.console&&"object"==typeof window.console&&("function"==typeof window.console.warn?window.console.warn("Error registering performance metric ZAZ_CEREBRO_DEFINED. "+perfException.message):"function"==typeof window.console.log&&window.console.error("[ZaZ Warning] Error registering performance metric ZAZ_CEREBRO_DEFINED. "+perfException.message))}!function(){"use strict";var environment,minified,q,eq,o;window.zaz||(window.zaz=(minified=!1,o={getQueue:function(){return q},getExtQueue:function(){return eq},use:function(fn,scope){return fn.scope=scope,q.push(fn),window.zaz},extend:function(name,fn){eq.push([name,fn])},status:"pre-build",options:{global:{}},version:"1.7.6",notSupported:!(eq=[]),fullyLoaded:!(q=[]),lastRevision:""}),window.zaz.environment=void 0)}();


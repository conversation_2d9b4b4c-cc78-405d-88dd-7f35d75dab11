
var includerComponents = [{"components": {"app.t360.live": {"meta": null, "events": null, "depends": ["app.player", "ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-t360-live/_js/app-t360-live.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-live/_js/app-t360-live.min.js", "env": "prod", "component": "app.t360.live", "manifest": "zaz-app-t360-live", "concat": false, "type": "js", "namespaces": "zaz:app.t360.live", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-t360-live/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-live/_css/theme-default.min.css", "env": "prod", "component": "app.t360.live", "manifest": "zaz-app-t360-live", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "app.player": {"meta": null, "events": null, "depends": ["mod.videojs", "cerebro"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-player/_js/app-player.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-player/_js/app-player.min.js", "env": "prod", "component": "app.player", "manifest": "zaz-app-player", "concat": false, "type": "js", "namespaces": "zaz:app.player", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-player/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-player/_css/theme-default.min.css", "env": "prod", "component": "app.player", "manifest": "zaz-app-player", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "app.t360.svaBanner": {"meta": null, "events": null, "depends": ["ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-t360-sva-banner/_js/app-t360-sva-banner.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-sva-banner/_js/app-t360-sva-banner.min.js", "env": "prod", "component": "app.t360.svaBanner", "manifest": "zaz-app-t360-sva-banner", "concat": false, "type": "js", "namespaces": "zaz:app.t360.svaBanner", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-t360-sva-banner/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-sva-banner/_css/theme-default.min.css", "env": "prod", "component": "app.t360.svaBanner", "manifest": "zaz-app-t360-sva-banner", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "mod.realtime": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-realtime/_js/realtime.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-realtime/_js/realtime.min.js", "env": "prod", "component": "mod.realtime", "manifest": "zaz-mod-realtime", "concat": false, "type": "js", "namespaces": "global:Realtime", "corsAllowed": true, "events": null}]}, "mod.globalStorage": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-globalstorage/zaz-globalstorage-min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-globalstorage/zaz-globalstorage-min.js", "env": "prod", "component": "mod.globalStorage", "manifest": "zaz-mod-globalstorage", "concat": false, "type": "js", "namespaces": "global:GlobalStorage", "corsAllowed": true, "events": null}]}, "mod.datetime": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-datetime/zaz-datetime.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-datetime/zaz-datetime.min.js", "env": "prod", "component": "mod.datetime", "manifest": "zaz-mod-datetime", "concat": false, "type": "js", "namespaces": "global:Datetime", "corsAllowed": true, "events": null}]}, "ui.t360.style": {"meta": null, "events": null, "depends": ["cerebro"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-ui-t360/_css/fonts.min.css", "attributes": {}, "inline": true, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-ui-t360/_css/fonts.min.css", "env": "prod", "component": "ui.t360.style", "manifest": "zaz-ui-t360", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-ui-t360/_css/context.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-ui-t360/_css/context.min.css", "env": "prod", "component": "ui.t360.style", "manifest": "zaz-ui-t360", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "app.t360.standings": {"meta": null, "events": null, "depends": ["ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-t360-standings/_js/app-t360-standings.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-standings/_js/app-t360-standings.min.js", "env": "prod", "component": "app.t360.standings", "manifest": "zaz-app-t360-standings", "concat": false, "type": "js", "namespaces": "zaz:app.t360.standings", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-t360-standings/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-standings/_css/theme-default.min.css", "env": "prod", "component": "app.t360.standings", "manifest": "zaz-app-t360-standings", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "mod.t360.icons.sizes": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-t360-icons/_css/sizes.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-mod-t360-icons/_css/sizes.min.css", "env": "prod", "component": "mod.t360.icons.sizes", "manifest": "zaz-mod-t360-icons", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "app.t360.terratv": {"meta": null, "events": null, "depends": ["app.player", "ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-t360-terratv/_js/app-t360-terratv.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-terratv/_js/app-t360-terratv.min.js", "env": "prod", "component": "app.t360.terratv", "manifest": "zaz-app-t360-terratv", "concat": false, "type": "js", "namespaces": "zaz:app.t360.terratv", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-t360-terratv/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-terratv/_css/theme-default.min.css", "env": "prod", "component": "app.t360.terratv", "manifest": "zaz-app-t360-terratv", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "cerebro": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-cerebro/prd/scripts/zaz.scope.min.js", "attributes": {"charset": "utf-8"}, "inline": true, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-cerebro/prd/scripts/zaz.scope.min.js", "env": "prod", "component": "cerebro", "manifest": "zaz-cerebro", "concat": false, "type": "js", "namespaces": null, "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-cerebro/prd/scripts/range.inline.min.js", "attributes": {"charset": "utf-8"}, "inline": true, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-cerebro/prd/scripts/range.inline.min.js", "env": "prod", "component": "cerebro", "manifest": "zaz-cerebro", "concat": false, "type": "js", "namespaces": null, "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-cerebro/prd/scripts/zaz.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-cerebro/prd/scripts/zaz.min.js", "env": "prod", "component": "cerebro", "manifest": "zaz-cerebro", "concat": false, "type": "js", "namespaces": "global:zaz", "corsAllowed": true, "events": null}]}, "mod.t360.carouselRanges": {"meta": null, "events": null, "depends": ["ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-t360-carousel-ranges/_js/mod-t360-carousel-ranges.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-t360-carousel-ranges/_js/mod-t360-carousel-ranges.min.js", "env": "prod", "component": "mod.t360.carouselRanges", "manifest": "zaz-mod-t360-carousel-ranges", "concat": false, "type": "js", "namespaces": "zaz:mod.t360.carouselRanges", "corsAllowed": true, "events": null}]}, "mod.googleAccounts": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://accounts.google.com/gsi/client", "attributes": {"charset": "utf-8"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.googleAccounts", "type": "js", "namespaces": "global:google.accounts", "corsAllowed": false, "events": null}]}, "mod.prebid": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-3rd/prebid/prebid.js", "attributes": {"charset": "utf-8", "async": "async"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-3rd/prebid/prebid.js", "env": "prod", "component": "mod.prebid", "manifest": "zaz-3rd", "concat": false, "type": "js", "namespaces": "window.pbjs", "corsAllowed": false, "events": null}]}, "mod.adsbygoogle": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "pre", "inline": false, "concat": false, "component": "mod.adsbygoogle", "type": "js", "namespaces": "window.adsbygoogle", "corsAllowed": false, "events": null}]}, "mod.chromecast": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-mod-player-plugin", "path": "https://www.gstatic.com/cv/js/sender/v1/cast_sender.js", "attributes": {"charset": "utf-8"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.chromecast", "type": "js", "namespaces": "global:chrome.cast", "corsAllowed": false, "events": null}]}, "mod.analytics": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://www.google-analytics.com/analytics.js", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.analytics", "type": "js", "namespaces": "global:ga", "corsAllowed": false, "events": null}, {"env": "prod", "manifest": "zaz-3rd", "path": "https://www.google-analytics.com/plugins/ua/ec.js", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.analytics", "type": "js", "namespaces": null, "corsAllowed": false, "events": null}]}, "mod.t360.icons.zodiac.solid": {"meta": null, "events": null, "depends": ["mod.t360.icons.sizes"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-t360-icons/_css/zodiac-solid.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-mod-t360-icons/_css/zodiac-solid.min.css", "env": "prod", "component": "mod.t360.icons.zodiac.solid", "manifest": "zaz-mod-t360-icons", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "mod.videojs": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-3rd/videojs/video.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-3rd/videojs/video.min.js", "env": "prod", "component": "mod.videojs", "manifest": "zaz-3rd", "concat": false, "type": "js", "namespaces": "global:videojs", "corsAllowed": true, "events": null}]}, "mod.notifications": {"meta": null, "events": null, "depends": ["cerebro"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-notifications/_js/mod-notifications.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-notifications/_js/mod-notifications.min.js", "env": "prod", "component": "mod.notifications", "manifest": "zaz-mod-notifications", "concat": false, "type": "js", "namespaces": "zaz:mod.notifications", "corsAllowed": true, "events": null}]}, "mod.dfp": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://imasdk.googleapis.com/js/sdkloader/ima3.js", "attributes": {"charset": "utf-8"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.dfp", "type": "js", "namespaces": "global:google", "corsAllowed": false, "events": null}]}, "mod.t360.realtime": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-t360-realtime/_js/mod-t360-realtime.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-t360-realtime/_js/mod-t360-realtime.min.js", "env": "prod", "component": "mod.t360.realtime", "manifest": "zaz-mod-t360-realtime", "concat": false, "type": "js", "namespaces": "zaz:mod.t360.realtime", "corsAllowed": true, "events": null}]}, "mod.analytics4": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://www.googletagmanager.com/gtm.js?id=GTM-T4ZBMQJ5&l=dataLayer", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.analytics4", "type": "js", "namespaces": "global:google_tag_manager", "corsAllowed": false, "events": null}]}, "mod.gemini": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://s.yimg.com/dy/ads/native.js", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.gemini", "type": "js", "namespaces": "global:NativeJS", "corsAllowed": false, "events": null}]}, "app.t360.footer": {"meta": null, "events": null, "depends": ["ui.t360.style"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-app-t360-footer/_js/app-t360-footer.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-footer/_js/app-t360-footer.min.js", "env": "prod", "component": "app.t360.footer", "manifest": "zaz-app-t360-footer", "concat": false, "type": "js", "namespaces": "zaz:app.t360.footer", "corsAllowed": true, "events": null}, {"path": "https://s1.trrsf.com/fe/zaz-app-t360-footer/_css/theme-default.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-app-t360-footer/_css/theme-default.min.css", "env": "prod", "component": "app.t360.footer", "manifest": "zaz-app-t360-footer", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "mod.fireBaseApp": {"meta": null, "events": null, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://www.gstatic.com/firebasejs/9.19.1/firebase-app-compat.js", "attributes": {"charset": "utf-8"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.fireBaseApp", "type": "js", "namespaces": "global:firebase", "corsAllowed": false, "events": null}]}, "mod.stalker": {"meta": null, "events": null, "depends": ["cerebro"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-stalker/_js/mod-stalker.min.js", "attributes": {"charset": "utf-8"}, "inline": false, "render": "post", "file": "/nfs/stf-htdocs/fe/zaz-mod-stalker/_js/mod-stalker.min.js", "env": "prod", "component": "mod.stalker", "manifest": "zaz-mod-stalker", "concat": false, "type": "js", "namespaces": "zaz:mod.stalker", "corsAllowed": true, "events": null}]}, "mod.gpt": {"meta": null, "events": {"onBeforeExport": "window.googletag = window.googletag || {}; window.googletag.cmd = window.googletag.cmd || [];"}, "depends": [], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://securepubads.g.doubleclick.net/tag/js/gpt.js", "attributes": {"charset": "utf-8", "async": "async"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "mod.gpt", "type": "js", "namespaces": "window.googletag", "corsAllowed": false, "events": null}]}, "mod.t360.icons.shields": {"meta": null, "events": null, "depends": ["mod.t360.icons.sizes"], "requests": [], "delivers": [], "includes": [{"path": "https://s1.trrsf.com/fe/zaz-mod-t360-icons/_css/shields.min.css", "attributes": {}, "inline": false, "render": "pre", "file": "/nfs/stf-htdocs/fe/zaz-mod-t360-icons/_css/shields.min.css", "env": "prod", "component": "mod.t360.icons.shields", "manifest": "zaz-mod-t360-icons", "concat": false, "type": "css", "namespaces": null, "corsAllowed": true, "events": null}]}, "FireBaseCore": {"meta": null, "events": null, "depends": ["mod.fireBaseApp"], "requests": [], "delivers": [], "includes": [{"env": "prod", "manifest": "zaz-3rd", "path": "https://www.gstatic.com/firebasejs/9.19.1/firebase-messaging-compat.js", "attributes": {"charset": "utf-8"}, "file": "", "render": "post", "inline": false, "concat": false, "component": "FireBaseCore", "type": "js", "namespaces": null, "corsAllowed": false, "events": null}]}}, "invalid_components": [], "content-type": "text/css", "hostname": "api.tpn.terra.com.br"}];
(function() {
    'use strict';
    if (!window.zaz || typeof window.zaz.use != 'function') {
        throw new Error('Framework not found as "window.zaz".');
    }
    window.zaz.use(function(pkg) {
        try {
            if (typeof pkg.includer != 'object') {
                throw new Error('Includer not delivered on this pkg');
            }
            if (includerComponents.length > 0) {
                pkg.includer.registerFetchedComponents(includerComponents[0].components);
            }
        } catch (e) {
            e.message = 'Error on Includer\'s reporter: ' + e.messsage;
            if (window.console && typeof window.console.error == 'function') {
                window.console.error(e);
            }
        }
    });
})();

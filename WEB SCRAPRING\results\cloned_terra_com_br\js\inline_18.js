

try { AdManager.set('backend_getviewport', { }) } catch(e) { };

window.zaz.use(function(pkg) { try { if ("br.terra.homepage".search("chat") != -1 || "br.terra.homepage".search("mail") != -1) { window.AdManager.configure({ VIEWPORT_MAX: 1 }); } else { if ("home360".search("articles") != -1 || pkg.context.page.get("template") == "content") { if (pkg.context.platform.get("type") !== "web") { window.AdManager.configure({ VIEWPORT_MAX: 3, VIEWPORT_ORDER: [3] }); } else { window.AdManager.configure({ VIEWPORT_MAX: 4, VIEWPORT_ORDER: [4, 3, 2], }); } } else { window.AdManager.configure({ VIEWPORT_MAX: 3, VIEWPORT_ORDER: [2, 3, 2, 1], }); } } } catch (error) {} try { top.AdManager.set("google_tag", "/1211/br.terra.homepage/home360"); } catch (error) {} window.getAudCookie = function() { var getTailTargetParam = function(a) { var ttCookieName = "_ttdmp"; if (!window._ttprofilescache) { window._ttprofilescache = {}; try { var c = document.cookie.match("(^|;)\\s*" + ttCookieName + "\\s*=\\s*([^;]+)"); var d = c ? c.pop().split("|") : []; for (var i = 0; i < d.length; i++) { var kv = d[i].split(":"); if (kv[1] && kv[1].indexOf(",") > 0) { window._ttprofilescache[kv[0]] = kv[1].split(","); } else { window._ttprofilescache[kv[0]] = kv[1]; } } } catch (e) {} } return window._ttprofilescache[a]; }; var f = ""; try { if ("testeab" == "testeab") { if (Math.random() * 100 < 50) { f = "testfloor=true"; } else { f = "testfloor=false"; } } if (getTailTargetParam("A")) { f = f + ";age=" + getTailTargetParam("A"); } if (getTailTargetParam("G")) { f = f + ";gender=" + getTailTargetParam("G"); } if (getTailTargetParam("LS")) { f = f + ";cluster=" + getTailTargetParam("LS"); } if (getTailTargetParam("S")) { f = f + ";subjects=" + getTailTargetParam("S"); } if (getTailTargetParam("T")) { f = f + ";team=" + getTailTargetParam("T"); } if (getTailTargetParam("C")) { f = f + ";sclass=" + getTailTargetParam("C"); } if (getTailTargetParam("U")) { f = f + ";msegments=" + getTailTargetParam("U"); } if (getTailTargetParam("L")) { f = f + ";lists=" + getTailTargetParam("L"); } if (getTailTargetParam("CA")) { f = f + ";customaud=" + getTailTargetParam("CA"); } if (window.location.search.match("[?&]lineid=([^&]+)")) { f = f + ";lineid=" + window.location.search.match("[?&]lineid=([^&]+)")[1]; } } catch (error) {} return f; }; window.getAdRelevantWords = function() { if (typeof window.adRelevantWords != "undefined" && window.adRelevantWords.length > 0) { return "words=" + window.adRelevantWords.toString(); } return ""; }; window.keyGen = function(h) { keyword = h; customCriteria = {}; try { if (pkg.context.page.get("adultContent")) { keyword = keyword + ";adultcontent=true"; } } catch (i) {} try { if (window.trr.contextData.vgnID) { keyword = keyword + ";pageid=" + window.trr.contextData.vgnID; } } catch (i) {} try { if (window.terra_info_source) { keyword = keyword + ";contentsource=" + window.terra_info_source; } } catch (i) {} try { if (contextData.pageModel == "freecontent") { keyword = keyword + ";freecontent=true"; } } catch (i) {} if (window.info_path) { keyword = keyword + ";breadcrumb=" + window.info_path.breadcrumb; keyword = keyword + ";channel=" + window.info_path.channel; if (window.info_path.subchannel) { keyword = keyword + ";subchannel=" + window.info_path.subchannel; } if (window.info_path.channeldetail) { keyword = keyword + ";channeldetail=" + window.info_path.channeldetail; } } try { keyword = keyword + ";" + window.getAudCookie(); } catch (i) {} try { keyword = keyword + ";" + window.getAdRelevantWords(); } catch (i) {} try { if (navigator.connection.effectiveType) { keyword = keyword + ";" + "connection=" + navigator.connection.effectiveType; } } catch (i) {} try { if (navigator.userAgent.search("TerraLauncher") != -1) { keyword = keyword + ";" + "iosapp=true"; } } catch (i) {} try { if (navigator.runAdAuction) { keyword = keyword + ";" + "fledge=true"; } else { keyword = keyword + ";" + "fledge=false"; } } catch (i) {} try { if ("br.terra.homepage".search("cobranded") != -1 || "br.terra.homepage".search("br.terra.teste") != -1) { keyword = keyword + ";parceiro=true"; } } catch (i) {} try { if (pkg.context.page.get("templateLayout") == "liveBlog") { keyword = keyword + ";liveblog=true"; } } catch (i) {} try { if (pkg.context.page.get("templateLayout") == "special-article") { keyword = keyword + ";specialarticle=true"; } } catch (i) {} if (keyword != "") { var p = keyword.split(";"); for (var o = 0; o < p.length; o++) { if (p[o].search("=") != -1) { customCriteriaArray2 = p[o].split("="); customCriteria[customCriteriaArray2[0]] = customCriteriaArray2[1].split(","); } } } return customCriteria; }; window.sizeGen = function(sizes) { var slotSize = sizes; if (slotSize != "") { var slotSizeArray1 = slotSize.split(";"); slotSize = ""; for (var i = 0; i < slotSizeArray1.length; i++) { if (slotSizeArray1[i]) { slotSize = slotSize + "[" + slotSizeArray1[i].replace("x", ",") + "],"; } } slotSize = eval("[" + slotSize.substring(0, slotSize.length - 1) + "]"); } return slotSize; };});if ("" != "false") { var keywords = top.keyGen(""); top.AdManager.outOfPage(keywords);}function searchInStringByArray(searchList, baseString) { for (var i = 0; i < searchList.length; i++) { if (baseString.toLowerCase().includes(searchList[i].toLowerCase())) { return true; } } return false;}var blockedPages = ["mail", "centraldoassinante"];var isBlockedPage = searchInStringByArray(blockedPages, "br.terra.homepage");if ("false" != "false") { if (!isBlockedPage) { var keywordsii = top.keyGen("interstitial=" + "br.terra.homepage"); top.AdManager.set("google_tag_interstitial", "/1211/br.terra.homepage/interstitial"); top.AdManager.outOfPageInterstitial(keywordsii); }}window.addEventListener("message", function(b) { if (b.origin.indexOf("googlesyndication.com") != -1) { if (b.data == "videofloater") { document.getElementById("ad-floater").style.display = "none"; } }}, false);(function() { var key = /[\?&]tgmkey=([^&\s]+)/i.exec(window.location.search); key = key ? key[1] : window.tgmkey || window.tgmKey; if (key && window.AdManager) { window.AdManager.set("setup_done_for_" + key, true); }})();if (top.AdManager.get("prebid") != "false") { var pbjs = pbjs || {}; pbjs.que = pbjs.que || []; pbjs.que.push(function() { var customGranularityDisplay = { buckets: [{ max: 50, increment: 0.01 }] }; var customGranularityVideo = { buckets: [{ max: 5, increment: 0.01 }, { max: 50, increment: 0.1 }, ], }; pbjs.setConfig({ userSync: { userIds: [{ name: "criteo" }, { name: "pairId", params: { liveramp: {} } }, { name: "identityLink", params: { pid: "13911", notUse3P: false }, storage: { type: "cookie", name: "idl_env", expires: 15, refreshInSeconds: 1800, }, }, ], syncDelay: 3000, filterSettings: { iframe: { bidders: "*", filter: "include" } }, }, enableSendAllBids: true, mediaTypepriceGranularity: { banner: customGranularityDisplay, native: customGranularityDisplay, "video-outstream": customGranularityDisplay, video: customGranularityVideo, }, priceGranularity: customGranularityDisplay, rubicon: { singleRequest: true }, useBidCache: true, deviceAccess: true, pbjs_configSet: true, }); pbjs.bidderSettings = { criteo: { storageAllowed: true }, taboola: { storageAllowed : true } }; var getDomain = window.location.hostname; zaz.use(function t360(pkg) { pkg.context.user.get("emailHashes", function(d) { hemmd5 = d.md5; hemsha256 = d.sha256; pbjs.setBidderConfig({ bidders: ["criteo"], config: { ortb2: { user: { ext: { data: { eids: [{ source: getDomain, uids: [{ id: hemmd5, atype: 3, ext: { stype: "hemmd5" } }, { id: hemsha256, atype: 3, ext: { stype: "hemsha256" }, }, ], }, ], }, }, }, }, }, }); }); }); pbjs.aliasBidder("appnexus", "projectagora"); pbjs.setBidderConfig({ bidders: ["projectagora"], config: { schain: { validation: "strict", config: { ver: "1.0", complete: 1, nodes: [{ asi: "projectagora.com", sid: "109142", hp: 1 }], }, }, }, }); pbjs.bidderSettings["projectagora"] = { bidCpmAdjustment: function(bidCpm) { return bidCpm * 0.8; }, }; });}


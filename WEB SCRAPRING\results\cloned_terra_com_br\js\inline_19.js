

try { AdManager.get('backend_getviewport')['s1'] = function() { (function() { adSizes = "300x50;320x50;300x100;320x100"; try { slotSize = window.sizeGen(adSizes); } catch (a) { slotSize = [ [1, 1] ]; } if (top.AdManager.get("prebid") != "false") { if ("true" == "true") { adserver = "prebid"; } else { adserver = "dfp"; } } else { adserver = "dfp"; } if ("true" == "true") { reloadKey = true; reloadTime = 35; if ("" != "") { reloadTime = parseInt(""); } } else { reloadKey = false; reloadTime = 0; } if ("" != "false") { reloadViewable = "viewable"; } else { reloadViewable = "always"; } var kw = "viewport=s1;"; try { if ("s1" == "cabeceira") { if (window.AdManager.get("s0Sticky") && "mob" == "web") { kw = kw + "sticky=true;"; } } } catch (a) {} try { var changeCorrelator = null; if ("s1" == "vitrine") { changeCorrelator = false; } } catch (a) {} try { if ("300x50;320x50;300x100;320x100".search("300x250") != -1) { mediatype = { native: { type: "image", sendTargetingKeys: false, image: { required: true }, title: { required: true, len: 90 }, }, }; } else { mediatype = null; } } catch (a) {} try { customCriteria = window.keyGen(kw, adSizes); } catch (a) { customCriteria = { seg: ["error"] }; } googleTag = "br.terra.homepage/home360" == "/" ? window.AdManager.get("google_tag") : "/1211/br.terra.homepage/home360"; googleTag_area = googleTag + "/s1"; if (adserver == "prebid") { bidders = [{ bidder: "rubicon", params: { accountId: "10828", siteId: "25078", zoneId: "mob" != "mob" ? "97396" : "100306", inventory: { adunit: googleTag_area.replace(/^\/.+?\//, "").replace(/\./g, "_"), viewport: "s" + window.AdManager.getCurrentViewport(), }, visitor: customCriteria, }, }, { bidder: "appnexus", params: { placementId: "********", keywords: customCriteria }, }, { bidder: "smartadserver", params: { domain: "https://prg.smartadserver.com", siteId: 254198, pageId: "mob" != "mob" ? 1354600 : 1354599, formatId: parseInt("68543"), }, }, { bidder: "criteo", params: { networkId: 6729 } }, { bidder: "medianet", params: { cid: "8CUDV2PQ3", crid: "*********" } }, { bidder: "teads", params: { placementId: 220849, pageId: 204602, }, }, { bidder: "taboola", params: { publisherId: '1826887' }, }]; if ("mob" == "mob") { bidders.push({ bidder: "seedtag", params: { adUnitId: "********" == "" ? "********" : "********", publisherId: "8105-5884-01", placement: "" == "" ? "inBanner" : "", } }); } } else { bidders = []; } try { window.AdManager.defineAd({ platform: "mob", adserver: adserver, type: "full", bids: bidders, sizes: slotSize, google_tag: googleTag_area, keywords: customCriteria, timeout: 3000, reloadRule: { reload: reloadKey, loop: reloadTime * 1000, type: reloadViewable, times: 1000, }, changeCorrelator: changeCorrelator, mediaTypes: mediatype, }); } catch (a) {}})();(function() { adSizes = "728x90;970x90;970x250;1272x250"; try { slotSize = window.sizeGen(adSizes); } catch (a) { slotSize = [ [1, 1] ]; } if (top.AdManager.get("prebid") != "false") { if ("true" == "true") { adserver = "prebid"; } else { adserver = "dfp"; } } else { adserver = "dfp"; } if ("true" == "true") { reloadKey = true; reloadTime = 35; if ("" != "") { reloadTime = parseInt(""); } } else { reloadKey = false; reloadTime = 0; } if ("" != "false") { reloadViewable = "viewable"; } else { reloadViewable = "always"; } var kw = "viewport=s1;"; try { if ("s1" == "cabeceira") { if (window.AdManager.get("s0Sticky") && "web" == "web") { kw = kw + "sticky=true;"; } } } catch (a) {} try { var changeCorrelator = null; if ("s1" == "vitrine") { changeCorrelator = false; } } catch (a) {} try { if ("728x90;970x90;970x250;1272x250".search("300x250") != -1) { mediatype = { native: { type: "image", sendTargetingKeys: false, image: { required: true }, title: { required: true, len: 90 }, }, }; } else { mediatype = null; } } catch (a) {} try { customCriteria = window.keyGen(kw, adSizes); } catch (a) { customCriteria = { seg: ["error"] }; } googleTag = "br.terra.homepage/home360" == "/" ? window.AdManager.get("google_tag") : "/1211/br.terra.homepage/home360"; googleTag_area = googleTag + "/s1"; if (adserver == "prebid") { bidders = [{ bidder: "rubicon", params: { accountId: "10828", siteId: "25078", zoneId: "web" != "mob" ? "97396" : "100306", inventory: { adunit: googleTag_area.replace(/^\/.+?\//, "").replace(/\./g, "_"), viewport: "s" + window.AdManager.getCurrentViewport(), }, visitor: customCriteria, }, }, { bidder: "appnexus", params: { placementId: "********", keywords: customCriteria }, }, { bidder: "smartadserver", params: { domain: "https://prg.smartadserver.com", siteId: 254198, pageId: "web" != "mob" ? 1354600 : 1354599, formatId: parseInt("68543"), }, }, { bidder: "criteo", params: { networkId: 6729 } }, { bidder: "medianet", params: { cid: "8CUDV2PQ3", crid: "*********" } }, { bidder: "teads", params: { placementId: 220849, pageId: 204602, }, }, { bidder: "taboola", params: { publisherId: '1826887' }, }]; if ("web" == "mob") { bidders.push({ bidder: "seedtag", params: { adUnitId: "********" == "" ? "********" : "********", publisherId: "8105-5884-01", placement: "" == "" ? "inBanner" : "", } }); } } else { bidders = []; } try { window.AdManager.defineAd({ platform: "web", adserver: adserver, type: "full", bids: bidders, sizes: slotSize, google_tag: googleTag_area, keywords: customCriteria, timeout: 3000, reloadRule: { reload: reloadKey, loop: reloadTime * 1000, type: reloadViewable, times: 1000, }, changeCorrelator: changeCorrelator, mediaTypes: mediatype, }); } catch (a) {}})();(function() { adSizes = "300x250"; try { slotSize = window.sizeGen(adSizes); } catch (a) { slotSize = [ [1, 1] ]; } if (top.AdManager.get("prebid") != "false") { if ("true" == "true") { adserver = "prebid"; } else { adserver = "dfp"; } } else { adserver = "dfp"; } if ("true" == "true") { reloadKey = true; reloadTime = 35; if ("" != "") { reloadTime = parseInt(""); } } else { reloadKey = false; reloadTime = 0; } if ("" != "false") { reloadViewable = "viewable"; } else { reloadViewable = "always"; } var kw = ";"; try { if ("s1" == "cabeceira") { if (window.AdManager.get("s0Sticky") && "web" == "web") { kw = kw + "sticky=true;"; } } } catch (a) {} try { var changeCorrelator = null; if ("s1" == "vitrine") { changeCorrelator = false; } } catch (a) {} try { if ("300x250".search("300x250") != -1) { mediatype = { native: { type: "image", sendTargetingKeys: false, image: { required: true }, title: { required: true, len: 90 }, }, }; } else { mediatype = null; } } catch (a) {} try { customCriteria = window.keyGen(kw, adSizes); } catch (a) { customCriteria = { seg: ["error"] }; } googleTag = "br.terra.homepage/home360" == "/" ? window.AdManager.get("google_tag") : "/1211/br.terra.homepage/home360"; googleTag_area = googleTag + "/s1"; if (adserver == "prebid") { bidders = [{ bidder: "rubicon", params: { accountId: "10828", siteId: "25078", zoneId: "web" != "mob" ? "97396" : "100306", inventory: { adunit: googleTag_area.replace(/^\/.+?\//, "").replace(/\./g, "_"), viewport: "s" + window.AdManager.getCurrentViewport(), }, visitor: customCriteria, }, }, { bidder: "appnexus", params: { placementId: "********", keywords: customCriteria }, }, { bidder: "smartadserver", params: { domain: "https://prg.smartadserver.com", siteId: 254198, pageId: "web" != "mob" ? 1354600 : 1354599, formatId: parseInt("68541"), }, }, { bidder: "criteo", params: { networkId: 6729 } }, { bidder: "medianet", params: { cid: "8CUDV2PQ3", crid: "*********" } }, { bidder: "teads", params: { placementId: 220849, pageId: 204602, }, }, { bidder: "taboola", params: { publisherId: '1826887' }, }]; if ("web" == "mob") { bidders.push({ bidder: "seedtag", params: { adUnitId: "********" == "" ? "********" : "********", publisherId: "8105-5884-01", placement: "" == "" ? "inBanner" : "", } }); } } else { bidders = []; } try { window.AdManager.defineAd({ platform: "web", adserver: adserver, type: "card", bids: bidders, sizes: slotSize, google_tag: googleTag_area, keywords: customCriteria, timeout: 3000, reloadRule: { reload: reloadKey, loop: reloadTime * 1000, type: reloadViewable, times: 1000, }, changeCorrelator: changeCorrelator, mediaTypes: mediatype, }); } catch (a) {}})();(function() { adSizes = "300x250"; try { slotSize = window.sizeGen(adSizes); } catch (a) { slotSize = [ [1, 1] ]; } if (top.AdManager.get("prebid") != "false") { if ("true" == "true") { adserver = "prebid"; } else { adserver = "dfp"; } } else { adserver = "dfp"; } if ("true" == "true") { reloadKey = true; reloadTime = 35; if ("" != "") { reloadTime = parseInt(""); } } else { reloadKey = false; reloadTime = 0; } if ("" != "false") { reloadViewable = "viewable"; } else { reloadViewable = "always"; } var kw = ";"; try { if ("s1" == "cabeceira") { if (window.AdManager.get("s0Sticky") && "mob" == "web") { kw = kw + "sticky=true;"; } } } catch (a) {} try { var changeCorrelator = null; if ("s1" == "vitrine") { changeCorrelator = false; } } catch (a) {} try { if ("300x250".search("300x250") != -1) { mediatype = { native: { type: "image", sendTargetingKeys: false, image: { required: true }, title: { required: true, len: 90 }, }, }; } else { mediatype = null; } } catch (a) {} try { customCriteria = window.keyGen(kw, adSizes); } catch (a) { customCriteria = { seg: ["error"] }; } googleTag = "br.terra.homepage/home360" == "/" ? window.AdManager.get("google_tag") : "/1211/br.terra.homepage/home360"; googleTag_area = googleTag + "/s1"; if (adserver == "prebid") { bidders = [{ bidder: "rubicon", params: { accountId: "10828", siteId: "25078", zoneId: "mob" != "mob" ? "97396" : "100306", inventory: { adunit: googleTag_area.replace(/^\/.+?\//, "").replace(/\./g, "_"), viewport: "s" + window.AdManager.getCurrentViewport(), }, visitor: customCriteria, }, }, { bidder: "appnexus", params: { placementId: "********", keywords: customCriteria }, }, { bidder: "smartadserver", params: { domain: "https://prg.smartadserver.com", siteId: 254198, pageId: "mob" != "mob" ? 1354600 : 1354599, formatId: parseInt("68541"), }, }, { bidder: "criteo", params: { networkId: 6729 } }, { bidder: "medianet", params: { cid: "8CUDV2PQ3", crid: "*********" } }, { bidder: "teads", params: { placementId: 220849, pageId: 204602, }, }, { bidder: "taboola", params: { publisherId: '1826887' }, }]; if ("mob" == "mob") { bidders.push({ bidder: "seedtag", params: { adUnitId: "********" == "" ? "********" : "********", publisherId: "8105-5884-01", placement: "" == "" ? "inBanner" : "", } }); } } else { bidders = []; } try { window.AdManager.defineAd({ platform: "mob", adserver: adserver, type: "card", bids: bidders, sizes: slotSize, google_tag: googleTag_area, keywords: customCriteria, timeout: 3000, reloadRule: { reload: reloadKey, loop: reloadTime * 1000, type: reloadViewable, times: 1000, }, changeCorrelator: changeCorrelator, mediaTypes: mediatype, }); } catch (a) {}})(); } } catch(e) { }


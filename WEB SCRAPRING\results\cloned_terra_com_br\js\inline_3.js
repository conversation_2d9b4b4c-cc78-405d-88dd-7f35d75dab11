
zaz.use(function(pkg){
/***** TAGMAN DEFINIÇOES DAS AREAS ****/
pkg.context.publisher.set("env", "prd");
pkg.context.publisher.set("template", "home");
pkg.context.platform.set("type", "web");
pkg.context.browser.set("agentProfile","modern");

pkg.context.page.set("isHome", true);
pkg.context.page.set("country", "br");
pkg.context.page.set("lang", "pt");
pkg.context.page.set("locale", "pt-BR");
pkg.context.page.set("channel", "home");
pkg.context.page.set("idItemMenu","home");
pkg.context.page.set("subjectTables","8a8775b4786b2310VgnVCM3000009af154d0RCRD|6,2d19f517cd779310VgnVCM5000009ccceb0aRCRD|8,4e9775b4786b2310VgnVCM3000009af154d0RCRD|10,187775b4786b2310VgnVCM3000009af154d0RCRD|9,562f4ce4eaea4310VgnVCM3000009af154d0RCRD|27,app.t360.customNews.1649923|40,app.t360.customNews.1649924|40,app.t360.customNews.1649921|40,app.t360.customNews.1649919|40,app.t360.customNews.1649920|40,app.t360.customNews.1649922|40,app.t360.customNews.1649909|40,app.t360.customNews.1649910|40,app.t360.customNews.1649915|40,app.t360.customNews.1649913|40,app.t360.customNews.1649912|40,app.t360.customNews.1649911|40,app.t360.customNews.1649914|40,app.t360.customNews.1649843|40,app.t360.customNews.1649908|40,app.t360.customNews.1649847|40,app.t360.customNews.1649845|40,app.t360.customNews.1649844|40");
pkg.context.page.set("specialCoverageCards","1266db6f7194d26121122142d3e79e3do7sxkgtu,b8443aca7323a310VgnVCM20000099cceb0aRCRD,830e81e5deb28fa42b3190e52b55ed75nmouchff,ebbd507fb533d6f52e4a1d7c7909cf6eolfc71pn,05b4ca652d218310VgnVCM4000009bcceb0aRCRD,6ecf93beb6345d02a2cd282cecc686f59md0i07z,6f22bd10e1c4a310VgnVCM4000009bcceb0aRCRD,50999de762a7a310VgnVCM3000009acceb0aRCRD,4f8333b10cf2c04c474b63655ed0626ahj8u2ym5,0e20577eba149d7d712ed1d705f649b8h8sk435z,c6459cf3f1d2a310VgnVCM3000009acceb0aRCRD");
pkg.context.page.set("pageSize", "80");
pkg.context.page.set("timelineSize", "240");
pkg.context.page.set("dflt", "0");
pkg.context.page.set("prioritized", "");
pkg.context.page.set("isLatestPage", false);
pkg.context.page.set("cardTypes", "");
pkg.context.page.set("lomas", "br.*.*");
pkg.context.page.set("countryLive", "br");
pkg.context.page.set("uuid", "eb762420-6ba6-48bc-811e-a7da4dd7700c");
pkg.context.page.set("channelID", "20e07ef2795b2310VgnVCM3000009af154d0RCRD");
pkg.context.page.set("channelPath", "Brasil");
pkg.context.page.set('ticker', true);
pkg.context.page.set('header', true);
pkg.context.page.set('menu_contextual', false);
pkg.context.page.set('editorialTable', 'editorial-1');
pkg.context.page.set('playerHighlight', 'low');
pkg.context.page.set('nativeAd', 2);
pkg.context.page.set('hasPagination', true);
pkg.context.page.set("musaIdTeam", 0);
pkg.context.page.set("truvid", '4e9775b4786b2310VgnVCM3000009af154d0RCRD');
pkg.context.page.set("landingColor","");
pkg.context.page.set("loadAds",true);
pkg.context.page.set("isAgeRestrictive","false");

pkg.context.page.set("dateResponse", '2025-09-07 21:40');
    pkg.context.page.set("mediaService", false);







});

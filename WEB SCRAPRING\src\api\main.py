"""
FastAPI Main - API REST para WebScraper Empresarial.

Esta API fornece endpoints para gerenciamento, monitoramento e
controle do sistema de web scraping.
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, List, Optional

import structlog
from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import PlainTextResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST

from ..core.alerts import alert_manager
from ..core.config import get_settings
from ..core.database import init_database, close_database, get_db_session
from ..core.elasticsearch_client import init_elasticsearch, close_elasticsearch, elasticsearch_client
from ..core.logging import configure_logging
from ..core.metrics import get_metrics, get_content_type
from ..core.redis_client import init_redis, close_redis, redis_client
# from .routers import crawl, domains, pages, analytics, system

logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gerenciar ciclo de vida da aplicação."""
    logger.info("Starting WebScraper API")
    
    # Inicializar sistemas
    try:
        await init_database()
        await init_redis()
        await init_elasticsearch()
        
        # Iniciar loop de alertas em background
        alert_task = asyncio.create_task(alert_manager.evaluate_all_rules())
        
        logger.info("All systems initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to initialize systems", error=str(e))
        raise
    
    finally:
        # Cleanup
        logger.info("Shutting down WebScraper API")
        
        try:
            alert_task.cancel()
            await close_elasticsearch()
            await close_redis()
            await close_database()
        except Exception as e:
            logger.error("Error during shutdown", error=str(e))


# Configurar aplicação
settings = get_settings()
configure_logging(level=settings.log_level, structured=True)

app = FastAPI(
    title="WebScraper Enterprise API",
    description="API REST para gerenciamento e monitoramento do WebScraper",
    version="3.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configurar adequadamente em produção
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Incluir routers (serão criados)
# app.include_router(crawl.router, prefix="/api/v1/crawl", tags=["Crawling"])
# app.include_router(domains.router, prefix="/api/v1/domains", tags=["Domains"])
# app.include_router(pages.router, prefix="/api/v1/pages", tags=["Pages"])
# app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["Analytics"])
# app.include_router(system.router, prefix="/api/v1/system", tags=["System"])


# ================================
# Health & Monitoring Endpoints
# ================================

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Verificar sistemas principais
        db_health = {"status": "unknown"}
        redis_health = {"status": "unknown"}
        es_health = {"status": "unknown"}
        
        try:
            from ..core.database import db_manager
            db_health = await db_manager.health_check()
        except Exception as e:
            db_health = {"status": "unhealthy", "error": str(e)}
        
        try:
            redis_health = await redis_client.health_check()
        except Exception as e:
            redis_health = {"status": "unhealthy", "error": str(e)}
        
        try:
            es_health = await elasticsearch_client.health_check()
        except Exception as e:
            es_health = {"status": "unhealthy", "error": str(e)}
        
        # Status geral
        all_healthy = all(
            health.get("status") == "healthy"
            for health in [db_health, redis_health, es_health]
        )
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "3.0.0",
            "services": {
                "database": db_health,
                "redis": redis_health,
                "elasticsearch": es_health
            }
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unavailable")


@app.get("/metrics", response_class=PlainTextResponse)
async def metrics_endpoint():
    """Endpoint de métricas Prometheus."""
    try:
        return get_metrics()
    except Exception as e:
        logger.error("Failed to generate metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to generate metrics")


@app.get("/")
async def root():
    """Endpoint raiz."""
    return {
        "service": "WebScraper Enterprise API",
        "version": "3.0.0",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "docs": "/docs",
        "health": "/health",
        "metrics": "/metrics"
    }


# ================================
# Quick Actions Endpoints
# ================================

@app.post("/api/v1/quick/crawl")
async def quick_crawl(
    domain: str,
    max_pages: int = Query(10, ge=1, le=1000),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Iniciar crawling rápido de um domínio."""
    try:
        # Validar domínio
        if not domain or "." not in domain:
            raise HTTPException(status_code=400, detail="Invalid domain")
        
        # Criar task em background
        from ..flows.enterprise_flow import enterprise_webscraper_flow
        
        background_tasks.add_task(
            enterprise_webscraper_flow,
            domain=domain,
            max_pages=max_pages,
            incremental=False,
            dry_run=False
        )
        
        return {
            "message": f"Crawling started for {domain}",
            "domain": domain,
            "max_pages": max_pages,
            "status": "queued"
        }
        
    except Exception as e:
        logger.error("Quick crawl failed", domain=domain, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/quick/stats")
async def quick_stats():
    """Obter estatísticas rápidas do sistema."""
    try:
        # Stats do banco
        db_stats = {"error": "unavailable"}
        try:
            from ..core.database import db_manager
            db_stats = await db_manager.get_stats()
        except Exception:
            pass
        
        # Stats do Redis
        redis_stats = {"error": "unavailable"}
        try:
            redis_stats = await redis_client.get_stats()
        except Exception:
            pass
        
        # Stats do Elasticsearch
        es_stats = {"error": "unavailable"}
        try:
            es_stats = await elasticsearch_client.get_stats()
        except Exception:
            pass
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "database": db_stats,
            "redis": redis_stats,
            "elasticsearch": es_stats
        }
        
    except Exception as e:
        logger.error("Quick stats failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/quick/search")
async def quick_search(
    q: str = Query(..., min_length=1),
    size: int = Query(10, ge=1, le=100)
):
    """Busca rápida de páginas."""
    try:
        results = await elasticsearch_client.search_pages(
            query=q,
            size=size
        )
        
        return {
            "query": q,
            "total": results["total"],
            "results": [
                {
                    "url": page["source"]["url"],
                    "title": page["source"]["title"],
                    "quality_score": page["source"]["quality_score"],
                    "domain": page["source"]["domain"],
                    "score": page["score"],
                    "highlight": page.get("highlight", {})
                }
                for page in results["pages"]
            ]
        }
        
    except Exception as e:
        logger.error("Quick search failed", query=q, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# ================================
# Cache Management
# ================================

@app.delete("/api/v1/cache/clear")
async def clear_cache(pattern: str = "*"):
    """Limpar cache Redis."""
    try:
        deleted = await redis_client.cache_clear_pattern(pattern)
        
        return {
            "message": f"Cache cleared",
            "pattern": pattern,
            "deleted_keys": deleted
        }
        
    except Exception as e:
        logger.error("Cache clear failed", pattern=pattern, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/cache/stats")
async def cache_stats():
    """Obter estatísticas do cache."""
    try:
        stats = await redis_client.get_stats()
        return stats
        
    except Exception as e:
        logger.error("Cache stats failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# ================================
# Alert Management
# ================================

@app.get("/api/v1/alerts")
async def get_alerts():
    """Obter alertas ativos."""
    try:
        alerts = await alert_manager.get_active_alerts()
        return {"alerts": alerts}
        
    except Exception as e:
        logger.error("Get alerts failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/alerts/{alert_id}/silence")
async def silence_alert(alert_id: str, duration_minutes: int = 60):
    """Silenciar alerta."""
    try:
        from datetime import timedelta
        
        success = await alert_manager.silence_alert(
            alert_id,
            timedelta(minutes=duration_minutes)
        )
        
        if success:
            return {"message": f"Alert {alert_id} silenced for {duration_minutes} minutes"}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
            
    except Exception as e:
        logger.error("Silence alert failed", alert_id=alert_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# ================================
# Error Handlers
# ================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {
        "error": "Not Found",
        "message": "The requested resource was not found",
        "status_code": 404
    }


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error("Internal server error", error=str(exc))
    return {
        "error": "Internal Server Error",
        "message": "An unexpected error occurred",
        "status_code": 500
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )

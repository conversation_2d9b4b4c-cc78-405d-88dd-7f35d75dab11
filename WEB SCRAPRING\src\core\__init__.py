"""
Core - Componentes fundamentais do WebScraper.

Este módulo contém as classes e funções base que são usadas por todo o sistema:

- http_client: Cliente HTTP assíncrono com retries e cache
- browser: Wrapper para Playwright com esperas robustas  
- parser_base: Classe base para parsers específicos
- normalize: Funções de limpeza e normalização de conteúdo
- validators: Modelos Pydantic para validação de dados
- storage: Abstração para diferentes tipos de storage
"""

from .http_client import AsyncHTTPClient
from .parser_base import BaseParser
from .validators import PageData, ScrapingConfig
from .storage import StorageManager

__all__ = [
    "AsyncHTTPClient",
    "BaseParser",
    "PageData", 
    "ScrapingConfig",
    "StorageManager",
]

"""
Alerts - Sistema de alertas e monitoramento.

Este módulo implementa alertas baseados em métricas, SLOs e condições
customizáveis para monitoramento proativo do WebScraper.
"""

import asyncio
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import structlog
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from .database import get_db_session
from .metrics import metrics_collector
from .models import Alert, Metric

logger = structlog.get_logger(__name__)


class AlertSeverity(Enum):
    """Severidade dos alertas."""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


class AlertStatus(Enum):
    """Status dos alertas."""
    FIRING = "firing"
    RESOLVED = "resolved"
    SILENCED = "silenced"


class AlertRule:
    """Regra de alerta."""
    
    def __init__(
        self,
        name: str,
        condition: str,
        threshold: Union[int, float],
        severity: AlertSeverity,
        component: str,
        description: str = "",
        duration: timedelta = timedelta(minutes=5),
        labels: Optional[Dict[str, str]] = None,
    ):
        self.name = name
        self.condition = condition
        self.threshold = threshold
        self.severity = severity
        self.component = component
        self.description = description
        self.duration = duration
        self.labels = labels or {}
        self.last_evaluation = None
        self.firing_since = None
    
    async def evaluate(self, session: AsyncSession) -> Optional[Dict]:
        """Avaliar regra de alerta."""
        try:
            current_value = await self._get_current_value(session)
            
            if current_value is None:
                return None
            
            # Verificar condição
            is_firing = self._check_condition(current_value)
            
            self.last_evaluation = datetime.utcnow()
            
            if is_firing:
                if self.firing_since is None:
                    self.firing_since = datetime.utcnow()
                
                # Verificar se já está disparando há tempo suficiente
                if datetime.utcnow() - self.firing_since >= self.duration:
                    return {
                        "name": self.name,
                        "severity": self.severity.value,
                        "condition": self.condition,
                        "threshold": self.threshold,
                        "current_value": current_value,
                        "component": self.component,
                        "description": self.description,
                        "labels": self.labels,
                        "firing_since": self.firing_since,
                    }
            else:
                self.firing_since = None
            
            return None
            
        except Exception as e:
            logger.error("Failed to evaluate alert rule", rule=self.name, error=str(e))
            return None
    
    async def _get_current_value(self, session: AsyncSession) -> Optional[float]:
        """Obter valor atual da métrica."""
        # Implementar queries específicas baseadas na condição
        if "error_rate" in self.condition:
            return await self._calculate_error_rate(session)
        elif "response_time" in self.condition:
            return await self._calculate_avg_response_time(session)
        elif "quality_score" in self.condition:
            return await self._calculate_avg_quality_score(session)
        elif "storage_failures" in self.condition:
            return await self._count_storage_failures(session)
        elif "database_connections" in self.condition:
            return await self._get_database_connections(session)
        
        return None
    
    def _check_condition(self, current_value: float) -> bool:
        """Verificar se condição é atendida."""
        if ">" in self.condition:
            return current_value > self.threshold
        elif "<" in self.condition:
            return current_value < self.threshold
        elif ">=" in self.condition:
            return current_value >= self.threshold
        elif "<=" in self.condition:
            return current_value <= self.threshold
        elif "==" in self.condition:
            return abs(current_value - self.threshold) < 0.001
        
        return False
    
    async def _calculate_error_rate(self, session: AsyncSession) -> float:
        """Calcular taxa de erro dos últimos 5 minutos."""
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        
        # Query para contar erros vs sucessos
        stmt = select(Metric).where(
            Metric.name == "webscraper_http_requests_total",
            Metric.timestamp >= five_minutes_ago
        )
        
        result = await session.execute(stmt)
        metrics = result.scalars().all()
        
        total_requests = 0
        error_requests = 0
        
        for metric in metrics:
            labels = metric.labels or {}
            status_code = labels.get("status_code", "200")
            
            total_requests += metric.value
            if status_code.startswith(("4", "5")):
                error_requests += metric.value
        
        if total_requests == 0:
            return 0.0
        
        return (error_requests / total_requests) * 100
    
    async def _calculate_avg_response_time(self, session: AsyncSession) -> float:
        """Calcular tempo médio de resposta dos últimos 5 minutos."""
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        
        stmt = select(Metric).where(
            Metric.name == "webscraper_http_request_duration_seconds",
            Metric.timestamp >= five_minutes_ago
        )
        
        result = await session.execute(stmt)
        metrics = result.scalars().all()
        
        if not metrics:
            return 0.0
        
        total_time = sum(m.value for m in metrics)
        return total_time / len(metrics)
    
    async def _calculate_avg_quality_score(self, session: AsyncSession) -> float:
        """Calcular score médio de qualidade dos últimos 30 minutos."""
        thirty_minutes_ago = datetime.utcnow() - timedelta(minutes=30)
        
        stmt = select(Metric).where(
            Metric.name == "webscraper_content_quality_score",
            Metric.timestamp >= thirty_minutes_ago
        )
        
        result = await session.execute(stmt)
        metrics = result.scalars().all()
        
        if not metrics:
            return 100.0  # Assumir qualidade boa se não há dados
        
        total_score = sum(m.value for m in metrics)
        return total_score / len(metrics)
    
    async def _count_storage_failures(self, session: AsyncSession) -> float:
        """Contar falhas de storage dos últimos 10 minutos."""
        ten_minutes_ago = datetime.utcnow() - timedelta(minutes=10)
        
        stmt = select(Metric).where(
            Metric.name == "webscraper_storage_operations_total",
            Metric.timestamp >= ten_minutes_ago
        )
        
        result = await session.execute(stmt)
        metrics = result.scalars().all()
        
        failures = 0
        for metric in metrics:
            labels = metric.labels or {}
            if labels.get("status") == "error":
                failures += metric.value
        
        return failures
    
    async def _get_database_connections(self, session: AsyncSession) -> float:
        """Obter número atual de conexões do banco."""
        # Esta seria uma métrica em tempo real
        return 0.0  # Placeholder


class AlertManager:
    """Gerenciador de alertas."""
    
    def __init__(self):
        self.rules: List[AlertRule] = []
        self.notification_channels: List[Dict] = []
        self.active_alerts: Dict[str, Alert] = {}
        
        # Configurar regras padrão
        self._setup_default_rules()
    
    def _setup_default_rules(self) -> None:
        """Configurar regras de alerta padrão."""
        
        # Taxa de erro alta
        self.rules.append(AlertRule(
            name="high_error_rate",
            condition="error_rate > threshold",
            threshold=10.0,  # 10%
            severity=AlertSeverity.CRITICAL,
            component="http_client",
            description="Taxa de erro HTTP acima de 10% nos últimos 5 minutos",
            duration=timedelta(minutes=2),
        ))
        
        # Tempo de resposta alto
        self.rules.append(AlertRule(
            name="high_response_time",
            condition="response_time > threshold",
            threshold=10.0,  # 10 segundos
            severity=AlertSeverity.WARNING,
            component="http_client",
            description="Tempo médio de resposta acima de 10s nos últimos 5 minutos",
            duration=timedelta(minutes=3),
        ))
        
        # Qualidade baixa
        self.rules.append(AlertRule(
            name="low_content_quality",
            condition="quality_score < threshold",
            threshold=30.0,
            severity=AlertSeverity.WARNING,
            component="parser",
            description="Score médio de qualidade abaixo de 30 nos últimos 30 minutos",
            duration=timedelta(minutes=10),
        ))
        
        # Falhas de storage
        self.rules.append(AlertRule(
            name="storage_failures",
            condition="storage_failures > threshold",
            threshold=5.0,
            severity=AlertSeverity.CRITICAL,
            component="storage",
            description="Mais de 5 falhas de storage nos últimos 10 minutos",
            duration=timedelta(minutes=1),
        ))
        
        # Muitas conexões de banco
        self.rules.append(AlertRule(
            name="high_database_connections",
            condition="database_connections > threshold",
            threshold=50.0,
            severity=AlertSeverity.WARNING,
            component="database",
            description="Mais de 50 conexões ativas no banco de dados",
            duration=timedelta(minutes=5),
        ))
    
    def add_rule(self, rule: AlertRule) -> None:
        """Adicionar regra de alerta."""
        self.rules.append(rule)
        logger.info("Alert rule added", rule_name=rule.name)
    
    def add_notification_channel(
        self,
        channel_type: str,
        config: Dict[str, Any]
    ) -> None:
        """Adicionar canal de notificação."""
        channel = {
            "type": channel_type,
            "config": config,
            "enabled": True,
        }
        self.notification_channels.append(channel)
        logger.info("Notification channel added", type=channel_type)
    
    async def evaluate_all_rules(self) -> List[Dict]:
        """Avaliar todas as regras de alerta."""
        new_alerts = []
        
        async with get_db_session() as session:
            for rule in self.rules:
                try:
                    alert_data = await rule.evaluate(session)
                    
                    if alert_data:
                        # Verificar se já existe alerta ativo
                        existing_alert = await self._get_existing_alert(session, rule.name)
                        
                        if not existing_alert:
                            # Criar novo alerta
                            alert = await self._create_alert(session, alert_data)
                            new_alerts.append(alert_data)
                            
                            # Enviar notificações
                            await self._send_notifications(alert_data)
                        else:
                            # Atualizar alerta existente
                            await self._update_alert(session, existing_alert, alert_data)
                    
                    else:
                        # Verificar se há alerta ativo para resolver
                        existing_alert = await self._get_existing_alert(session, rule.name)
                        if existing_alert and existing_alert.status == AlertStatus.FIRING.value:
                            await self._resolve_alert(session, existing_alert)
                
                except Exception as e:
                    logger.error("Failed to evaluate rule", rule=rule.name, error=str(e))
        
        return new_alerts
    
    async def _get_existing_alert(self, session: AsyncSession, rule_name: str) -> Optional[Alert]:
        """Obter alerta existente."""
        stmt = select(Alert).where(
            Alert.name == rule_name,
            Alert.status == AlertStatus.FIRING.value
        )
        
        result = await session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def _create_alert(self, session: AsyncSession, alert_data: Dict) -> Alert:
        """Criar novo alerta."""
        alert = Alert(
            name=alert_data["name"],
            severity=alert_data["severity"],
            condition=alert_data["condition"],
            threshold=alert_data["threshold"],
            current_value=alert_data["current_value"],
            component=alert_data["component"],
            summary=f"{alert_data['name']}: {alert_data['description']}",
            description=alert_data["description"],
            labels=alert_data["labels"],
            status=AlertStatus.FIRING.value,
        )
        
        session.add(alert)
        await session.commit()
        
        logger.warning(
            "Alert fired",
            alert_name=alert.name,
            severity=alert.severity,
            current_value=alert.current_value,
            threshold=alert.threshold,
        )
        
        return alert
    
    async def _update_alert(self, session: AsyncSession, alert: Alert, alert_data: Dict) -> None:
        """Atualizar alerta existente."""
        alert.current_value = alert_data["current_value"]
        await session.commit()
    
    async def _resolve_alert(self, session: AsyncSession, alert: Alert) -> None:
        """Resolver alerta."""
        alert.status = AlertStatus.RESOLVED.value
        alert.resolved_at = datetime.utcnow()
        await session.commit()
        
        logger.info("Alert resolved", alert_name=alert.name)
        
        # Enviar notificação de resolução
        await self._send_resolution_notification(alert)
    
    async def _send_notifications(self, alert_data: Dict) -> None:
        """Enviar notificações para todos os canais."""
        for channel in self.notification_channels:
            if not channel["enabled"]:
                continue
            
            try:
                if channel["type"] == "webhook":
                    await self._send_webhook_notification(channel["config"], alert_data)
                elif channel["type"] == "email":
                    await self._send_email_notification(channel["config"], alert_data)
                elif channel["type"] == "slack":
                    await self._send_slack_notification(channel["config"], alert_data)
                
            except Exception as e:
                logger.error(
                    "Failed to send notification",
                    channel_type=channel["type"],
                    alert=alert_data["name"],
                    error=str(e),
                )
    
    async def _send_webhook_notification(self, config: Dict, alert_data: Dict) -> None:
        """Enviar notificação via webhook."""
        import aiohttp
        
        payload = {
            "alert": alert_data,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "alert_fired",
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                config["url"],
                json=payload,
                headers=config.get("headers", {}),
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                if response.status >= 400:
                    logger.error("Webhook notification failed", status=response.status)
    
    async def _send_email_notification(self, config: Dict, alert_data: Dict) -> None:
        """Enviar notificação via email."""
        # Implementar envio de email
        logger.info("Email notification would be sent", alert=alert_data["name"])
    
    async def _send_slack_notification(self, config: Dict, alert_data: Dict) -> None:
        """Enviar notificação via Slack."""
        # Implementar notificação Slack
        logger.info("Slack notification would be sent", alert=alert_data["name"])
    
    async def _send_resolution_notification(self, alert: Alert) -> None:
        """Enviar notificação de resolução."""
        resolution_data = {
            "name": alert.name,
            "severity": alert.severity,
            "resolved_at": alert.resolved_at.isoformat(),
            "duration": (alert.resolved_at - alert.fired_at).total_seconds(),
        }
        
        for channel in self.notification_channels:
            if not channel["enabled"]:
                continue
            
            try:
                if channel["type"] == "webhook":
                    payload = {
                        "alert": resolution_data,
                        "timestamp": datetime.utcnow().isoformat(),
                        "type": "alert_resolved",
                    }
                    
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        await session.post(channel["config"]["url"], json=payload)
                
            except Exception as e:
                logger.error("Failed to send resolution notification", error=str(e))
    
    async def get_active_alerts(self) -> List[Dict]:
        """Obter alertas ativos."""
        async with get_db_session() as session:
            stmt = select(Alert).where(Alert.status == AlertStatus.FIRING.value)
            result = await session.execute(stmt)
            alerts = result.scalars().all()
            
            return [
                {
                    "id": str(alert.id),
                    "name": alert.name,
                    "severity": alert.severity,
                    "component": alert.component,
                    "summary": alert.summary,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "fired_at": alert.fired_at.isoformat(),
                    "labels": alert.labels,
                }
                for alert in alerts
            ]
    
    async def silence_alert(self, alert_id: str, duration: timedelta) -> bool:
        """Silenciar alerta por um período."""
        async with get_db_session() as session:
            stmt = select(Alert).where(Alert.id == alert_id)
            result = await session.execute(stmt)
            alert = result.scalar_one_or_none()
            
            if alert:
                alert.status = AlertStatus.SILENCED.value
                await session.commit()
                
                # Agendar reativação
                asyncio.create_task(self._unsilence_after_duration(alert_id, duration))
                
                logger.info("Alert silenced", alert_id=alert_id, duration=duration)
                return True
            
            return False
    
    async def _unsilence_after_duration(self, alert_id: str, duration: timedelta) -> None:
        """Reativar alerta após duração."""
        await asyncio.sleep(duration.total_seconds())
        
        async with get_db_session() as session:
            stmt = update(Alert).where(
                Alert.id == alert_id,
                Alert.status == AlertStatus.SILENCED.value
            ).values(status=AlertStatus.FIRING.value)
            
            await session.execute(stmt)
            await session.commit()
            
            logger.info("Alert unsilenced", alert_id=alert_id)


# Instância global do gerenciador de alertas
alert_manager = AlertManager()


async def run_alert_evaluation_loop() -> None:
    """Loop principal de avaliação de alertas."""
    logger.info("Starting alert evaluation loop")
    
    while True:
        try:
            await alert_manager.evaluate_all_rules()
            await asyncio.sleep(60)  # Avaliar a cada minuto
            
        except Exception as e:
            logger.error("Error in alert evaluation loop", error=str(e))
            await asyncio.sleep(60)

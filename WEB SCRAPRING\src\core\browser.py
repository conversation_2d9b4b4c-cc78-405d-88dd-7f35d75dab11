"""
Browser - Sistema de browser headless com Playwright.

Este módulo implementa um wrapper robusto para Playwright com:
- Pool de browsers para performance
- Esperas inteligentes e robustas
- Screenshots automáticos em caso de erro
- Gestão de recursos e timeouts
- Suporte a diferentes tipos de conteúdo dinâmico
"""

import asyncio
import hashlib
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set
from urllib.parse import urlparse

import structlog
from playwright.async_api import <PERSON><PERSON><PERSON>, BrowserContext, Page, Playwright, async_playwright

from .validators import DomainConfig, HTTPResponse, ScrapingConfig

logger = structlog.get_logger(__name__)


class BrowserPool:
    """Pool de browsers para otimizar performance."""
    
    def __init__(self, pool_size: int = 3, browser_type: str = "chromium"):
        self.pool_size = pool_size
        self.browser_type = browser_type
        self.browsers: List[Browser] = []
        self.available_browsers: asyncio.Queue = asyncio.Queue()
        self.playwright: Optional[Playwright] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Inicializar pool de browsers."""
        if self._initialized:
            return
        
        logger.info("Initializing browser pool", pool_size=self.pool_size, browser_type=self.browser_type)
        
        self.playwright = await async_playwright().start()
        
        # Criar browsers no pool
        for i in range(self.pool_size):
            browser = await self._create_browser()
            self.browsers.append(browser)
            await self.available_browsers.put(browser)
            
            logger.debug("Browser created", browser_id=i, total_browsers=len(self.browsers))
        
        self._initialized = True
        logger.info("Browser pool initialized successfully")
    
    async def _create_browser(self) -> Browser:
        """Criar um browser com configurações otimizadas."""
        browser_launcher = getattr(self.playwright, self.browser_type)
        
        return await browser_launcher.launch(
            headless=True,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
            ]
        )
    
    async def acquire(self) -> Browser:
        """Adquirir browser do pool."""
        if not self._initialized:
            await self.initialize()
        
        browser = await self.available_browsers.get()
        logger.debug("Browser acquired from pool", available_count=self.available_browsers.qsize())
        return browser
    
    async def release(self, browser: Browser) -> None:
        """Devolver browser ao pool."""
        await self.available_browsers.put(browser)
        logger.debug("Browser released to pool", available_count=self.available_browsers.qsize())
    
    async def close(self) -> None:
        """Fechar todos os browsers e o pool."""
        logger.info("Closing browser pool")
        
        for browser in self.browsers:
            try:
                await browser.close()
            except Exception as e:
                logger.warning("Error closing browser", error=str(e))
        
        if self.playwright:
            await self.playwright.stop()
        
        self.browsers.clear()
        self._initialized = False
        logger.info("Browser pool closed")


class PlaywrightClient:
    """Cliente Playwright com recursos avançados."""
    
    def __init__(self, config: ScrapingConfig, screenshot_path: str = "./data/screenshots"):
        self.config = config
        self.screenshot_path = Path(screenshot_path)
        self.screenshot_path.mkdir(parents=True, exist_ok=True)
        
        # Pool de browsers
        self.browser_pool = BrowserPool(
            pool_size=3,  # TODO: Configurável
            browser_type="chromium"
        )
        
        # Estatísticas
        self.stats = {
            "pages_processed": 0,
            "screenshots_taken": 0,
            "errors": 0,
            "total_wait_time": 0,
        }
    
    async def __aenter__(self):
        """Inicializar cliente."""
        await self.browser_pool.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Fechar cliente."""
        await self.browser_pool.close()
    
    async def fetch_dynamic_page(self, url: str, domain_config: DomainConfig) -> HTTPResponse:
        """Fazer fetch de página dinâmica com Playwright."""
        start_time = time.time()
        
        logger.info("Fetching dynamic page", url=url, wait_for_selector=domain_config.wait_for_selector)
        
        browser = await self.browser_pool.acquire()
        context = None
        page = None
        
        try:
            # Criar contexto com configurações
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent=self.config.user_agent,
                ignore_https_errors=True,
            )
            
            # Criar página
            page = await context.new_page()
            
            # Configurar timeouts
            page.set_default_timeout(domain_config.wait_timeout)
            page.set_default_navigation_timeout(domain_config.wait_timeout)
            
            # Navegar para a página
            response = await page.goto(
                url,
                wait_until="domcontentloaded",
                timeout=domain_config.wait_timeout
            )
            
            if not response:
                raise Exception("No response received from page")
            
            # Aguardar conteúdo dinâmico se especificado
            if domain_config.wait_for_selector:
                await self._wait_for_content(page, domain_config)
            else:
                # Aguardar padrão para JavaScript
                await self._wait_for_default_content(page)
            
            # Obter conteúdo final
            content = await page.content()
            
            # Criar resposta HTTP
            duration_ms = int((time.time() - start_time) * 1000)
            
            http_response = HTTPResponse(
                url=url,
                status_code=response.status,
                content=content,
                duration_ms=duration_ms,
            )
            
            self.stats["pages_processed"] += 1
            
            logger.info(
                "Dynamic page fetched successfully",
                url=url,
                status_code=response.status,
                content_length=len(content),
                duration_ms=duration_ms,
            )
            
            return http_response
            
        except Exception as e:
            self.stats["errors"] += 1
            
            # Capturar screenshot em caso de erro
            if page:
                await self._capture_error_screenshot(page, url, str(e))
            
            logger.error(
                "Failed to fetch dynamic page",
                url=url,
                error=str(e),
                exc_info=True,
            )
            raise
            
        finally:
            # Limpar recursos
            if page:
                await page.close()
            if context:
                await context.close()
            
            await self.browser_pool.release(browser)
    
    async def _wait_for_content(self, page: Page, domain_config: DomainConfig) -> None:
        """Aguardar conteúdo específico carregar."""
        wait_start = time.time()
        
        try:
            # Aguardar seletor específico
            if domain_config.wait_for_selector:
                logger.debug("Waiting for selector", selector=domain_config.wait_for_selector)
                
                await page.wait_for_selector(
                    domain_config.wait_for_selector,
                    timeout=domain_config.wait_timeout,
                    state="visible"
                )
            
            # Aguardar rede ficar idle (opcional)
            try:
                await page.wait_for_load_state("networkidle", timeout=5000)
            except Exception:
                # Não é crítico se networkidle falhar
                pass
            
            wait_time = time.time() - wait_start
            self.stats["total_wait_time"] += wait_time
            
            logger.debug(
                "Content wait completed",
                wait_time_ms=int(wait_time * 1000),
                selector=domain_config.wait_for_selector,
            )
            
        except Exception as e:
            logger.warning(
                "Wait for content failed",
                selector=domain_config.wait_for_selector,
                error=str(e),
            )
            # Continuar mesmo se a espera falhar
    
    async def _wait_for_default_content(self, page: Page) -> None:
        """Aguardar conteúdo padrão para páginas JavaScript."""
        wait_start = time.time()
        
        try:
            # Aguardar um pouco para JavaScript executar
            await asyncio.sleep(2)
            
            # Tentar aguardar alguns seletores comuns
            common_selectors = [
                "main", "article", ".content", "#content",
                ".main-content", ".page-content"
            ]
            
            for selector in common_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=2000, state="visible")
                    logger.debug("Found common content selector", selector=selector)
                    break
                except Exception:
                    continue
            
            # Aguardar rede ficar idle
            try:
                await page.wait_for_load_state("networkidle", timeout=3000)
            except Exception:
                pass
            
            wait_time = time.time() - wait_start
            self.stats["total_wait_time"] += wait_time
            
            logger.debug("Default content wait completed", wait_time_ms=int(wait_time * 1000))
            
        except Exception as e:
            logger.debug("Default wait failed", error=str(e))
    
    async def _capture_error_screenshot(self, page: Page, url: str, error: str) -> Optional[str]:
        """Capturar screenshot em caso de erro."""
        try:
            # Gerar nome do arquivo
            url_hash = hashlib.sha256(url.encode('utf-8')).hexdigest()[:16]
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"error_{timestamp}_{url_hash}.png"
            
            screenshot_path = self.screenshot_path / filename
            
            # Capturar screenshot
            await page.screenshot(path=str(screenshot_path), full_page=True)
            
            self.stats["screenshots_taken"] += 1
            
            logger.info(
                "Error screenshot captured",
                url=url,
                screenshot_path=str(screenshot_path),
                error=error,
            )
            
            return str(screenshot_path)
            
        except Exception as e:
            logger.warning(
                "Failed to capture error screenshot",
                url=url,
                error=str(e),
            )
            return None
    
    def get_stats(self) -> Dict:
        """Obter estatísticas do cliente."""
        return {
            **self.stats,
            "average_wait_time_ms": (
                int(self.stats["total_wait_time"] / self.stats["pages_processed"] * 1000)
                if self.stats["pages_processed"] > 0 else 0
            ),
            "error_rate": (
                self.stats["errors"] / self.stats["pages_processed"] * 100
                if self.stats["pages_processed"] > 0 else 0
            ),
        }

"""
Configuration - Sistema de configuração centralizado.

Este módulo gerencia o carregamento e validação de configurações
do WebScraper a partir de arquivos YAML e variáveis de ambiente.
"""

import os
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
import yaml
from pydantic import Field
from pydantic_settings import BaseSettings

from .validators import DomainConfig, ScrapingConfig

logger = structlog.get_logger(__name__)


class Settings(BaseSettings):
    """Configurações globais do sistema."""
    
    # Ambiente
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Banco de dados
    database_url: str = Field(
        default="sqlite+aiosqlite:///./data/webscraper.db",
        env="DATABASE_URL"
    )
    
    # Storage
    storage_type: str = Field(default="filesystem", env="STORAGE_TYPE")
    raw_data_path: str = Field(default="./data/raw", env="RAW_DATA_PATH")
    processed_data_path: str = Field(default="./data/processed", env="PROCESSED_DATA_PATH")
    
    # Cache
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    enable_cache: bool = Field(default=True, env="ENABLE_CACHE")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL_SECONDS")
    
    # Scraping
    default_requests_per_second: float = Field(default=1.0, env="DEFAULT_REQUESTS_PER_SECOND")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")

    # S3/MinIO Storage
    s3_endpoint_url: Optional[str] = Field(default=None, env="S3_ENDPOINT_URL")
    s3_access_key: Optional[str] = Field(default=None, env="S3_ACCESS_KEY")
    s3_secret_key: Optional[str] = Field(default=None, env="S3_SECRET_KEY")
    s3_bucket_name: str = Field(default="webscraper", env="S3_BUCKET_NAME")
    s3_region: str = Field(default="us-east-1", env="S3_REGION")

    # Métricas Prometheus
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    metrics_port: int = Field(default=8000, env="METRICS_PORT")
    metrics_path: str = Field(default="/metrics", env="METRICS_PATH")

    # Sistema de Alertas
    alerts_enabled: bool = Field(default=True, env="ALERTS_ENABLED")
    webhook_url: Optional[str] = Field(default=None, env="WEBHOOK_URL")
    slack_webhook_url: Optional[str] = Field(default=None, env="SLACK_WEBHOOK_URL")
    email_smtp_host: Optional[str] = Field(default=None, env="EMAIL_SMTP_HOST")
    email_smtp_port: int = Field(default=587, env="EMAIL_SMTP_PORT")
    email_username: Optional[str] = Field(default=None, env="EMAIL_USERNAME")
    email_password: Optional[str] = Field(default=None, env="EMAIL_PASSWORD")

    # Coleta Incremental
    incremental_enabled: bool = Field(default=True, env="INCREMENTAL_ENABLED")
    use_etag: bool = Field(default=True, env="USE_ETAG")
    use_last_modified: bool = Field(default=True, env="USE_LAST_MODIFIED")
    incremental_interval_hours: int = Field(default=24, env="INCREMENTAL_INTERVAL_HOURS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT_SECONDS")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    user_agent: str = Field(
        default="WebScraper/1.0 (+mailto:<EMAIL>)",
        env="USER_AGENT"
    )
    
    # Playwright
    playwright_headless: bool = Field(default=True, env="PLAYWRIGHT_HEADLESS")
    playwright_timeout: int = Field(default=30000, env="PLAYWRIGHT_TIMEOUT")
    
    # Observabilidade
    structured_logging: bool = Field(default=True, env="STRUCTURED_LOGGING")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    prometheus_port: int = Field(default=8000, env="PROMETHEUS_PORT")
    
    # Qualidade
    min_quality_score: int = Field(default=50, env="MIN_QUALITY_SCORE")
    min_content_length: int = Field(default=100, env="MIN_CONTENT_LENGTH")
    min_word_count: int = Field(default=20, env="MIN_WORD_COUNT")
    
    # Domínios
    allowed_domains: str = Field(default="", env="ALLOWED_DOMAINS")
    respect_robots_txt: bool = Field(default=True, env="RESPECT_ROBOTS_TXT")
    
    # Features opcionais
    enable_rag: bool = Field(default=False, env="ENABLE_RAG")
    api_enabled: bool = Field(default=False, env="API_ENABLED")
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8080, env="API_PORT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class ConfigManager:
    """Gerenciador de configurações do WebScraper."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.settings = Settings()
        self._domain_configs: Dict[str, DomainConfig] = {}
        self._scraping_config: Optional[ScrapingConfig] = None
    
    def load_config(self) -> ScrapingConfig:
        """Carregar configuração completa de scraping."""
        if self._scraping_config:
            return self._scraping_config
        
        logger.info("Loading scraping configuration", config_file=self.config_file)
        
        # Carregar configurações de domínio
        domain_configs = self._load_domain_configs()
        
        # Criar configuração padrão
        default_config = self._create_default_domain_config()
        
        # Criar configuração de scraping
        self._scraping_config = ScrapingConfig(
            default=default_config,
            domains=domain_configs,
            user_agent=self.settings.user_agent,
            respect_robots_txt=self.settings.respect_robots_txt,
            enable_cache=self.settings.enable_cache,
            cache_ttl=self.settings.cache_ttl,
            max_concurrent_requests=self.settings.max_concurrent_requests,
        )
        
        logger.info(
            "Configuration loaded successfully",
            domains_count=len(domain_configs),
            cache_enabled=self.settings.enable_cache,
            max_concurrent=self.settings.max_concurrent_requests,
        )
        
        return self._scraping_config
    
    def _load_domain_configs(self) -> Dict[str, DomainConfig]:
        """Carregar configurações específicas de domínio."""
        domain_configs = {}
        
        # Tentar carregar do arquivo de configuração
        config_path = self._get_config_path()
        if config_path and config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                domains_data = config_data.get('domains', {})
                for domain, domain_data in domains_data.items():
                    try:
                        domain_config = self._parse_domain_config(domain_data)
                        domain_configs[domain] = domain_config
                        logger.debug("Domain config loaded", domain=domain)
                    except Exception as e:
                        logger.error(
                            "Failed to parse domain config",
                            domain=domain,
                            error=str(e),
                        )
                        
            except Exception as e:
                logger.error(
                    "Failed to load config file",
                    config_path=str(config_path),
                    error=str(e),
                )
        
        # Adicionar domínios das variáveis de ambiente
        if self.settings.allowed_domains:
            env_domains = [d.strip() for d in self.settings.allowed_domains.split(',')]
            for domain in env_domains:
                if domain and domain not in domain_configs:
                    domain_configs[domain] = self._create_default_domain_config()
        
        return domain_configs
    
    def _get_config_path(self) -> Optional[Path]:
        """Obter caminho do arquivo de configuração."""
        if self.config_file:
            return Path(self.config_file)
        
        # Tentar caminhos padrão
        default_paths = [
            Path("configs/domains.yml"),
            Path("configs/config.yml"),
            Path("config.yml"),
        ]
        
        for path in default_paths:
            if path.exists():
                return path
        
        return None
    
    def _parse_domain_config(self, domain_data: Dict[str, Any]) -> DomainConfig:
        """Parse configuração de um domínio."""
        from .validators import RateLimitConfig, TimeoutConfig, ParsingConfig, QualityConfig
        
        # Rate limiting
        rate_limit_data = domain_data.get('rate_limit', {})
        rate_limit = RateLimitConfig(
            requests_per_second=rate_limit_data.get(
                'requests_per_second', 
                self.settings.default_requests_per_second
            ),
            burst_limit=rate_limit_data.get('burst_limit', 5),
            backoff_factor=rate_limit_data.get('backoff_factor', 2.0),
            max_retries=rate_limit_data.get('max_retries', self.settings.max_retries),
        )
        
        # Timeouts
        timeout_data = domain_data.get('timeouts', {})
        timeouts = TimeoutConfig(
            connect=timeout_data.get('connect', 10),
            read=timeout_data.get('read', self.settings.request_timeout),
            total=timeout_data.get('total', 60),
        )
        
        # Parsing
        parsing_data = domain_data.get('parsing', {})
        parsing = ParsingConfig(
            content_selectors=parsing_data.get('content_selectors', {}),
            remove_selectors=parsing_data.get('remove_selectors', []),
            required_elements=parsing_data.get('required_elements', []),
        )
        
        # Quality
        quality_data = domain_data.get('quality', {})
        quality = QualityConfig(
            min_content_length=quality_data.get(
                'min_content_length', 
                self.settings.min_content_length
            ),
            min_word_count=quality_data.get(
                'min_word_count', 
                self.settings.min_word_count
            ),
            min_headings=quality_data.get('min_headings', 1),
            bonus_selectors=quality_data.get('bonus_selectors', []),
        )
        
        # Scope
        scope_data = domain_data.get('scope', {})
        allowed_paths = scope_data.get('allowed_paths', [])
        blocked_paths = scope_data.get('blocked_paths', [])
        
        # Dynamic content
        dynamic_data = domain_data.get('dynamic_content', {})
        dynamic_content = dynamic_data.get('enabled', False)
        wait_for_selector = dynamic_data.get('wait_for_selector')
        wait_timeout = dynamic_data.get('wait_timeout', self.settings.playwright_timeout)
        
        return DomainConfig(
            rate_limit=rate_limit,
            timeouts=timeouts,
            parsing=parsing,
            quality=quality,
            allowed_paths=allowed_paths,
            blocked_paths=blocked_paths,
            dynamic_content=dynamic_content,
            wait_for_selector=wait_for_selector,
            wait_timeout=wait_timeout,
        )
    
    def _create_default_domain_config(self) -> DomainConfig:
        """Criar configuração padrão de domínio."""
        from .validators import RateLimitConfig, TimeoutConfig, ParsingConfig, QualityConfig
        
        return DomainConfig(
            rate_limit=RateLimitConfig(
                requests_per_second=self.settings.default_requests_per_second,
                max_retries=self.settings.max_retries,
            ),
            timeouts=TimeoutConfig(
                read=self.settings.request_timeout,
            ),
            parsing=ParsingConfig(),
            quality=QualityConfig(
                min_content_length=self.settings.min_content_length,
                min_word_count=self.settings.min_word_count,
            ),
        )
    
    def get_settings(self) -> Settings:
        """Obter configurações globais."""
        return self.settings
    
    def reload_config(self) -> ScrapingConfig:
        """Recarregar configuração."""
        self._scraping_config = None
        self._domain_configs.clear()
        return self.load_config()


# Instância global do gerenciador de configuração
config_manager = ConfigManager()


def get_config(config_file: Optional[str] = None) -> ScrapingConfig:
    """Obter configuração de scraping."""
    if config_file:
        manager = ConfigManager(config_file)
        return manager.load_config()
    return config_manager.load_config()


def get_settings() -> Settings:
    """Obter configurações globais."""
    return config_manager.get_settings()

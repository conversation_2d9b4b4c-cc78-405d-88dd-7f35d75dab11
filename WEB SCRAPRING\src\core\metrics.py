"""
Metrics - Sistema de métricas Prometheus.

Este módulo implementa instrumentação completa com métricas Prometheus
para observabilidade do WebScraper.
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import structlog
from prometheus_client import (
    Counter, Gauge, Histogram, Info, Summary,
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)

from .config import get_settings

logger = structlog.get_logger(__name__)


class MetricsCollector:
    """Coletor de métricas Prometheus."""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self.settings = get_settings()
        
        # Métricas de HTTP
        self.http_requests_total = Counter(
            'webscraper_http_requests_total',
            'Total HTTP requests made',
            ['domain', 'status_code', 'method'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'webscraper_http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['domain', 'status_code'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0],
            registry=self.registry
        )
        
        self.http_response_size = Histogram(
            'webscraper_http_response_size_bytes',
            'HTTP response size in bytes',
            ['domain', 'content_type'],
            buckets=[1024, 10240, 102400, 1048576, 10485760],
            registry=self.registry
        )
        
        # Métricas de parsing
        self.pages_parsed_total = Counter(
            'webscraper_pages_parsed_total',
            'Total pages parsed',
            ['domain', 'status', 'parser_type'],
            registry=self.registry
        )
        
        self.parsing_duration = Histogram(
            'webscraper_parsing_duration_seconds',
            'Page parsing duration in seconds',
            ['domain', 'parser_type'],
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        self.content_quality_score = Histogram(
            'webscraper_content_quality_score',
            'Content quality score (0-100)',
            ['domain', 'quality_tier'],
            buckets=[10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            registry=self.registry
        )
        
        self.content_word_count = Histogram(
            'webscraper_content_word_count',
            'Content word count',
            ['domain'],
            buckets=[10, 50, 100, 500, 1000, 5000, 10000],
            registry=self.registry
        )
        
        # Métricas de storage
        self.storage_operations_total = Counter(
            'webscraper_storage_operations_total',
            'Total storage operations',
            ['operation', 'backend', 'status'],
            registry=self.registry
        )
        
        self.storage_operation_duration = Histogram(
            'webscraper_storage_operation_duration_seconds',
            'Storage operation duration in seconds',
            ['operation', 'backend'],
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        # Métricas de versionamento
        self.content_versions_created = Counter(
            'webscraper_content_versions_created_total',
            'Total content versions created',
            ['domain', 'change_type'],
            registry=self.registry
        )
        
        self.content_changes_detected = Counter(
            'webscraper_content_changes_detected_total',
            'Total content changes detected',
            ['domain', 'change_type'],
            registry=self.registry
        )
        
        # Métricas de sistema
        self.active_crawl_sessions = Gauge(
            'webscraper_active_crawl_sessions',
            'Number of active crawl sessions',
            ['domain'],
            registry=self.registry
        )
        
        self.queue_size = Gauge(
            'webscraper_queue_size',
            'Size of URL queue',
            ['domain', 'queue_type'],
            registry=self.registry
        )
        
        self.database_connections = Gauge(
            'webscraper_database_connections',
            'Number of database connections',
            ['state'],
            registry=self.registry
        )
        
        # Métricas de qualidade
        self.quality_distribution = Counter(
            'webscraper_quality_distribution_total',
            'Distribution of quality scores',
            ['domain', 'quality_tier'],
            registry=self.registry
        )
        
        # Métricas de erro
        self.errors_total = Counter(
            'webscraper_errors_total',
            'Total errors encountered',
            ['component', 'error_type', 'domain'],
            registry=self.registry
        )
        
        # Métricas de performance
        self.memory_usage = Gauge(
            'webscraper_memory_usage_bytes',
            'Memory usage in bytes',
            ['component'],
            registry=self.registry
        )
        
        self.cpu_usage = Gauge(
            'webscraper_cpu_usage_percent',
            'CPU usage percentage',
            ['component'],
            registry=self.registry
        )
        
        # Info sobre o sistema
        self.build_info = Info(
            'webscraper_build_info',
            'Build information',
            registry=self.registry
        )
        
        # Configurar info inicial
        self.build_info.info({
            'version': '1.0.0',
            'environment': self.settings.environment,
            'python_version': '3.11+',
        })
        
        logger.info("Metrics collector initialized")
    
    # Métodos para HTTP
    def record_http_request(
        self, 
        domain: str, 
        status_code: int, 
        method: str = "GET",
        duration: float = 0.0,
        response_size: int = 0,
        content_type: str = "text/html"
    ) -> None:
        """Registrar métricas de requisição HTTP."""
        self.http_requests_total.labels(
            domain=domain,
            status_code=str(status_code),
            method=method
        ).inc()
        
        if duration > 0:
            self.http_request_duration.labels(
                domain=domain,
                status_code=str(status_code)
            ).observe(duration)
        
        if response_size > 0:
            self.http_response_size.labels(
                domain=domain,
                content_type=content_type.split(';')[0]  # Remove charset
            ).observe(response_size)
    
    # Métodos para parsing
    def record_page_parsed(
        self,
        domain: str,
        status: str,
        parser_type: str = "generic",
        duration: float = 0.0,
        quality_score: int = 0,
        quality_tier: str = "unknown",
        word_count: int = 0
    ) -> None:
        """Registrar métricas de parsing."""
        self.pages_parsed_total.labels(
            domain=domain,
            status=status,
            parser_type=parser_type
        ).inc()
        
        if duration > 0:
            self.parsing_duration.labels(
                domain=domain,
                parser_type=parser_type
            ).observe(duration)
        
        if quality_score > 0:
            self.content_quality_score.labels(
                domain=domain,
                quality_tier=quality_tier
            ).observe(quality_score)
            
            self.quality_distribution.labels(
                domain=domain,
                quality_tier=quality_tier
            ).inc()
        
        if word_count > 0:
            self.content_word_count.labels(domain=domain).observe(word_count)
    
    # Métodos para storage
    def record_storage_operation(
        self,
        operation: str,
        backend: str,
        status: str = "success",
        duration: float = 0.0
    ) -> None:
        """Registrar métricas de storage."""
        self.storage_operations_total.labels(
            operation=operation,
            backend=backend,
            status=status
        ).inc()
        
        if duration > 0:
            self.storage_operation_duration.labels(
                operation=operation,
                backend=backend
            ).observe(duration)
    
    # Métodos para versionamento
    def record_content_version(
        self,
        domain: str,
        change_type: str = "unknown"
    ) -> None:
        """Registrar criação de versão."""
        self.content_versions_created.labels(
            domain=domain,
            change_type=change_type
        ).inc()
    
    def record_content_change(
        self,
        domain: str,
        change_type: str = "unknown"
    ) -> None:
        """Registrar mudança de conteúdo."""
        self.content_changes_detected.labels(
            domain=domain,
            change_type=change_type
        ).inc()
    
    # Métodos para sistema
    def set_active_sessions(self, domain: str, count: int) -> None:
        """Definir número de sessões ativas."""
        self.active_crawl_sessions.labels(domain=domain).set(count)
    
    def set_queue_size(self, domain: str, queue_type: str, size: int) -> None:
        """Definir tamanho da fila."""
        self.queue_size.labels(domain=domain, queue_type=queue_type).set(size)
    
    def set_database_connections(self, state: str, count: int) -> None:
        """Definir número de conexões do banco."""
        self.database_connections.labels(state=state).set(count)
    
    # Métodos para erros
    def record_error(
        self,
        component: str,
        error_type: str,
        domain: str = "unknown"
    ) -> None:
        """Registrar erro."""
        self.errors_total.labels(
            component=component,
            error_type=error_type,
            domain=domain
        ).inc()
    
    # Métodos para performance
    def set_memory_usage(self, component: str, bytes_used: int) -> None:
        """Definir uso de memória."""
        self.memory_usage.labels(component=component).set(bytes_used)
    
    def set_cpu_usage(self, component: str, percent: float) -> None:
        """Definir uso de CPU."""
        self.cpu_usage.labels(component=component).set(percent)
    
    def get_metrics(self) -> str:
        """Obter métricas no formato Prometheus."""
        return generate_latest(self.registry).decode('utf-8')


# Context managers para timing
@asynccontextmanager
async def time_http_request(
    metrics: MetricsCollector,
    domain: str,
    method: str = "GET"
):
    """Context manager para timing de requisições HTTP."""
    start_time = time.time()
    status_code = 0
    response_size = 0
    content_type = "text/html"
    
    try:
        yield locals()  # Permite modificar variáveis no contexto
    finally:
        duration = time.time() - start_time
        metrics.record_http_request(
            domain=domain,
            status_code=status_code,
            method=method,
            duration=duration,
            response_size=response_size,
            content_type=content_type
        )


@asynccontextmanager
async def time_parsing(
    metrics: MetricsCollector,
    domain: str,
    parser_type: str = "generic"
):
    """Context manager para timing de parsing."""
    start_time = time.time()
    status = "unknown"
    quality_score = 0
    quality_tier = "unknown"
    word_count = 0
    
    try:
        yield locals()
    finally:
        duration = time.time() - start_time
        metrics.record_page_parsed(
            domain=domain,
            status=status,
            parser_type=parser_type,
            duration=duration,
            quality_score=quality_score,
            quality_tier=quality_tier,
            word_count=word_count
        )


@asynccontextmanager
async def time_storage_operation(
    metrics: MetricsCollector,
    operation: str,
    backend: str
):
    """Context manager para timing de operações de storage."""
    start_time = time.time()
    status = "success"
    
    try:
        yield locals()
    except Exception:
        status = "error"
        raise
    finally:
        duration = time.time() - start_time
        metrics.record_storage_operation(
            operation=operation,
            backend=backend,
            status=status,
            duration=duration
        )


# Instância global do coletor de métricas
metrics_collector = MetricsCollector()


def get_metrics() -> str:
    """Obter métricas no formato Prometheus."""
    return metrics_collector.get_metrics()


def get_content_type() -> str:
    """Obter content type das métricas."""
    return CONTENT_TYPE_LATEST

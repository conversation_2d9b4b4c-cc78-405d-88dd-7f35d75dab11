"""
Models - Modelos SQLAlchemy para PostgreSQL.

Este módulo define os modelos de dados para o banco PostgreSQL,
incluindo páginas, versões, métricas e configurações.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Float, ForeignKey, Index, Integer, 
    JSON, String, Text, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Domain(Base):
    """Modelo para domínios configurados."""
    
    __tablename__ = "domains"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), unique=True, nullable=False, index=True)
    
    # Configurações
    rate_limit_rps = Column(Float, default=1.0)
    max_pages = Column(Integer, default=10000)
    dynamic_content = Column(Boolean, default=False)
    wait_for_selector = Column(String(500))
    
    # Status
    active = Column(Boolean, default=True)
    last_crawled = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relacionamentos
    pages = relationship("Page", back_populates="domain", cascade="all, delete-orphan")
    crawl_sessions = relationship("CrawlSession", back_populates="domain")
    
    def __repr__(self):
        return f"<Domain(name='{self.name}', active={self.active})>"


class Page(Base):
    """Modelo para páginas coletadas."""
    
    __tablename__ = "pages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(Text, nullable=False, index=True)
    url_hash = Column(String(64), nullable=False, index=True)  # SHA256 da URL normalizada
    
    # Relacionamento com domínio
    domain_id = Column(UUID(as_uuid=True), ForeignKey("domains.id"), nullable=False)
    domain = relationship("Domain", back_populates="pages")
    
    # Conteúdo
    title = Column(Text)
    content_hash = Column(String(64), nullable=False, index=True)  # SHA256 do conteúdo
    semantic_hash = Column(String(64), nullable=False, index=True)  # Hash semântico
    
    # Metadados estruturados
    word_count = Column(Integer, default=0)
    headings_count = Column(Integer, default=0)
    code_blocks_count = Column(Integer, default=0)
    tables_count = Column(Integer, default=0)
    internal_links_count = Column(Integer, default=0)
    external_links_count = Column(Integer, default=0)
    
    # Qualidade
    quality_score = Column(Integer, default=0)
    quality_tier = Column(String(20))  # excellent, good, fair, poor
    quality_confidence = Column(Float, default=0.0)
    
    # Status
    status = Column(String(20), default="pending")  # pending, success, failed, skipped
    errors = Column(JSON)
    
    # Storage
    raw_storage_path = Column(Text)  # Caminho no S3/MinIO
    processed_storage_path = Column(Text)
    
    # HTTP
    http_status_code = Column(Integer)
    content_type = Column(String(100))
    content_length = Column(Integer)
    etag = Column(String(100))
    last_modified = Column(DateTime(timezone=True))
    
    # Timestamps
    discovered_at = Column(DateTime(timezone=True), server_default=func.now())
    fetched_at = Column(DateTime(timezone=True))
    processed_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relacionamentos
    versions = relationship("PageVersion", back_populates="page", cascade="all, delete-orphan")
    
    # Índices compostos
    __table_args__ = (
        Index("idx_pages_domain_status", "domain_id", "status"),
        Index("idx_pages_content_hash", "content_hash"),
        Index("idx_pages_quality", "quality_score", "quality_tier"),
        Index("idx_pages_fetched", "fetched_at"),
        UniqueConstraint("url_hash", name="uq_pages_url_hash"),
    )
    
    def __repr__(self):
        return f"<Page(url='{self.url[:50]}...', status='{self.status}')>"


class PageVersion(Base):
    """Modelo para versões de páginas."""
    
    __tablename__ = "page_versions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Relacionamento com página
    page_id = Column(UUID(as_uuid=True), ForeignKey("pages.id"), nullable=False)
    page = relationship("Page", back_populates="versions")
    
    # Versionamento
    version_number = Column(Integer, nullable=False)
    content_hash = Column(String(64), nullable=False)
    semantic_hash = Column(String(64), nullable=False)
    
    # Mudanças
    changes = Column(JSON)  # Lista de mudanças detectadas
    change_type = Column(String(20))  # major, minor, formatting
    
    # Dados da versão (snapshot)
    title = Column(Text)
    word_count = Column(Integer)
    quality_score = Column(Integer)
    
    # Storage desta versão
    storage_path = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Índices
    __table_args__ = (
        Index("idx_versions_page_version", "page_id", "version_number"),
        Index("idx_versions_content_hash", "content_hash"),
        UniqueConstraint("page_id", "version_number", name="uq_page_version"),
    )
    
    def __repr__(self):
        return f"<PageVersion(page_id='{self.page_id}', version={self.version_number})>"


class CrawlSession(Base):
    """Modelo para sessões de crawling."""
    
    __tablename__ = "crawl_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Relacionamento com domínio
    domain_id = Column(UUID(as_uuid=True), ForeignKey("domains.id"), nullable=False)
    domain = relationship("Domain", back_populates="crawl_sessions")
    
    # Configuração da sessão
    session_type = Column(String(20), default="full")  # full, incremental, targeted
    max_pages = Column(Integer)
    dry_run = Column(Boolean, default=False)
    
    # Status
    status = Column(String(20), default="running")  # running, completed, failed, cancelled
    
    # Estatísticas
    urls_discovered = Column(Integer, default=0)
    urls_processed = Column(Integer, default=0)
    urls_successful = Column(Integer, default=0)
    urls_failed = Column(Integer, default=0)
    urls_skipped = Column(Integer, default=0)
    
    # Performance
    duration_seconds = Column(Float)
    avg_response_time_ms = Column(Float)
    total_bytes_downloaded = Column(Integer, default=0)
    
    # Qualidade
    avg_quality_score = Column(Float)
    high_quality_pages = Column(Integer, default=0)  # score >= 80
    
    # Erros
    errors = Column(JSON)
    error_summary = Column(Text)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Índices
    __table_args__ = (
        Index("idx_sessions_domain_status", "domain_id", "status"),
        Index("idx_sessions_started", "started_at"),
    )
    
    def __repr__(self):
        return f"<CrawlSession(domain='{self.domain.name}', status='{self.status}')>"


class Metric(Base):
    """Modelo para métricas do sistema."""
    
    __tablename__ = "metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Identificação da métrica
    name = Column(String(100), nullable=False, index=True)
    metric_type = Column(String(20), nullable=False)  # counter, gauge, histogram
    
    # Valor
    value = Column(Float, nullable=False)
    
    # Labels/Tags
    labels = Column(JSON)  # {"domain": "example.com", "status": "success"}
    
    # Contexto
    component = Column(String(50))  # http_client, parser, storage, etc.
    session_id = Column(UUID(as_uuid=True))  # Relacionar com sessão de crawl
    
    # Timestamp
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Índices para queries eficientes
    __table_args__ = (
        Index("idx_metrics_name_timestamp", "name", "timestamp"),
        Index("idx_metrics_component_timestamp", "component", "timestamp"),
        Index("idx_metrics_session", "session_id"),
    )
    
    def __repr__(self):
        return f"<Metric(name='{self.name}', value={self.value})>"


class Alert(Base):
    """Modelo para alertas do sistema."""
    
    __tablename__ = "alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Identificação
    name = Column(String(100), nullable=False)
    severity = Column(String(20), nullable=False)  # critical, warning, info
    
    # Condição
    condition = Column(Text, nullable=False)  # Descrição da condição que disparou
    threshold = Column(Float)
    current_value = Column(Float)
    
    # Status
    status = Column(String(20), default="firing")  # firing, resolved, silenced
    
    # Contexto
    component = Column(String(50))
    domain = Column(String(255))
    labels = Column(JSON)
    
    # Descrição
    summary = Column(Text)
    description = Column(Text)
    
    # Timestamps
    fired_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Índices
    __table_args__ = (
        Index("idx_alerts_status_severity", "status", "severity"),
        Index("idx_alerts_component", "component"),
        Index("idx_alerts_fired", "fired_at"),
    )
    
    def __repr__(self):
        return f"<Alert(name='{self.name}', severity='{self.severity}', status='{self.status}')>"


class Configuration(Base):
    """Modelo para configurações do sistema."""
    
    __tablename__ = "configurations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Chave e valor
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(JSON, nullable=False)
    
    # Metadados
    description = Column(Text)
    category = Column(String(50))  # scraping, quality, storage, etc.
    
    # Versionamento de config
    version = Column(Integer, default=1)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Configuration(key='{self.key}', category='{self.category}')>"

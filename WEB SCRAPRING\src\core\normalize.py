"""
Normalize - Sistema avançado de normalização de conteúdo.

Este módulo implementa técnicas avançadas de limpeza e normalização
de conteúdo web, incluindo remoção de boilerplate e resolução de links.
"""

import re
from typing import Dict, List, Set, Tuple
from urllib.parse import urljoin, urlparse

import structlog
from selectolax.parser import HTMLParser

from .validators import PageData

logger = structlog.get_logger(__name__)


class BoilerplateRemover:
    """Remove conteúdo boilerplate comum em páginas web."""
    
    # Padrões de texto que são tipicamente boilerplate
    BOILERPLATE_PATTERNS = [
        # Navegação
        r'\b(?:home|about|contact|login|register|sign\s+in|sign\s+up)\b',
        r'\b(?:menu|navigation|nav|breadcrumb)\b',
        
        # Footer/Copyright
        r'©\s*\d{4}',
        r'\bcopyright\b.*\d{4}',
        r'\ball\s+rights\s+reserved\b',
        r'\bprivacy\s+policy\b',
        r'\bterms\s+of\s+service\b',
        
        # Social/Sharing
        r'\b(?:share|like|tweet|facebook|twitter|linkedin)\b',
        r'\bfollow\s+us\b',
        r'\bsocial\s+media\b',
        
        # Ads/Marketing
        r'\badvertisement\b',
        r'\bsponsored\b',
        r'\bpromotion\b',
        
        # Common UI elements
        r'\b(?:click\s+here|read\s+more|learn\s+more|see\s+more)\b',
        r'\b(?:previous|next|page\s+\d+)\b',
        r'\bshow\s+(?:more|less)\b',
        
        # Cookie notices
        r'\bcookie\s+(?:policy|notice|consent)\b',
        r'\bwe\s+use\s+cookies\b',
    ]
    
    @classmethod
    def is_boilerplate_text(cls, text: str, min_length: int = 10) -> bool:
        """Verificar se um texto é provavelmente boilerplate."""
        if len(text.strip()) < min_length:
            return True
        
        text_lower = text.lower()
        
        # Verificar padrões de boilerplate
        for pattern in cls.BOILERPLATE_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        # Texto muito repetitivo
        words = text_lower.split()
        if len(words) > 3:
            unique_words = set(words)
            repetition_ratio = len(words) / len(unique_words)
            if repetition_ratio > 3:  # Muita repetição
                return True
        
        return False
    
    @classmethod
    def remove_boilerplate_sentences(cls, text: str) -> str:
        """Remover sentenças que são boilerplate."""
        if not text:
            return text
        
        # Dividir em sentenças
        sentences = re.split(r'[.!?]+', text)
        
        # Filtrar sentenças boilerplate
        clean_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and not cls.is_boilerplate_text(sentence):
                clean_sentences.append(sentence)
        
        return '. '.join(clean_sentences)


class TextNormalizer:
    """Normaliza texto extraído de páginas web."""
    
    @staticmethod
    def normalize_whitespace(text: str) -> str:
        """Normalizar espaços em branco."""
        if not text:
            return ""
        
        # Remover caracteres de controle
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalizar quebras de linha
        text = re.sub(r'\r\n|\r', '\n', text)
        
        # Remover espaços extras
        text = re.sub(r'[ \t]+', ' ', text)
        
        # Normalizar quebras de linha múltiplas
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        return text.strip()
    
    @staticmethod
    def remove_extra_punctuation(text: str) -> str:
        """Remover pontuação excessiva."""
        if not text:
            return ""
        
        # Remover múltiplos pontos/exclamações/interrogações
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Remover espaços antes de pontuação
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        
        # Adicionar espaço após pontuação se necessário
        text = re.sub(r'([,.!?;:])([A-Za-z])', r'\1 \2', text)
        
        return text
    
    @staticmethod
    def fix_encoding_issues(text: str) -> str:
        """Corrigir problemas comuns de encoding."""
        if not text:
            return ""
        
        # Substituições comuns de encoding
        replacements = {
            'â€™': "'",
            'â€œ': '"',
            'â€': '"',
            'â€"': '—',
            'â€"': '–',
            'Â': '',
            'â€¦': '...',
            'â€¢': '•',
        }
        
        for bad, good in replacements.items():
            text = text.replace(bad, good)
        
        return text
    
    @staticmethod
    def normalize_quotes(text: str) -> str:
        """Normalizar aspas."""
        if not text:
            return ""
        
        # Normalizar aspas curvas para retas
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r"[''']", "'", text)
        
        return text
    
    @classmethod
    def normalize_text(cls, text: str) -> str:
        """Aplicar todas as normalizações de texto."""
        if not text:
            return ""
        
        text = cls.normalize_whitespace(text)
        text = cls.fix_encoding_issues(text)
        text = cls.normalize_quotes(text)
        text = cls.remove_extra_punctuation(text)
        
        return text


class LinkResolver:
    """Resolve e normaliza links."""
    
    @staticmethod
    def resolve_relative_links(links: List[str], base_url: str) -> List[str]:
        """Resolver links relativos para absolutos."""
        resolved_links = []
        
        for link in links:
            try:
                # Resolver link relativo
                absolute_link = urljoin(base_url, link)
                
                # Normalizar URL
                parsed = urlparse(absolute_link)
                
                # Remover fragmentos e parâmetros de tracking
                clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
                
                if parsed.query:
                    # Filtrar parâmetros de tracking
                    query_parts = []
                    for part in parsed.query.split('&'):
                        if '=' in part:
                            key = part.split('=')[0]
                            if not key.startswith(('utm_', 'fbclid', 'gclid', '_ga')):
                                query_parts.append(part)
                        else:
                            query_parts.append(part)
                    
                    if query_parts:
                        clean_url += '?' + '&'.join(query_parts)
                
                resolved_links.append(clean_url)
                
            except Exception as e:
                logger.debug("Failed to resolve link", link=link, error=str(e))
                continue
        
        # Remover duplicatas mantendo ordem
        return list(dict.fromkeys(resolved_links))
    
    @staticmethod
    def filter_valid_links(links: List[str]) -> List[str]:
        """Filtrar apenas links válidos."""
        valid_links = []
        
        for link in links:
            try:
                parsed = urlparse(link)
                
                # Verificar se é um link válido
                if (parsed.scheme in ['http', 'https'] and 
                    parsed.netloc and 
                    not link.endswith(('.pdf', '.jpg', '.png', '.gif', '.css', '.js'))):
                    valid_links.append(link)
                    
            except Exception:
                continue
        
        return valid_links


class ContentNormalizer:
    """Normalizador principal de conteúdo."""
    
    def __init__(self):
        self.boilerplate_remover = BoilerplateRemover()
        self.text_normalizer = TextNormalizer()
        self.link_resolver = LinkResolver()
    
    def normalize_page_data(self, page_data: PageData, base_url: str) -> PageData:
        """Normalizar todos os dados de uma página."""
        logger.debug("Starting content normalization", url=page_data.url)
        
        # Normalizar título
        page_data.title = self.text_normalizer.normalize_text(page_data.title)
        
        # Normalizar conteúdo de texto
        page_data.text_content = self._normalize_main_content(page_data.text_content)
        
        # Normalizar headings
        for heading in page_data.headings_tree:
            heading.text = self.text_normalizer.normalize_text(heading.text)
        
        # Normalizar blocos de código
        for code_block in page_data.code_blocks:
            code_block.content = self.text_normalizer.normalize_whitespace(code_block.content)
        
        # Normalizar tabelas
        for table in page_data.tables:
            table.headers = [
                self.text_normalizer.normalize_text(header) 
                for header in table.headers
            ]
            table.rows = [
                [self.text_normalizer.normalize_text(cell) for cell in row]
                for row in table.rows
            ]
            if table.caption:
                table.caption = self.text_normalizer.normalize_text(table.caption)
        
        # Resolver e filtrar links
        page_data.internal_links = self.link_resolver.resolve_relative_links(
            page_data.internal_links, base_url
        )
        page_data.internal_links = self.link_resolver.filter_valid_links(
            page_data.internal_links
        )
        
        page_data.external_links = self.link_resolver.filter_valid_links(
            page_data.external_links
        )
        
        # Recalcular word count após normalização
        page_data.word_count = len(page_data.text_content.split())
        
        logger.debug(
            "Content normalization completed",
            url=page_data.url,
            title_length=len(page_data.title),
            content_length=len(page_data.text_content),
            word_count=page_data.word_count,
        )
        
        return page_data
    
    def _normalize_main_content(self, text: str) -> str:
        """Normalizar conteúdo principal."""
        if not text:
            return ""
        
        # Aplicar normalização básica
        text = self.text_normalizer.normalize_text(text)
        
        # Remover boilerplate
        text = self.boilerplate_remover.remove_boilerplate_sentences(text)
        
        # Remover linhas muito curtas que são provavelmente navegação
        lines = text.split('\n')
        content_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) > 20 or not self.boilerplate_remover.is_boilerplate_text(line):
                content_lines.append(line)
        
        return '\n'.join(content_lines)
    
    def get_content_quality_indicators(self, page_data: PageData) -> Dict[str, float]:
        """Obter indicadores de qualidade do conteúdo normalizado."""
        text = page_data.text_content
        
        if not text:
            return {"overall": 0.0}
        
        # Densidade de informação (palavras únicas / total)
        words = text.lower().split()
        unique_words = set(words)
        info_density = len(unique_words) / len(words) if words else 0
        
        # Complexidade de sentenças (palavras por sentença)
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        sentence_complexity = min(avg_sentence_length / 15, 1.0)  # Normalizar para 0-1
        
        # Presença de estrutura (headings, listas, etc.)
        structure_score = min(len(page_data.headings_tree) / 5, 1.0)
        
        # Score geral
        overall = (info_density * 0.4 + sentence_complexity * 0.3 + structure_score * 0.3)
        
        return {
            "overall": round(overall, 3),
            "info_density": round(info_density, 3),
            "sentence_complexity": round(sentence_complexity, 3),
            "structure_score": round(structure_score, 3),
        }

"""
Parser Base - Classe base para parsers de conteúdo.

<PERSON><PERSON> módulo define a interface base para parsers específicos de domínio,
fornecendo funcionalidades comuns de parsing HTML e extração de conteúdo.
"""

import re
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse

import structlog
from selectolax.parser import HTMLParser

from .browser import PlaywrightClient
from .validators import (
    CodeBlock,
    DomainConfig,
    HeadingNode,
    HTTPResponse,
    PageData,
    QualityConfig,
    ScrapingConfig,
    ScrapingStatus,
    TableData,
)

logger = structlog.get_logger(__name__)


class BaseParser(ABC):
    """Classe base para parsers de conteúdo."""

    def __init__(self, domain_config: DomainConfig, scraping_config: Optional[ScrapingConfig] = None):
        self.config = domain_config
        self.scraping_config = scraping_config
        self.base_url = ""
        self.playwright_client: Optional[PlaywrightClient] = None
    
    @abstractmethod
    def can_parse(self, url: str, content_type: str) -> bool:
        """Verificar se este parser pode processar a URL/conteúdo."""
        pass

    def needs_javascript(self, url: str, initial_content: str = "") -> bool:
        """Verificar se a página precisa de JavaScript para carregar conteúdo."""
        # Se configurado explicitamente para usar conteúdo dinâmico
        if self.config.dynamic_content:
            return True

        # Detectar indicadores de SPA/JavaScript
        if initial_content:
            js_indicators = [
                # Frameworks JavaScript
                r'<script[^>]*(?:react|vue|angular|ember)',
                r'data-reactroot',
                r'ng-app',
                r'v-app',

                # Bundlers e loaders
                r'webpack',
                r'__webpack_require__',
                r'System\.import',

                # Conteúdo muito pequeno (possível SPA)
                r'^[\s\n]*<!DOCTYPE[^>]*>[\s\n]*<html[^>]*>[\s\n]*<head>.*</head>[\s\n]*<body[^>]*>[\s\n]*<div[^>]*></div>[\s\n]*</body>[\s\n]*</html>[\s\n]*$',

                # Loading indicators
                r'loading|spinner|skeleton',
            ]

            for pattern in js_indicators:
                if re.search(pattern, initial_content, re.IGNORECASE | re.DOTALL):
                    logger.debug("JavaScript indicator detected", pattern=pattern, url=url)
                    return True

        return False
    
    async def parse(self, response: HTTPResponse) -> PageData:
        """Método principal de parsing."""
        try:
            self.base_url = str(response.url)

            # Verificar se precisa de JavaScript
            if self.needs_javascript(str(response.url), response.content):
                logger.info("Page requires JavaScript, using Playwright", url=response.url)
                response = await self._fetch_with_playwright(response)

            # Pré-processamento
            cleaned_html = await self.preprocess_html(response.content)

            # Parsing principal
            page_data = await self.extract_content(cleaned_html, response)

            # Pós-processamento
            page_data = await self.postprocess_data(page_data)

            # Calcular score de qualidade
            page_data.quality_score = page_data.calculate_quality_score(
                self.config.quality
            )

            # Definir status baseado na qualidade
            if page_data.quality_score >= 50:
                page_data.status = ScrapingStatus.SUCCESS
            else:
                page_data.status = ScrapingStatus.FAILED
                page_data.errors.append(
                    f"Low quality score: {page_data.quality_score}"
                )

            logger.info(
                "Page parsed successfully",
                url=response.url,
                quality_score=page_data.quality_score,
                word_count=page_data.word_count,
                headings_count=len(page_data.headings_tree),
                code_blocks_count=len(page_data.code_blocks),
            )

            return page_data

        except Exception as e:
            logger.error(
                "Parsing failed",
                url=response.url,
                error=str(e),
                exc_info=True,
            )

            # Retornar dados básicos em caso de erro
            return PageData(
                url=response.url,
                status=ScrapingStatus.FAILED,
                errors=[f"Parsing error: {str(e)}"],
                fetched_at=response.fetched_at,
            )

    async def _fetch_with_playwright(self, original_response: HTTPResponse) -> HTTPResponse:
        """Refazer fetch usando Playwright para conteúdo dinâmico."""
        if not self.scraping_config:
            logger.warning("No scraping config available for Playwright")
            return original_response

        try:
            # Inicializar Playwright client se necessário
            if not self.playwright_client:
                self.playwright_client = PlaywrightClient(self.scraping_config)
                await self.playwright_client.__aenter__()

            # Fazer fetch com Playwright
            dynamic_response = await self.playwright_client.fetch_dynamic_page(
                str(original_response.url),
                self.config
            )

            logger.info(
                "Dynamic content fetched",
                url=original_response.url,
                original_length=len(original_response.content),
                dynamic_length=len(dynamic_response.content),
            )

            return dynamic_response

        except Exception as e:
            logger.error(
                "Playwright fetch failed, using original content",
                url=original_response.url,
                error=str(e),
            )
            return original_response
    
    async def preprocess_html(self, html: str) -> str:
        """Pré-processar HTML antes do parsing."""
        # Remover comentários HTML
        html = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)
        
        # Remover scripts e styles
        parser = HTMLParser(html)
        
        # Remover elementos indesejados
        for selector in ["script", "style", "noscript"]:
            for element in parser.css(selector):
                element.decompose()
        
        # Remover elementos baseados na configuração
        for selector in self.config.parsing.remove_selectors:
            for element in parser.css(selector):
                element.decompose()
        
        return str(parser.html)
    
    async def extract_content(self, html: str, response: HTTPResponse) -> PageData:
        """Extrair conteúdo principal da página."""
        parser = HTMLParser(html)
        
        # Extrair título
        title = await self.extract_title(parser)
        
        # Extrair headings e criar árvore
        headings_tree = await self.extract_headings_tree(parser)
        
        # Extrair texto principal
        text_content = await self.extract_text_content(parser)
        
        # Extrair blocos de código
        code_blocks = await self.extract_code_blocks(parser)
        
        # Extrair tabelas
        tables = await self.extract_tables(parser)
        
        # Extrair links
        internal_links, external_links = await self.extract_links(parser)
        
        return PageData(
            url=response.url,
            title=title,
            headings_tree=headings_tree,
            text_content=text_content,
            code_blocks=code_blocks,
            tables=tables,
            internal_links=internal_links,
            external_links=external_links,
            fetched_at=response.fetched_at,
        )
    
    async def extract_title(self, parser: HTMLParser) -> str:
        """Extrair título da página."""
        # Tentar seletores configurados primeiro
        title_selector = self.config.parsing.content_selectors.get("title", "h1, title")
        
        for selector in title_selector.split(","):
            selector = selector.strip()
            elements = parser.css(selector)
            if elements:
                title = elements[0].text(strip=True)
                if title:
                    return title
        
        # Fallback para title tag
        title_tag = parser.css_first("title")
        if title_tag:
            return title_tag.text(strip=True)
        
        return ""
    
    async def extract_headings_tree(self, parser: HTMLParser) -> List[HeadingNode]:
        """Extrair árvore de headings."""
        headings_selector = self.config.parsing.content_selectors.get(
            "headings", "h1, h2, h3, h4, h5, h6"
        )
        
        headings = []
        for element in parser.css(headings_selector):
            level = int(element.tag[1])  # h1 -> 1, h2 -> 2, etc.
            text = element.text(strip=True)
            heading_id = element.attributes.get("id")
            
            if text:
                headings.append(HeadingNode(
                    level=level,
                    text=text,
                    id=heading_id,
                ))
        
        # Construir árvore hierárquica
        return self._build_heading_tree(headings)
    
    def _build_heading_tree(self, headings: List[HeadingNode]) -> List[HeadingNode]:
        """Construir árvore hierárquica de headings."""
        if not headings:
            return []
        
        root_nodes = []
        stack = []
        
        for heading in headings:
            # Remover elementos da stack que são de nível maior ou igual
            while stack and stack[-1].level >= heading.level:
                stack.pop()
            
            if not stack:
                # É um nó raiz
                root_nodes.append(heading)
            else:
                # É filho do último elemento na stack
                stack[-1].children.append(heading)
            
            stack.append(heading)
        
        return root_nodes
    
    async def extract_text_content(self, parser: HTMLParser) -> str:
        """Extrair texto principal da página."""
        main_selector = self.config.parsing.content_selectors.get(
            "main_content", "main, article, .content, #content"
        )
        
        # Tentar seletores de conteúdo principal
        for selector in main_selector.split(","):
            selector = selector.strip()
            elements = parser.css(selector)
            if elements:
                return elements[0].text(strip=True)
        
        # Fallback para body
        body = parser.css_first("body")
        if body:
            return body.text(strip=True)
        
        # Último recurso: todo o texto
        return parser.text(strip=True)
    
    async def extract_code_blocks(self, parser: HTMLParser) -> List[CodeBlock]:
        """Extrair blocos de código."""
        code_blocks = []
        
        # Procurar por elementos de código
        for element in parser.css("pre, code, .highlight, .code-block"):
            content = element.text(strip=True)
            if not content:
                continue
            
            # Tentar detectar linguagem
            language = None
            
            # Verificar atributo class
            classes = element.attributes.get("class", "").split()
            for cls in classes:
                if cls.startswith("language-"):
                    language = cls[9:]
                    break
                elif cls.startswith("lang-"):
                    language = cls[5:]
                    break
                elif cls in ["python", "javascript", "java", "cpp", "csharp", "sql"]:
                    language = cls
                    break
            
            code_blocks.append(CodeBlock(
                language=language,
                content=content,
            ))
        
        return code_blocks
    
    async def extract_tables(self, parser: HTMLParser) -> List[TableData]:
        """Extrair dados de tabelas."""
        tables = []
        
        for table in parser.css("table"):
            # Extrair cabeçalhos
            headers = []
            header_row = table.css_first("thead tr, tr:first-child")
            if header_row:
                for th in header_row.css("th, td"):
                    headers.append(th.text(strip=True))
            
            # Extrair linhas de dados
            rows = []
            tbody = table.css_first("tbody")
            row_elements = tbody.css("tr") if tbody else table.css("tr")[1:]  # Pular header
            
            for row in row_elements:
                row_data = []
                for cell in row.css("td, th"):
                    row_data.append(cell.text(strip=True))
                if row_data:
                    rows.append(row_data)
            
            # Extrair caption se existir
            caption_element = table.css_first("caption")
            caption = caption_element.text(strip=True) if caption_element else None
            
            if headers or rows:
                tables.append(TableData(
                    headers=headers,
                    rows=rows,
                    caption=caption,
                ))
        
        return tables
    
    async def extract_links(self, parser: HTMLParser) -> Tuple[List[str], List[str]]:
        """Extrair links internos e externos."""
        internal_links = []
        external_links = []
        
        base_domain = urlparse(self.base_url).netloc
        
        for link in parser.css("a[href]"):
            href = link.attributes.get("href", "").strip()
            if not href or href.startswith(("#", "javascript:", "mailto:")):
                continue
            
            # Resolver URL relativa
            absolute_url = urljoin(self.base_url, href)
            link_domain = urlparse(absolute_url).netloc
            
            if link_domain == base_domain:
                internal_links.append(absolute_url)
            else:
                external_links.append(absolute_url)
        
        # Remover duplicatas mantendo ordem
        internal_links = list(dict.fromkeys(internal_links))
        external_links = list(dict.fromkeys(external_links))
        
        return internal_links, external_links
    
    async def postprocess_data(self, page_data: PageData) -> PageData:
        """Pós-processar dados extraídos."""
        # Limpar texto
        page_data.text_content = self._clean_text(page_data.text_content)
        page_data.title = self._clean_text(page_data.title)
        
        # Validar links
        page_data.internal_links = self._validate_links(page_data.internal_links)
        page_data.external_links = self._validate_links(page_data.external_links)
        
        return page_data
    
    def _clean_text(self, text: str) -> str:
        """Limpar e normalizar texto."""
        if not text:
            return ""
        
        # Remover espaços extras
        text = re.sub(r'\s+', ' ', text)
        
        # Remover caracteres de controle
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        return text.strip()
    
    def _validate_links(self, links: List[str]) -> List[str]:
        """Validar e filtrar links."""
        valid_links = []
        
        for link in links:
            try:
                parsed = urlparse(link)
                if parsed.scheme in ["http", "https"] and parsed.netloc:
                    valid_links.append(link)
            except Exception:
                continue
        
        return valid_links

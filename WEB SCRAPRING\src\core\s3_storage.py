"""
S3 Storage - Sistema de storage usando S3/MinIO.

Este módulo implementa storage de arquivos usando S3 ou MinIO,
com versionamento, lifecycle policies e otimizações.
"""

import asyncio
import gzip
import hashlib
import json
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

import structlog
from aiobotocore.session import get_session
from botocore.exceptions import ClientError

from .config import get_settings
from .validators import PageData

logger = structlog.get_logger(__name__)


class S3StorageBackend:
    """Backend de storage usando S3/MinIO."""
    
    def __init__(
        self,
        endpoint_url: Optional[str] = None,
        access_key: Optional[str] = None,
        secret_key: Optional[str] = None,
        bucket_name: str = "webscraper",
        region: str = "us-east-1",
    ):
        self.settings = get_settings()
        
        # Configuração S3/MinIO
        self.endpoint_url = endpoint_url or getattr(self.settings, 's3_endpoint_url', None)
        self.access_key = access_key or getattr(self.settings, 's3_access_key', None)
        self.secret_key = secret_key or getattr(self.settings, 's3_secret_key', None)
        self.bucket_name = bucket_name
        self.region = region
        
        # Configurações de storage
        self.compress_content = True
        self.enable_versioning = True
        self.lifecycle_days = 365  # Manter por 1 ano
        
        # Session do aiobotocore
        self.session = get_session()
        
        logger.info(
            "S3 storage backend initialized",
            bucket=self.bucket_name,
            endpoint=self.endpoint_url,
            region=self.region,
        )
    
    async def _get_client(self):
        """Obter cliente S3 assíncrono."""
        config = {
            'region_name': self.region,
            'aws_access_key_id': self.access_key,
            'aws_secret_access_key': self.secret_key,
        }
        
        if self.endpoint_url:
            config['endpoint_url'] = self.endpoint_url
        
        return self.session.create_client('s3', **config)
    
    async def initialize(self) -> None:
        """Inicializar bucket e configurações."""
        async with await self._get_client() as s3:
            try:
                # Verificar se bucket existe
                await s3.head_bucket(Bucket=self.bucket_name)
                logger.debug("Bucket exists", bucket=self.bucket_name)
                
            except ClientError as e:
                error_code = e.response['Error']['Code']
                
                if error_code == '404':
                    # Criar bucket
                    logger.info("Creating bucket", bucket=self.bucket_name)
                    
                    if self.region == 'us-east-1':
                        await s3.create_bucket(Bucket=self.bucket_name)
                    else:
                        await s3.create_bucket(
                            Bucket=self.bucket_name,
                            CreateBucketConfiguration={'LocationConstraint': self.region}
                        )
                    
                    logger.info("Bucket created successfully", bucket=self.bucket_name)
                else:
                    raise
            
            # Configurar versionamento se habilitado
            if self.enable_versioning:
                await self._setup_versioning(s3)
            
            # Configurar lifecycle policy
            await self._setup_lifecycle_policy(s3)
    
    async def _setup_versioning(self, s3) -> None:
        """Configurar versionamento do bucket."""
        try:
            await s3.put_bucket_versioning(
                Bucket=self.bucket_name,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            logger.debug("Bucket versioning enabled", bucket=self.bucket_name)
            
        except Exception as e:
            logger.warning("Failed to enable versioning", error=str(e))
    
    async def _setup_lifecycle_policy(self, s3) -> None:
        """Configurar política de lifecycle."""
        lifecycle_config = {
            'Rules': [
                {
                    'ID': 'webscraper-lifecycle',
                    'Status': 'Enabled',
                    'Filter': {'Prefix': 'raw/'},
                    'Transitions': [
                        {
                            'Days': 30,
                            'StorageClass': 'STANDARD_IA'
                        },
                        {
                            'Days': 90,
                            'StorageClass': 'GLACIER'
                        }
                    ],
                    'Expiration': {
                        'Days': self.lifecycle_days
                    }
                },
                {
                    'ID': 'processed-lifecycle',
                    'Status': 'Enabled',
                    'Filter': {'Prefix': 'processed/'},
                    'Expiration': {
                        'Days': self.lifecycle_days * 2  # Manter processados por mais tempo
                    }
                }
            ]
        }
        
        try:
            await s3.put_bucket_lifecycle_configuration(
                Bucket=self.bucket_name,
                LifecycleConfiguration=lifecycle_config
            )
            logger.debug("Lifecycle policy configured", bucket=self.bucket_name)
            
        except Exception as e:
            logger.warning("Failed to configure lifecycle policy", error=str(e))
    
    def _generate_object_key(self, url: str, content_type: str = "raw") -> str:
        """Gerar chave do objeto S3."""
        # Parse da URL
        parsed = urlparse(url)
        domain = parsed.netloc
        
        # Hash da URL para nome único
        url_hash = hashlib.sha256(url.encode('utf-8')).hexdigest()
        
        # Data para organização
        date_path = datetime.utcnow().strftime("%Y/%m/%d")
        
        # Extensão baseada no tipo
        extension = "html" if content_type == "raw" else "json"
        
        return f"{content_type}/{domain}/{date_path}/{url_hash}.{extension}"
    
    async def save_raw_content(
        self, 
        url: str, 
        content: str, 
        metadata: Dict
    ) -> str:
        """Salvar conteúdo HTML bruto."""
        object_key = self._generate_object_key(url, "raw")
        
        # Preparar conteúdo
        content_bytes = content.encode('utf-8')
        
        # Comprimir se habilitado
        if self.compress_content:
            content_bytes = gzip.compress(content_bytes)
            content_encoding = 'gzip'
        else:
            content_encoding = None
        
        # Metadados S3
        s3_metadata = {
            'url': url,
            'saved_at': datetime.utcnow().isoformat(),
            'content_length': str(len(content)),
            'compressed': str(self.compress_content),
        }
        
        # Adicionar metadados customizados
        for key, value in metadata.items():
            if isinstance(value, (str, int, float)):
                s3_metadata[f'custom_{key}'] = str(value)
        
        async with await self._get_client() as s3:
            try:
                # Upload do arquivo
                await s3.put_object(
                    Bucket=self.bucket_name,
                    Key=object_key,
                    Body=content_bytes,
                    ContentType='text/html',
                    ContentEncoding=content_encoding,
                    Metadata=s3_metadata,
                    ServerSideEncryption='AES256',
                )
                
                logger.debug(
                    "Raw content saved to S3",
                    url=url,
                    key=object_key,
                    size=len(content_bytes),
                    compressed=self.compress_content,
                )
                
                return f"s3://{self.bucket_name}/{object_key}"
                
            except Exception as e:
                logger.error(
                    "Failed to save raw content to S3",
                    url=url,
                    key=object_key,
                    error=str(e),
                )
                raise
    
    async def save_processed_data(self, page_data: PageData) -> str:
        """Salvar dados processados."""
        object_key = self._generate_object_key(str(page_data.url), "processed")
        
        # Serializar dados
        data_dict = page_data.model_dump()
        content = json.dumps(data_dict, ensure_ascii=False, default=str)
        content_bytes = content.encode('utf-8')
        
        # Comprimir
        if self.compress_content:
            content_bytes = gzip.compress(content_bytes)
            content_encoding = 'gzip'
        else:
            content_encoding = None
        
        # Metadados
        s3_metadata = {
            'url': str(page_data.url),
            'content_hash': page_data.content_hash,
            'quality_score': str(page_data.quality_score),
            'word_count': str(page_data.word_count),
            'processed_at': page_data.processed_at.isoformat(),
        }
        
        async with await self._get_client() as s3:
            try:
                await s3.put_object(
                    Bucket=self.bucket_name,
                    Key=object_key,
                    Body=content_bytes,
                    ContentType='application/json',
                    ContentEncoding=content_encoding,
                    Metadata=s3_metadata,
                    ServerSideEncryption='AES256',
                )
                
                logger.debug(
                    "Processed data saved to S3",
                    url=page_data.url,
                    key=object_key,
                    quality_score=page_data.quality_score,
                )
                
                return f"s3://{self.bucket_name}/{object_key}"
                
            except Exception as e:
                logger.error(
                    "Failed to save processed data to S3",
                    url=page_data.url,
                    key=object_key,
                    error=str(e),
                )
                raise
    
    async def load_processed_data(self, s3_path: str) -> Optional[PageData]:
        """Carregar dados processados do S3."""
        # Extrair bucket e key do path
        if not s3_path.startswith('s3://'):
            raise ValueError(f"Invalid S3 path: {s3_path}")
        
        path_parts = s3_path[5:].split('/', 1)
        bucket = path_parts[0]
        key = path_parts[1]
        
        async with await self._get_client() as s3:
            try:
                response = await s3.get_object(Bucket=bucket, Key=key)
                
                # Ler conteúdo
                content_bytes = await response['Body'].read()
                
                # Descomprimir se necessário
                if response.get('ContentEncoding') == 'gzip':
                    content_bytes = gzip.decompress(content_bytes)
                
                content = content_bytes.decode('utf-8')
                data_dict = json.loads(content)
                
                # Reconstruir PageData
                page_data = PageData(**data_dict)
                
                logger.debug("Processed data loaded from S3", s3_path=s3_path)
                return page_data
                
            except ClientError as e:
                if e.response['Error']['Code'] == 'NoSuchKey':
                    logger.debug("Object not found in S3", s3_path=s3_path)
                    return None
                else:
                    logger.error("Failed to load from S3", s3_path=s3_path, error=str(e))
                    raise
            except Exception as e:
                logger.error("Failed to load from S3", s3_path=s3_path, error=str(e))
                raise
    
    async def exists(self, s3_path: str) -> bool:
        """Verificar se objeto existe no S3."""
        if not s3_path.startswith('s3://'):
            return False
        
        path_parts = s3_path[5:].split('/', 1)
        bucket = path_parts[0]
        key = path_parts[1]
        
        async with await self._get_client() as s3:
            try:
                await s3.head_object(Bucket=bucket, Key=key)
                return True
            except ClientError:
                return False
    
    async def list_objects(self, prefix: str = "", max_keys: int = 1000) -> List[Dict]:
        """Listar objetos no bucket."""
        async with await self._get_client() as s3:
            try:
                response = await s3.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=prefix,
                    MaxKeys=max_keys
                )
                
                objects = []
                for obj in response.get('Contents', []):
                    objects.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'],
                        'etag': obj['ETag'].strip('"'),
                    })
                
                return objects
                
            except Exception as e:
                logger.error("Failed to list objects", prefix=prefix, error=str(e))
                raise
    
    async def get_stats(self) -> Dict:
        """Obter estatísticas do storage."""
        try:
            # Listar objetos por tipo
            raw_objects = await self.list_objects("raw/")
            processed_objects = await self.list_objects("processed/")
            
            # Calcular tamanhos
            raw_size = sum(obj['size'] for obj in raw_objects)
            processed_size = sum(obj['size'] for obj in processed_objects)
            
            return {
                "bucket": self.bucket_name,
                "raw_objects": len(raw_objects),
                "processed_objects": len(processed_objects),
                "total_objects": len(raw_objects) + len(processed_objects),
                "raw_size_bytes": raw_size,
                "processed_size_bytes": processed_size,
                "total_size_bytes": raw_size + processed_size,
                "compression_enabled": self.compress_content,
                "versioning_enabled": self.enable_versioning,
            }
            
        except Exception as e:
            logger.error("Failed to get S3 stats", error=str(e))
            return {"error": str(e)}
    
    async def cleanup_old_objects(self, days_old: int = 30) -> int:
        """Limpar objetos antigos."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        deleted_count = 0
        
        async with await self._get_client() as s3:
            try:
                # Listar objetos antigos
                response = await s3.list_objects_v2(Bucket=self.bucket_name)
                
                objects_to_delete = []
                for obj in response.get('Contents', []):
                    if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                        objects_to_delete.append({'Key': obj['Key']})
                
                # Deletar em lotes
                if objects_to_delete:
                    await s3.delete_objects(
                        Bucket=self.bucket_name,
                        Delete={'Objects': objects_to_delete}
                    )
                    deleted_count = len(objects_to_delete)
                
                logger.info("S3 cleanup completed", deleted_objects=deleted_count)
                return deleted_count
                
            except Exception as e:
                logger.error("Failed to cleanup S3 objects", error=str(e))
                raise

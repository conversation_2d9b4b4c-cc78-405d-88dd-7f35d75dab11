"""
Validators - Modelos Pydantic para validação de dados.

Este módulo contém os modelos de dados usando Pydantic para validação
e serialização de dados do WebScraper.
"""

import hashlib
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from pydantic import BaseModel, Field, HttpUrl, validator


class LogLevel(str, Enum):
    """Níveis de log disponíveis."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ScrapingStatus(str, Enum):
    """Status de scraping de uma página."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class ContentType(str, Enum):
    """Tipos de conteúdo suportados."""
    HTML = "html"
    JSON = "json"
    XML = "xml"
    TEXT = "text"
    PDF = "pdf"


class RateLimitConfig(BaseModel):
    """Configuração de rate limiting."""
    requests_per_second: float = Field(default=1.0, ge=0.1, le=100.0)
    burst_limit: int = Field(default=5, ge=1, le=100)
    backoff_factor: float = Field(default=2.0, ge=1.0, le=10.0)
    max_retries: int = Field(default=3, ge=0, le=10)


class TimeoutConfig(BaseModel):
    """Configuração de timeouts."""
    connect: int = Field(default=10, ge=1, le=300)
    read: int = Field(default=30, ge=1, le=300)
    total: int = Field(default=60, ge=1, le=600)


class ParsingConfig(BaseModel):
    """Configuração de parsing."""
    content_selectors: Dict[str, str] = Field(default_factory=dict)
    remove_selectors: List[str] = Field(default_factory=list)
    required_elements: List[str] = Field(default_factory=list)


class QualityConfig(BaseModel):
    """Configuração de qualidade."""
    min_content_length: int = Field(default=100, ge=0)
    min_word_count: int = Field(default=20, ge=0)
    min_headings: int = Field(default=1, ge=0)
    bonus_selectors: List[str] = Field(default_factory=list)


class DomainConfig(BaseModel):
    """Configuração específica de um domínio."""
    rate_limit: RateLimitConfig = Field(default_factory=RateLimitConfig)
    timeouts: TimeoutConfig = Field(default_factory=TimeoutConfig)
    parsing: ParsingConfig = Field(default_factory=ParsingConfig)
    quality: QualityConfig = Field(default_factory=QualityConfig)
    
    allowed_paths: List[str] = Field(default_factory=list)
    blocked_paths: List[str] = Field(default_factory=list)
    
    dynamic_content: bool = Field(default=False)
    wait_for_selector: Optional[str] = None
    wait_timeout: int = Field(default=10000, ge=1000, le=60000)


class ScrapingConfig(BaseModel):
    """Configuração geral de scraping."""
    default: DomainConfig = Field(default_factory=DomainConfig)
    domains: Dict[str, DomainConfig] = Field(default_factory=dict)
    
    user_agent: str = Field(default="WebScraper/1.0")
    respect_robots_txt: bool = Field(default=True)
    enable_cache: bool = Field(default=True)
    cache_ttl: int = Field(default=3600, ge=0)
    
    max_concurrent_requests: int = Field(default=10, ge=1, le=100)
    max_pages_per_domain: int = Field(default=10000, ge=1)
    
    def get_domain_config(self, domain: str) -> DomainConfig:
        """Obter configuração para um domínio específico."""
        return self.domains.get(domain, self.default)


class HTTPHeaders(BaseModel):
    """Headers HTTP."""
    content_type: Optional[str] = None
    etag: Optional[str] = None
    last_modified: Optional[str] = None
    content_length: Optional[int] = None
    
    class Config:
        extra = "allow"  # Permitir headers adicionais


class HTTPResponse(BaseModel):
    """Resposta HTTP."""
    url: HttpUrl
    status_code: int = Field(ge=100, le=599)
    headers: HTTPHeaders = Field(default_factory=HTTPHeaders)
    content: str = ""
    encoding: str = "utf-8"
    
    fetched_at: datetime = Field(default_factory=datetime.utcnow)
    duration_ms: int = Field(default=0, ge=0)
    
    @validator('content')
    def validate_content(cls, v):
        """Validar que o conteúdo não é muito grande."""
        max_size = 10 * 1024 * 1024  # 10MB
        if len(v.encode('utf-8')) > max_size:
            raise ValueError(f"Content too large: {len(v)} bytes")
        return v


class CodeBlock(BaseModel):
    """Bloco de código extraído."""
    language: Optional[str] = None
    content: str
    line_start: Optional[int] = None
    snippet_hash: str = ""
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.snippet_hash:
            self.snippet_hash = hashlib.sha256(
                self.content.encode('utf-8')
            ).hexdigest()[:16]


class TableData(BaseModel):
    """Dados de tabela extraídos."""
    headers: List[str] = Field(default_factory=list)
    rows: List[List[str]] = Field(default_factory=list)
    caption: Optional[str] = None


class HeadingNode(BaseModel):
    """Nó da árvore de headings."""
    level: int = Field(ge=1, le=6)
    text: str
    id: Optional[str] = None
    children: List['HeadingNode'] = Field(default_factory=list)


# Permitir referências circulares
HeadingNode.model_rebuild()


class PageData(BaseModel):
    """Dados extraídos de uma página."""
    url: HttpUrl
    title: str = ""
    
    # Conteúdo estruturado
    headings_tree: List[HeadingNode] = Field(default_factory=list)
    text_content: str = ""
    code_blocks: List[CodeBlock] = Field(default_factory=list)
    tables: List[TableData] = Field(default_factory=list)
    
    # Links
    internal_links: List[str] = Field(default_factory=list)
    external_links: List[str] = Field(default_factory=list)
    
    # Metadados
    content_hash: str = ""
    quality_score: int = Field(default=0, ge=0, le=100)
    word_count: int = Field(default=0, ge=0)
    
    # Timestamps
    fetched_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Status e erros
    status: ScrapingStatus = ScrapingStatus.PENDING
    errors: List[str] = Field(default_factory=list)
    
    @validator('content_hash', always=True)
    def generate_content_hash(cls, v, values):
        """Gerar hash do conteúdo se não fornecido."""
        if not v and 'text_content' in values:
            content = values['text_content']
            return hashlib.sha256(content.encode('utf-8')).hexdigest()
        return v
    
    @validator('word_count', always=True)
    def calculate_word_count(cls, v, values):
        """Calcular número de palavras se não fornecido."""
        if not v and 'text_content' in values:
            content = values['text_content']
            return len(content.split())
        return v
    
    def calculate_quality_score(self, config: QualityConfig) -> int:
        """Calcular score de qualidade baseado na configuração."""
        score = 0
        
        # Score base por tamanho do conteúdo
        if len(self.text_content) >= config.min_content_length:
            score += 30
        
        # Score por número de palavras
        if self.word_count >= config.min_word_count:
            score += 20
        
        # Score por título
        if self.title.strip():
            score += 15
        
        # Score por headings
        if len(self.headings_tree) >= config.min_headings:
            score += 15
        
        # Score por código
        if self.code_blocks:
            score += 10
        
        # Score por tabelas
        if self.tables:
            score += 5
        
        # Score por links internos
        if self.internal_links:
            score += 5
        
        return min(score, 100)


class CrawlStats(BaseModel):
    """Estatísticas de crawling."""
    total_urls: int = 0
    processed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    skipped_urls: int = 0
    
    start_time: datetime = Field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    
    domains_processed: Dict[str, int] = Field(default_factory=dict)
    error_counts: Dict[str, int] = Field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """Taxa de sucesso."""
        if self.processed_urls == 0:
            return 0.0
        return (self.successful_urls / self.processed_urls) * 100
    
    @property
    def duration_seconds(self) -> float:
        """Duração em segundos."""
        if not self.end_time:
            return (datetime.utcnow() - self.start_time).total_seconds()
        return (self.end_time - self.start_time).total_seconds()


class URLInfo(BaseModel):
    """Informações sobre uma URL."""
    url: HttpUrl
    domain: str = ""
    depth: int = Field(default=0, ge=0)
    priority: int = Field(default=0, ge=0, le=10)
    
    discovered_at: datetime = Field(default_factory=datetime.utcnow)
    last_attempt: Optional[datetime] = None
    attempt_count: int = Field(default=0, ge=0)
    
    status: ScrapingStatus = ScrapingStatus.PENDING
    
    @validator('domain', always=True)
    def extract_domain(cls, v, values):
        """Extrair domínio da URL se não fornecido."""
        if not v and 'url' in values:
            parsed = urlparse(str(values['url']))
            return parsed.netloc.lower()
        return v

"""
Versioning - Sistema de versionamento por hash de conteúdo.

Este módulo implementa versionamento inteligente baseado em hash do conteúdo,
permitindo detectar mudanças e manter histórico de versões.
"""

import hashlib
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import structlog

from .validators import PageData

logger = structlog.get_logger(__name__)


class ContentHasher:
    """Gerador de hashes de conteúdo para versionamento."""
    
    @staticmethod
    def hash_content(content: str, algorithm: str = "sha256") -> str:
        """Gerar hash do conteúdo."""
        if not content:
            return ""
        
        hasher = hashlib.new(algorithm)
        hasher.update(content.encode('utf-8'))
        return hasher.hexdigest()
    
    @staticmethod
    def hash_page_data(page_data: PageData, include_metadata: bool = False) -> str:
        """<PERSON>erar hash dos dados estruturados da página."""
        # Criar representação normalizada dos dados
        content_dict = {
            "title": page_data.title.strip(),
            "text_content": page_data.text_content.strip(),
            "headings": [
                {"level": h.level, "text": h.text.strip()}
                for h in page_data.headings_tree
            ],
            "code_blocks": [
                {"language": cb.language, "content": cb.content.strip()}
                for cb in page_data.code_blocks
            ],
            "tables": [
                {"headers": t.headers, "rows": t.rows}
                for t in page_data.tables
            ],
        }
        
        if include_metadata:
            content_dict.update({
                "internal_links_count": len(page_data.internal_links),
                "external_links_count": len(page_data.external_links),
                "word_count": page_data.word_count,
            })
        
        # Serializar de forma determinística
        content_json = json.dumps(content_dict, sort_keys=True, ensure_ascii=False)
        
        return ContentHasher.hash_content(content_json)
    
    @staticmethod
    def hash_semantic_content(page_data: PageData) -> str:
        """Gerar hash apenas do conteúdo semântico (ignora formatação)."""
        # Normalizar texto removendo espaços extras e quebras de linha
        normalized_text = " ".join(page_data.text_content.split())
        
        # Incluir apenas headings principais (h1, h2)
        main_headings = [
            h.text.strip() for h in page_data.headings_tree 
            if h.level <= 2
        ]
        
        semantic_content = {
            "title": page_data.title.strip(),
            "text": normalized_text,
            "main_headings": main_headings,
            "code_count": len(page_data.code_blocks),
            "table_count": len(page_data.tables),
        }
        
        content_json = json.dumps(semantic_content, sort_keys=True, ensure_ascii=False)
        return ContentHasher.hash_content(content_json)


class VersionInfo:
    """Informações sobre uma versão de conteúdo."""
    
    def __init__(
        self,
        content_hash: str,
        semantic_hash: str,
        version_number: int,
        created_at: datetime,
        page_data: PageData,
        changes: Optional[List[str]] = None
    ):
        self.content_hash = content_hash
        self.semantic_hash = semantic_hash
        self.version_number = version_number
        self.created_at = created_at
        self.page_data = page_data
        self.changes = changes or []
    
    def to_dict(self) -> Dict:
        """Converter para dicionário."""
        return {
            "content_hash": self.content_hash,
            "semantic_hash": self.semantic_hash,
            "version_number": self.version_number,
            "created_at": self.created_at.isoformat(),
            "changes": self.changes,
            "quality_score": self.page_data.quality_score,
            "word_count": self.page_data.word_count,
        }


class ContentVersionManager:
    """Gerenciador de versões de conteúdo."""
    
    def __init__(self):
        # Histórico de versões por URL
        self.version_history: Dict[str, List[VersionInfo]] = {}
        
        # Cache de hashes atuais
        self.current_hashes: Dict[str, Tuple[str, str]] = {}  # url -> (content_hash, semantic_hash)
    
    def detect_changes(
        self, 
        url: str, 
        new_page_data: PageData
    ) -> Tuple[bool, bool, List[str]]:
        """
        Detectar mudanças no conteúdo.
        
        Returns:
            (content_changed, semantic_changed, change_descriptions)
        """
        # Gerar hashes do novo conteúdo
        new_content_hash = ContentHasher.hash_page_data(new_page_data)
        new_semantic_hash = ContentHasher.hash_semantic_content(new_page_data)
        
        # Verificar se há versão anterior
        if url not in self.current_hashes:
            logger.info("First version detected", url=url, content_hash=new_content_hash[:16])
            return True, True, ["Initial version"]
        
        old_content_hash, old_semantic_hash = self.current_hashes[url]
        
        content_changed = new_content_hash != old_content_hash
        semantic_changed = new_semantic_hash != old_semantic_hash
        
        changes = []
        
        if content_changed:
            if semantic_changed:
                changes.append("Semantic content changed")
                
                # Detectar tipos específicos de mudança
                if url in self.version_history:
                    old_version = self.version_history[url][-1]
                    changes.extend(self._detect_specific_changes(old_version.page_data, new_page_data))
            else:
                changes.append("Formatting or minor changes only")
        
        logger.info(
            "Change detection completed",
            url=url,
            content_changed=content_changed,
            semantic_changed=semantic_changed,
            changes=changes,
        )
        
        return content_changed, semantic_changed, changes
    
    def _detect_specific_changes(self, old_data: PageData, new_data: PageData) -> List[str]:
        """Detectar tipos específicos de mudanças."""
        changes = []
        
        # Mudança no título
        if old_data.title != new_data.title:
            changes.append("Title changed")
        
        # Mudança significativa no tamanho do conteúdo
        old_words = old_data.word_count
        new_words = new_data.word_count
        word_change_percent = abs(new_words - old_words) / max(old_words, 1) * 100
        
        if word_change_percent > 10:
            if new_words > old_words:
                changes.append(f"Content expanded (+{word_change_percent:.1f}%)")
            else:
                changes.append(f"Content reduced (-{word_change_percent:.1f}%)")
        
        # Mudança na estrutura de headings
        old_headings = [h.text for h in old_data.headings_tree]
        new_headings = [h.text for h in new_data.headings_tree]
        
        if old_headings != new_headings:
            changes.append("Heading structure changed")
        
        # Mudança em blocos de código
        if len(old_data.code_blocks) != len(new_data.code_blocks):
            changes.append("Code blocks changed")
        
        # Mudança em tabelas
        if len(old_data.tables) != len(new_data.tables):
            changes.append("Tables changed")
        
        return changes
    
    def create_version(
        self, 
        url: str, 
        page_data: PageData, 
        changes: Optional[List[str]] = None
    ) -> VersionInfo:
        """Criar nova versão do conteúdo."""
        # Gerar hashes
        content_hash = ContentHasher.hash_page_data(page_data)
        semantic_hash = ContentHasher.hash_semantic_content(page_data)
        
        # Determinar número da versão
        if url in self.version_history:
            version_number = len(self.version_history[url]) + 1
        else:
            version_number = 1
            self.version_history[url] = []
        
        # Criar versão
        version = VersionInfo(
            content_hash=content_hash,
            semantic_hash=semantic_hash,
            version_number=version_number,
            created_at=datetime.utcnow(),
            page_data=page_data,
            changes=changes or [],
        )
        
        # Adicionar ao histórico
        self.version_history[url].append(version)
        
        # Atualizar cache de hashes
        self.current_hashes[url] = (content_hash, semantic_hash)
        
        logger.info(
            "Version created",
            url=url,
            version_number=version_number,
            content_hash=content_hash[:16],
            changes=changes,
        )
        
        return version
    
    def get_version_history(self, url: str) -> List[VersionInfo]:
        """Obter histórico de versões de uma URL."""
        return self.version_history.get(url, [])
    
    def get_latest_version(self, url: str) -> Optional[VersionInfo]:
        """Obter última versão de uma URL."""
        history = self.get_version_history(url)
        return history[-1] if history else None
    
    def should_update(
        self, 
        url: str, 
        new_page_data: PageData,
        semantic_only: bool = True
    ) -> bool:
        """Verificar se deve atualizar baseado nas mudanças."""
        content_changed, semantic_changed, _ = self.detect_changes(url, new_page_data)
        
        if semantic_only:
            return semantic_changed
        else:
            return content_changed
    
    def get_stats(self) -> Dict:
        """Obter estatísticas do versionamento."""
        total_urls = len(self.version_history)
        total_versions = sum(len(versions) for versions in self.version_history.values())
        
        # URLs com múltiplas versões
        multi_version_urls = sum(
            1 for versions in self.version_history.values() 
            if len(versions) > 1
        )
        
        # Versão média por URL
        avg_versions = total_versions / total_urls if total_urls > 0 else 0
        
        return {
            "total_urls": total_urls,
            "total_versions": total_versions,
            "multi_version_urls": multi_version_urls,
            "average_versions_per_url": round(avg_versions, 2),
            "change_rate": round(multi_version_urls / total_urls * 100, 1) if total_urls > 0 else 0,
        }
    
    def cleanup_old_versions(self, max_versions_per_url: int = 10) -> int:
        """Limpar versões antigas mantendo apenas as mais recentes."""
        cleaned_count = 0
        
        for url, versions in self.version_history.items():
            if len(versions) > max_versions_per_url:
                # Manter apenas as versões mais recentes
                versions_to_keep = versions[-max_versions_per_url:]
                removed_count = len(versions) - len(versions_to_keep)
                
                self.version_history[url] = versions_to_keep
                cleaned_count += removed_count
                
                logger.debug(
                    "Cleaned old versions",
                    url=url,
                    removed_count=removed_count,
                    kept_count=len(versions_to_keep),
                )
        
        logger.info("Version cleanup completed", cleaned_versions=cleaned_count)
        return cleaned_count

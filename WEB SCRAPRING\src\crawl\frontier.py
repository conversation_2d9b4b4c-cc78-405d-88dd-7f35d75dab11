"""
URL Frontier - Fila de URLs com priorização e deduplicação.

Este módulo implementa uma fila inteligente de URLs para crawling,
com suporte a priorização, deduplicação e controle de profundidade.
"""

import hashlib
import heapq
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse

import structlog

from ..core.validators import ScrapingStatus, URLInfo

logger = structlog.get_logger(__name__)


class URLFrontier:
    """Fila de URLs com priorização e deduplicação."""
    
    def __init__(self, max_depth: int = 5, max_urls_per_domain: int = 10000):
        self.max_depth = max_depth
        self.max_urls_per_domain = max_urls_per_domain
        
        # Fila de prioridade: (prioridade_negativa, timestamp, url_info)
        self._queue: List[Tuple[int, float, URLInfo]] = []
        
        # Controle de duplicatas
        self._seen_urls: Set[str] = set()
        self._url_hashes: Set[str] = set()
        
        # Estatísticas por domínio
        self._domain_counts: Dict[str, int] = defaultdict(int)
        self._domain_last_access: Dict[str, datetime] = {}
        
        # URLs processadas
        self._processed_urls: Dict[str, URLInfo] = {}
        
        # Controle de rate limiting por domínio
        self._domain_delays: Dict[str, float] = defaultdict(lambda: 1.0)
    
    def add_url(self, url_info: URLInfo) -> bool:
        """Adicionar URL à fila."""
        url = str(url_info.url)
        domain = url_info.domain
        
        # Verificar se já foi vista
        if self._is_duplicate(url):
            logger.debug("Duplicate URL skipped", url=url)
            return False
        
        # Verificar limites por domínio
        if self._domain_counts[domain] >= self.max_urls_per_domain:
            logger.debug(
                "Domain limit reached",
                domain=domain,
                limit=self.max_urls_per_domain,
            )
            return False
        
        # Verificar profundidade máxima
        if url_info.depth > self.max_depth:
            logger.debug(
                "Max depth exceeded",
                url=url,
                depth=url_info.depth,
                max_depth=self.max_depth,
            )
            return False
        
        # Adicionar à fila
        priority = -url_info.priority  # Heap é min-heap, queremos max-priority
        timestamp = datetime.utcnow().timestamp()
        
        heapq.heappush(self._queue, (priority, timestamp, url_info))
        
        # Marcar como vista
        self._seen_urls.add(url)
        self._url_hashes.add(self._get_url_hash(url))
        self._domain_counts[domain] += 1
        
        logger.debug(
            "URL added to frontier",
            url=url,
            domain=domain,
            priority=url_info.priority,
            depth=url_info.depth,
            queue_size=len(self._queue),
        )
        
        return True
    
    def add_urls(self, url_infos: List[URLInfo]) -> int:
        """Adicionar múltiplas URLs à fila."""
        added_count = 0
        
        for url_info in url_infos:
            if self.add_url(url_info):
                added_count += 1
        
        logger.info(
            "Bulk URLs added",
            total_provided=len(url_infos),
            added=added_count,
            queue_size=len(self._queue),
        )
        
        return added_count
    
    def get_next_url(self, respect_delays: bool = True) -> Optional[URLInfo]:
        """Obter próxima URL da fila."""
        if not self._queue:
            return None
        
        # Se respeitar delays, verificar se algum domínio está disponível
        if respect_delays:
            available_url = self._find_available_url()
            if available_url:
                return available_url
        
        # Caso contrário, pegar a próxima da fila
        if self._queue:
            _, _, url_info = heapq.heappop(self._queue)
            self._mark_url_accessed(url_info)
            return url_info
        
        return None
    
    def _find_available_url(self) -> Optional[URLInfo]:
        """Encontrar URL de domínio que não está em delay."""
        now = datetime.utcnow()
        temp_queue = []
        found_url = None
        
        # Procurar na fila por URL disponível
        while self._queue and not found_url:
            priority, timestamp, url_info = heapq.heappop(self._queue)
            domain = url_info.domain
            
            # Verificar se o domínio está disponível
            if self._is_domain_available(domain, now):
                found_url = url_info
                self._mark_url_accessed(url_info)
            else:
                # Recolocar na fila temporária
                temp_queue.append((priority, timestamp, url_info))
        
        # Recolocar URLs não processadas na fila
        for item in temp_queue:
            heapq.heappush(self._queue, item)
        
        return found_url
    
    def _is_domain_available(self, domain: str, now: datetime) -> bool:
        """Verificar se domínio está disponível (não em delay)."""
        if domain not in self._domain_last_access:
            return True
        
        last_access = self._domain_last_access[domain]
        delay = self._domain_delays[domain]
        
        return (now - last_access).total_seconds() >= delay
    
    def _mark_url_accessed(self, url_info: URLInfo) -> None:
        """Marcar URL como acessada."""
        domain = url_info.domain
        self._domain_last_access[domain] = datetime.utcnow()
        
        # Atualizar contadores
        url_info.last_attempt = datetime.utcnow()
        url_info.attempt_count += 1
        
        logger.debug(
            "URL accessed",
            url=url_info.url,
            domain=domain,
            attempt_count=url_info.attempt_count,
        )
    
    def mark_url_completed(self, url: str, status: ScrapingStatus) -> None:
        """Marcar URL como processada."""
        url_info = self._processed_urls.get(url)
        if url_info:
            url_info.status = status
            
            logger.debug(
                "URL marked as completed",
                url=url,
                status=status,
            )
    
    def mark_url_failed(self, url: str, error: str) -> None:
        """Marcar URL como falhada."""
        url_info = self._processed_urls.get(url)
        if url_info:
            url_info.status = ScrapingStatus.FAILED
            
            # Aumentar delay do domínio se muitas falhas
            domain = url_info.domain
            self._domain_delays[domain] = min(
                self._domain_delays[domain] * 1.5,
                30.0  # Máximo 30 segundos
            )
            
            logger.warning(
                "URL marked as failed",
                url=url,
                error=error,
                domain=domain,
                new_delay=self._domain_delays[domain],
            )
    
    def requeue_url(self, url_info: URLInfo, new_priority: Optional[int] = None) -> bool:
        """Recolocar URL na fila com nova prioridade."""
        if new_priority is not None:
            url_info.priority = new_priority
        
        # Remover da lista de processadas se estiver lá
        url = str(url_info.url)
        if url in self._processed_urls:
            del self._processed_urls[url]
        
        # Adicionar de volta à fila
        priority = -url_info.priority
        timestamp = datetime.utcnow().timestamp()
        
        heapq.heappush(self._queue, (priority, timestamp, url_info))
        
        logger.debug(
            "URL requeued",
            url=url,
            new_priority=url_info.priority,
        )
        
        return True
    
    def _is_duplicate(self, url: str) -> bool:
        """Verificar se URL é duplicata."""
        # Verificar URL exata
        if url in self._seen_urls:
            return True
        
        # Verificar hash normalizado
        url_hash = self._get_url_hash(url)
        if url_hash in self._url_hashes:
            return True
        
        return False
    
    def _get_url_hash(self, url: str) -> str:
        """Gerar hash normalizado da URL."""
        # Normalizar URL para detecção de duplicatas
        parsed = urlparse(url.lower())
        
        # Remover fragmentos e alguns parâmetros
        normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        
        # Adicionar query string se não for parâmetro de tracking
        if parsed.query:
            # Filtrar parâmetros de tracking comuns
            tracking_params = {
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'msclkid', '_ga', '_gl'
            }
            
            query_parts = []
            for part in parsed.query.split('&'):
                if '=' in part:
                    key = part.split('=')[0]
                    if key not in tracking_params:
                        query_parts.append(part)
                else:
                    query_parts.append(part)
            
            if query_parts:
                normalized += '?' + '&'.join(sorted(query_parts))
        
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()
    
    def get_stats(self) -> Dict:
        """Obter estatísticas da fila."""
        total_processed = len(self._processed_urls)
        successful = sum(
            1 for url_info in self._processed_urls.values()
            if url_info.status == ScrapingStatus.SUCCESS
        )
        failed = sum(
            1 for url_info in self._processed_urls.values()
            if url_info.status == ScrapingStatus.FAILED
        )
        
        return {
            "queue_size": len(self._queue),
            "total_seen": len(self._seen_urls),
            "total_processed": total_processed,
            "successful": successful,
            "failed": failed,
            "domains": dict(self._domain_counts),
            "success_rate": (successful / total_processed * 100) if total_processed > 0 else 0,
        }
    
    def is_empty(self) -> bool:
        """Verificar se a fila está vazia."""
        return len(self._queue) == 0
    
    def clear(self) -> None:
        """Limpar toda a fila."""
        self._queue.clear()
        self._seen_urls.clear()
        self._url_hashes.clear()
        self._domain_counts.clear()
        self._domain_last_access.clear()
        self._processed_urls.clear()
        self._domain_delays.clear()
        
        logger.info("Frontier cleared")
    
    def get_domain_stats(self, domain: str) -> Dict:
        """Obter estatísticas de um domínio específico."""
        domain_urls = [
            url_info for url_info in self._processed_urls.values()
            if url_info.domain == domain
        ]
        
        successful = sum(
            1 for url_info in domain_urls
            if url_info.status == ScrapingStatus.SUCCESS
        )
        failed = sum(
            1 for url_info in domain_urls
            if url_info.status == ScrapingStatus.FAILED
        )
        
        return {
            "domain": domain,
            "total_urls": self._domain_counts[domain],
            "processed": len(domain_urls),
            "successful": successful,
            "failed": failed,
            "current_delay": self._domain_delays[domain],
            "last_access": self._domain_last_access.get(domain),
            "success_rate": (successful / len(domain_urls) * 100) if domain_urls else 0,
        }

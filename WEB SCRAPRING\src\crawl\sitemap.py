"""
Sitemap Discovery - Descoberta de URLs via sitemap.xml.

Este módulo implementa a descoberta de URLs através de sitemaps,
incluindo suporte para índices de sitemap e filtros de URL.
"""

import re
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Optional, Set
from urllib.parse import urljoin, urlparse

import structlog

from ..core.http_client import AsyncHTTPClient
from ..core.validators import ScrapingConfig, URLInfo

logger = structlog.get_logger(__name__)


class SitemapDiscovery:
    """Descoberta de URLs através de sitemaps."""
    
    def __init__(self, http_client: AsyncHTTPClient, config: ScrapingConfig):
        self.http_client = http_client
        self.config = config
        self._processed_sitemaps: Set[str] = set()
    
    async def discover_urls(self, domain: str, max_urls: int = 10000) -> List[URLInfo]:
        """Descobrir URLs de um domínio através de sitemaps."""
        logger.info("Starting sitemap discovery", domain=domain, max_urls=max_urls)
        
        base_url = f"https://{domain}"
        discovered_urls = []
        
        # Tentar encontrar sitemaps
        sitemap_urls = await self._find_sitemaps(base_url)
        
        if not sitemap_urls:
            logger.warning("No sitemaps found", domain=domain)
            return []
        
        # Processar cada sitemap
        for sitemap_url in sitemap_urls:
            if len(discovered_urls) >= max_urls:
                break
            
            try:
                urls = await self._process_sitemap(
                    sitemap_url, 
                    max_urls - len(discovered_urls)
                )
                discovered_urls.extend(urls)
                
            except Exception as e:
                logger.error(
                    "Failed to process sitemap",
                    sitemap_url=sitemap_url,
                    error=str(e),
                )
        
        # Filtrar e priorizar URLs
        filtered_urls = await self._filter_and_prioritize_urls(
            discovered_urls, domain
        )
        
        logger.info(
            "Sitemap discovery completed",
            domain=domain,
            total_discovered=len(discovered_urls),
            after_filtering=len(filtered_urls),
        )
        
        return filtered_urls[:max_urls]
    
    async def _find_sitemaps(self, base_url: str) -> List[str]:
        """Encontrar URLs de sitemaps para um domínio."""
        sitemap_urls = []
        
        # Padrões comuns de sitemap
        common_paths = [
            "/sitemap.xml",
            "/sitemap_index.xml",
            "/sitemaps.xml",
            "/sitemap/sitemap.xml",
            "/wp-sitemap.xml",  # WordPress
            "/sitemap-index.xml",
        ]
        
        # Tentar URLs comuns de sitemap
        for path in common_paths:
            sitemap_url = urljoin(base_url, path)
            
            try:
                response = await self.http_client.head(sitemap_url)
                if response.status_code == 200:
                    content_type = response.headers.content_type or ""
                    if "xml" in content_type.lower():
                        sitemap_urls.append(sitemap_url)
                        logger.debug("Found sitemap", url=sitemap_url)
                        
            except Exception as e:
                logger.debug(
                    "Sitemap not found",
                    url=sitemap_url,
                    error=str(e),
                )
        
        # Tentar robots.txt
        robots_sitemaps = await self._find_sitemaps_in_robots(base_url)
        sitemap_urls.extend(robots_sitemaps)
        
        # Remover duplicatas
        return list(dict.fromkeys(sitemap_urls))
    
    async def _find_sitemaps_in_robots(self, base_url: str) -> List[str]:
        """Encontrar sitemaps referenciados no robots.txt."""
        robots_url = urljoin(base_url, "/robots.txt")
        sitemap_urls = []
        
        try:
            response = await self.http_client.get(robots_url)
            if response.status_code == 200:
                content = response.content
                
                # Procurar por linhas Sitemap:
                for line in content.split('\n'):
                    line = line.strip()
                    if line.lower().startswith('sitemap:'):
                        sitemap_url = line[8:].strip()
                        if sitemap_url:
                            sitemap_urls.append(sitemap_url)
                            logger.debug(
                                "Found sitemap in robots.txt",
                                url=sitemap_url,
                            )
                            
        except Exception as e:
            logger.debug(
                "Could not fetch robots.txt",
                url=robots_url,
                error=str(e),
            )
        
        return sitemap_urls
    
    async def _process_sitemap(self, sitemap_url: str, max_urls: int) -> List[URLInfo]:
        """Processar um sitemap individual."""
        if sitemap_url in self._processed_sitemaps:
            return []
        
        self._processed_sitemaps.add(sitemap_url)
        
        try:
            response = await self.http_client.get(sitemap_url)
            if response.status_code != 200:
                logger.warning(
                    "Sitemap returned non-200 status",
                    url=sitemap_url,
                    status_code=response.status_code,
                )
                return []
            
            # Parse XML
            root = ET.fromstring(response.content)
            
            # Detectar tipo de sitemap
            if self._is_sitemap_index(root):
                return await self._process_sitemap_index(root, max_urls)
            else:
                return await self._process_url_sitemap(root, max_urls)
                
        except ET.ParseError as e:
            logger.error(
                "Failed to parse sitemap XML",
                url=sitemap_url,
                error=str(e),
            )
            return []
        
        except Exception as e:
            logger.error(
                "Failed to process sitemap",
                url=sitemap_url,
                error=str(e),
            )
            return []
    
    def _is_sitemap_index(self, root: ET.Element) -> bool:
        """Verificar se é um índice de sitemaps."""
        # Verificar namespace e tag root
        tag = root.tag.lower()
        return "sitemapindex" in tag or any(
            "sitemap" in child.tag.lower() and "loc" not in child.tag.lower()
            for child in root
        )
    
    async def _process_sitemap_index(self, root: ET.Element, max_urls: int) -> List[URLInfo]:
        """Processar índice de sitemaps."""
        urls = []
        
        # Encontrar elementos sitemap
        sitemap_elements = []
        for child in root:
            if "sitemap" in child.tag.lower():
                sitemap_elements.append(child)
        
        # Processar cada sitemap referenciado
        for sitemap_elem in sitemap_elements:
            if len(urls) >= max_urls:
                break
            
            loc_elem = sitemap_elem.find(".//*[local-name()='loc']")
            if loc_elem is not None and loc_elem.text:
                sitemap_url = loc_elem.text.strip()
                
                try:
                    sitemap_urls = await self._process_sitemap(
                        sitemap_url,
                        max_urls - len(urls)
                    )
                    urls.extend(sitemap_urls)
                    
                except Exception as e:
                    logger.error(
                        "Failed to process nested sitemap",
                        url=sitemap_url,
                        error=str(e),
                    )
        
        return urls
    
    async def _process_url_sitemap(self, root: ET.Element, max_urls: int) -> List[URLInfo]:
        """Processar sitemap de URLs."""
        urls = []
        
        # Encontrar elementos URL
        url_elements = []
        for child in root:
            if "url" in child.tag.lower():
                url_elements.append(child)
        
        for url_elem in url_elements[:max_urls]:
            try:
                url_info = self._parse_url_element(url_elem)
                if url_info:
                    urls.append(url_info)
                    
            except Exception as e:
                logger.debug(
                    "Failed to parse URL element",
                    error=str(e),
                )
        
        return urls
    
    def _parse_url_element(self, url_elem: ET.Element) -> Optional[URLInfo]:
        """Parse um elemento URL do sitemap."""
        # Extrair URL
        loc_elem = url_elem.find(".//*[local-name()='loc']")
        if loc_elem is None or not loc_elem.text:
            return None
        
        url = loc_elem.text.strip()
        
        # Extrair prioridade (se disponível)
        priority = 0
        priority_elem = url_elem.find(".//*[local-name()='priority']")
        if priority_elem is not None and priority_elem.text:
            try:
                priority_value = float(priority_elem.text)
                priority = int(priority_value * 10)  # Converter para 0-10
            except ValueError:
                pass
        
        # Extrair última modificação (se disponível)
        lastmod_elem = url_elem.find(".//*[local-name()='lastmod']")
        last_modified = None
        if lastmod_elem is not None and lastmod_elem.text:
            try:
                # Tentar parsear data ISO
                date_str = lastmod_elem.text.strip()
                last_modified = datetime.fromisoformat(
                    date_str.replace('Z', '+00:00')
                )
            except ValueError:
                pass
        
        return URLInfo(
            url=url,
            priority=priority,
            discovered_at=last_modified or datetime.utcnow(),
        )
    
    async def _filter_and_prioritize_urls(
        self, urls: List[URLInfo], domain: str
    ) -> List[URLInfo]:
        """Filtrar e priorizar URLs descobertas."""
        domain_config = self.config.get_domain_config(domain)
        filtered_urls = []
        
        for url_info in urls:
            url = str(url_info.url)
            
            # Verificar se a URL está no escopo permitido
            if not self._is_url_allowed(url, domain_config):
                continue
            
            # Aplicar filtros de padrão
            if not self._matches_include_patterns(url):
                continue
            
            if self._matches_exclude_patterns(url):
                continue
            
            # Ajustar prioridade baseado em padrões
            url_info.priority = self._calculate_priority(url, url_info.priority)
            
            filtered_urls.append(url_info)
        
        # Ordenar por prioridade (maior primeiro)
        filtered_urls.sort(key=lambda x: x.priority, reverse=True)
        
        return filtered_urls
    
    def _is_url_allowed(self, url: str, domain_config) -> bool:
        """Verificar se URL está no escopo permitido."""
        parsed = urlparse(url)
        path = parsed.path
        
        # Verificar paths permitidos
        if domain_config.allowed_paths:
            allowed = any(
                path.startswith(allowed_path)
                for allowed_path in domain_config.allowed_paths
            )
            if not allowed:
                return False
        
        # Verificar paths bloqueados
        if domain_config.blocked_paths:
            blocked = any(
                path.startswith(blocked_path)
                for blocked_path in domain_config.blocked_paths
            )
            if blocked:
                return False
        
        return True
    
    def _matches_include_patterns(self, url: str) -> bool:
        """Verificar se URL corresponde aos padrões de inclusão."""
        # TODO: Implementar padrões de inclusão da configuração
        # Por enquanto, incluir todas as URLs HTML
        return url.endswith(('.html', '.htm', '/')) or '.' not in url.split('/')[-1]
    
    def _matches_exclude_patterns(self, url: str) -> bool:
        """Verificar se URL corresponde aos padrões de exclusão."""
        # Padrões comuns de exclusão
        exclude_patterns = [
            r'\.(pdf|jpg|jpeg|png|gif|css|js|ico)$',
            r'/search\?',
            r'/login',
            r'/admin',
            r'/api/',
        ]
        
        for pattern in exclude_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        
        return False
    
    def _calculate_priority(self, url: str, base_priority: int) -> int:
        """Calcular prioridade baseado em padrões de URL."""
        priority = base_priority
        
        # Padrões de alta prioridade
        high_priority_patterns = [
            r'/api/',
            r'/docs?/',
            r'/reference/',
            r'/tutorial/',
            r'/guide/',
        ]
        
        # Padrões de baixa prioridade
        low_priority_patterns = [
            r'/blog/',
            r'/news/',
            r'/archive/',
        ]
        
        for pattern in high_priority_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                priority += 3
                break
        
        for pattern in low_priority_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                priority = max(0, priority - 2)
                break
        
        return min(priority, 10)

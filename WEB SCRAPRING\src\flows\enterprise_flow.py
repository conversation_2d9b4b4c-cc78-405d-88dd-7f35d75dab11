"""
Enterprise Flow - Flow empresarial com todas as funcionalidades da Fase 2.

Este flow implementa coleta em escala com PostgreSQL, S3, métricas
Prometheus, alertas e coleta incremental.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import structlog
from prefect import flow, task
from sqlalchemy import select

from ..core.alerts import alert_manager
from ..core.config import get_settings
from ..core.database import get_db_session, init_database
from ..core.http_client import AsyncHTTPClient
from ..core.incremental import IncrementalCollector, perform_incremental_crawl
from ..core.metrics import metrics_collector, time_http_request, time_parsing, time_storage_operation
from ..core.models import CrawlSession, Domain, Page
from ..core.normalize import ContentNormalizer
from ..core.quality import AdvancedQualityScorer
from ..core.s3_storage import S3StorageBackend
from ..core.versioning import ContentVersionManager
from ..crawl.frontier import URLFrontier
from ..crawl.sitemap import SitemapDiscovery
from ..domains.generic import GenericParser

logger = structlog.get_logger(__name__)


@task(name="initialize_enterprise_systems")
async def initialize_enterprise_systems() -> Dict:
    """Inicializar todos os sistemas empresariais."""
    logger.info("Initializing enterprise systems")
    
    settings = get_settings()
    
    # Inicializar banco de dados
    await init_database()
    
    # Inicializar S3 storage se configurado
    s3_backend = None
    if settings.s3_endpoint_url:
        s3_backend = S3StorageBackend(
            endpoint_url=settings.s3_endpoint_url,
            access_key=settings.s3_access_key,
            secret_key=settings.s3_secret_key,
            bucket_name=settings.s3_bucket_name,
            region=settings.s3_region,
        )
        await s3_backend.initialize()
        logger.info("S3 storage initialized")
    
    # Configurar alertas se habilitado
    if settings.alerts_enabled:
        if settings.webhook_url:
            alert_manager.add_notification_channel("webhook", {
                "url": settings.webhook_url,
                "headers": {"Content-Type": "application/json"}
            })
        
        if settings.slack_webhook_url:
            alert_manager.add_notification_channel("slack", {
                "webhook_url": settings.slack_webhook_url
            })
        
        logger.info("Alert system configured")
    
    return {
        "database_initialized": True,
        "s3_enabled": s3_backend is not None,
        "alerts_enabled": settings.alerts_enabled,
        "metrics_enabled": settings.metrics_enabled,
    }


@task(name="create_crawl_session")
async def create_crawl_session(
    domain: str,
    session_type: str = "full",
    max_pages: int = 10000,
    dry_run: bool = False
) -> str:
    """Criar sessão de crawling no banco."""
    async with get_db_session() as session:
        # Buscar ou criar domínio
        domain_obj = await session.execute(
            select(Domain).where(Domain.name == domain)
        )
        domain_obj = domain_obj.scalar_one_or_none()
        
        if not domain_obj:
            domain_obj = Domain(
                name=domain,
                active=True,
                dynamic_content=False,  # Será detectado automaticamente
            )
            session.add(domain_obj)
            await session.flush()
        
        # Criar sessão de crawl
        crawl_session = CrawlSession(
            domain_id=domain_obj.id,
            session_type=session_type,
            max_pages=max_pages,
            dry_run=dry_run,
            status="running",
        )
        
        session.add(crawl_session)
        await session.commit()
        
        logger.info(
            "Crawl session created",
            session_id=str(crawl_session.id),
            domain=domain,
            type=session_type,
        )
        
        return str(crawl_session.id)


@task(name="discover_urls_enterprise")
async def discover_urls_enterprise(
    domain: str,
    session_id: str,
    max_urls: int = 10000,
    incremental: bool = False
) -> List[str]:
    """Descobrir URLs usando estratégias empresariais."""
    settings = get_settings()
    
    async with AsyncHTTPClient(settings) as http_client:
        if incremental and settings.incremental_enabled:
            # Usar coleta incremental
            logger.info("Using incremental URL discovery", domain=domain)
            
            async with get_db_session() as session:
                from ..core.incremental import SmartCrawlScheduler
                scheduler = SmartCrawlScheduler(settings)
                urls = await scheduler.get_urls_for_incremental_crawl(
                    session, domain, max_urls
                )
        else:
            # Descoberta tradicional via sitemap
            logger.info("Using traditional sitemap discovery", domain=domain)
            
            sitemap_discovery = SitemapDiscovery(http_client, settings)
            urls = await sitemap_discovery.discover_urls(domain, max_urls)
        
        # Atualizar métricas
        metrics_collector.set_queue_size(domain, "discovered", len(urls))
        
        # Atualizar sessão
        async with get_db_session() as session:
            crawl_session = await session.get(CrawlSession, session_id)
            if crawl_session:
                crawl_session.urls_discovered = len(urls)
                await session.commit()
        
        logger.info("URL discovery completed", domain=domain, urls_found=len(urls))
        return urls


@task(name="process_page_enterprise")
async def process_page_enterprise(
    url: str,
    session_id: str,
    use_incremental: bool = True
) -> Optional[Dict]:
    """Processar página com todas as funcionalidades empresariais."""
    settings = get_settings()
    domain = url.split("//")[1].split("/")[0]
    
    try:
        async with AsyncHTTPClient(settings) as http_client:
            async with get_db_session() as db_session:
                
                # Usar coleta incremental se habilitado
                if use_incremental and settings.incremental_enabled:
                    collector = IncrementalCollector(settings)
                    
                    async with time_http_request(metrics_collector, domain) as timing_ctx:
                        response, is_cache_hit = await collector.fetch_with_cache_headers(
                            http_client, url, db_session
                        )
                        
                        timing_ctx['status_code'] = response.status_code
                        timing_ctx['response_size'] = len(response.content)
                        timing_ctx['content_type'] = response.headers.content_type or "text/html"
                    
                    if is_cache_hit:
                        logger.debug("Cache hit - skipping processing", url=url)
                        return None
                else:
                    # Requisição tradicional
                    async with time_http_request(metrics_collector, domain) as timing_ctx:
                        response = await http_client.get(url)
                        
                        timing_ctx['status_code'] = response.status_code
                        timing_ctx['response_size'] = len(response.content)
                        timing_ctx['content_type'] = response.headers.content_type or "text/html"
                
                # Parse da página
                domain_config = settings.get_domain_config(domain)
                parser = GenericParser(domain_config, settings)
                
                async with time_parsing(metrics_collector, domain) as parsing_ctx:
                    page_data = await parser.parse(response)
                    
                    parsing_ctx['status'] = page_data.status.value
                    parsing_ctx['quality_score'] = page_data.quality_score
                    parsing_ctx['word_count'] = page_data.word_count
                
                # Normalização avançada
                normalizer = ContentNormalizer()
                normalized_data = normalizer.normalize_page_data(page_data, url)
                
                # Score de qualidade avançado
                quality_scorer = AdvancedQualityScorer()
                advanced_score, quality_metrics = quality_scorer.calculate_quality_score(
                    normalized_data, domain_config.quality
                )
                normalized_data.quality_score = advanced_score
                
                # Versionamento
                version_manager = ContentVersionManager()
                content_changed, semantic_changed, changes = version_manager.detect_changes(
                    url, normalized_data
                )
                
                if content_changed:
                    version = version_manager.create_version(url, normalized_data, changes)
                    
                    # Registrar métricas de versionamento
                    change_type = "major" if semantic_changed else "minor"
                    metrics_collector.record_content_version(domain, change_type)
                    metrics_collector.record_content_change(domain, change_type)
                
                # Storage em S3 se configurado
                s3_paths = {}
                if settings.s3_endpoint_url:
                    s3_backend = S3StorageBackend(
                        endpoint_url=settings.s3_endpoint_url,
                        access_key=settings.s3_access_key,
                        secret_key=settings.s3_secret_key,
                        bucket_name=settings.s3_bucket_name,
                    )
                    
                    async with time_storage_operation(metrics_collector, "save_raw", "s3"):
                        raw_path = await s3_backend.save_raw_content(
                            url, response.content, {
                                "status_code": response.status_code,
                                "content_type": response.headers.content_type,
                                "session_id": session_id,
                            }
                        )
                        s3_paths["raw"] = raw_path
                    
                    async with time_storage_operation(metrics_collector, "save_processed", "s3"):
                        processed_path = await s3_backend.save_processed_data(normalized_data)
                        s3_paths["processed"] = processed_path
                
                # Salvar no banco de dados
                await _save_page_to_database(
                    db_session, normalized_data, response, quality_metrics, s3_paths, session_id
                )
                
                logger.info(
                    "Page processed successfully",
                    url=url,
                    quality_score=advanced_score,
                    quality_tier=quality_metrics.quality_tier,
                    content_changed=content_changed,
                    s3_enabled=bool(s3_paths),
                )
                
                return {
                    "url": url,
                    "status": normalized_data.status.value,
                    "quality_score": advanced_score,
                    "quality_tier": quality_metrics.quality_tier,
                    "word_count": normalized_data.word_count,
                    "content_changed": content_changed,
                    "s3_paths": s3_paths,
                }
                
    except Exception as e:
        # Registrar erro nas métricas
        metrics_collector.record_error("page_processing", type(e).__name__, domain)
        
        logger.error("Failed to process page", url=url, error=str(e), exc_info=True)
        return None


async def _save_page_to_database(
    session,
    page_data,
    response,
    quality_metrics,
    s3_paths: Dict,
    session_id: str
) -> None:
    """Salvar página no banco de dados."""
    import hashlib
    
    url_hash = hashlib.sha256(str(page_data.url).encode()).hexdigest()
    
    # Buscar página existente
    existing_page = await session.execute(
        select(Page).where(Page.url_hash == url_hash)
    )
    existing_page = existing_page.scalar_one_or_none()
    
    if existing_page:
        # Atualizar página existente
        existing_page.title = page_data.title
        existing_page.content_hash = page_data.content_hash
        existing_page.word_count = page_data.word_count
        existing_page.quality_score = page_data.quality_score
        existing_page.quality_tier = quality_metrics.quality_tier
        existing_page.quality_confidence = quality_metrics.confidence
        existing_page.status = page_data.status.value
        existing_page.processed_at = datetime.utcnow()
        
        if s3_paths:
            existing_page.raw_storage_path = s3_paths.get("raw")
            existing_page.processed_storage_path = s3_paths.get("processed")
    else:
        # Criar nova página
        new_page = Page(
            url=str(page_data.url),
            url_hash=url_hash,
            title=page_data.title,
            content_hash=page_data.content_hash,
            word_count=page_data.word_count,
            quality_score=page_data.quality_score,
            quality_tier=quality_metrics.quality_tier,
            quality_confidence=quality_metrics.confidence,
            status=page_data.status.value,
            http_status_code=response.status_code,
            content_type=response.headers.content_type,
            raw_storage_path=s3_paths.get("raw"),
            processed_storage_path=s3_paths.get("processed"),
            fetched_at=response.fetched_at,
            processed_at=datetime.utcnow(),
        )
        session.add(new_page)
    
    await session.commit()


@task(name="finalize_crawl_session")
async def finalize_crawl_session(session_id: str, results: List[Dict]) -> Dict:
    """Finalizar sessão de crawling com estatísticas."""
    async with get_db_session() as session:
        crawl_session = await session.get(CrawlSession, session_id)
        
        if crawl_session:
            # Calcular estatísticas
            successful = [r for r in results if r and r.get("status") == "success"]
            failed = [r for r in results if r and r.get("status") == "failed"]
            
            total_quality = sum(r.get("quality_score", 0) for r in successful)
            avg_quality = total_quality / len(successful) if successful else 0
            
            high_quality = len([r for r in successful if r.get("quality_score", 0) >= 80])
            
            # Atualizar sessão
            crawl_session.status = "completed"
            crawl_session.completed_at = datetime.utcnow()
            crawl_session.urls_processed = len(results)
            crawl_session.urls_successful = len(successful)
            crawl_session.urls_failed = len(failed)
            crawl_session.avg_quality_score = avg_quality
            crawl_session.high_quality_pages = high_quality
            
            if crawl_session.started_at:
                duration = datetime.utcnow() - crawl_session.started_at
                crawl_session.duration_seconds = duration.total_seconds()
            
            await session.commit()
            
            logger.info(
                "Crawl session finalized",
                session_id=session_id,
                processed=len(results),
                successful=len(successful),
                failed=len(failed),
                avg_quality=round(avg_quality, 2),
            )
            
            return {
                "session_id": session_id,
                "status": "completed",
                "urls_processed": len(results),
                "urls_successful": len(successful),
                "urls_failed": len(failed),
                "avg_quality_score": round(avg_quality, 2),
                "high_quality_pages": high_quality,
                "duration_seconds": crawl_session.duration_seconds,
            }
    
    return {"error": "Session not found"}


@flow(name="enterprise-webscraper")
async def enterprise_webscraper_flow(
    domain: str,
    max_pages: int = 10000,
    incremental: bool = False,
    dry_run: bool = False
) -> Dict:
    """
    Flow empresarial completo com todas as funcionalidades da Fase 2.
    
    Args:
        domain: Domínio para fazer scraping
        max_pages: Número máximo de páginas
        incremental: Usar coleta incremental
        dry_run: Modo de teste (não salva dados)
    
    Returns:
        Estatísticas da execução
    """
    logger.info(
        "Starting enterprise webscraper flow",
        domain=domain,
        max_pages=max_pages,
        incremental=incremental,
        dry_run=dry_run,
    )
    
    # Inicializar sistemas
    init_result = await initialize_enterprise_systems()
    
    # Criar sessão de crawling
    session_type = "incremental" if incremental else "full"
    session_id = await create_crawl_session(domain, session_type, max_pages, dry_run)
    
    # Descobrir URLs
    urls = await discover_urls_enterprise(domain, session_id, max_pages, incremental)
    
    if not urls:
        logger.warning("No URLs discovered", domain=domain)
        return {
            "domain": domain,
            "status": "completed",
            "urls_discovered": 0,
            "urls_processed": 0,
            "message": "No URLs found to process"
        }
    
    # Processar páginas em paralelo (limitado)
    semaphore = asyncio.Semaphore(10)  # Máximo 10 páginas simultâneas
    
    async def process_with_semaphore(url):
        async with semaphore:
            return await process_page_enterprise(url, session_id, incremental)
    
    # Executar processamento
    tasks = [process_with_semaphore(url) for url in urls[:max_pages]]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Filtrar exceções
    valid_results = [r for r in results if not isinstance(r, Exception)]
    
    # Finalizar sessão
    final_stats = await finalize_crawl_session(session_id, valid_results)
    
    # Adicionar informações do sistema
    final_stats.update({
        "domain": domain,
        "incremental_mode": incremental,
        "dry_run": dry_run,
        "system_info": init_result,
        "urls_discovered": len(urls),
    })
    
    logger.info("Enterprise webscraper flow completed", stats=final_stats)
    return final_stats

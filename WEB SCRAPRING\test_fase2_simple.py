#!/usr/bin/env python3
"""
Teste Simplificado da Fase 2 - Escala & Observabilidade.

Este script testa as funcionalidades core da Fase 2 sem dependências
externas complexas.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.alerts import alert_manager, AlertRule, AlertSeverity
from src.core.config import get_settings
from src.core.database import db_manager, create_tables
from src.core.logging import configure_logging, get_logger
from src.core.metrics import metrics_collector, get_metrics


async def test_database_integration():
    """Testar integração com banco de dados."""
    logger = get_logger(__name__)
    logger.info("Testing database integration")
    
    try:
        # Inicializar banco
        await db_manager.initialize()
        
        # Criar tabelas
        await create_tables()
        
        # Testar health check
        health = await db_manager.health_check()
        logger.info("Database health check", **health)
        
        # Obter estatísticas
        stats = await db_manager.get_stats()
        logger.info("Database stats", **stats)
        
        assert health["status"] == "healthy"
        assert "engine_info" in stats
        
        return True
        
    except Exception as e:
        logger.error("Database test failed", error=str(e), exc_info=True)
        return False


async def test_metrics_system():
    """Testar sistema de métricas Prometheus."""
    logger = get_logger(__name__)
    logger.info("Testing metrics system")
    
    try:
        # Registrar algumas métricas de teste
        metrics_collector.record_http_request(
            domain="example.com",
            status_code=200,
            method="GET",
            duration=1.5,
            response_size=1024,
            content_type="text/html"
        )
        
        metrics_collector.record_page_parsed(
            domain="example.com",
            status="success",
            parser_type="generic",
            duration=0.5,
            quality_score=85,
            quality_tier="good",
            word_count=500
        )
        
        metrics_collector.record_storage_operation(
            operation="save",
            backend="s3",
            status="success",
            duration=0.2
        )
        
        metrics_collector.record_content_version("example.com", "minor")
        metrics_collector.record_error("parser", "timeout", "example.com")
        
        # Definir métricas de gauge
        metrics_collector.set_active_sessions("example.com", 1)
        metrics_collector.set_queue_size("example.com", "pending", 100)
        metrics_collector.set_database_connections("active", 5)
        
        # Obter métricas no formato Prometheus
        metrics_output = get_metrics()
        
        logger.info("Metrics generated", lines=len(metrics_output.split('\n')))
        
        # Verificar se contém métricas esperadas
        assert "webscraper_http_requests_total" in metrics_output
        assert "webscraper_pages_parsed_total" in metrics_output
        assert "webscraper_storage_operations_total" in metrics_output
        assert "webscraper_content_versions_created_total" in metrics_output
        assert "webscraper_errors_total" in metrics_output
        
        return True
        
    except Exception as e:
        logger.error("Metrics test failed", error=str(e), exc_info=True)
        return False


async def test_alert_system():
    """Testar sistema de alertas."""
    logger = get_logger(__name__)
    logger.info("Testing alert system")
    
    try:
        # Adicionar regra de teste
        test_rule = AlertRule(
            name="test_high_error_rate",
            condition="error_rate > threshold",
            threshold=5.0,  # 5% (baixo para teste)
            severity=AlertSeverity.WARNING,
            component="test",
            description="Test alert rule for high error rate",
        )
        
        alert_manager.add_rule(test_rule)
        
        # Adicionar canal de notificação de teste
        alert_manager.add_notification_channel("webhook", {
            "url": "http://localhost:8080/test-webhook",
            "headers": {"Content-Type": "application/json"}
        })
        
        # Simular algumas métricas que podem disparar alertas
        for i in range(10):
            # Simular requisições com alguns erros
            status_code = 500 if i < 2 else 200  # 20% de erro
            metrics_collector.record_http_request(
                domain="test.com",
                status_code=status_code,
                method="GET",
                duration=1.0,
                response_size=1024
            )
        
        # Avaliar regras (normalmente seria feito em loop)
        new_alerts = await alert_manager.evaluate_all_rules()
        
        logger.info("Alert evaluation completed", new_alerts=len(new_alerts))
        
        # Obter alertas ativos
        active_alerts = await alert_manager.get_active_alerts()
        logger.info("Active alerts", count=len(active_alerts))
        
        return True
        
    except Exception as e:
        logger.error("Alert system test failed", error=str(e), exc_info=True)
        return False


async def test_database_models():
    """Testar modelos do banco de dados."""
    logger = get_logger(__name__)
    logger.info("Testing database models")
    
    try:
        from src.core.models import Domain, Page, CrawlSession
        from src.core.database import get_db_session
        
        async with get_db_session() as session:
            # Criar domínio de teste
            test_domain = Domain(
                name="test.example.com",
                rate_limit_rps=2.0,
                max_pages=1000,
                dynamic_content=True,
                active=True
            )
            
            session.add(test_domain)
            await session.flush()  # Para obter o ID
            
            # Criar sessão de crawl
            crawl_session = CrawlSession(
                domain_id=test_domain.id,
                session_type="test",
                max_pages=100,
                dry_run=True,
                status="running"
            )
            
            session.add(crawl_session)
            await session.flush()
            
            # Criar página de teste
            test_page = Page(
                url="https://test.example.com/page1",
                url_hash="test_hash_123",
                domain_id=test_domain.id,
                title="Test Page",
                content_hash="content_hash_123",
                semantic_hash="semantic_hash_123",
                word_count=500,
                quality_score=85,
                quality_tier="good",
                status="success"
            )
            
            session.add(test_page)
            await session.commit()
            
            logger.info(
                "Database models test completed",
                domain_id=str(test_domain.id),
                session_id=str(crawl_session.id),
                page_id=str(test_page.id)
            )
            
            return True
            
    except Exception as e:
        logger.error("Database models test failed", error=str(e), exc_info=True)
        return False


async def test_versioning_system():
    """Testar sistema de versionamento."""
    logger = get_logger(__name__)
    logger.info("Testing versioning system")
    
    try:
        from src.core.versioning import ContentVersionManager
        from src.core.validators import PageData
        from datetime import datetime
        
        version_manager = ContentVersionManager()
        
        # Criar dados de página de teste
        page_data = PageData(
            url="https://test.com/page1",
            title="Test Page",
            text_content="This is test content for versioning.",
            word_count=7,
            quality_score=75,
            status="success",
            processed_at=datetime.utcnow()
        )
        
        # Primeira versão
        content_changed, semantic_changed, changes = version_manager.detect_changes(
            str(page_data.url), page_data
        )
        
        version = version_manager.create_version(str(page_data.url), page_data, changes)
        
        logger.info(
            "Version created",
            version_number=version.version_number,
            content_hash=version.content_hash[:16],
            changes=changes
        )
        
        # Segunda verificação (mesmo conteúdo)
        content_changed_2, semantic_changed_2, changes_2 = version_manager.detect_changes(
            str(page_data.url), page_data
        )
        
        logger.info(
            "Second version check",
            content_changed=content_changed_2,
            semantic_changed=semantic_changed_2
        )
        
        # Obter estatísticas
        stats = version_manager.get_stats()
        logger.info("Versioning stats", **stats)
        
        assert version.version_number == 1
        assert content_changed is True  # Primeira versão
        assert content_changed_2 is False  # Segunda vez, sem mudança
        
        return True
        
    except Exception as e:
        logger.error("Versioning system test failed", error=str(e), exc_info=True)
        return False


async def test_content_normalization():
    """Testar normalização de conteúdo."""
    logger = get_logger(__name__)
    logger.info("Testing content normalization")
    
    try:
        from src.core.normalize import ContentNormalizer
        from src.core.validators import PageData
        from datetime import datetime
        
        normalizer = ContentNormalizer()
        
        # Criar dados de página com conteúdo para normalizar
        page_data = PageData(
            url="https://test.com/page1",
            title="  Test   Page  ",  # Espaços extras
            text_content="This is test content with   extra   spaces and\n\nnewlines.",
            word_count=10,
            quality_score=60,
            status="success",
            processed_at=datetime.utcnow()
        )
        
        # Normalizar
        normalized = normalizer.normalize_page_data(page_data, str(page_data.url))
        
        # Obter indicadores de qualidade
        indicators = normalizer.get_content_quality_indicators(normalized)
        
        logger.info(
            "Content normalized",
            original_title=repr(page_data.title),
            normalized_title=repr(normalized.title),
            quality_indicators=indicators
        )
        
        # Verificações
        assert normalized.title == "Test Page"  # Espaços removidos
        assert "extra   spaces" not in normalized.text_content  # Espaços normalizados
        assert indicators["overall"] >= 0
        
        return True
        
    except Exception as e:
        logger.error("Content normalization test failed", error=str(e), exc_info=True)
        return False


async def main():
    """Função principal de teste da Fase 2 simplificado."""
    print("🏢 Testando Fase 2 - Escala & Observabilidade (Simplificado)...")
    
    # Configurar logging
    configure_logging(level="INFO", structured=True)
    
    tests = [
        ("Database Integration", test_database_integration),
        ("Metrics System", test_metrics_system),
        ("Alert System", test_alert_system),
        ("Database Models", test_database_models),
        ("Versioning System", test_versioning_system),
        ("Content Normalization", test_content_normalization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Executando: {test_name}")
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name}: PASSOU")
                results.append(True)
            else:
                print(f"❌ {test_name}: FALHOU")
                results.append(False)
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results.append(False)
    
    # Resumo final
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Resumo dos Testes da Fase 2:")
    print(f"✅ Passou: {passed}/{total}")
    print(f"❌ Falhou: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 Fase 2 implementada com sucesso!")
        return 0
    elif passed >= total * 0.8:  # 80% ou mais
        print("⚠️ Fase 2 parcialmente implementada com excelente cobertura!")
        return 0
    else:
        print("⚠️ Alguns testes falharam. Verifique os logs.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

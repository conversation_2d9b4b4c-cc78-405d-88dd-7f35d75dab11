"""
Testes HTTP usando VCR para requisições determinísticas.

Este módulo implementa testes que gravam e reproduzem requisições HTTP
para garantir testes determinísticos e rápidos.
"""

import pytest
import vcr
from pathlib import Path

from src.core.config import get_config
from src.core.http_client import AsyncHTTPClient
from src.crawl.sitemap import SitemapDiscovery


# Configurar VCR
def vcr_config():
    """Configuração padrão do VCR."""
    return vcr.VCR(
        cassette_library_dir='tests/cassettes',
        record_mode='once',  # Gravar apenas na primeira vez
        match_on=['method', 'scheme', 'host', 'port', 'path', 'query'],
        filter_headers=['authorization', 'user-agent'],
        decode_compressed_response=True,
    )


@pytest.fixture
def vcr_cassette_dir(tmp_path):
    """Diretório para cassettes VCR."""
    cassette_dir = tmp_path / "cassettes"
    cassette_dir.mkdir(exist_ok=True)
    return cassette_dir


class TestHTTPClientVCR:
    """Testes do cliente HTTP usando VCR."""
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/http_client_basic_request.yaml')
    async def test_basic_http_request(self):
        """Testar requisição HTTP básica."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            response = await client.get("https://httpbin.org/html")
            
            assert response.status_code == 200
            assert "text/html" in response.headers.content_type
            assert len(response.content) > 1000
            assert "Herman Melville" in response.content
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/http_client_json_response.yaml')
    async def test_json_response(self):
        """Testar resposta JSON."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            response = await client.get("https://httpbin.org/json")
            
            assert response.status_code == 200
            assert "application/json" in response.headers.content_type
            assert "slideshow" in response.content
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/http_client_rate_limiting.yaml')
    async def test_rate_limiting(self):
        """Testar rate limiting."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # Fazer múltiplas requisições para testar rate limiting
            responses = []
            
            for i in range(3):
                response = await client.get(f"https://httpbin.org/delay/1")
                responses.append(response)
            
            # Todas devem ser bem-sucedidas
            for response in responses:
                assert response.status_code == 200
            
            # Verificar que o rate limiting funcionou (duração total > soma dos delays)
            total_duration = sum(r.duration_ms for r in responses)
            assert total_duration > 3000  # Pelo menos 3 segundos devido aos delays
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/http_client_error_handling.yaml')
    async def test_error_handling(self):
        """Testar tratamento de erros."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # Testar 404
            response = await client.get("https://httpbin.org/status/404")
            assert response.status_code == 404
            
            # Testar 500
            response = await client.get("https://httpbin.org/status/500")
            assert response.status_code == 500
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/http_client_headers.yaml')
    async def test_custom_headers(self):
        """Testar headers customizados."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            response = await client.get("https://httpbin.org/headers")
            
            assert response.status_code == 200
            assert "WebScraper" in response.content  # User-Agent deve aparecer


class TestSitemapDiscoveryVCR:
    """Testes de descoberta de sitemap usando VCR."""
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/sitemap_discovery_basic.yaml')
    async def test_sitemap_discovery_basic(self):
        """Testar descoberta básica de sitemap."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            sitemap_discovery = SitemapDiscovery(client, config)
            
            # Testar com um domínio que tem sitemap
            urls = await sitemap_discovery.discover_urls("httpbin.org", max_urls=10)
            
            # httpbin.org pode não ter sitemap, mas o teste deve executar sem erro
            assert isinstance(urls, list)
            # Não assertamos URLs específicas pois httpbin.org pode não ter sitemap
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/sitemap_robots_txt.yaml')
    async def test_sitemap_from_robots_txt(self):
        """Testar descoberta de sitemap via robots.txt."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            sitemap_discovery = SitemapDiscovery(client, config)
            
            # Testar descoberta via robots.txt
            base_url = "https://httpbin.org"
            sitemaps = await sitemap_discovery._find_sitemaps_in_robots(base_url)
            
            # httpbin.org pode não ter sitemaps em robots.txt
            assert isinstance(sitemaps, list)
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/sitemap_xml_parsing.yaml')
    async def test_sitemap_xml_parsing(self):
        """Testar parsing de XML de sitemap."""
        config = get_config()
        
        # Criar um sitemap XML de teste
        test_sitemap_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url>
                <loc>https://example.com/page1</loc>
                <lastmod>2025-01-01</lastmod>
                <priority>0.8</priority>
            </url>
            <url>
                <loc>https://example.com/page2</loc>
                <lastmod>2025-01-02</lastmod>
                <priority>0.6</priority>
            </url>
        </urlset>"""
        
        async with AsyncHTTPClient(config) as client:
            sitemap_discovery = SitemapDiscovery(client, config)
            
            # Simular parsing de XML (método interno)
            import xml.etree.ElementTree as ET
            root = ET.fromstring(test_sitemap_xml)
            
            urls = await sitemap_discovery._process_url_sitemap(root, max_urls=10)
            
            assert len(urls) == 2
            assert str(urls[0].url) == "https://example.com/page1"
            assert str(urls[1].url) == "https://example.com/page2"
            assert urls[0].priority == 8  # 0.8 * 10
            assert urls[1].priority == 6  # 0.6 * 10


class TestIntegrationVCR:
    """Testes de integração usando VCR."""
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/integration_full_pipeline.yaml')
    async def test_full_scraping_pipeline(self):
        """Testar pipeline completo de scraping."""
        from src.domains.generic import GenericParser
        from src.core.quality import AdvancedQualityScorer
        from src.core.normalize import ContentNormalizer
        
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # 1. Fazer requisição HTTP
            response = await client.get("https://httpbin.org/html")
            
            # 2. Parse com parser genérico
            domain_config = config.get_domain_config("httpbin.org")
            parser = GenericParser(domain_config, config)
            page_data = await parser.parse(response)
            
            # 3. Normalização
            normalizer = ContentNormalizer()
            normalized_data = normalizer.normalize_page_data(page_data, str(response.url))
            
            # 4. Score de qualidade
            quality_scorer = AdvancedQualityScorer()
            score, metrics = quality_scorer.calculate_quality_score(
                normalized_data, domain_config.quality
            )
            
            # Verificações do pipeline completo
            assert response.status_code == 200
            assert normalized_data.title
            assert normalized_data.word_count > 0
            assert score > 0
            assert metrics.quality_tier in ["poor", "fair", "good", "excellent"]
            assert 0 <= metrics.confidence <= 1
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/integration_error_recovery.yaml')
    async def test_error_recovery_pipeline(self):
        """Testar recuperação de erros no pipeline."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # Testar com URL que retorna erro
            response = await client.get("https://httpbin.org/status/404")
            
            # Parser deve lidar com erro graciosamente
            domain_config = config.get_domain_config("httpbin.org")
            
            from src.domains.generic import GenericParser
            parser = GenericParser(domain_config, config)
            
            # Mesmo com 404, o parser deve retornar dados básicos
            page_data = await parser.parse(response)
            
            assert page_data.status.value in ["failed", "skipped"]
            assert len(page_data.errors) > 0


@pytest.mark.slow
class TestPerformanceVCR:
    """Testes de performance usando VCR."""
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/performance_concurrent_requests.yaml')
    async def test_concurrent_requests_performance(self):
        """Testar performance de requisições concorrentes."""
        import asyncio
        import time
        
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # Fazer múltiplas requisições concorrentes
            urls = [
                "https://httpbin.org/delay/1",
                "https://httpbin.org/html",
                "https://httpbin.org/json",
                "https://httpbin.org/xml",
            ]
            
            start_time = time.time()
            
            # Executar requisições concorrentemente
            tasks = [client.get(url) for url in urls]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Verificar que as requisições foram bem-sucedidas
            successful_responses = [r for r in responses if not isinstance(r, Exception)]
            assert len(successful_responses) >= 3  # Pelo menos 3 devem funcionar
            
            # Com concorrência, deve ser mais rápido que sequencial
            # (considerando que uma das URLs tem delay de 1 segundo)
            assert duration < 5  # Deve ser bem mais rápido que 4+ segundos sequenciais
    
    @pytest.mark.asyncio
    @vcr.use_cassette('tests/cassettes/performance_large_content.yaml')
    async def test_large_content_handling(self):
        """Testar handling de conteúdo grande."""
        config = get_config()
        
        async with AsyncHTTPClient(config) as client:
            # Requisitar conteúdo grande
            response = await client.get("https://httpbin.org/base64/SFRUUEJJTiBpcyBhd2Vzb21l")
            
            assert response.status_code == 200
            assert len(response.content) > 0
            assert response.duration_ms > 0


# Utilitários para testes VCR
def clean_vcr_cassettes():
    """Limpar cassettes VCR para regenerar."""
    cassette_dir = Path("tests/cassettes")
    if cassette_dir.exists():
        for cassette_file in cassette_dir.glob("*.yaml"):
            cassette_file.unlink()


if __name__ == "__main__":
    # Permitir limpeza manual de cassettes
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_vcr_cassettes()
        print("VCR cassettes cleaned")

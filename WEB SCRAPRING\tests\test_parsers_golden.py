"""
Testes de parsers usando golden files.

Este módulo implementa testes determinísticos usando snapshots de HTML
e validação rigorosa de parsers com golden files.
"""

import json
import pytest
from pathlib import Path
from typing import Dict, Any

from src.core.validators import DomainConfig, HTTPResponse, ScrapingConfig
from src.domains.generic import GenericParser
from src.core.quality import AdvancedQualityScorer
from src.core.normalize import ContentNormalizer


class GoldenFileManager:
    """Gerenciador de golden files para testes."""
    
    def __init__(self, test_data_dir: Path):
        self.test_data_dir = test_data_dir
        self.html_dir = test_data_dir / "html"
        self.expected_dir = test_data_dir / "expected"
        
        # Criar diretórios se não existirem
        self.html_dir.mkdir(parents=True, exist_ok=True)
        self.expected_dir.mkdir(parents=True, exist_ok=True)
    
    def save_html_snapshot(self, test_name: str, html_content: str) -> Path:
        """Salvar snapshot de HTML."""
        file_path = self.html_dir / f"{test_name}.html"
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        return file_path
    
    def load_html_snapshot(self, test_name: str) -> str:
        """Carregar snapshot de HTML."""
        file_path = self.html_dir / f"{test_name}.html"
        if not file_path.exists():
            raise FileNotFoundError(f"HTML snapshot not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def save_expected_result(self, test_name: str, result: Dict[str, Any]) -> Path:
        """Salvar resultado esperado."""
        file_path = self.expected_dir / f"{test_name}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        return file_path
    
    def load_expected_result(self, test_name: str) -> Dict[str, Any]:
        """Carregar resultado esperado."""
        file_path = self.expected_dir / f"{test_name}.json"
        if not file_path.exists():
            raise FileNotFoundError(f"Expected result not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def has_golden_files(self, test_name: str) -> bool:
        """Verificar se existem golden files para um teste."""
        html_path = self.html_dir / f"{test_name}.html"
        expected_path = self.expected_dir / f"{test_name}.json"
        return html_path.exists() and expected_path.exists()


@pytest.fixture
def golden_manager(tmp_path):
    """Fixture para gerenciador de golden files."""
    return GoldenFileManager(tmp_path / "golden")


@pytest.fixture
def sample_html_files():
    """Fixture com exemplos de HTML para testes."""
    return {
        "simple_article": """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Simple Article Example</title>
            <meta charset="utf-8">
        </head>
        <body>
            <nav>
                <a href="/home">Home</a>
                <a href="/about">About</a>
            </nav>
            
            <main>
                <h1>Understanding Web Scraping</h1>
                <h2>Introduction</h2>
                <p>Web scraping is the process of extracting data from websites. 
                It involves making HTTP requests to web pages and parsing the HTML content 
                to extract useful information.</p>
                
                <h2>Basic Techniques</h2>
                <p>There are several approaches to web scraping:</p>
                <ul>
                    <li>HTTP requests with parsing libraries</li>
                    <li>Browser automation tools</li>
                    <li>API integration when available</li>
                </ul>
                
                <h3>Code Example</h3>
                <pre><code class="python">
import requests
from bs4 import BeautifulSoup

response = requests.get('https://example.com')
soup = BeautifulSoup(response.content, 'html.parser')
title = soup.find('h1').text
                </code></pre>
                
                <h2>Best Practices</h2>
                <p>When scraping websites, it's important to:</p>
                <ol>
                    <li>Respect robots.txt</li>
                    <li>Implement rate limiting</li>
                    <li>Handle errors gracefully</li>
                </ol>
                
                <table>
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Pros</th>
                            <th>Cons</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>HTTP + Parsing</td>
                            <td>Fast, lightweight</td>
                            <td>No JavaScript support</td>
                        </tr>
                        <tr>
                            <td>Browser Automation</td>
                            <td>Full JavaScript support</td>
                            <td>Slower, resource intensive</td>
                        </tr>
                    </tbody>
                </table>
            </main>
            
            <footer>
                <p>&copy; 2025 Example Site. All rights reserved.</p>
            </footer>
        </body>
        </html>
        """,
        
        "documentation_page": """
        <!DOCTYPE html>
        <html>
        <head>
            <title>API Documentation - WebScraper</title>
        </head>
        <body>
            <header>
                <h1>WebScraper API Documentation</h1>
            </header>
            
            <nav class="sidebar">
                <ul>
                    <li><a href="#overview">Overview</a></li>
                    <li><a href="#authentication">Authentication</a></li>
                    <li><a href="#endpoints">Endpoints</a></li>
                </ul>
            </nav>
            
            <main class="content">
                <section id="overview">
                    <h2>Overview</h2>
                    <p>The WebScraper API provides programmatic access to web scraping functionality.</p>
                </section>
                
                <section id="authentication">
                    <h2>Authentication</h2>
                    <p>All API requests require authentication using API keys.</p>
                    
                    <h3>Example Request</h3>
                    <pre><code class="bash">
curl -H "Authorization: Bearer YOUR_API_KEY" \\
     https://api.webscraper.com/v1/scrape
                    </code></pre>
                </section>
                
                <section id="endpoints">
                    <h2>API Endpoints</h2>
                    
                    <h3>POST /v1/scrape</h3>
                    <p>Initiate a scraping job.</p>
                    
                    <h4>Parameters</h4>
                    <table>
                        <tr><th>Parameter</th><th>Type</th><th>Description</th></tr>
                        <tr><td>url</td><td>string</td><td>Target URL to scrape</td></tr>
                        <tr><td>format</td><td>string</td><td>Output format (json, xml)</td></tr>
                    </table>
                </section>
            </main>
        </body>
        </html>
        """,
        
        "low_quality_page": """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Page</title>
        </head>
        <body>
            <div>
                <p>Short content.</p>
                <p>Click here for more.</p>
                <p>Follow us on social media.</p>
            </div>
        </body>
        </html>
        """
    }


class TestGenericParserGolden:
    """Testes do parser genérico usando golden files."""
    
    @pytest.mark.asyncio
    async def test_simple_article_parsing(self, golden_manager, sample_html_files):
        """Testar parsing de artigo simples."""
        test_name = "simple_article"
        html_content = sample_html_files[test_name]
        
        # Configurar parser
        domain_config = DomainConfig()
        scraping_config = ScrapingConfig()
        parser = GenericParser(domain_config, scraping_config)
        
        # Criar response mock
        response = HTTPResponse(
            url="https://example.com/article",
            status_code=200,
            content=html_content,
        )
        
        # Fazer parsing
        page_data = await parser.parse(response)
        
        # Extrair dados relevantes para comparação
        result = {
            "title": page_data.title,
            "word_count": page_data.word_count,
            "headings_count": len(page_data.headings_tree),
            "code_blocks_count": len(page_data.code_blocks),
            "tables_count": len(page_data.tables),
            "quality_score": page_data.quality_score,
            "status": page_data.status.value,
            "has_content": len(page_data.text_content) > 100,
        }
        
        # Verificar se existem golden files
        if golden_manager.has_golden_files(test_name):
            # Comparar com resultado esperado
            expected = golden_manager.load_expected_result(test_name)
            
            # Verificações específicas
            assert result["title"] == expected["title"]
            assert result["headings_count"] == expected["headings_count"]
            assert result["code_blocks_count"] == expected["code_blocks_count"]
            assert result["tables_count"] == expected["tables_count"]
            assert result["status"] == expected["status"]
            
            # Tolerância para word_count e quality_score
            assert abs(result["word_count"] - expected["word_count"]) <= 5
            assert abs(result["quality_score"] - expected["quality_score"]) <= 10
            
        else:
            # Criar golden files na primeira execução
            golden_manager.save_html_snapshot(test_name, html_content)
            golden_manager.save_expected_result(test_name, result)
            
            # Verificações básicas para primeira execução
            assert result["title"] == "Understanding Web Scraping"
            assert result["headings_count"] >= 4
            assert result["code_blocks_count"] >= 1
            assert result["tables_count"] >= 1
            assert result["status"] == "success"
            assert result["has_content"] is True
    
    @pytest.mark.asyncio
    async def test_documentation_page_parsing(self, golden_manager, sample_html_files):
        """Testar parsing de página de documentação."""
        test_name = "documentation_page"
        html_content = sample_html_files[test_name]
        
        # Configurar parser
        domain_config = DomainConfig()
        scraping_config = ScrapingConfig()
        parser = GenericParser(domain_config, scraping_config)
        
        # Criar response mock
        response = HTTPResponse(
            url="https://api.example.com/docs",
            status_code=200,
            content=html_content,
        )
        
        # Fazer parsing
        page_data = await parser.parse(response)
        
        # Extrair dados relevantes
        result = {
            "title": page_data.title,
            "word_count": page_data.word_count,
            "headings_count": len(page_data.headings_tree),
            "code_blocks_count": len(page_data.code_blocks),
            "tables_count": len(page_data.tables),
            "quality_score": page_data.quality_score,
            "status": page_data.status.value,
        }
        
        # Verificar ou criar golden files
        if golden_manager.has_golden_files(test_name):
            expected = golden_manager.load_expected_result(test_name)
            
            assert result["title"] == expected["title"]
            assert result["headings_count"] == expected["headings_count"]
            assert result["code_blocks_count"] == expected["code_blocks_count"]
            assert result["tables_count"] == expected["tables_count"]
            assert result["status"] == expected["status"]
            
        else:
            golden_manager.save_html_snapshot(test_name, html_content)
            golden_manager.save_expected_result(test_name, result)
            
            # Verificações para documentação
            assert "API Documentation" in result["title"]
            assert result["headings_count"] >= 5
            assert result["code_blocks_count"] >= 1
            assert result["tables_count"] >= 1
            assert result["status"] == "success"
    
    @pytest.mark.asyncio
    async def test_low_quality_page_detection(self, golden_manager, sample_html_files):
        """Testar detecção de página de baixa qualidade."""
        test_name = "low_quality_page"
        html_content = sample_html_files[test_name]
        
        # Configurar parser
        domain_config = DomainConfig()
        scraping_config = ScrapingConfig()
        parser = GenericParser(domain_config, scraping_config)
        
        # Criar response mock
        response = HTTPResponse(
            url="https://example.com/low-quality",
            status_code=200,
            content=html_content,
        )
        
        # Fazer parsing
        page_data = await parser.parse(response)
        
        # Extrair dados relevantes
        result = {
            "title": page_data.title,
            "word_count": page_data.word_count,
            "quality_score": page_data.quality_score,
            "status": page_data.status.value,
            "errors": page_data.errors,
        }
        
        # Verificar ou criar golden files
        if golden_manager.has_golden_files(test_name):
            expected = golden_manager.load_expected_result(test_name)
            
            assert result["status"] == expected["status"]
            assert result["quality_score"] <= expected["quality_score"] + 10
            
        else:
            golden_manager.save_html_snapshot(test_name, html_content)
            golden_manager.save_expected_result(test_name, result)
            
            # Verificações para baixa qualidade
            assert result["word_count"] < 50
            assert result["quality_score"] < 50
            assert result["status"] == "failed"
            assert any("Low quality score" in error for error in result["errors"])


class TestQualityScorerGolden:
    """Testes do sistema de qualidade usando golden files."""
    
    @pytest.mark.asyncio
    async def test_quality_scoring_consistency(self, golden_manager, sample_html_files):
        """Testar consistência do sistema de qualidade."""
        test_name = "quality_scoring"
        
        # Configurar componentes
        domain_config = DomainConfig()
        scraping_config = ScrapingConfig()
        parser = GenericParser(domain_config, scraping_config)
        quality_scorer = AdvancedQualityScorer()
        normalizer = ContentNormalizer()
        
        results = {}
        
        # Testar cada tipo de página
        for page_type, html_content in sample_html_files.items():
            response = HTTPResponse(
                url=f"https://example.com/{page_type}",
                status_code=200,
                content=html_content,
            )
            
            # Parse e normalização
            page_data = await parser.parse(response)
            page_data = normalizer.normalize_page_data(page_data, str(response.url))
            
            # Score de qualidade avançado
            score, metrics = quality_scorer.calculate_quality_score(page_data, domain_config.quality)
            
            results[page_type] = {
                "overall_score": score,
                "quality_tier": metrics.quality_tier,
                "confidence": metrics.confidence,
                "content_length_score": metrics.content_length_score,
                "readability_score": metrics.readability_score,
                "information_density_score": metrics.information_density_score,
            }
        
        # Verificar ou criar golden files
        if golden_manager.has_golden_files(test_name):
            expected = golden_manager.load_expected_result(test_name)
            
            # Verificar consistência dos scores
            for page_type, result in results.items():
                expected_result = expected[page_type]
                
                # Tolerância para scores
                assert abs(result["overall_score"] - expected_result["overall_score"]) <= 5
                assert result["quality_tier"] == expected_result["quality_tier"]
                assert abs(result["confidence"] - expected_result["confidence"]) <= 0.1
                
        else:
            golden_manager.save_expected_result(test_name, results)
            
            # Verificações básicas
            assert results["simple_article"]["quality_tier"] in ["good", "excellent"]
            assert results["documentation_page"]["quality_tier"] in ["good", "excellent"]
            assert results["low_quality_page"]["quality_tier"] in ["poor", "fair"]


@pytest.mark.integration
class TestParserIntegrationGolden:
    """Testes de integração usando golden files."""
    
    @pytest.mark.asyncio
    async def test_full_pipeline_consistency(self, golden_manager, sample_html_files):
        """Testar consistência do pipeline completo."""
        test_name = "full_pipeline"
        
        # Usar página de artigo para teste completo
        html_content = sample_html_files["simple_article"]
        
        # Configurar pipeline completo
        domain_config = DomainConfig()
        scraping_config = ScrapingConfig()
        parser = GenericParser(domain_config, scraping_config)
        quality_scorer = AdvancedQualityScorer()
        normalizer = ContentNormalizer()
        
        # Executar pipeline
        response = HTTPResponse(
            url="https://example.com/test-article",
            status_code=200,
            content=html_content,
        )
        
        # 1. Parse inicial
        page_data = await parser.parse(response)
        
        # 2. Normalização
        normalized_data = normalizer.normalize_page_data(page_data, str(response.url))
        
        # 3. Score de qualidade avançado
        final_score, detailed_metrics = quality_scorer.calculate_quality_score(
            normalized_data, domain_config.quality
        )
        
        # 4. Indicadores de qualidade do conteúdo
        quality_indicators = normalizer.get_content_quality_indicators(normalized_data)
        
        # Compilar resultado final
        result = {
            "parsing": {
                "title": normalized_data.title,
                "word_count": normalized_data.word_count,
                "headings_count": len(normalized_data.headings_tree),
                "code_blocks_count": len(normalized_data.code_blocks),
                "tables_count": len(normalized_data.tables),
                "internal_links_count": len(normalized_data.internal_links),
                "external_links_count": len(normalized_data.external_links),
            },
            "quality": {
                "overall_score": final_score,
                "quality_tier": detailed_metrics.quality_tier,
                "confidence": detailed_metrics.confidence,
                "content_length_score": detailed_metrics.content_length_score,
                "readability_score": detailed_metrics.readability_score,
            },
            "content_indicators": quality_indicators,
            "status": normalized_data.status.value,
        }
        
        # Verificar ou criar golden files
        if golden_manager.has_golden_files(test_name):
            expected = golden_manager.load_expected_result(test_name)
            
            # Verificações detalhadas
            parsing_expected = expected["parsing"]
            parsing_result = result["parsing"]
            
            assert parsing_result["title"] == parsing_expected["title"]
            assert abs(parsing_result["word_count"] - parsing_expected["word_count"]) <= 10
            assert parsing_result["headings_count"] == parsing_expected["headings_count"]
            assert parsing_result["code_blocks_count"] == parsing_expected["code_blocks_count"]
            assert parsing_result["tables_count"] == parsing_expected["tables_count"]
            
            # Verificações de qualidade
            quality_expected = expected["quality"]
            quality_result = result["quality"]
            
            assert abs(quality_result["overall_score"] - quality_expected["overall_score"]) <= 5
            assert quality_result["quality_tier"] == quality_expected["quality_tier"]
            assert abs(quality_result["confidence"] - quality_expected["confidence"]) <= 0.1
            
        else:
            golden_manager.save_html_snapshot(test_name, html_content)
            golden_manager.save_expected_result(test_name, result)
            
            # Verificações básicas para primeira execução
            assert result["parsing"]["title"] == "Understanding Web Scraping"
            assert result["parsing"]["word_count"] > 100
            assert result["parsing"]["headings_count"] >= 4
            assert result["quality"]["overall_score"] >= 60
            assert result["quality"]["quality_tier"] in ["good", "excellent"]
            assert result["status"] == "success"

#!/usr/bin/env python3
"""
🕷️ ENHANCED MULTI-SITES SCRAPER - Scraping Avançado com Parsers Específicos

Este script faz scraping avançado dos sites com parsers específicos para cada plataforma:
- dentrodahistoria.com.br/clube - Parser para livros personalizados
- clubefundamento.com.br - Parser para clube de livros
- meutibi.com.br - Parser para livros infantis
- storyspark.ai/pt - Parser para plataforma de criação

Versão melhorada do multi_sites_scraper.py com extração específica de dados.
"""

import asyncio
import os
import sys
import json
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from pathlib import Path
from datetime import datetime
import re

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class EnhancedMultiSitesScraper:
    """Scraper avançado com parsers específicos para cada site."""
    
    def __init__(self, output_dir="enhanced_multi_sites_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuração da sessão
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Sites para fazer scraping
        self.sites_config = {
            'dentrodahistoria': {
                'url': 'https://www.dentrodahistoria.com.br/clube/',
                'name': 'Clube Dentro da História',
                'type': 'livros_personalizados',
                'framework': 'nuxt',
                'parser': 'parse_dentrodahistoria'
            },
            'clubefundamento': {
                'url': 'https://www.clubefundamento.com.br/',
                'name': 'Clube Fundamento',
                'type': 'clube_livros',
                'framework': 'svelte',
                'parser': 'parse_clubefundamento'
            },
            'meutibi': {
                'url': 'https://meutibi.com.br/',
                'name': 'Tibi - Livros Infantis',
                'type': 'livros_infantis',
                'framework': 'aspnet',
                'parser': 'parse_meutibi'
            },
            'storyspark': {
                'url': 'https://storyspark.ai/pt',
                'name': 'Story Spark - Criação de Histórias',
                'type': 'criacao_historias',
                'framework': 'nextjs',
                'parser': 'parse_storyspark'
            }
        }
        
        # Dados coletados
        self.scraped_data = {}
    
    def scrape_all_sites(self):
        """Fazer scraping de todos os sites configurados."""
        print("🕷️ ENHANCED MULTI-SITES SCRAPER - Iniciando scraping avançado")
        print("="*70)
        
        for site_key, config in self.sites_config.items():
            print(f"\n📄 Processando: {config['name']}")
            print(f"🔗 URL: {config['url']}")
            print(f"🔧 Parser: {config['parser']}")
            
            try:
                site_data = self.scrape_site(site_key, config)
                self.scraped_data[site_key] = site_data
                
                # Salvar dados individuais
                self.save_site_data(site_key, site_data)
                
                print(f"✅ {config['name']} processado com sucesso!")
                print(f"   📊 Produtos encontrados: {len(site_data.get('products', []))}")
                print(f"   💰 Preços encontrados: {len(site_data.get('prices', []))}")
                
                # Delay entre sites
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ Erro ao processar {config['name']}: {e}")
                self.scraped_data[site_key] = {'error': str(e)}
        
        # Salvar dados consolidados
        self.save_consolidated_data()
        self.generate_enhanced_analysis_report()
        
        print(f"\n🎉 SCRAPING AVANÇADO CONCLUÍDO!")
        print(f"📁 Resultados salvos em: {self.output_dir}")
    
    def scrape_site(self, site_key, config):
        """Fazer scraping de um site específico."""
        url = config['url']
        
        # Fazer requisição
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extrair dados básicos
        site_data = {
            'url': url,
            'name': config['name'],
            'type': config['type'],
            'framework': config['framework'],
            'scraped_at': datetime.now().isoformat(),
            'status_code': response.status_code,
            'content_length': len(response.content),
            'title': self.extract_title(soup),
            'meta_description': self.extract_meta_description(soup),
            'meta_keywords': self.extract_meta_keywords(soup),
            'headings': self.extract_headings(soup),
            'links': self.extract_links(soup, url),
            'images': self.extract_images(soup, url),
            'technology_stack': self.detect_technology(soup, response),
        }
        
        # Aplicar parser específico
        parser_method = getattr(self, config['parser'])
        specific_data = parser_method(soup, url)
        site_data.update(specific_data)
        
        # Salvar HTML
        html_dir = self.output_dir / site_key / "html"
        html_dir.mkdir(parents=True, exist_ok=True)
        
        with open(html_dir / "original.html", 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        return site_data
    
    def parse_dentrodahistoria(self, soup, url):
        """Parser específico para Dentro da História."""
        data = {
            'products': [],
            'prices': [],
            'features': [],
            'subscription_info': {},
            'target_audience': []
        }
        
        # Extrair informações de produtos/kits
        # O site usa estrutura Nuxt.js com dados dinâmicos
        text_content = soup.get_text()
        
        # Procurar por informações de preços
        price_patterns = [
            r'R\$\s*(\d+[,.]?\d*)',
            r'(\d+[,.]?\d*)\s*reais?',
            r'por\s*R\$\s*(\d+[,.]?\d*)'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if match and float(match.replace(',', '.')) > 10:  # Filtrar preços válidos
                    data['prices'].append({
                        'value': match,
                        'formatted': f"R$ {match}"
                    })
        
        # Extrair features/benefícios
        feature_keywords = ['personalizado', 'criança', 'história', 'livro', 'kit', 'educativo']
        for keyword in feature_keywords:
            count = text_content.lower().count(keyword)
            if count > 0:
                data['features'].append({
                    'keyword': keyword,
                    'mentions': count
                })
        
        # Informações de assinatura
        subscription_keywords = ['mensal', 'trimestral', 'semestral', 'anual', 'assinatura']
        for keyword in subscription_keywords:
            if keyword in text_content.lower():
                data['subscription_info'][keyword] = text_content.lower().count(keyword)
        
        # Público-alvo
        age_patterns = [
            r'(\d+)\s*a\s*(\d+)\s*anos?',
            r'(\d+)\s*anos?',
            r'crianças?\s*de\s*(\d+)'
        ]
        
        for pattern in age_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    data['target_audience'].append(f"{match[0]}-{match[1]} anos")
                else:
                    data['target_audience'].append(f"{match} anos")
        
        return data
    
    def parse_clubefundamento(self, soup, url):
        """Parser específico para Clube Fundamento."""
        data = {
            'products': [],
            'prices': [],
            'plans': [],
            'book_info': [],
            'statistics': {}
        }
        
        text_content = soup.get_text()
        
        # Extrair estatísticas do site
        stats_patterns = [
            (r'(\d+)\s*milhões?\s*de\s*livros', 'livros_vendidos'),
            (r'(\d+)\s*anos', 'anos_mercado'),
            (r'(\d+)%\s*de\s*aprovação', 'aprovacao'),
            (r'(\d+[,.]\d+)\s*estrelas', 'avaliacao')
        ]
        
        for pattern, key in stats_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                data['statistics'][key] = matches[0]
        
        # Extrair informações de planos
        plan_keywords = ['plano completo', 'plano duplo', 'plano simples', 'mensal', 'semestral', 'anual']
        for keyword in plan_keywords:
            if keyword in text_content.lower():
                data['plans'].append({
                    'type': keyword,
                    'mentions': text_content.lower().count(keyword)
                })
        
        # Extrair preços
        price_patterns = [
            r'R\$\s*(\d+[,.]?\d*)',
            r'de\s*R\$\s*(\d+[,.]?\d*)\s*por\s*R\$\s*(\d+[,.]?\d*)'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    data['prices'].append({
                        'original': match[0],
                        'promotional': match[1],
                        'type': 'promotional'
                    })
                else:
                    data['prices'].append({
                        'value': match,
                        'type': 'regular'
                    })
        
        # Extrair informações de livros
        book_patterns = [
            r'([A-Z][a-zA-Z\s]+)\s*•\s*([^•]+)',
            r'(\w+\s*\w*)\s*Idade:\s*(\d+[,-]\d*\s*anos?)'
        ]
        
        for pattern in book_patterns:
            matches = re.findall(pattern, text_content)
            for match in matches:
                if len(match) == 2:
                    data['book_info'].append({
                        'title': match[0].strip(),
                        'description': match[1].strip()
                    })
        
        return data

    def parse_meutibi(self, soup, url):
        """Parser específico para Meu Tibi."""
        data = {
            'products': [],
            'prices': [],
            'categories': [],
            'age_groups': [],
            'book_details': []
        }

        text_content = soup.get_text()

        # Extrair produtos específicos
        product_selectors = [
            '.card-top-livros',
            '.livro-item',
            '.produto',
            '[class*="book"]',
            '[class*="livro"]'
        ]

        for selector in product_selectors:
            products = soup.select(selector)
            for product in products:
                product_text = product.get_text().strip()
                if product_text and len(product_text) > 10:
                    # Extrair título do produto
                    title_elem = product.find(['h1', 'h2', 'h3', 'h4', '.title', '.nome'])
                    title = title_elem.get_text().strip() if title_elem else product_text[:50]

                    # Extrair preço do produto
                    price_elem = product.find(['.price', '.valor', '.preco', '[class*="price"]'])
                    price = price_elem.get_text().strip() if price_elem else ''

                    # Extrair imagem do produto
                    img_elem = product.find('img')
                    image_url = img_elem.get('src') if img_elem else ''

                    data['products'].append({
                        'title': title,
                        'price': price,
                        'image': urljoin(url, image_url) if image_url else '',
                        'description': product_text[:200]
                    })

        # Extrair categorias por idade
        age_categories = ['primeiros livros', 'leitores curiosos', 'amo ler']
        for category in age_categories:
            if category in text_content.lower():
                data['age_groups'].append({
                    'category': category,
                    'mentions': text_content.lower().count(category)
                })

        # Extrair preços gerais
        price_patterns = [
            r'R\$\s*(\d+[,.]?\d*)',
            r'(\d+[,.]?\d*)\s*reais?'
        ]

        for pattern in price_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                try:
                    price_value = float(match.replace(',', '.'))
                    if 10 <= price_value <= 500:  # Filtrar preços válidos para livros
                        data['prices'].append({
                            'value': match,
                            'formatted': f"R$ {match}",
                            'numeric': price_value
                        })
                except ValueError:
                    continue

        return data

    def parse_storyspark(self, soup, url):
        """Parser específico para Story Spark."""
        data = {
            'features': [],
            'pricing': [],
            'ai_capabilities': [],
            'target_audience': [],
            'demo_info': []
        }

        text_content = soup.get_text()

        # Extrair recursos de IA
        ai_keywords = [
            'inteligência artificial', 'ia', 'ai', 'machine learning',
            'gerar histórias', 'criar histórias', 'automático', 'personalizado'
        ]

        for keyword in ai_keywords:
            count = text_content.lower().count(keyword)
            if count > 0:
                data['ai_capabilities'].append({
                    'feature': keyword,
                    'mentions': count
                })

        # Extrair features principais
        feature_selectors = [
            '.feature',
            '.card',
            '.benefit',
            '[class*="feature"]',
            '[class*="benefit"]'
        ]

        for selector in feature_selectors:
            features = soup.select(selector)
            for feature in features:
                feature_text = feature.get_text().strip()
                if feature_text and len(feature_text) > 10:
                    data['features'].append({
                        'title': feature_text[:100],
                        'description': feature_text
                    })

        # Extrair informações de preços/planos
        pricing_keywords = ['grátis', 'free', 'premium', 'pro', 'básico', 'avançado']
        for keyword in pricing_keywords:
            if keyword in text_content.lower():
                data['pricing'].append({
                    'plan': keyword,
                    'mentions': text_content.lower().count(keyword)
                })

        # Extrair público-alvo
        target_patterns = [
            r'para\s+(crianças?|pais?|professores?|educadores?)',
            r'(crianças?|pais?|professores?|educadores?)\s+podem?',
            r'ideal\s+para\s+(crianças?|pais?|professores?|educadores?)'
        ]

        for pattern in target_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                data['target_audience'].append(match)

        # Extrair informações de demo/teste
        demo_keywords = ['demo', 'teste', 'experimentar', 'grátis', 'trial']
        for keyword in demo_keywords:
            if keyword in text_content.lower():
                data['demo_info'].append({
                    'type': keyword,
                    'mentions': text_content.lower().count(keyword)
                })

        return data

    # Métodos auxiliares (reutilizados do scraper anterior)
    def extract_title(self, soup):
        """Extrair título da página."""
        title_tag = soup.find('title')
        return title_tag.get_text().strip() if title_tag else ''

    def extract_meta_description(self, soup):
        """Extrair meta description."""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '') if meta_desc else ''

    def extract_meta_keywords(self, soup):
        """Extrair meta keywords."""
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        return meta_keywords.get('content', '') if meta_keywords else ''

    def extract_headings(self, soup):
        """Extrair headings (h1-h6)."""
        headings = {}
        for level in range(1, 7):
            headings[f'h{level}'] = [h.get_text().strip() for h in soup.find_all(f'h{level}')]
        return headings

    def extract_links(self, soup, base_url):
        """Extrair todos os links."""
        links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                links.append({
                    'url': full_url,
                    'text': link.get_text().strip(),
                    'title': link.get('title', ''),
                    'classes': link.get('class', [])
                })
        return links[:50]  # Limitar para evitar dados excessivos

    def extract_images(self, soup, base_url):
        """Extrair informações das imagens."""
        images = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                full_url = urljoin(base_url, src)
                images.append({
                    'url': full_url,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', ''),
                    'classes': img.get('class', [])
                })
        return images[:30]  # Limitar para evitar dados excessivos

    def detect_technology(self, soup, response):
        """Detectar tecnologias utilizadas."""
        tech_stack = {
            'frameworks': [],
            'libraries': [],
            'analytics': [],
            'other': []
        }

        # Verificar headers
        headers = response.headers
        if 'server' in headers:
            tech_stack['server'] = headers['server']

        # Verificar scripts e meta tags
        scripts = soup.find_all('script')
        for script in scripts:
            src = script.get('src', '')
            content = script.get_text()

            # Detectar frameworks/bibliotecas
            frameworks = {
                'react': 'React',
                'vue': 'Vue.js',
                'angular': 'Angular',
                'nuxt': 'Nuxt.js',
                'next': 'Next.js',
                'svelte': 'Svelte'
            }

            for key, name in frameworks.items():
                if key in src.lower() or key in content.lower():
                    if name not in tech_stack['frameworks']:
                        tech_stack['frameworks'].append(name)

            # Detectar analytics
            if 'google-analytics' in src or 'gtag' in content:
                tech_stack['analytics'].append('Google Analytics')
            if 'gtm' in src or 'googletagmanager' in content:
                tech_stack['analytics'].append('Google Tag Manager')

        return tech_stack

    def save_site_data(self, site_key, site_data):
        """Salvar dados de um site específico."""
        site_dir = self.output_dir / site_key
        site_dir.mkdir(parents=True, exist_ok=True)

        # Salvar dados em JSON
        with open(site_dir / "enhanced_data.json", 'w', encoding='utf-8') as f:
            json.dump(site_data, f, indent=2, ensure_ascii=False)

        # Salvar relatório em markdown
        self.generate_enhanced_site_report(site_key, site_data)

    def save_consolidated_data(self):
        """Salvar dados consolidados de todos os sites."""
        consolidated_file = self.output_dir / "enhanced_consolidated_data.json"

        with open(consolidated_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, indent=2, ensure_ascii=False)

        print(f"📄 Dados consolidados salvos em: {consolidated_file}")

    def generate_enhanced_site_report(self, site_key, site_data):
        """Gerar relatório individual melhorado para um site."""
        site_dir = self.output_dir / site_key
        report_file = site_dir / f"{site_key}_enhanced_analysis.md"

        report_content = f"""# 🚀 Análise Avançada - {site_data['name']}

> **Web Scraping Avançado Executado com Sucesso**
> Site: {site_data['url']}
> Data: {site_data['scraped_at']}
> Parser: {self.sites_config[site_key]['parser']}

## 📊 **Resumo Executivo**

O **{site_data['name']}** é uma plataforma de **{site_data['type']}** construída com **{site_data['framework']}**. A análise avançada revelou informações detalhadas sobre produtos, preços e funcionalidades específicas.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: {site_data['framework']}
- **Tipo**: {site_data['type']}
- **Status**: {site_data['status_code']}
- **Tamanho**: {site_data['content_length']} bytes

### **Tecnologias Detectadas**
"""

        # Adicionar tecnologias detectadas
        tech_stack = site_data.get('technology_stack', {})
        for category, items in tech_stack.items():
            if items:
                if isinstance(items, list):
                    report_content += f"- **{category.title()}**: {', '.join(items)}\n"
                else:
                    report_content += f"- **{category.title()}**: {items}\n"

        report_content += f"""

## 🎯 **Informações Básicas**

- **Título**: {site_data['title']}
- **Descrição**: {site_data['meta_description']}
- **Palavras-chave**: {site_data['meta_keywords']}

## 📦 **Produtos Encontrados**

"""

        # Adicionar produtos específicos
        products = site_data.get('products', [])
        if products:
            report_content += f"**Total de Produtos**: {len(products)}\n\n"
            for i, product in enumerate(products[:5], 1):  # Mostrar apenas os primeiros 5
                report_content += f"### Produto {i}\n"
                report_content += f"- **Título**: {product.get('title', 'N/A')}\n"
                report_content += f"- **Preço**: {product.get('price', 'N/A')}\n"
                if product.get('description'):
                    report_content += f"- **Descrição**: {product['description'][:100]}...\n"
                report_content += "\n"
        else:
            report_content += "Nenhum produto específico encontrado com os seletores utilizados.\n\n"

        report_content += f"""## 💰 **Análise de Preços**

"""

        # Adicionar análise de preços
        prices = site_data.get('prices', [])
        if prices:
            report_content += f"**Total de Preços Encontrados**: {len(prices)}\n\n"

            # Calcular estatísticas de preços
            numeric_prices = []
            for price in prices:
                if isinstance(price, dict) and 'numeric' in price:
                    numeric_prices.append(price['numeric'])
                elif isinstance(price, dict) and 'value' in price:
                    try:
                        numeric_prices.append(float(price['value'].replace(',', '.')))
                    except:
                        pass

            if numeric_prices:
                report_content += f"- **Preço Mínimo**: R$ {min(numeric_prices):.2f}\n"
                report_content += f"- **Preço Máximo**: R$ {max(numeric_prices):.2f}\n"
                report_content += f"- **Preço Médio**: R$ {sum(numeric_prices)/len(numeric_prices):.2f}\n\n"

            # Listar alguns preços
            for i, price in enumerate(prices[:5], 1):
                if isinstance(price, dict):
                    report_content += f"{i}. {price.get('formatted', price.get('value', 'N/A'))}\n"
                else:
                    report_content += f"{i}. {price}\n"
        else:
            report_content += "Nenhum preço específico encontrado.\n"

        # Adicionar análises específicas por tipo de site
        if site_data['type'] == 'livros_personalizados':
            self.add_personalized_books_analysis(report_content, site_data)
        elif site_data['type'] == 'clube_livros':
            self.add_book_club_analysis(report_content, site_data)
        elif site_data['type'] == 'livros_infantis':
            self.add_children_books_analysis(report_content, site_data)
        elif site_data['type'] == 'criacao_historias':
            self.add_story_creation_analysis(report_content, site_data)

        report_content += f"""

## 🔍 **Estrutura de Headings**
"""

        # Adicionar headings
        headings = site_data.get('headings', {})
        for level, texts in headings.items():
            if texts:
                report_content += f"- **{level.upper()}**: {len(texts)} elementos\n"
                for text in texts[:3]:  # Mostrar apenas os primeiros 3
                    report_content += f"  - {text[:100]}...\n"

        report_content += f"""

## 🎉 **Conclusão**

A análise avançada do **{site_data['name']}** revelou:

- ✅ **Produtos identificados**: {len(site_data.get('products', []))}
- ✅ **Preços extraídos**: {len(site_data.get('prices', []))}
- ✅ **Tecnologia**: {site_data['framework']} bem implementado
- ✅ **Conteúdo estruturado**: Dados organizados e acessíveis

---

## 📁 **Arquivos Gerados**

- `{site_key}/enhanced_data.json` - Dados estruturados avançados
- `{site_key}/html/original.html` - HTML original
- `{site_key}_enhanced_analysis.md` - Este relatório

**🚀 Web Scraping Avançado Concluído com Sucesso!**
"""

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"   📄 Relatório avançado salvo: {report_file}")

    def add_personalized_books_analysis(self, report_content, site_data):
        """Adicionar análise específica para livros personalizados."""
        features = site_data.get('features', [])
        subscription_info = site_data.get('subscription_info', {})
        target_audience = site_data.get('target_audience', [])

        analysis = f"""

## 📚 **Análise de Livros Personalizados**

### **Recursos Principais**
"""

        if features:
            for feature in features:
                analysis += f"- **{feature['keyword'].title()}**: {feature['mentions']} menções\n"

        analysis += "\n### **Informações de Assinatura**\n"
        if subscription_info:
            for key, value in subscription_info.items():
                analysis += f"- **{key.title()}**: {value} menções\n"

        analysis += "\n### **Público-Alvo**\n"
        if target_audience:
            for audience in target_audience:
                analysis += f"- {audience}\n"

        return analysis

    def add_book_club_analysis(self, report_content, site_data):
        """Adicionar análise específica para clube de livros."""
        plans = site_data.get('plans', [])
        statistics = site_data.get('statistics', {})
        book_info = site_data.get('book_info', [])

        analysis = f"""

## 📖 **Análise de Clube de Livros**

### **Estatísticas do Negócio**
"""

        if statistics:
            for key, value in statistics.items():
                analysis += f"- **{key.replace('_', ' ').title()}**: {value}\n"

        analysis += "\n### **Planos Disponíveis**\n"
        if plans:
            for plan in plans:
                analysis += f"- **{plan['type'].title()}**: {plan['mentions']} menções\n"

        analysis += "\n### **Informações de Livros**\n"
        if book_info:
            for i, book in enumerate(book_info[:3], 1):
                analysis += f"{i}. **{book['title']}**: {book['description'][:100]}...\n"

        return analysis

    def add_children_books_analysis(self, report_content, site_data):
        """Adicionar análise específica para livros infantis."""
        categories = site_data.get('categories', [])
        age_groups = site_data.get('age_groups', [])
        book_details = site_data.get('book_details', [])

        analysis = f"""

## 👶 **Análise de Livros Infantis**

### **Grupos por Idade**
"""

        if age_groups:
            for group in age_groups:
                analysis += f"- **{group['category'].title()}**: {group['mentions']} menções\n"

        analysis += "\n### **Categorias**\n"
        if categories:
            for category in categories:
                analysis += f"- {category}\n"

        analysis += "\n### **Detalhes dos Livros**\n"
        if book_details:
            for detail in book_details[:3]:
                analysis += f"- {detail}\n"

        return analysis

    def add_story_creation_analysis(self, report_content, site_data):
        """Adicionar análise específica para criação de histórias."""
        ai_capabilities = site_data.get('ai_capabilities', [])
        features = site_data.get('features', [])
        pricing = site_data.get('pricing', [])
        target_audience = site_data.get('target_audience', [])
        demo_info = site_data.get('demo_info', [])

        analysis = f"""

## 🤖 **Análise de Criação de Histórias com IA**

### **Capacidades de IA**
"""

        if ai_capabilities:
            for capability in ai_capabilities:
                analysis += f"- **{capability['feature'].title()}**: {capability['mentions']} menções\n"

        analysis += "\n### **Recursos Principais**\n"
        if features:
            for i, feature in enumerate(features[:3], 1):
                analysis += f"{i}. **{feature['title']}**\n"

        analysis += "\n### **Planos de Preços**\n"
        if pricing:
            for plan in pricing:
                analysis += f"- **{plan['plan'].title()}**: {plan['mentions']} menções\n"

        analysis += "\n### **Público-Alvo**\n"
        if target_audience:
            unique_audience = list(set(target_audience))
            for audience in unique_audience:
                analysis += f"- {audience.title()}\n"

        analysis += "\n### **Informações de Demo/Teste**\n"
        if demo_info:
            for demo in demo_info:
                analysis += f"- **{demo['type'].title()}**: {demo['mentions']} menções\n"

        return analysis

    def generate_enhanced_analysis_report(self):
        """Gerar relatório consolidado avançado."""
        report_file = self.output_dir / "enhanced_multi_sites_analysis.md"

        report_content = f"""# 🚀 Análise Consolidada Avançada - Multi-Sites Scraper

> **Web Scraping Avançado de Múltiplos Sites Executado com Sucesso**
> Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
> Versão: Enhanced Multi-Sites Scraper v2.0

## 📊 **Resumo Executivo**

Este relatório apresenta a análise consolidada avançada de **{len(self.sites_config)} sites** do segmento de livros infantis e criação de histórias, com parsers específicos para cada plataforma:

"""

        # Adicionar resumo de cada site
        for site_key, site_data in self.scraped_data.items():
            if 'error' not in site_data:
                config = self.sites_config[site_key]
                products_count = len(site_data.get('products', []))
                prices_count = len(site_data.get('prices', []))

                report_content += f"""
### 🚀 **{site_data['name']}**
- **URL**: {site_data['url']}
- **Tipo**: {site_data['type']}
- **Framework**: {site_data['framework']}
- **Parser**: {config['parser']}
- **Status**: ✅ Sucesso ({site_data['status_code']})
- **Produtos**: {products_count} encontrados
- **Preços**: {prices_count} extraídos
- **Conteúdo**: {site_data['content_length']} bytes
"""
            else:
                config = self.sites_config[site_key]
                report_content += f"""
### ❌ **{config['name']}**
- **URL**: {config['url']}
- **Parser**: {config['parser']}
- **Status**: Erro
- **Erro**: {site_data['error']}
"""

        report_content += f"""

## 🏗️ **Comparativo Avançado de Tecnologias**

| Site | Framework | Parser | Produtos | Preços | Tamanho (KB) |
|------|-----------|--------|----------|--------|--------------|
"""

        # Tabela comparativa avançada
        for site_key, site_data in self.scraped_data.items():
            if 'error' not in site_data:
                config = self.sites_config[site_key]
                products = len(site_data.get('products', []))
                prices = len(site_data.get('prices', []))
                size_kb = round(site_data['content_length'] / 1024, 1)

                report_content += f"| {site_data['name']} | {site_data['framework']} | {config['parser']} | {products} | {prices} | {size_kb} |\n"

        report_content += f"""

## 🎯 **Insights Avançados**

### **Análise de Frameworks**
"""

        # Análise de frameworks
        frameworks = {}
        for site_data in self.scraped_data.values():
            if 'error' not in site_data:
                framework = site_data.get('framework', 'unknown')
                frameworks[framework] = frameworks.get(framework, 0) + 1

        for framework, count in frameworks.items():
            report_content += f"- **{framework.title()}**: {count} site(s)\n"

        report_content += f"""

### **Análise de Produtos e Preços**
"""

        # Estatísticas de produtos e preços
        total_products = sum(len(site_data.get('products', [])) for site_data in self.scraped_data.values() if 'error' not in site_data)
        total_prices = sum(len(site_data.get('prices', [])) for site_data in self.scraped_data.values() if 'error' not in site_data)

        report_content += f"- **Total de Produtos Extraídos**: {total_products}\n"
        report_content += f"- **Total de Preços Encontrados**: {total_prices}\n"

        # Análise por tipo de negócio
        business_analysis = {}
        for site_data in self.scraped_data.values():
            if 'error' not in site_data:
                btype = site_data.get('type', 'unknown')
                if btype not in business_analysis:
                    business_analysis[btype] = {
                        'count': 0,
                        'products': 0,
                        'prices': 0
                    }
                business_analysis[btype]['count'] += 1
                business_analysis[btype]['products'] += len(site_data.get('products', []))
                business_analysis[btype]['prices'] += len(site_data.get('prices', []))

        report_content += f"""

### **Análise por Tipo de Negócio**
"""

        for btype, stats in business_analysis.items():
            report_content += f"- **{btype.replace('_', ' ').title()}**: {stats['count']} site(s), {stats['products']} produtos, {stats['prices']} preços\n"

        report_content += f"""

## 📁 **Estrutura de Arquivos Gerados**

```
{self.output_dir.name}/
├── 📊 enhanced_multi_sites_analysis.md   # Este relatório
├── 📄 enhanced_consolidated_data.json    # Dados consolidados avançados
"""

        for site_key in self.sites_config.keys():
            report_content += f"""├── 📁 {site_key}/
│   ├── 📄 enhanced_data.json             # Dados estruturados avançados
│   ├── 📄 {site_key}_enhanced_analysis.md # Relatório individual avançado
│   └── 📁 html/
│       └── 📄 original.html              # HTML original
"""

        report_content += f"""

## 🎉 **Conclusão**

O scraping avançado de múltiplos sites foi **executado com sucesso**, revelando:

- ✅ **Parsers específicos**: Cada site teve extração customizada
- ✅ **Dados estruturados**: {total_products} produtos e {total_prices} preços extraídos
- ✅ **Diversidade tecnológica**: {len(frameworks)} frameworks diferentes
- ✅ **Análise detalhada**: Relatórios individuais e consolidado
- ✅ **Segmento focado**: Todos os sites são do nicho de livros infantis

**Total de sites processados**: {len([s for s in self.scraped_data.values() if 'error' not in s])}/{len(self.sites_config)}

---

**🚀 Enhanced Multi-Sites Scraper - Concluído com Sucesso!**
"""

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"📊 Relatório consolidado avançado salvo: {report_file}")


def main():
    """Função principal."""
    print("🚀 ENHANCED MULTI-SITES SCRAPER - Scraping Avançado de Sites de Livros Infantis")
    print("="*80)

    # Criar scraper e executar
    scraper = EnhancedMultiSitesScraper("enhanced_multi_sites_results")
    scraper.scrape_all_sites()

    print(f"\n🎉 PROCESSO AVANÇADO CONCLUÍDO!")
    print(f"📁 Verifique os resultados em: {scraper.output_dir}")


if __name__ == "__main__":
    main()

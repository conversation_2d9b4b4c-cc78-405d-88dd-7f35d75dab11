#!/usr/bin/env python3
"""
🗂️ LIBRAS VIDEO ORGANIZER
Sistema para organizar vídeos de Libras baixados em diferentes estruturas.

Funcionalidades:
- Organização por articulador
- Organização por categoria de sinal
- Organização alfabética
- Geração de índices e catálogos
- Análise estatística dos vídeos
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List
import re
from datetime import datetime
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LibrasVideoOrganizer:
    """Organizador de vídeos de Libras."""
    
    def __init__(self, source_dir: str, output_dir: str = "libras_organized"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.videos_dir = self.source_dir / "videos"
        self.metadata_file = self.source_dir / "metadata" / "complete_metadata.json"
        
        # Estatísticas
        self.stats = {
            'total_videos': 0,
            'articuladores': {1: 0, 2: 0, 3: 0},
            'sinais_unicos': set(),
            'categorias': {},
            'tamanho_total': 0
        }
        
        # Criar estrutura de saída
        self.setup_output_structure()
    
    def setup_output_structure(self):
        """Criar estrutura de diretórios de saída."""
        self.output_dir.mkdir(exist_ok=True)
        
        # Estruturas de organização
        (self.output_dir / "por_articulador").mkdir(exist_ok=True)
        (self.output_dir / "por_articulador" / "articulador_1").mkdir(exist_ok=True)
        (self.output_dir / "por_articulador" / "articulador_2").mkdir(exist_ok=True)
        (self.output_dir / "por_articulador" / "articulador_3").mkdir(exist_ok=True)
        
        (self.output_dir / "alfabetico").mkdir(exist_ok=True)
        for letra in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            (self.output_dir / "alfabetico" / letra).mkdir(exist_ok=True)
        
        (self.output_dir / "por_categoria").mkdir(exist_ok=True)
        (self.output_dir / "indices").mkdir(exist_ok=True)
        (self.output_dir / "relatorios").mkdir(exist_ok=True)
        
        logger.info(f"📁 Estrutura criada em: {self.output_dir}")
    
    def load_metadata(self) -> Dict:
        """Carregar metadados dos vídeos."""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def analyze_videos(self):
        """Analisar vídeos existentes."""
        logger.info("📊 Analisando vídeos...")
        
        if not self.videos_dir.exists():
            logger.error(f"❌ Diretório de vídeos não encontrado: {self.videos_dir}")
            return
        
        video_files = list(self.videos_dir.glob("*.mp4"))
        self.stats['total_videos'] = len(video_files)
        
        for video_file in video_files:
            # Analisar nome do arquivo
            match = re.match(r'(.+)_articulador_(\d+)\.mp4', video_file.name)
            if match:
                sinal_name = match.group(1)
                articulador = int(match.group(2))
                
                self.stats['sinais_unicos'].add(sinal_name)
                self.stats['articuladores'][articulador] += 1
                
                # Tamanho do arquivo
                file_size = video_file.stat().st_size
                self.stats['tamanho_total'] += file_size
                
                # Categorizar por primeira letra
                primeira_letra = sinal_name[0].upper() if sinal_name else 'Z'
                if primeira_letra not in self.stats['categorias']:
                    self.stats['categorias'][primeira_letra] = 0
                self.stats['categorias'][primeira_letra] += 1
        
        logger.info(f"✅ Análise concluída: {self.stats['total_videos']} vídeos")
    
    def organize_by_articulador(self):
        """Organizar vídeos por articulador."""
        logger.info("🗂️ Organizando por articulador...")
        
        video_files = list(self.videos_dir.glob("*.mp4"))
        
        for video_file in video_files:
            match = re.match(r'(.+)_articulador_(\d+)\.mp4', video_file.name)
            if match:
                articulador = match.group(2)
                dest_dir = self.output_dir / "por_articulador" / f"articulador_{articulador}"
                dest_file = dest_dir / video_file.name
                
                if not dest_file.exists():
                    shutil.copy2(video_file, dest_file)
        
        logger.info("✅ Organização por articulador concluída")
    
    def organize_alphabetically(self):
        """Organizar vídeos alfabeticamente."""
        logger.info("🔤 Organizando alfabeticamente...")
        
        video_files = list(self.videos_dir.glob("*.mp4"))
        
        for video_file in video_files:
            match = re.match(r'(.+)_articulador_(\d+)\.mp4', video_file.name)
            if match:
                sinal_name = match.group(1)
                primeira_letra = sinal_name[0].upper() if sinal_name else 'Z'
                
                # Garantir que a letra está no alfabeto
                if primeira_letra not in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
                    primeira_letra = 'Z'
                
                dest_dir = self.output_dir / "alfabetico" / primeira_letra
                dest_file = dest_dir / video_file.name
                
                if not dest_file.exists():
                    shutil.copy2(video_file, dest_file)
        
        logger.info("✅ Organização alfabética concluída")
    
    def organize_by_category(self):
        """Organizar vídeos por categoria semântica."""
        logger.info("📂 Organizando por categoria...")
        
        # Categorias semânticas baseadas em padrões comuns
        categorias = {
            'Tempo': ['noite', 'tarde', 'manhã', 'dia', 'hora', 'tempo', 'ontem', 'hoje', 'amanhã'],
            'Animais': ['abelha', 'gato', 'cachorro', 'pássaro', 'peixe', 'animal'],
            'Alimentos': ['abacaxi', 'abóbora', 'comida', 'fruta', 'verdura'],
            'Ações': ['abanar', 'abandonar', 'abençoar', 'abraço', 'acenar', 'aceitar', 'acabar', 'abrir'],
            'Lugares': ['academia', 'casa', 'escola', 'hospital'],
            'Sentimentos': ['amor', 'raiva', 'feliz', 'triste'],
            'Família': ['pai', 'mãe', 'filho', 'irmão'],
            'Cores': ['azul', 'vermelho', 'verde', 'amarelo'],
            'Números': ['um', 'dois', 'três', 'quatro', 'cinco'],
            'Outros': []  # Categoria padrão
        }
        
        video_files = list(self.videos_dir.glob("*.mp4"))
        
        # Criar diretórios de categoria
        for categoria in categorias.keys():
            (self.output_dir / "por_categoria" / categoria).mkdir(exist_ok=True)
        
        for video_file in video_files:
            match = re.match(r'(.+)_articulador_(\d+)\.mp4', video_file.name)
            if match:
                sinal_name = match.group(1).lower()
                categoria_encontrada = 'Outros'
                
                # Encontrar categoria
                for categoria, palavras_chave in categorias.items():
                    if any(palavra in sinal_name for palavra in palavras_chave):
                        categoria_encontrada = categoria
                        break
                
                dest_dir = self.output_dir / "por_categoria" / categoria_encontrada
                dest_file = dest_dir / video_file.name
                
                if not dest_file.exists():
                    shutil.copy2(video_file, dest_file)
        
        logger.info("✅ Organização por categoria concluída")
    
    def generate_indices(self):
        """Gerar índices e catálogos."""
        logger.info("📋 Gerando índices...")
        
        # Índice geral
        indice_geral = {
            'sinais': sorted(list(self.stats['sinais_unicos'])),
            'total_sinais': len(self.stats['sinais_unicos']),
            'total_videos': self.stats['total_videos'],
            'articuladores': self.stats['articuladores'],
            'gerado_em': datetime.now().isoformat()
        }
        
        with open(self.output_dir / "indices" / "indice_geral.json", 'w', encoding='utf-8') as f:
            json.dump(indice_geral, f, indent=2, ensure_ascii=False)
        
        # Índice alfabético
        indice_alfabetico = {}
        for letra in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            sinais_letra = [s for s in self.stats['sinais_unicos'] if s.upper().startswith(letra)]
            if sinais_letra:
                indice_alfabetico[letra] = sorted(sinais_letra)
        
        with open(self.output_dir / "indices" / "indice_alfabetico.json", 'w', encoding='utf-8') as f:
            json.dump(indice_alfabetico, f, indent=2, ensure_ascii=False)
        
        # Catálogo HTML
        self.generate_html_catalog()
        
        logger.info("✅ Índices gerados")
    
    def generate_html_catalog(self):
        """Gerar catálogo HTML navegável."""
        html_content = f"""
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catálogo de Vídeos de Libras</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
        .stat-card {{ background: #ecf0f1; padding: 15px; border-radius: 8px; text-align: center; }}
        .sinais-list {{ columns: 3; column-gap: 20px; }}
        .sinal-item {{ break-inside: avoid; margin-bottom: 5px; }}
        .letra-section {{ margin: 20px 0; }}
        .letra-title {{ background: #3498db; color: white; padding: 10px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Catálogo de Vídeos de Libras</h1>
        <p>Base de dados V-LIBRASIL completa</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <h3>{self.stats['total_videos']}</h3>
            <p>Total de Vídeos</p>
        </div>
        <div class="stat-card">
            <h3>{len(self.stats['sinais_unicos'])}</h3>
            <p>Sinais Únicos</p>
        </div>
        <div class="stat-card">
            <h3>{self.stats['tamanho_total']/1024/1024:.1f} MB</h3>
            <p>Tamanho Total</p>
        </div>
    </div>
    
    <h2>📋 Lista de Sinais por Letra</h2>
"""
        
        # Organizar sinais por letra
        sinais_por_letra = {}
        for sinal in sorted(self.stats['sinais_unicos']):
            letra = sinal[0].upper() if sinal else 'Z'
            if letra not in sinais_por_letra:
                sinais_por_letra[letra] = []
            sinais_por_letra[letra].append(sinal)
        
        for letra in sorted(sinais_por_letra.keys()):
            html_content += f"""
    <div class="letra-section">
        <div class="letra-title">
            <h3>Letra {letra} ({len(sinais_por_letra[letra])} sinais)</h3>
        </div>
        <div class="sinais-list">
"""
            for sinal in sorted(sinais_por_letra[letra]):
                html_content += f'            <div class="sinal-item">• {sinal}</div>\n'
            
            html_content += """        </div>
    </div>
"""
        
        html_content += """
    <footer style="margin-top: 40px; text-align: center; color: #7f8c8d;">
        <p>Gerado automaticamente pelo Libras Video Organizer</p>
        <p>Data: """ + datetime.now().strftime('%d/%m/%Y %H:%M') + """</p>
    </footer>
</body>
</html>
"""
        
        with open(self.output_dir / "indices" / "catalogo.html", 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def generate_report(self):
        """Gerar relatório de organização."""
        report = f"""
🗂️ RELATÓRIO DE ORGANIZAÇÃO - LIBRAS VIDEOS
{'='*60}

📊 ESTATÍSTICAS:
- Total de vídeos: {self.stats['total_videos']}
- Sinais únicos: {len(self.stats['sinais_unicos'])}
- Tamanho total: {self.stats['tamanho_total']:,} bytes ({self.stats['tamanho_total']/1024/1024:.1f} MB)

👥 POR ARTICULADOR:
- Articulador 1: {self.stats['articuladores'][1]} vídeos
- Articulador 2: {self.stats['articuladores'][2]} vídeos  
- Articulador 3: {self.stats['articuladores'][3]} vídeos

🔤 DISTRIBUIÇÃO ALFABÉTICA:
"""
        
        for letra in sorted(self.stats['categorias'].keys()):
            count = self.stats['categorias'][letra]
            report += f"- Letra {letra}: {count} vídeos\n"
        
        report += f"""

📁 ESTRUTURA ORGANIZADA:
- {self.output_dir}/por_articulador/     # Vídeos por articulador
- {self.output_dir}/alfabetico/          # Vídeos por letra
- {self.output_dir}/por_categoria/       # Vídeos por categoria semântica
- {self.output_dir}/indices/             # Índices e catálogos
- {self.output_dir}/relatorios/          # Relatórios

🎯 ARQUIVOS GERADOS:
- Catálogo HTML navegável
- Índices JSON estruturados
- Organização múltipla dos vídeos
- Relatórios detalhados

✅ ORGANIZAÇÃO CONCLUÍDA COM SUCESSO!
"""
        
        report_file = self.output_dir / "relatorios" / f"organizacao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info(f"📋 Relatório salvo: {report_file}")
    
    def organize_all(self):
        """Executar organização completa."""
        logger.info("🗂️ INICIANDO ORGANIZAÇÃO COMPLETA")
        
        # 1. Analisar vídeos
        self.analyze_videos()
        
        # 2. Organizar por diferentes critérios
        self.organize_by_articulador()
        self.organize_alphabetically()
        self.organize_by_category()
        
        # 3. Gerar índices
        self.generate_indices()
        
        # 4. Gerar relatório
        self.generate_report()
        
        logger.info("🎉 ORGANIZAÇÃO CONCLUÍDA!")

def main():
    """Função principal."""
    print("🗂️ LIBRAS VIDEO ORGANIZER")
    print("="*50)
    
    # Verificar se existe diretório de vídeos
    source_options = [
        "libras_videos_complete",
        "test_libras_sample",
        "libras_videos"
    ]
    
    source_dir = None
    for option in source_options:
        if Path(option).exists():
            source_dir = option
            break
    
    if not source_dir:
        print("❌ Nenhum diretório de vídeos encontrado")
        print("📁 Procurados:", ", ".join(source_options))
        return
    
    print(f"📁 Usando diretório: {source_dir}")
    
    # Criar organizador
    organizer = LibrasVideoOrganizer(source_dir)
    
    # Executar organização
    organizer.organize_all()

if __name__ == "__main__":
    main()

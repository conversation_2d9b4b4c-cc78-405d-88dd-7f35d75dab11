#!/usr/bin/env python3
"""
🕷️ MULTI-SITES SCRAPER - Scraping Automatizado de Múltiplos Sites

Este script faz scraping automatizado dos sites solicitados:
- dentrodahistoria.com.br/clube
- clubefundamento.com.br
- meutibi.com.br
- storyspark.ai/pt

Baseado no spider_completo.py e frontend_cloner.py existentes.
"""

import asyncio
import os
import sys
import json
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from pathlib import Path
from datetime import datetime
import re

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class MultiSitesScraper:
    """Scraper para múltiplos sites com configurações específicas."""
    
    def __init__(self, output_dir="multi_sites_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuração da sessão
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Sites para fazer scraping
        self.sites_config = {
            'dentrodahistoria': {
                'url': 'https://www.dentrodahistoria.com.br/clube/',
                'name': 'Clube Dentro da História',
                'type': 'livros_personalizados',
                'framework': 'nuxt',
                'selectors': {
                    'title': 'title',
                    'description': 'meta[name="description"]',
                    'products': '.product, .livro, .kit',
                    'prices': '.price, .valor, .preco',
                    'images': 'img[src*="livro"], img[src*="kit"], img[src*="produto"]'
                }
            },
            'clubefundamento': {
                'url': 'https://www.clubefundamento.com.br/',
                'name': 'Clube Fundamento',
                'type': 'clube_livros',
                'framework': 'svelte',
                'selectors': {
                    'title': 'title',
                    'description': 'meta[name="description"]',
                    'products': '.livro, .book, .produto',
                    'prices': '.price, .valor, .preco',
                    'images': 'img[src*="livro"], img[src*="book"]'
                }
            },
            'meutibi': {
                'url': 'https://meutibi.com.br/',
                'name': 'Tibi - Livros Infantis',
                'type': 'livros_infantis',
                'framework': 'aspnet',
                'selectors': {
                    'title': 'title',
                    'description': 'meta[name="description"]',
                    'products': '.card-top-livros, .livro, .book',
                    'prices': '.price-book, .price, .valor',
                    'images': 'img[src*="livro"], img[src*="book"], .card-top-livros img'
                }
            },
            'storyspark': {
                'url': 'https://storyspark.ai/pt',
                'name': 'Story Spark - Criação de Histórias',
                'type': 'criacao_historias',
                'framework': 'nextjs',
                'selectors': {
                    'title': 'title',
                    'description': 'meta[name="description"]',
                    'features': '.feature, .card, .section',
                    'buttons': 'button, .btn, a[href*="criar"]',
                    'images': 'img'
                }
            }
        }
        
        # Dados coletados
        self.scraped_data = {}
    
    def scrape_all_sites(self):
        """Fazer scraping de todos os sites configurados."""
        print("🕷️ MULTI-SITES SCRAPER - Iniciando scraping")
        print("="*60)
        
        for site_key, config in self.sites_config.items():
            print(f"\n📄 Processando: {config['name']}")
            print(f"🔗 URL: {config['url']}")
            
            try:
                site_data = self.scrape_site(site_key, config)
                self.scraped_data[site_key] = site_data
                
                # Salvar dados individuais
                self.save_site_data(site_key, site_data)
                
                print(f"✅ {config['name']} processado com sucesso!")
                
                # Delay entre sites
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ Erro ao processar {config['name']}: {e}")
                self.scraped_data[site_key] = {'error': str(e)}
        
        # Salvar dados consolidados
        self.save_consolidated_data()
        self.generate_analysis_report()
        
        print(f"\n🎉 SCRAPING CONCLUÍDO!")
        print(f"📁 Resultados salvos em: {self.output_dir}")
    
    def scrape_site(self, site_key, config):
        """Fazer scraping de um site específico."""
        url = config['url']
        
        # Fazer requisição
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extrair dados básicos
        site_data = {
            'url': url,
            'name': config['name'],
            'type': config['type'],
            'framework': config['framework'],
            'scraped_at': datetime.now().isoformat(),
            'status_code': response.status_code,
            'content_length': len(response.content),
            'title': self.extract_title(soup),
            'meta_description': self.extract_meta_description(soup),
            'meta_keywords': self.extract_meta_keywords(soup),
            'headings': self.extract_headings(soup),
            'links': self.extract_links(soup, url),
            'images': self.extract_images(soup, url),
            'structure': self.analyze_structure(soup, config),
            'content_analysis': self.analyze_content(soup, config),
            'technology_stack': self.detect_technology(soup, response),
        }
        
        # Salvar HTML
        html_dir = self.output_dir / site_key / "html"
        html_dir.mkdir(parents=True, exist_ok=True)
        
        with open(html_dir / "original.html", 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        return site_data
    
    def extract_title(self, soup):
        """Extrair título da página."""
        title_tag = soup.find('title')
        return title_tag.get_text().strip() if title_tag else ''
    
    def extract_meta_description(self, soup):
        """Extrair meta description."""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '') if meta_desc else ''
    
    def extract_meta_keywords(self, soup):
        """Extrair meta keywords."""
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        return meta_keywords.get('content', '') if meta_keywords else ''
    
    def extract_headings(self, soup):
        """Extrair headings (h1-h6)."""
        headings = {}
        for level in range(1, 7):
            headings[f'h{level}'] = [h.get_text().strip() for h in soup.find_all(f'h{level}')]
        return headings
    
    def extract_links(self, soup, base_url):
        """Extrair todos os links."""
        links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                links.append({
                    'url': full_url,
                    'text': link.get_text().strip(),
                    'title': link.get('title', ''),
                    'classes': link.get('class', [])
                })
        return links[:50]  # Limitar para evitar dados excessivos
    
    def extract_images(self, soup, base_url):
        """Extrair informações das imagens."""
        images = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                full_url = urljoin(base_url, src)
                images.append({
                    'url': full_url,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', ''),
                    'classes': img.get('class', [])
                })
        return images[:30]  # Limitar para evitar dados excessivos
    
    def analyze_structure(self, soup, config):
        """Analisar estrutura específica do site."""
        structure = {
            'total_elements': len(soup.find_all()),
            'forms': len(soup.find_all('form')),
            'buttons': len(soup.find_all(['button', 'input[type="submit"]'])),
            'navigation': len(soup.find_all('nav')),
            'sections': len(soup.find_all('section')),
            'articles': len(soup.find_all('article')),
        }
        
        # Análise específica por seletores
        selectors = config.get('selectors', {})
        for key, selector in selectors.items():
            try:
                elements = soup.select(selector)
                structure[f'{key}_count'] = len(elements)
                if elements:
                    structure[f'{key}_sample'] = [elem.get_text().strip()[:100] for elem in elements[:3]]
            except Exception as e:
                structure[f'{key}_error'] = str(e)
        
        return structure
    
    def analyze_content(self, soup, config):
        """Analisar conteúdo específico do site."""
        # Extrair texto principal
        text_content = soup.get_text()
        words = text_content.split()
        
        analysis = {
            'word_count': len(words),
            'char_count': len(text_content),
            'paragraphs': len(soup.find_all('p')),
            'lists': len(soup.find_all(['ul', 'ol'])),
        }
        
        # Análise específica por tipo de site
        site_type = config.get('type', '')
        
        if 'livros' in site_type:
            # Procurar por informações de livros
            analysis['book_keywords'] = self.count_keywords(text_content, [
                'livro', 'história', 'criança', 'infantil', 'leitura', 'educativo'
            ])
            
        if 'clube' in site_type:
            # Procurar por informações de assinatura
            analysis['subscription_keywords'] = self.count_keywords(text_content, [
                'assinatura', 'mensal', 'plano', 'clube', 'receber'
            ])
        
        return analysis
    
    def count_keywords(self, text, keywords):
        """Contar ocorrências de palavras-chave."""
        text_lower = text.lower()
        return {keyword: text_lower.count(keyword) for keyword in keywords}
    
    def detect_technology(self, soup, response):
        """Detectar tecnologias utilizadas."""
        tech_stack = {
            'frameworks': [],
            'libraries': [],
            'analytics': [],
            'other': []
        }
        
        # Verificar headers
        headers = response.headers
        if 'server' in headers:
            tech_stack['server'] = headers['server']
        
        # Verificar scripts e meta tags
        scripts = soup.find_all('script')
        for script in scripts:
            src = script.get('src', '')
            content = script.get_text()
            
            # Detectar frameworks/bibliotecas
            if 'react' in src.lower() or 'react' in content.lower():
                tech_stack['frameworks'].append('React')
            if 'vue' in src.lower() or 'vue' in content.lower():
                tech_stack['frameworks'].append('Vue.js')
            if 'angular' in src.lower() or 'angular' in content.lower():
                tech_stack['frameworks'].append('Angular')
            if 'nuxt' in src.lower() or 'nuxt' in content.lower():
                tech_stack['frameworks'].append('Nuxt.js')
            if 'next' in src.lower() or 'next' in content.lower():
                tech_stack['frameworks'].append('Next.js')
            if 'svelte' in src.lower() or 'svelte' in content.lower():
                tech_stack['frameworks'].append('Svelte')
            
            # Detectar analytics
            if 'google-analytics' in src or 'gtag' in content:
                tech_stack['analytics'].append('Google Analytics')
            if 'gtm' in src or 'googletagmanager' in content:
                tech_stack['analytics'].append('Google Tag Manager')
        
        return tech_stack

    def save_site_data(self, site_key, site_data):
        """Salvar dados de um site específico."""
        site_dir = self.output_dir / site_key
        site_dir.mkdir(parents=True, exist_ok=True)

        # Salvar dados em JSON
        with open(site_dir / "data.json", 'w', encoding='utf-8') as f:
            json.dump(site_data, f, indent=2, ensure_ascii=False)

        # Salvar relatório em markdown
        self.generate_site_report(site_key, site_data)

    def save_consolidated_data(self):
        """Salvar dados consolidados de todos os sites."""
        consolidated_file = self.output_dir / "consolidated_data.json"

        with open(consolidated_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, indent=2, ensure_ascii=False)

        print(f"📄 Dados consolidados salvos em: {consolidated_file}")

    def generate_site_report(self, site_key, site_data):
        """Gerar relatório individual para um site."""
        site_dir = self.output_dir / site_key
        report_file = site_dir / f"{site_key}_analysis.md"

        report_content = f"""# 📊 Análise Completa - {site_data['name']}

> **Web Scraping Executado com Sucesso**
> Site: {site_data['url']}
> Data: {site_data['scraped_at']}

## 📊 **Resumo Executivo**

O **{site_data['name']}** é uma plataforma de **{site_data['type']}** construída com **{site_data['framework']}**. A análise revelou uma arquitetura moderna e funcionalidades específicas para o segmento de livros infantis.

## 🏗️ **Arquitetura Técnica**

### **Stack Tecnológico**
- **Framework**: {site_data['framework']}
- **Tipo**: {site_data['type']}
- **Status**: {site_data['status_code']}
- **Tamanho**: {site_data['content_length']} bytes

### **Tecnologias Detectadas**
"""

        # Adicionar tecnologias detectadas
        tech_stack = site_data.get('technology_stack', {})
        for category, items in tech_stack.items():
            if items:
                report_content += f"- **{category.title()}**: {', '.join(items) if isinstance(items, list) else items}\n"

        report_content += f"""

## 🎯 **Conteúdo Principal**

### **Informações Básicas**
- **Título**: {site_data['title']}
- **Descrição**: {site_data['meta_description']}
- **Palavras-chave**: {site_data['meta_keywords']}

### **Estrutura de Headings**
"""

        # Adicionar headings
        headings = site_data.get('headings', {})
        for level, texts in headings.items():
            if texts:
                report_content += f"- **{level.upper()}**: {len(texts)} elementos\n"
                for text in texts[:3]:  # Mostrar apenas os primeiros 3
                    report_content += f"  - {text[:100]}...\n"

        report_content += f"""

## 📊 **Análise Estrutural**

### **Elementos da Página**
"""

        # Adicionar análise estrutural
        structure = site_data.get('structure', {})
        for key, value in structure.items():
            if isinstance(value, int):
                report_content += f"- **{key.replace('_', ' ').title()}**: {value}\n"

        report_content += f"""

## 🔍 **Análise de Conteúdo**

### **Estatísticas de Texto**
"""

        # Adicionar análise de conteúdo
        content_analysis = site_data.get('content_analysis', {})
        for key, value in content_analysis.items():
            if isinstance(value, int):
                report_content += f"- **{key.replace('_', ' ').title()}**: {value}\n"
            elif isinstance(value, dict):
                report_content += f"- **{key.replace('_', ' ').title()}**:\n"
                for subkey, subvalue in value.items():
                    report_content += f"  - {subkey}: {subvalue}\n"

        report_content += f"""

## 🖼️ **Recursos Extraídos**

### **Links Encontrados**
- **Total de Links**: {len(site_data.get('links', []))}

### **Imagens Encontradas**
- **Total de Imagens**: {len(site_data.get('images', []))}

## 🎯 **Conclusão**

O **{site_data['name']}** demonstra uma arquitetura **{site_data['framework']}** bem estruturada para o segmento de **{site_data['type']}**. A análise revelou:

- ✅ **Estrutura organizada** com {structure.get('total_elements', 0)} elementos
- ✅ **Conteúdo rico** com {content_analysis.get('word_count', 0)} palavras
- ✅ **Recursos visuais** com {len(site_data.get('images', []))} imagens
- ✅ **Navegação** com {len(site_data.get('links', []))} links

---

## 📁 **Arquivos Gerados**

- `{site_key}/` - Dados do site
- `{site_key}/html/original.html` - HTML original
- `{site_key}_analysis.md` - Este relatório

**🎉 Web Scraping Concluído com Sucesso!**
"""

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"   📄 Relatório salvo: {report_file}")

    def generate_analysis_report(self):
        """Gerar relatório consolidado de análise."""
        report_file = self.output_dir / "multi_sites_analysis.md"

        report_content = f"""# 🕷️ Análise Consolidada - Multi-Sites Scraper

> **Web Scraping de Múltiplos Sites Executado com Sucesso**
> Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 **Resumo Executivo**

Este relatório apresenta a análise consolidada de **{len(self.sites_config)} sites** do segmento de livros infantis e criação de histórias:

"""

        # Adicionar resumo de cada site
        for site_key, site_data in self.scraped_data.items():
            if 'error' not in site_data:
                config = self.sites_config[site_key]
                report_content += f"""
### 📖 **{site_data['name']}**
- **URL**: {site_data['url']}
- **Tipo**: {site_data['type']}
- **Framework**: {site_data['framework']}
- **Status**: ✅ Sucesso ({site_data['status_code']})
- **Conteúdo**: {site_data['content_length']} bytes
- **Palavras**: {site_data.get('content_analysis', {}).get('word_count', 0)}
"""
            else:
                config = self.sites_config[site_key]
                report_content += f"""
### ❌ **{config['name']}**
- **URL**: {config['url']}
- **Status**: Erro
- **Erro**: {site_data['error']}
"""

        report_content += f"""

## 🏗️ **Comparativo de Tecnologias**

| Site | Framework | Tipo | Elementos | Palavras |
|------|-----------|------|-----------|----------|
"""

        # Tabela comparativa
        for site_key, site_data in self.scraped_data.items():
            if 'error' not in site_data:
                elements = site_data.get('structure', {}).get('total_elements', 0)
                words = site_data.get('content_analysis', {}).get('word_count', 0)
                report_content += f"| {site_data['name']} | {site_data['framework']} | {site_data['type']} | {elements} | {words} |\n"

        report_content += f"""

## 🎯 **Insights e Descobertas**

### **Frameworks Utilizados**
"""

        # Análise de frameworks
        frameworks = {}
        for site_data in self.scraped_data.values():
            if 'error' not in site_data:
                framework = site_data.get('framework', 'unknown')
                frameworks[framework] = frameworks.get(framework, 0) + 1

        for framework, count in frameworks.items():
            report_content += f"- **{framework.title()}**: {count} site(s)\n"

        report_content += f"""

### **Tipos de Negócio**
"""

        # Análise de tipos de negócio
        business_types = {}
        for site_data in self.scraped_data.values():
            if 'error' not in site_data:
                btype = site_data.get('type', 'unknown')
                business_types[btype] = business_types.get(btype, 0) + 1

        for btype, count in business_types.items():
            report_content += f"- **{btype.replace('_', ' ').title()}**: {count} site(s)\n"

        report_content += f"""

## 📁 **Estrutura de Arquivos Gerados**

```
{self.output_dir.name}/
├── 📊 multi_sites_analysis.md          # Este relatório
├── 📄 consolidated_data.json           # Dados consolidados
"""

        for site_key in self.sites_config.keys():
            report_content += f"""├── 📁 {site_key}/
│   ├── 📄 data.json                    # Dados estruturados
│   ├── 📄 {site_key}_analysis.md       # Relatório individual
│   └── 📁 html/
│       └── 📄 original.html            # HTML original
"""

        report_content += f"""

## 🎉 **Conclusão**

O scraping de múltiplos sites foi **executado com sucesso**, revelando:

- ✅ **Diversidade tecnológica**: {len(frameworks)} frameworks diferentes
- ✅ **Segmento focado**: Todos os sites são do nicho de livros infantis
- ✅ **Dados estruturados**: Informações organizadas e analisadas
- ✅ **Relatórios detalhados**: Análise individual e consolidada

**Total de sites processados**: {len([s for s in self.scraped_data.values() if 'error' not in s])}/{len(self.sites_config)}

---

**🕷️ Multi-Sites Scraper - Concluído com Sucesso!**
"""

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"📊 Relatório consolidado salvo: {report_file}")


def main():
    """Função principal."""
    print("🕷️ MULTI-SITES SCRAPER - Scraping de Sites de Livros Infantis")
    print("="*70)

    # Criar scraper e executar
    scraper = MultiSitesScraper("multi_sites_results")
    scraper.scrape_all_sites()

    print(f"\n🎉 PROCESSO CONCLUÍDO!")
    print(f"📁 Verifique os resultados em: {scraper.output_dir}")


if __name__ == "__main__":
    main()

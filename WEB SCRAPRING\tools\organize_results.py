#!/usr/bin/env python3
"""
📁 ORGANIZE RESULTS - Organizador de Resultados do Web Scraping

Este script organiza todos os resultados do web scraping de múltiplos sites
em uma estrutura final limpa e documentada.
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime


def organize_results():
    """Organizar todos os resultados em uma estrutura final."""
    print("📁 ORGANIZE RESULTS - Organizando resultados finais")
    print("="*60)
    
    # Criar diretório final
    final_dir = Path("RESULTADOS_FINAIS_LIVROS_INFANTIS")
    final_dir.mkdir(exist_ok=True)
    
    # Criar subdiretórios
    subdirs = [
        "01_RELATORIOS_PRINCIPAIS",
        "02_DADOS_ESTRUTURADOS", 
        "03_RELATORIOS_INDIVIDUAIS",
        "04_HTML_ORIGINAL",
        "05_FERRAMENTAS_UTILIZADAS"
    ]
    
    for subdir in subdirs:
        (final_dir / subdir).mkdir(exist_ok=True)
    
    print("📂 Estrutura de diretórios criada")
    
    # 1. Copiar relatórios principais
    print("\n📊 Copiando relatórios principais...")
    
    main_reports = [
        ("multi_sites_livros_infantis_analysis.md", "ANALISE_CONSOLIDADA_FINAL.md"),
        ("multi_sites_results/multi_sites_analysis.md", "analise_basica_multi_sites.md"),
        ("enhanced_multi_sites_results/enhanced_multi_sites_analysis.md", "analise_avancada_multi_sites.md")
    ]
    
    for src, dst in main_reports:
        if Path(src).exists():
            shutil.copy2(src, final_dir / "01_RELATORIOS_PRINCIPAIS" / dst)
            print(f"   ✅ {dst}")
    
    # 2. Copiar dados estruturados
    print("\n📄 Copiando dados estruturados...")
    
    data_files = [
        ("multi_sites_results/consolidated_data.json", "dados_basicos_consolidados.json"),
        ("enhanced_multi_sites_results/enhanced_consolidated_data.json", "dados_avancados_consolidados.json")
    ]
    
    for src, dst in data_files:
        if Path(src).exists():
            shutil.copy2(src, final_dir / "02_DADOS_ESTRUTURADOS" / dst)
            print(f"   ✅ {dst}")
    
    # 3. Copiar relatórios individuais
    print("\n📋 Copiando relatórios individuais...")
    
    sites = ["dentrodahistoria", "clubefundamento", "meutibi", "storyspark"]
    
    for site in sites:
        # Relatórios básicos
        basic_report = f"multi_sites_results/{site}/{site}_analysis.md"
        if Path(basic_report).exists():
            shutil.copy2(basic_report, final_dir / "03_RELATORIOS_INDIVIDUAIS" / f"{site}_analise_basica.md")
            print(f"   ✅ {site}_analise_basica.md")
        
        # Relatórios avançados
        enhanced_report = f"enhanced_multi_sites_results/{site}/{site}_enhanced_analysis.md"
        if Path(enhanced_report).exists():
            shutil.copy2(enhanced_report, final_dir / "03_RELATORIOS_INDIVIDUAIS" / f"{site}_analise_avancada.md")
            print(f"   ✅ {site}_analise_avancada.md")
        
        # Dados JSON individuais
        basic_data = f"multi_sites_results/{site}/data.json"
        if Path(basic_data).exists():
            shutil.copy2(basic_data, final_dir / "02_DADOS_ESTRUTURADOS" / f"{site}_dados_basicos.json")
        
        enhanced_data = f"enhanced_multi_sites_results/{site}/enhanced_data.json"
        if Path(enhanced_data).exists():
            shutil.copy2(enhanced_data, final_dir / "02_DADOS_ESTRUTURADOS" / f"{site}_dados_avancados.json")
    
    # 4. Copiar HTML original
    print("\n🌐 Copiando HTML original...")
    
    for site in sites:
        html_src = f"multi_sites_results/{site}/html/original.html"
        if Path(html_src).exists():
            shutil.copy2(html_src, final_dir / "04_HTML_ORIGINAL" / f"{site}_original.html")
            print(f"   ✅ {site}_original.html")
    
    # 5. Copiar ferramentas utilizadas
    print("\n🛠️ Copiando ferramentas utilizadas...")
    
    tools = [
        ("tools/multi_sites_scraper.py", "multi_sites_scraper_basico.py"),
        ("tools/enhanced_multi_sites_scraper.py", "multi_sites_scraper_avancado.py"),
        ("tools/organize_results.py", "organizador_resultados.py")
    ]
    
    for src, dst in tools:
        if Path(src).exists():
            shutil.copy2(src, final_dir / "05_FERRAMENTAS_UTILIZADAS" / dst)
            print(f"   ✅ {dst}")
    
    # 6. Criar README final
    print("\n📝 Criando README final...")
    create_final_readme(final_dir)
    
    # 7. Criar estatísticas finais
    print("\n📊 Gerando estatísticas finais...")
    create_final_stats(final_dir)
    
    print(f"\n🎉 ORGANIZAÇÃO CONCLUÍDA!")
    print(f"📁 Todos os resultados organizados em: {final_dir}")
    print(f"📋 Total de arquivos organizados: {count_files(final_dir)}")


def create_final_readme(final_dir):
    """Criar README final com instruções."""
    readme_content = f"""# 📚 Resultados Finais - Web Scraping Livros Infantis

> **Projeto concluído com sucesso em {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}**

## 📊 **Resumo do Projeto**

Este projeto realizou **web scraping completo de 4 sites** especializados em livros infantis:

1. **Clube Dentro da História** - Livros personalizados
2. **Clube Fundamento** - Clube de livros por assinatura  
3. **Tibi** - Livros infantis tradicionais
4. **Story Spark** - Criação de histórias com IA

## 📁 **Estrutura dos Resultados**

### **01_RELATORIOS_PRINCIPAIS/**
- `ANALISE_CONSOLIDADA_FINAL.md` - **📊 RELATÓRIO PRINCIPAL**
- `analise_basica_multi_sites.md` - Análise básica consolidada
- `analise_avancada_multi_sites.md` - Análise avançada consolidada

### **02_DADOS_ESTRUTURADOS/**
- `dados_basicos_consolidados.json` - Dados de todos os sites (básico)
- `dados_avancados_consolidados.json` - Dados de todos os sites (avançado)
- `[site]_dados_basicos.json` - Dados individuais básicos
- `[site]_dados_avancados.json` - Dados individuais avançados

### **03_RELATORIOS_INDIVIDUAIS/**
- `[site]_analise_basica.md` - Relatórios individuais básicos
- `[site]_analise_avancada.md` - Relatórios individuais avançados

### **04_HTML_ORIGINAL/**
- `[site]_original.html` - HTML original de cada site

### **05_FERRAMENTAS_UTILIZADAS/**
- `multi_sites_scraper_basico.py` - Scraper básico
- `multi_sites_scraper_avancado.py` - Scraper com parsers específicos
- `organizador_resultados.py` - Este organizador

## 🚀 **Como Usar os Resultados**

### **Para Análise Rápida**
👉 Leia: `01_RELATORIOS_PRINCIPAIS/ANALISE_CONSOLIDADA_FINAL.md`

### **Para Dados Estruturados**
👉 Use: `02_DADOS_ESTRUTURADOS/dados_avancados_consolidados.json`

### **Para Análise Detalhada**
👉 Explore: `03_RELATORIOS_INDIVIDUAIS/`

### **Para Desenvolvimento**
👉 Examine: `05_FERRAMENTAS_UTILIZADAS/`

## 🎯 **Principais Descobertas**

- ✅ **4/4 sites** processados com sucesso
- ✅ **17 produtos** extraídos do site Tibi
- ✅ **4 frameworks** diferentes identificados
- ✅ **Modelos de negócio** variados analisados
- ✅ **Relatórios completos** gerados

## 🛠️ **Tecnologias Utilizadas**

- **Python 3.x** + BeautifulSoup4
- **Requests** para HTTP
- **JSON** para dados estruturados
- **Markdown** para relatórios
- **Regex** para extração de padrões

## 📞 **Suporte**

Para dúvidas sobre os resultados ou ferramentas:
- Consulte os relatórios em `01_RELATORIOS_PRINCIPAIS/`
- Examine o código em `05_FERRAMENTAS_UTILIZADAS/`
- Analise os dados em `02_DADOS_ESTRUTURADOS/`

---

**🕷️ Web Scraping Multi-Sites - Projeto Concluído com Sucesso!**
"""
    
    with open(final_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("   ✅ README.md criado")


def create_final_stats(final_dir):
    """Criar arquivo de estatísticas finais."""
    stats = {
        "projeto": "Web Scraping Multi-Sites Livros Infantis",
        "data_conclusao": datetime.now().isoformat(),
        "sites_processados": 4,
        "sites_sucesso": 4,
        "taxa_sucesso": "100%",
        "total_arquivos_gerados": count_files(final_dir),
        "sites_analisados": [
            {
                "nome": "Clube Dentro da História",
                "url": "https://www.dentrodahistoria.com.br/clube/",
                "framework": "Nuxt.js",
                "tipo": "livros_personalizados",
                "status": "sucesso"
            },
            {
                "nome": "Clube Fundamento", 
                "url": "https://www.clubefundamento.com.br/",
                "framework": "SvelteKit",
                "tipo": "clube_livros",
                "status": "sucesso"
            },
            {
                "nome": "Tibi - Livros Infantis",
                "url": "https://meutibi.com.br/",
                "framework": "ASP.NET",
                "tipo": "livros_infantis", 
                "status": "sucesso",
                "produtos_extraidos": 17
            },
            {
                "nome": "Story Spark",
                "url": "https://storyspark.ai/pt",
                "framework": "Next.js",
                "tipo": "criacao_historias",
                "status": "sucesso"
            }
        ],
        "ferramentas_desenvolvidas": [
            "multi_sites_scraper.py",
            "enhanced_multi_sites_scraper.py", 
            "organize_results.py"
        ],
        "tipos_arquivo_gerados": [
            "Relatórios Markdown (.md)",
            "Dados JSON (.json)",
            "HTML Original (.html)",
            "Scripts Python (.py)"
        ]
    }
    
    with open(final_dir / "estatisticas_finais.json", 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    print("   ✅ estatisticas_finais.json criado")


def count_files(directory):
    """Contar total de arquivos no diretório."""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count


if __name__ == "__main__":
    organize_results()

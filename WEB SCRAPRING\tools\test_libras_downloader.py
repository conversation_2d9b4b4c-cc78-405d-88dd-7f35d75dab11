#!/usr/bin/env python3
"""
Teste do sistema de download de vídeos de Libras - versão de teste com apenas 2 páginas.
"""

import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def test_single_sign_page():
    """Testar extração de vídeo de uma página específica de sinal."""
    
    print("🧪 TESTE DE PÁGINA DE SINAL ESPECÍFICA")
    print("="*50)
    
    # URL de teste baseada na análise anterior
    test_url = "https://libras.cin.ufpe.br/sign/885#interpreter_3"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"🔍 Acessando: {test_url}")
        response = requests.get(test_url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"✅ Status: {response.status_code}")
        print(f"📄 Tamanho da página: {len(response.content):,} bytes")
        
        # Procurar por tags de vídeo
        print("\n🎬 PROCURANDO ELEMENTOS DE VÍDEO:")
        
        # 1. Tags video
        video_tags = soup.find_all('video')
        print(f"Tags <video>: {len(video_tags)}")
        for i, video in enumerate(video_tags):
            src = video.get('src')
            print(f"  Video {i+1}: src='{src}'")
        
        # 2. Tags source
        source_tags = soup.find_all('source')
        print(f"Tags <source>: {len(source_tags)}")
        for i, source in enumerate(source_tags):
            src = source.get('src')
            print(f"  Source {i+1}: src='{src}'")
        
        # 3. Links para .mp4
        mp4_links = soup.find_all('a', href=re.compile(r'\.mp4'))
        print(f"Links .mp4: {len(mp4_links)}")
        for i, link in enumerate(mp4_links):
            href = link.get('href')
            print(f"  Link {i+1}: href='{href}'")
        
        # 4. Procurar no texto da página
        page_text = str(soup)
        video_urls = re.findall(r'["\']([^"\']*\.mp4[^"\']*)["\']', page_text)
        print(f"URLs .mp4 no texto: {len(video_urls)}")
        for i, url in enumerate(video_urls[:5]):
            print(f"  URL {i+1}: {url}")
        
        # 5. Procurar por padrões específicos
        print("\n🔍 PROCURANDO PADRÕES ESPECÍFICOS:")
        
        # Padrão storage/videos
        storage_patterns = re.findall(r'storage/videos/[^"\'\\s]+', page_text)
        print(f"Padrões storage/videos: {len(storage_patterns)}")
        for pattern in storage_patterns[:3]:
            print(f"  - {pattern}")
        
        # Padrão com números (IDs)
        id_patterns = re.findall(r'(\d+).*\.mp4', page_text)
        print(f"IDs em URLs .mp4: {len(id_patterns)}")
        for pattern in id_patterns[:3]:
            print(f"  - ID: {pattern}")
        
        # 6. Tentar construir URL baseada no padrão da página
        print("\n🎯 TENTANDO CONSTRUIR URL DO VÍDEO:")
        
        # Extrair ID do sinal e intérprete da URL
        match = re.search(r'/sign/(\d+)#interpreter_(\d+)', test_url)
        if match:
            sign_id = match.group(1)
            interpreter_id = match.group(2)
            print(f"Sign ID: {sign_id}, Interpreter ID: {interpreter_id}")
            
            # Tentar diferentes padrões
            base_url = "https://libras.cin.ufpe.br"
            potential_urls = [
                f"{base_url}/storage/videos/{sign_id}_{interpreter_id}.mp4",
                f"{base_url}/storage/videos/sign_{sign_id}_interpreter_{interpreter_id}.mp4",
                f"{base_url}/storage/videos/{sign_id}.mp4",
                f"{base_url}/videos/{sign_id}_{interpreter_id}.mp4",
            ]
            
            print("🧪 Testando URLs potenciais:")
            for i, url in enumerate(potential_urls):
                try:
                    head_response = requests.head(url, headers=headers, timeout=5)
                    print(f"  {i+1}. {url} -> Status: {head_response.status_code}")
                    
                    if head_response.status_code == 200:
                        content_type = head_response.headers.get('content-type', '')
                        content_length = head_response.headers.get('content-length', '')
                        print(f"      ✅ ENCONTRADO! Type: {content_type}, Size: {content_length}")
                        return url
                        
                except Exception as e:
                    print(f"  {i+1}. {url} -> Erro: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None

def test_page_extraction():
    """Testar extração de links de uma página principal."""
    
    print("\n🧪 TESTE DE EXTRAÇÃO DE PÁGINA PRINCIPAL")
    print("="*50)
    
    url = "https://libras.cin.ufpe.br/?page=1"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"✅ Página carregada com sucesso")
        
        # Extrair links de vídeo
        exibir_buttons = soup.find_all('a', string=re.compile(r'Exibir', re.IGNORECASE))
        print(f"🔗 Botões 'Exibir' encontrados: {len(exibir_buttons)}")
        
        video_links = []
        table_rows = soup.find_all('tr')
        
        current_signal = None
        
        for row in table_rows[1:]:  # Pular cabeçalho
            cells = row.find_all(['td', 'th'])
            
            if len(cells) >= 5:
                # Primeira célula tem o nome do sinal
                signal_name = cells[0].get_text().strip()
                if signal_name and len(signal_name) > 1:
                    current_signal = signal_name
                
                # Células 3, 4, 5 têm os links dos articuladores
                for i, articulador_num in enumerate([1, 2, 3], start=2):
                    if i < len(cells):
                        cell = cells[i]
                        link = cell.find('a')
                        if link and link.get('href'):
                            video_links.append({
                                'signal_name': current_signal,
                                'articulador': articulador_num,
                                'page_url': link.get('href'),
                                'full_url': urljoin(url, link.get('href'))
                            })
        
        print(f"📊 Links de vídeo extraídos: {len(video_links)}")
        
        # Mostrar primeiros 5
        print("\n📋 Primeiros 5 links:")
        for i, link in enumerate(video_links[:5]):
            print(f"  {i+1}. {link['signal_name']} - Articulador {link['articulador']}")
            print(f"     URL: {link['full_url']}")
        
        return video_links
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return []

def main():
    """Função principal de teste."""
    print("🧪 TESTE DO SISTEMA DE DOWNLOAD DE LIBRAS")
    print("="*60)
    
    # Teste 1: Extrair links de uma página
    video_links = test_page_extraction()
    
    # Teste 2: Tentar encontrar vídeo real
    if video_links:
        print(f"\n🎯 Testando extração de vídeo do primeiro link...")
        first_link = video_links[0]
        video_url = test_single_sign_page()
        
        if video_url:
            print(f"\n🎉 SUCESSO! URL do vídeo encontrada: {video_url}")
        else:
            print(f"\n⚠️ Não foi possível encontrar a URL do vídeo")
    
    print(f"\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🔍 TESTE DE EXTRAÇÃO DE VÍDEO
Testa se conseguimos extrair URLs reais de vídeos das páginas de sinais
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re

def test_video_extraction():
    """Testa extração de vídeo de uma página específica"""
    
    # URL de teste (primeira página do JSON)
    test_url = "https://libras.cin.ufpe.br/sign/885#interpreter_3"
    base_url = "https://libras.cin.ufpe.br"
    
    print(f"🔍 Testando extração de vídeo...")
    print(f"📄 URL: {test_url}")
    print(f"🎯 Procurando por tags <source>...")
    
    try:
        # Fazer requisição
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        response = session.get(test_url, timeout=15)
        response.raise_for_status()
        
        print(f"✅ Resposta HTTP: {response.status_code}")
        print(f"📏 Tamanho da página: {len(response.content)} bytes")
        
        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Procurar por tags source
        source_tags = soup.find_all('source')
        print(f"🎬 Tags <source> encontradas: {len(source_tags)}")
        
        if source_tags:
            for i, source in enumerate(source_tags):
                src = source.get('src')
                type_attr = source.get('type', 'N/A')
                print(f"   [{i}] src: {src}")
                print(f"       type: {type_attr}")
                
                if src:
                    full_url = urljoin(base_url, src)
                    print(f"       URL completa: {full_url}")
                print()
        
        # Procurar por tags video
        video_tags = soup.find_all('video')
        print(f"📹 Tags <video> encontradas: {len(video_tags)}")
        
        if video_tags:
            for i, video in enumerate(video_tags):
                src = video.get('src')
                if src:
                    print(f"   [{i}] Video src: {src}")
                    full_url = urljoin(base_url, src)
                    print(f"       URL completa: {full_url}")
        
        # Procurar por qualquer referência a .mp4
        mp4_matches = re.findall(r'["\']([^"\']*\.mp4[^"\']*)["\']', response.text)
        print(f"🎥 Referências .mp4 encontradas: {len(mp4_matches)}")
        
        for i, match in enumerate(mp4_matches):
            print(f"   [{i}] {match}")
            full_url = urljoin(base_url, match)
            print(f"       URL completa: {full_url}")
        
        # Testar download de um vídeo se encontrado
        if source_tags and source_tags[0].get('src'):
            test_video_url = urljoin(base_url, source_tags[0].get('src'))
            print(f"\n🧪 TESTANDO DOWNLOAD...")
            print(f"📥 URL: {test_video_url}")
            
            try:
                head_response = session.head(test_video_url, timeout=10)
                print(f"✅ Status: {head_response.status_code}")
                print(f"📏 Content-Length: {head_response.headers.get('Content-Length', 'N/A')}")
                print(f"🎬 Content-Type: {head_response.headers.get('Content-Type', 'N/A')}")
                
                if head_response.status_code == 200:
                    print(f"🎉 VÍDEO ACESSÍVEL! Pronto para download.")
                else:
                    print(f"❌ Vídeo não acessível.")
                    
            except Exception as e:
                print(f"❌ Erro ao testar download: {e}")
        
    except Exception as e:
        print(f"❌ Erro na extração: {e}")

if __name__ == "__main__":
    test_video_extraction()

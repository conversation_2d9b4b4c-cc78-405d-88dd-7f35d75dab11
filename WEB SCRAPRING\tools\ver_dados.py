#!/usr/bin/env python3
"""
Ver dados que foram scrapados.
"""

import sqlite3
import json
from datetime import datetime

def ver_paginas():
    """Ver páginas scrapadas."""
    print("📄 PÁGINAS SCRAPADAS:")
    print("="*80)
    
    try:
        conn = sqlite3.connect('./data/webscraper.db')
        cursor = conn.cursor()
        
        # Contar total
        cursor.execute("SELECT COUNT(*) FROM pages")
        total = cursor.fetchone()[0]
        print(f"📊 Total de páginas: {total}")
        
        if total > 0:
            # Buscar páginas recentes
            cursor.execute("""
                SELECT url, title, quality_score, quality_tier, word_count, 
                       status, created_at, updated_at
                FROM pages 
                ORDER BY created_at DESC 
                LIMIT 20
            """)
            
            rows = cursor.fetchall()
            
            print(f"\n📋 Últimas {len(rows)} páginas:")
            print("-" * 80)
            
            for i, row in enumerate(rows, 1):
                url, title, quality, tier, words, status, created, updated = row
                
                print(f"{i:2d}. 🌐 {url}")
                print(f"    📄 {title}")
                print(f"    ⭐ Qualidade: {quality}/100 ({tier})")
                print(f"    📝 Palavras: {words}")
                print(f"    📅 Criado: {created}")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")


def ver_dominios():
    """Ver domínios scrapados."""
    print("\n🌍 DOMÍNIOS SCRAPADOS:")
    print("="*50)
    
    try:
        conn = sqlite3.connect('./data/webscraper.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT name, rate_limit_rps, max_pages, active, created_at
            FROM domains 
            ORDER BY created_at DESC
        """)
        
        rows = cursor.fetchall()
        
        if rows:
            for i, row in enumerate(rows, 1):
                name, rate_limit, max_pages, active, created = row
                status = "🟢 Ativo" if active else "🔴 Inativo"
                
                print(f"{i}. 🌐 {name}")
                print(f"   {status} | Rate: {rate_limit}/s | Max: {max_pages} páginas")
                print(f"   📅 {created}")
                print()
        else:
            print("   Nenhum domínio encontrado")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")


def ver_sessoes():
    """Ver sessões de crawling."""
    print("\n🔄 SESSÕES DE CRAWLING:")
    print("="*50)
    
    try:
        conn = sqlite3.connect('./data/webscraper.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT session_id, domain, status, urls_discovered, urls_processed,
                   urls_successful, urls_failed, started_at, completed_at
            FROM crawl_sessions 
            ORDER BY started_at DESC
            LIMIT 10
        """)
        
        rows = cursor.fetchall()
        
        if rows:
            for i, row in enumerate(rows, 1):
                session_id, domain, status, discovered, processed, successful, failed, started, completed = row
                
                print(f"{i}. 🆔 {session_id}")
                print(f"   🌐 Domínio: {domain}")
                print(f"   📊 Status: {status}")
                print(f"   📈 URLs: {discovered} descobertas, {processed} processadas")
                print(f"   ✅ Sucessos: {successful} | ❌ Falhas: {failed}")
                print(f"   📅 {started} → {completed or 'Em andamento'}")
                print()
        else:
            print("   Nenhuma sessão encontrada")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")


def ver_estatisticas():
    """Ver estatísticas gerais."""
    print("\n📊 ESTATÍSTICAS GERAIS:")
    print("="*50)
    
    try:
        conn = sqlite3.connect('./data/webscraper.db')
        cursor = conn.cursor()
        
        # Estatísticas das páginas
        cursor.execute("SELECT COUNT(*) FROM pages")
        total_pages = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(quality_score) FROM pages WHERE quality_score IS NOT NULL")
        avg_quality = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(word_count) FROM pages WHERE word_count IS NOT NULL")
        total_words = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT quality_tier, COUNT(*) FROM pages GROUP BY quality_tier")
        quality_dist = dict(cursor.fetchall())
        
        # Estatísticas dos domínios
        cursor.execute("SELECT COUNT(*) FROM domains")
        total_domains = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM crawl_sessions")
        total_sessions = cursor.fetchone()[0]
        
        print(f"📄 Total de páginas: {total_pages}")
        print(f"🌍 Total de domínios: {total_domains}")
        print(f"🔄 Total de sessões: {total_sessions}")
        print(f"⭐ Qualidade média: {avg_quality:.1f}/100")
        print(f"📝 Total de palavras: {total_words:,}")
        
        if quality_dist:
            print(f"\n📊 Distribuição de qualidade:")
            for tier, count in quality_dist.items():
                print(f"   {tier}: {count} páginas")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")


def main():
    print("🔍 VISUALIZADOR DE DADOS SCRAPADOS")
    print("="*50)
    
    # Verificar se banco existe
    import os
    if not os.path.exists('./data/webscraper.db'):
        print("❌ Banco de dados não encontrado!")
        print("💡 Execute primeiro: python exemplo_funcionando.py")
        return
    
    # Mostrar todas as informações
    ver_estatisticas()
    ver_paginas()
    ver_dominios()
    ver_sessoes()
    
    print("\n💡 OUTRAS FORMAS DE VER OS DADOS:")
    print("🌐 Dashboard: http://localhost:8080/dashboard")
    print("📊 API Stats: http://localhost:8080/stats")
    print("📄 API Pages: http://localhost:8080/pages")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Visualizador completo do site PVP Projects.
"""

import json
from pathlib import Path

def visualizar_site_completo():
    """Visualizar todas as páginas descobertas do site."""
    
    print("🕷️ ANÁLISE COMPLETA DO SEU SITE PVP PROJECTS")
    print("="*70)
    
    # Carregar dados do spider
    spider_file = Path("pvp_spider_completo/reports/spider_completo.json")
    urls_file = Path("pvp_spider_completo/reports/todas_urls.json")
    
    if not spider_file.exists():
        print("❌ Arquivo do spider não encontrado!")
        return
    
    with open(spider_file, 'r', encoding='utf-8') as f:
        spider_data = json.load(f)
    
    with open(urls_file, 'r', encoding='utf-8') as f:
        urls_data = json.load(f)
    
    # Estatísticas gerais
    stats = spider_data['statistics']
    pages = spider_data['pages']
    
    print(f"📊 RESUMO EXECUTIVO:")
    print(f"   🌐 Site: {spider_data['base_url']}")
    print(f"   📄 Páginas válidas: {stats['total_pages_crawled']}")
    print(f"   🔗 URLs descobertas: {stats['total_urls_discovered']}")
    print(f"   ⏱️ Tempo de crawling: ~1 minuto")
    
    # Organizar páginas por tipo
    pages_by_type = {}
    for url, page_data in pages.items():
        page_type = page_data['url_type']
        if page_type not in pages_by_type:
            pages_by_type[page_type] = []
        pages_by_type[page_type].append((url, page_data))
    
    print(f"\n📋 TODAS AS PÁGINAS ENCONTRADAS:")
    print("-" * 70)
    
    # 1. Homepage
    if 'homepage' in pages_by_type:
        print(f"\n🏠 HOMEPAGE (1 página):")
        for url, page_data in pages_by_type['homepage']:
            print(f"   ✅ {page_data['title']}")
            print(f"      📍 {url}")
            print(f"      📝 {page_data['word_count']} palavras")
            print(f"      🔗 {len(page_data['links'])} links")
    
    # 2. Serviços
    if 'service' in pages_by_type:
        print(f"\n🔧 SERVIÇOS ({len(pages_by_type['service'])} páginas):")
        for url, page_data in pages_by_type['service']:
            service_name = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
            print(f"   ✅ {service_name.upper()}")
            print(f"      📍 {url}")
            print(f"      📄 {page_data['title']}")
            print(f"      📝 {page_data['word_count']} palavras")
    
    # 3. Projetos
    if 'project' in pages_by_type:
        print(f"\n🏗️ PROJETOS ({len(pages_by_type['project'])} páginas):")
        
        # Separar projetos por categoria
        projetos_individuais = []
        categorias = []
        
        for url, page_data in pages_by_type['project']:
            if 'categoria' in url:
                categorias.append((url, page_data))
            else:
                projetos_individuais.append((url, page_data))
        
        # Mostrar categorias
        if categorias:
            print(f"   📂 CATEGORIAS ({len(categorias)}):")
            for url, page_data in categorias:
                categoria = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
                print(f"      📁 {categoria.upper()}")
                print(f"         📍 {url}")
        
        # Mostrar projetos individuais
        if projetos_individuais:
            print(f"   🏗️ PROJETOS INDIVIDUAIS ({len(projetos_individuais)}):")
            for url, page_data in projetos_individuais:
                projeto_name = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
                print(f"      🏠 {projeto_name.replace('-', ' ').title()}")
                print(f"         📍 {url}")
                print(f"         📝 {page_data['word_count']} palavras")
    
    # 4. Blog
    if 'blog' in pages_by_type:
        print(f"\n📝 BLOG ({len(pages_by_type['blog'])} páginas):")
        for url, page_data in pages_by_type['blog']:
            if url.endswith('/blog/'):
                print(f"   📋 PÁGINA PRINCIPAL DO BLOG")
                print(f"      📍 {url}")
            else:
                artigo = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
                print(f"   📄 {artigo.replace('-', ' ').title()}")
                print(f"      📍 {url}")
                print(f"      📝 {page_data['word_count']} palavras")
    
    # 5. Outras páginas
    outras_paginas = ['about', 'contact', 'page']
    for page_type in outras_paginas:
        if page_type in pages_by_type:
            type_name = {
                'about': 'SOBRE',
                'contact': 'CONTATO', 
                'page': 'OUTRAS PÁGINAS'
            }[page_type]
            
            print(f"\n📄 {type_name} ({len(pages_by_type[page_type])} páginas):")
            for url, page_data in pages_by_type[page_type]:
                if 'legal' in url:
                    legal_type = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
                    print(f"   ⚖️ {legal_type.upper()}")
                elif 'assets' in url:
                    if '.pdf' in url:
                        pdf_name = url.split('/')[-1]
                        print(f"   📄 PDF: {pdf_name}")
                    elif any(ext in url for ext in ['.png', '.jpg', '.jpeg']):
                        img_name = url.split('/')[-1]
                        print(f"   🖼️ IMAGEM: {img_name}")
                else:
                    print(f"   📄 {page_data['title'] or 'Página sem título'}")
                print(f"      📍 {url}")
    
    # Análise de conteúdo
    print(f"\n📊 ANÁLISE DE CONTEÚDO:")
    print("-" * 50)
    
    total_words = sum(page_data['word_count'] for page_data in pages.values())
    total_links = sum(len(page_data['links']) for page_data in pages.values())
    
    print(f"📝 Total de palavras: {total_words:,}")
    print(f"🔗 Total de links: {total_links:,}")
    print(f"📄 Média de palavras por página: {total_words // len(pages):,}")
    print(f"🔗 Média de links por página: {total_links // len(pages)}")
    
    # Páginas com mais conteúdo
    pages_by_words = sorted(pages.items(), key=lambda x: x[1]['word_count'], reverse=True)
    print(f"\n📈 PÁGINAS COM MAIS CONTEÚDO:")
    for i, (url, page_data) in enumerate(pages_by_words[:5], 1):
        page_name = url.split('/')[-2] if url.endswith('/') else url.split('/')[-1]
        print(f"   {i}. {page_name}: {page_data['word_count']:,} palavras")
    
    # URLs descobertas mas não crawled
    discovered_not_crawled = set(urls_data['discovered_urls']) - set(urls_data['crawled_urls'])
    if discovered_not_crawled:
        print(f"\n🔍 URLS DESCOBERTAS MAS NÃO CRAWLED ({len(discovered_not_crawled)}):")
        for i, url in enumerate(list(discovered_not_crawled)[:10], 1):
            print(f"   {i}. {url}")
        if len(discovered_not_crawled) > 10:
            print(f"   ... e mais {len(discovered_not_crawled) - 10} URLs")
    
    # Estrutura do site
    print(f"\n🗂️ ESTRUTURA DO SITE:")
    print("-" * 50)
    
    url_structure = {}
    for url in urls_data['crawled_urls']:
        parts = [p for p in url.replace(spider_data['base_url'], '').split('/') if p]
        if parts:
            first_level = parts[0]
            if first_level not in url_structure:
                url_structure[first_level] = 0
            url_structure[first_level] += 1
    
    for section, count in sorted(url_structure.items()):
        print(f"📁 /{section}: {count} páginas")
    
    # Tipos de arquivo
    file_types = {}
    for url in urls_data['crawled_urls']:
        if '.' in url.split('/')[-1]:
            ext = url.split('.')[-1].lower()
            if ext not in file_types:
                file_types[ext] = 0
            file_types[ext] += 1
    
    if file_types:
        print(f"\n📎 TIPOS DE ARQUIVO:")
        for ext, count in sorted(file_types.items()):
            print(f"   .{ext}: {count} arquivos")
    
    print(f"\n🎯 RESUMO FINAL:")
    print("="*50)
    print(f"✅ Site completamente mapeado!")
    print(f"📄 {len(pages)} páginas válidas encontradas")
    print(f"🔗 {len(urls_data['discovered_urls'])} URLs únicas descobertas")
    print(f"🏗️ Estrutura completa do site analisada")
    print(f"📊 Dados salvos em: pvp_spider_completo/")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print("🔍 1. Analise cada página individualmente")
    print("📊 2. Use os dados para otimização SEO")
    print("🎨 3. Replique estruturas para outros projetos")
    print("📈 4. Monitore performance de cada seção")


def main():
    """Função principal."""
    visualizar_site_completo()


if __name__ == "__main__":
    main()

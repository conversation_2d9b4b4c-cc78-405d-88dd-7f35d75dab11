<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON> - <PERSON> & Bob</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Background dinâmico com partículas flutuantes */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(1deg); }
        }
        
        /* Efeito de grade sutil */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                background: white;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
            .card {
                border: 1px solid #e2e8f0;
                box-shadow: none;
                background: white;
            }
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .card:hover::before {
            left: 100%;
        }
        
        .card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 
                0 32px 64px -12px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            border-color: rgba(99, 102, 241, 0.5);
        }
        
        .tag {
            display: inline-block;
            padding: 0.375rem 1rem;
            border-radius: 9999px;
            font-weight: 600;
            font-size: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.15);
        }
        
        /* Efeito de brilho no título */
        .title-glow {
            text-shadow: 
                0 0 20px rgba(59, 130, 246, 0.5),
                0 0 40px rgba(59, 130, 246, 0.3),
                0 0 60px rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Animação de entrada para os cards */
        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(30px) scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }
        
        .fade-in {
            animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
        
        /* Efeitos especiais para seções */
        .hero-section {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(99, 102, 241, 0.2);
            backdrop-filter: blur(20px);
        }
        
        .risk-card {
            position: relative;
            overflow: hidden;
        }
        
        .risk-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 30px 30px 0;
            border-color: transparent transparent rgba(239, 68, 68, 0.1) transparent;
        }
        
        /* Efeitos de hover para tabelas */
        .table-row {
            transition: all 0.3s ease;
            position: relative;
        }
        
        .table-row:hover {
            background: rgba(99, 102, 241, 0.05) !important;
            transform: translateX(8px);
        }
        
        /* Botão de impressão estilizado */
        .print-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(99, 102, 241, 0.4);
        }
        
        /* Efeitos para detalhes expansíveis */
        details {
            transition: all 0.3s ease;
        }
        
        details[open] {
            background: rgba(99, 102, 241, 0.02);
            border-radius: 8px;
            padding: 8px;
        }
        
        summary {
            transition: all 0.3s ease;
            border-radius: 6px;
            padding: 8px;
        }
        
        summary:hover {
            background: rgba(99, 102, 241, 0.05);
        }
    </style>
</head>
<body class="text-gray-800">

    <div class="max-w-6xl mx-auto p-4 sm:p-8">
        <!-- Botão de Impressão -->
        <div class="flex justify-end mb-8 no-print">
            <button onclick="window.print()" class="print-btn flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><polyline points="6 9 6 2 18 2 18 9"></polyline><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path><rect x="6" y="14" width="12" height="8"></rect></svg>
                Imprimir para PDF
            </button>
        </div>

        <!-- Cabeçalho Principal -->
        <header class="text-center mb-12 hero-section p-8 rounded-2xl">
            <h1 class="text-6xl font-bold title-glow mb-4">Papa & Bob</h1>
            <p class="text-3xl text-indigo-600 mt-2 font-semibold">Ata de Reunião de Alinhamento</p>
            <div class="mt-6 text-gray-600 text-xl">
                <span class="bg-white bg-opacity-80 px-4 py-2 rounded-full">19 de Agosto de 2025</span> 
                <span class="mx-2">•</span> 
                <span class="bg-white bg-opacity-80 px-4 py-2 rounded-full">19:00 - 01:30</span> 
                <span class="mx-2">•</span> 
                <span class="bg-white bg-opacity-80 px-4 py-2 rounded-full">Casa do Franco</span>
            </div>
        </header>

        <!-- Grid Principal -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Coluna Esquerda -->
            <div class="md:col-span-1 space-y-8">
                <!-- Participantes -->
                <div class="card p-6 fade-in" style="animation-delay: 0.1s;">
                    <h2 class="text-2xl font-bold mb-4 border-b pb-2 text-gray-900">Participantes</h2>
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-semibold text-green-600 mb-2">Presentes</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500 mr-2"><path d="M20 6 9 17l-5-5"/></svg>Franco</li>
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500 mr-2"><path d="M20 6 9 17l-5-5"/></svg>Felipe</li>
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500 mr-2"><path d="M20 6 9 17l-5-5"/></svg>PV (Pedro Vitor)</li>
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500 mr-2"><path d="M20 6 9 17l-5-5"/></svg>Victor</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold text-red-600 mb-2">Ausentes</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>Guilherme Pedro</li>
                                <li class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>GS</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Pauta da Reunião -->
                <div class="card p-6 fade-in" style="animation-delay: 0.2s;">
                    <h2 class="text-2xl font-bold mb-4 border-b pb-2 text-gray-900">Pauta da Reunião</h2>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Acompanhamento de Ações Anteriores</li>
                        <li>Requisitos para a Lei Rouanet</li>
                        <li>Definições Criativas e de Produção</li>
                        <li>Próximos Passos e Prazos</li>
                        <li>Planejamento Financeiro</li>
                        <li>Capacidade de Produção</li>
                        <li>Planejamento de Horas</li>
                        <li>Análise Legal e Societária</li>
                        <li>Riscos e Mitigações</li>
                        <li>Pendências (Parking Lot)</li>
                        <li>Encaminhamentos (Tabela de Ações)</li>
                        <li>Próxima Reunião</li>
                    </ol>
                </div>
            </div>

            <!-- Coluna Direita -->
            <div class="md:col-span-2 space-y-8">
                <!-- Deliberações -->
                <div class="card p-6 fade-in" style="animation-delay: 0.3s;">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white"><path d="M9 12l2 2 4-4"/><path d="M21 12c-1 0-2-1-2-2s1-2 2-2 2 1 2 2-1 2-2 2z"/><path d="M3 12c1 0 2-1 2-2s-1-2-2-2-2 1-2 2 1 2 2 2z"/><path d="M12 2v2"/><path d="M12 20v2"/><path d="m19 5-1.5 1.5"/><path d="m5.5 18.5L7 20"/><path d="m18.5 5L20 6.5"/><path d="m6.5 5.5L5 7"/></svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Deliberações por Tópico</h2>
                    </div>
                    <div class="space-y-6">
                        <!-- Tópico 1 -->
                        <details class="group" open>
                            <summary class="text-xl font-semibold cursor-pointer list-none flex justify-between items-center text-gray-800 group-hover:text-indigo-600">
                                1. Acompanhamento de Ações
                                <span class="transition-transform transform group-open:rotate-180">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                </span>
                            </summary>
                            <div class="mt-4 pl-4 border-l-2 border-indigo-200">
                                <p class="text-gray-600 mb-3"><strong class="text-gray-800">Resumo:</strong> Reunião com contadora concluída. Contrato social é pré-requisito para a proposta da Lei Rouanet. Estudo do edital em andamento.</p>
                                <p class="text-gray-600"><strong class="text-gray-800">Decisão:</strong> Priorizar definição do contrato social. Consolidar dúvidas do edital em um checklist unificado.</p>
                            </div>
                        </details>
                        <hr>
                        <!-- Tópico 2 -->
                        <details class="group">
                            <summary class="text-xl font-semibold cursor-pointer list-none flex justify-between items-center text-gray-800 group-hover:text-indigo-600">
                                2. Requisitos para a Lei Rouanet
                                <span class="transition-transform transform group-open:rotate-180">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                </span>
                            </summary>
                            <div class="mt-4 pl-4 border-l-2 border-indigo-200">
                                <p class="text-gray-600 mb-3"><strong class="text-gray-800">Resumo:</strong> Aprovada criação de Sociedade Limitada (LTDA). Franco será o ponto focal para o CNPJ. Apresentados escopos do MVP do App e plano de E-books.</p>
                                <p class="text-gray-600 mb-4"><strong class="text-gray-800">Decisão:</strong> Estrutura societária LTDA/Simples aprovada. Escopos validados para o dossiê da Lei Rouanet.</p>
                                <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
                                    <h4 class="font-semibold mb-2 text-indigo-800">Ações Criadas:</h4>
                                    <ul class="list-disc list-inside text-indigo-700 space-y-1">
                                        <li><span class="font-semibold">A-008:</span> Entregar portfólio/currículo (Prazo: 29/08)</li>
                                        <li><span class="font-semibold">A-009:</span> Detalhar plano do App (MVP) (Prazo: 05/09)</li>
                                        <li><span class="font-semibold">A-010:</span> Esboço e orçamento dos E-books (Prazo: 05/09)</li>
                                        <li><span class="font-semibold">A-011:</span> Criar checklist consolidado dos requisitos do edital.</li>
                                    </ul>
                                </div>
                            </div>
                        </details>
                        <hr>
                        <!-- Tópico 3 -->
                        <details class="group">
                            <summary class="text-xl font-semibold cursor-pointer list-none flex justify-between items-center text-gray-800 group-hover:text-indigo-600">
                                3. Definições Criativas (Bob e Piloto)
                                <span class="transition-transform transform group-open:rotate-180">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                </span>
                            </summary>
                            <div class="mt-4 pl-4 border-l-2 border-indigo-200">
                                <p class="text-gray-600 mb-3"><strong class="text-gray-800">Resumo:</strong> Persona de "Bob" revisada. Foco na análise de produção técnica, consumo de créditos e custos de software.</p>
                                <p class="text-gray-600 mb-4"><strong class="text-gray-800">Decisão:</strong> Realizar testes de voz para o Bob até 29/08. A voz escolhida será a base para o roteiro do piloto.</p>
                                <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
                                    <h4 class="font-semibold mb-2 text-indigo-800">Ação Criada:</h4>
                                    <ul class="list-disc list-inside text-indigo-700 space-y-1">
                                        <li><span class="font-semibold">A-012:</span> Gravar e validar testes de voz do Bob (Prazo: 29/08)</li>
                                    </ul>
                                </div>
                            </div>
                        </details>
                    </div>
                </div>

                 <!-- Riscos e Mitigações -->
                <div class="card p-6 fade-in" style="animation-delay: 0.4s;">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white"><path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-900">Análise de Riscos</h2>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 rounded-lg bg-red-50 border border-red-200 risk-card">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-4 flex-shrink-0 mt-1"><path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                            <div>
                                <h3 class="font-bold text-red-800">R-001: Atraso na Abertura do CNPJ</h3>
                                <p class="text-red-700"><strong>Impacto:</strong> Alto | <strong>Probabilidade:</strong> Média</p>
                                <p class="text-red-600 mt-1"><strong>Mitigação:</strong> Contato ativo com a contadora e pré-validação de documentos. (Dono: Franco)</p>
                            </div>
                        </div>
                        <div class="flex items-start p-4 rounded-lg bg-yellow-50 border border-yellow-200 risk-card">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-500 mr-4 flex-shrink-0 mt-1"><path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                            <div>
                                <h3 class="font-bold text-yellow-800">R-002: Inconformidade com a Lei Rouanet</h3>
                                <p class="text-red-700"><strong>Impacto:</strong> Alto | <strong>Probabilidade:</strong> Média</p>
                                <p class="text-red-600 mt-1"><strong>Mitigação:</strong> Checklist detalhado (A-011) e revisão cruzada. (Dono: Franco)</p>
                            </div>
                        </div>
                        <div class="flex items-start p-4 rounded-lg bg-yellow-50 border border-yellow-200 risk-card">
                             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-500 mr-4 flex-shrink-0 mt-1"><path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                            <div>
                                <h3 class="font-bold text-yellow-800">R-004: "Feature Creep" no App</h3>
                                <p class="text-yellow-700"><strong>Impacto:</strong> Médio | <strong>Probabilidade:</strong> Média</p>
                                <p class="text-yellow-600 mt-1"><strong>Mitigação:</strong> Foco estrito no MVP, definição de KPIs e congelamento de escopo. (Dono: PV)</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        
        <div class="page-break"></div>

        <!-- Tabelas de Planejamento -->
        <div class="mt-8 fade-in" style="animation-delay: 0.5s;">
            <div class="text-center mb-8">
                <h2 class="text-4xl font-bold text-gray-900 mb-2">Planejamento e Capacidade</h2>
                <div class="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 mx-auto rounded-full"></div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Capacidade de Produção -->
                <div class="card p-6">
                    <h3 class="text-2xl font-bold mb-4 text-gray-900">Capacidade de Produção</h3>
                    <p class="text-gray-600 mb-4">Hipótese: 1 episódio de 5 min consome 9.000 créditos. Meta: 4 episódios/mês.</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="p-3 font-semibold">Plano</th>
                                    <th class="p-3 font-semibold">Créditos/mês</th>
                                    <th class="p-3 font-semibold">Custo/Geração</th>
                                    <th class="p-3 font-semibold">Capacidade Estimada</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y">
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Veo-3 Pro</td>
                                    <td class="p-3">1,000</td>
                                    <td class="p-3">20 (Normal)</td>
                                    <td class="p-3">50 gerações "Normal"</td>
                                </tr>
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Veo-3 Ultra</td>
                                    <td class="p-3">12,500</td>
                                    <td class="p-3">20 (Plus)</td>
                                    <td class="p-3">625 gerações "Normal"</td>
                                </tr>
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Edra AI</td>
                                    <td class="p-3">1,250</td>
                                    <td class="p-3">N/D</td>
                                    <td class="p-3">N/D</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Planejamento de Horas -->
                <div class="card p-6">
                    <h3 class="text-2xl font-bold mb-4 text-gray-900">Planejamento de Horas (Anual)</h3>
                     <p class="text-gray-600 mb-4">Total com time base: 4.320 h/ano. Com Guilherme: 4.560 h/ano.</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="p-3 font-semibold">Membro</th>
                                    <th class="p-3 font-semibold">h/dia</th>
                                    <th class="p-3 font-semibold">h/semana</th>
                                    <th class="p-3 font-semibold">h/mês</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y">
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Papa</td>
                                    <td class="p-3">9</td>
                                    <td class="p-3">45</td>
                                    <td class="p-3">180</td>
                                </tr>
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">PV</td>
                                    <td class="p-3">3</td>
                                    <td class="p-3">15</td>
                                    <td class="p-3">60</td>
                                </tr>
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Franco</td>
                                    <td class="p-3">6</td>
                                    <td class="p-3">30</td>
                                    <td class="p-3">120</td>
                                </tr>
                                <tr class="hover:bg-gray-50 table-row">
                                    <td class="p-3">Guilherme (a confirmar)</td>
                                    <td class="p-3">-</td>
                                    <td class="p-3">5</td>
                                    <td class="p-3">20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-break"></div>

        <!-- Tabela de Ações -->
        <div class="mt-12 card p-6 sm:p-8 fade-in" style="animation-delay: 0.6s;">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white"><path d="M9 11H1l3-3-3-3v2a3 3 0 0 0 6 0V4a2 2 0 0 0-2-2H6"/><path d="M15 13h8l-3 3 3 3v-2a3 3 0 0 0-6 0v2a2 2 0 0 0 2 2h2"/></svg>
                </div>
                <h2 class="text-4xl font-bold text-gray-900 mb-2">Plano de Ação e Encaminhamentos</h2>
                <div class="w-32 h-1 bg-gradient-to-r from-green-500 to-blue-500 mx-auto rounded-full"></div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-left">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="p-4 font-semibold">ID</th>
                            <th class="p-4 font-semibold">Ação</th>
                            <th class="p-4 font-semibold">Responsável</th>
                            <th class="p-4 font-semibold">Prazo Final</th>
                            <th class="p-4 font-semibold">Status</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y">
                        <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-008</td>
                            <td class="p-4">Portfólio/currículo (1 pág.)</td>
                            <td class="p-4">PV, Franco, Felipe, Victor</td>
                            <td class="p-4">29/08/2025</td>
                            <td class="p-4"><span class="tag bg-blue-100 text-blue-800">Novo</span></td>
                        </tr>
                        <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-009</td>
                            <td class="p-4">Plano App (MVP) c/ cronograma/custos</td>
                            <td class="p-4">PV</td>
                            <td class="p-4">05/09/2025</td>
                            <td class="p-4"><span class="tag bg-blue-50 text-blue-800">Novo</span></td>
                        </tr>
                        <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-012</td>
                            <td class="p-4">Testes de voz do Bob</td>
                            <td class="p-4">Felipe</td>
                            <td class="p-4">29/08/2025</td>
                            <td class="p-4"><span class="tag bg-blue-100 text-blue-800">Novo</span></td>
                        </tr>
                        <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-017</td>
                            <td class="p-4">Definir quadro societário</td>
                            <td class="p-4">Franco + Sócios</td>
                            <td class="p-4">22/08/2025</td>
                            <td class="p-4"><span class="tag bg-yellow-100 text-yellow-800">Urgente</span></td>
                        </tr>
                        <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-020</td>
                            <td class="p-4">Indicar/contratar psicóloga e pedagoga</td>
                            <td class="p-4">Felipe, Guilherme, Franco</td>
                            <td class="p-4">29/08/2025</td>
                            <td class="p-4"><span class="tag bg-blue-100 text-blue-800">Novo</span></td>
                        </tr>
                         <tr class="hover:bg-gray-50 table-row">
                            <td class="p-4 font-medium">A-023</td>
                            <td class="p-4">Agendar 3 conversas com investidores</td>
                            <td class="p-4">Franco</td>
                            <td class="p-4">05/09/2025</td>
                            <td class="p-4"><span class="tag bg-blue-100 text-blue-800">Novo</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Próxima Reunião -->
        <div class="mt-12 text-center card p-8 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-500 text-white shadow-2xl fade-in relative overflow-hidden" style="animation-delay: 0.7s;">
            <!-- Efeito de partículas flutuantes -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-4 left-4 w-2 h-2 bg-white rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute top-8 right-8 w-3 h-3 bg-white rounded-full opacity-30 animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-8 left-12 w-1 h-1 bg-white rounded-full opacity-40 animate-pulse" style="animation-delay: 2s;"></div>
            </div>
            
            <h2 class="text-4xl font-bold mb-4 relative z-10">Próxima Reunião</h2>
            <div class="bg-white bg-opacity-20 rounded-full px-6 py-3 inline-block mb-4">
                <p class="text-3xl font-semibold text-white">02 de Setembro de 2025 - 19:30</p>
            </div>
            <p class="mt-6 max-w-3xl mx-auto text-indigo-100 text-lg leading-relaxed relative z-10">
                Pauta preliminar: Status do CNPJ, consolidação de portfólios, plano detalhado do App, orçamento dos E-books e resultado dos testes de voz do Bob.
            </p>
            
            <!-- Botão de ação -->
            <div class="mt-8">
                <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold py-3 px-6 rounded-full border border-white border-opacity-30 transition-all duration-300 hover:scale-105">
                    📅 Adicionar ao Calendário
                </button>
            </div>
        </div>

    </div>

    <!-- Script para efeitos adicionais -->
    <script>
        // Efeito de scroll suave
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Animação de entrada para elementos quando entram na viewport
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Aplicar aos cards
        document.querySelectorAll('.card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Efeito de parallax sutil no background
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('body::before');
            if (parallax) {
                const speed = scrolled * 0.5;
                document.body.style.setProperty('--scroll', `${speed}px`);
            }
        });

        // Efeito de hover nos cards
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-12px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>

</body>
</html>

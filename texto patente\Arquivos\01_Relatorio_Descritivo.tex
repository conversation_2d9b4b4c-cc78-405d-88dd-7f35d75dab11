\chead{\thepage\ / \pageref{fim_descriptivo}}
\cfoot{}

\section*{SISTEMA MULTI-AGENTE PARA GERAÇÃO AUTOMÁTICA DE SCRIPTS EM PLATAFORMAS DE MODELAGEM DE INFORMAÇÃO DA CONSTRUÇÃO (BIM)}

\noindent
\textbf{Campo da Invenção}

\pa A presente invenção insere-se no campo da automação em plataformas CAD/BIM e da inteligência artificial aplicada, mais especificamente refere-se a um sistema computacional multi-agente para geração automática de scripts no software Autodesk Revit a partir de comandos em linguagem natural. O sistema visa facilitar a automação de tarefas de modelagem e consulta para profissionais de arquitetura, engenharia e construção que não possuem conhecimento avançado em programação.

\noindent
\textbf{Estado da Técnica}

\pa No estado da técnica, são conhecidas soluções de inteligência artificial para geração automática de código a partir de linguagem natural, a exemplo dos documentos de patente US 11.960.867 B1 (Google) e CN 112947930 B (Nantong Univ.), que descrevem métodos genéricos de conversão de código. Entretanto, não há registros de invenções voltadas à automação de software CAD/BIM (como o Autodesk Revit) por meio de comandos em língua natural, tampouco que empreguem uma arquitetura multi-agente cooperativa com modelos de linguagem natural integrados.

\pa As soluções existentes exigem conhecimento de programação do usuário ou se limitam a converter código entre linguagens de programação. Nenhuma aborda a interpretação de comandos em português no contexto de projetos de construção civil (BIM) nem a geração de scripts específicos para a API do Revit. Além disso, não foi identificado na busca de anterioridade nenhum sistema que utilize múltiplos agentes de IA trabalhando em conjunto para garantir a precisão e eficiência na geração de código.

\pa Não há, até a presente data, ensinamentos na literatura patenteária de um sistema ou método que combine múltiplos agentes de IA para automatizar comandos em um software de modelagem BIM como o Revit, representando assim uma lacuna técnica significativa no estado da arte.

\noindent
\textbf{Problema Técnico e Objetivos da Invenção}

\pa Diante do exposto, verifica-se que profissionais da área AEC (Arquitetura, Engenharia e Construção) sem conhecimento avançado de programação enfrentam barreiras para automatizar tarefas no Revit, sendo obrigados a recorrer a programadores ou limitar-se a funcionalidades manuais. Esta limitação reduz significativamente a produtividade e impede a exploração plena das capacidades de automação disponíveis na plataforma BIM.

\pa Dessa forma, a presente invenção tem por objetivo fornecer um sistema computacional que permita a geração automática de scripts na plataforma Revit a partir de comandos em língua portuguesa, eliminando a necessidade de o usuário conhecer linguagens de programação. Outro objetivo da presente invenção é empregar uma arquitetura multi-agente cooperativa com modelos de linguagem de grande porte (LLMs), de forma a interpretar comandos complexos em linguagem natural e traduzi-los em código Python válido para a API do Revit, aumentando a precisão e eficiência do processo.

\noindent
\textbf{Caracterização Geral da Solução}

\pa Em linhas gerais, a presente invenção consiste em um sistema multi-agente de software que orquestra diferentes módulos de inteligência artificial para gerar automaticamente scripts em Python executáveis no Autodesk Revit. Um agente de linguagem natural (CommandParserAgent) interpreta o comando textual em português fornecido pelo usuário; um agente de geração de código (ScriptGeneratorAgent) baseado em modelos de linguagem de grande porte produz um script Python correspondente que utilize a API do Revit; um agente de banco de dados (DatabaseAgent) recupera exemplos relevantes para auxiliar na geração; e um agente coordenador (OrchestratorAgent) supervisiona todo o processo. A cooperação entre esses agentes garante que o comando do usuário seja compreendido corretamente e convertido em ações automatizadas no Revit de forma confiável.

\pa Diferentemente das soluções conhecidas, que operam de forma isolada ou requerem intervenção manual, a arquitetura proposta combina múltiplos agentes inteligentes trabalhando em conjunto. Essa abordagem multi-agente é inédita no contexto de automação de ferramentas BIM, permitindo escalabilidade e melhor adaptação a comandos complexos, o que não foi identificado em nenhuma patente anterior conforme as buscas realizadas.

\noindent
\textbf{Breve Descrição dos Desenhos}

\noindent
A Figura 1 apresenta o diagrama da arquitetura multi-agente do sistema, ilustrando os quatro agentes principais e suas interações.

\noindent
\textbf{Descrição Detalhada da Invenção}

\pa O sistema compreende quatro agentes principais: (i) OrchestratorAgent – responsável por coordenar o fluxo de dados e a execução sequencial dos demais agentes; (ii) CommandParserAgent – responsável por interpretar comandos em linguagem natural e extrair intenções e parâmetros estruturados; (iii) DatabaseAgent – responsável por recuperar exemplos de código relevantes utilizando busca semântica em banco de dados vetorial; e (iv) ScriptGeneratorAgent – responsável por gerar, validar e pós-processar scripts Python compatíveis com o ambiente PyRevit e IronPython 2.7.

\pa Os agentes comunicam-se entre si por meio de um protocolo interno estruturado: o CommandParserAgent recebe o comando em linguagem natural e produz uma representação estruturada (CommandSchema) contendo a intenção, elementos, parâmetros e consultas de busca. Esta representação é enviada ao DatabaseAgent, que realiza busca semântica em um banco de dados vetorial (Qdrant) para recuperar exemplos de código similares. O ScriptGeneratorAgent recebe tanto a representação estruturada quanto os exemplos recuperados, utilizando um modelo de linguagem de grande porte para gerar o script Python final. O OrchestratorAgent coordena todo este fluxo, mantém histórico de conversação e gerencia a execução sequencial.

\pa Exemplo de Implementação: Suponha que o usuário insere o comando: "Inserir uma tomada a 0,3m do piso na parede selecionada". O CommandParserAgent interpreta a intenção (inserir componente elétrico), elemento (tomada), parâmetros (altura=0,3m, contexto=parede selecionada). O DatabaseAgent busca exemplos similares de inserção de componentes elétricos. O ScriptGeneratorAgent utiliza essas informações para gerar um script Python que: (a) acessa a API do Revit, (b) identifica a parede selecionada, (c) cria uma instância de tomada nas coordenadas adequadas com altura de 0,3m do piso. Todo esse processo ocorre automaticamente, sem que o usuário precise conhecer programação.

\pa O processo de validação e pós-processamento implementado pelo ScriptGeneratorAgent inclui verificações sintáticas utilizando ast.parse, verificação da presença de função main(), contagem de linhas significativas, remoção de formatação Markdown indesejada, e inserção automática de esqueleto de código padrão quando necessário. Este processo iterativo com múltiplas tentativas (máximo de 3) garante a qualidade e executabilidade do código gerado.

\pa A integração com o ambiente Revit é realizada através de um botão personalizado no PyRevit que coleta o comando do usuário, executa o pipeline de geração como processo externo, e posteriormente carrega e executa o script gerado dentro do ambiente Revit. Esta arquitetura permite utilizar bibliotecas Python modernas no pipeline de geração enquanto mantém compatibilidade com o ambiente IronPython 2.7 do PyRevit.

\pa O sistema utiliza técnicas avançadas de Geração Aumentada por Recuperação (RAG) através do DatabaseAgent, que indexa exemplos de código utilizando embeddings semânticos de dimensionalidade combinada para código-fonte e documentação textual. A busca é realizada com filtros baseados em metadados (categoria Revit, tipo de operação) e mecanismos de fallback que relaxam restrições quando necessário.

\pa Portanto, a presente invenção provê uma solução técnica inédita ao viabilizar que usuários comuniquem-se com o Autodesk Revit em linguagem natural, delegando a execução das tarefas a um conjunto coordenado de agentes de IA, superando as limitações das ferramentas tradicionais de script ou macros. O sistema representa um avanço significativo na automação de plataformas BIM, democratizando o acesso a funcionalidades avançadas para profissionais sem conhecimento em programação.

\label{fim_descriptivo}

\chead{\thepage\ / \pageref{fim_descriptivo}}
\cfoot{}

\section*{SISTEMA MULTI-AGENTE PARA GERAÇÃO AUTOMÁTICA DE SCRIPTS EM PLATAFORMAS DE MODELAGEM DE INFORMAÇÃO DA CONSTRUÇÃO (BIM)}

\noindent
\textbf{Campo da Invenção}

\pa A presente invenção insere-se no campo da automação em plataformas CAD/BIM e da inteligência artificial aplicada, mais especificamente refere-se a um sistema computacional multi-agente para geração automática de scripts no software Autodesk Revit a partir de comandos em linguagem natural. O sistema emprega uma arquitetura multiagente coordenada que utiliza modelos de linguagem de larga escala (LLMs) para gerar scripts de automação Python, com aplicação específica na interface de programação de aplicações (API) do software Revit através da extensão PyRevit. O sistema visa traduzir comandos em linguagem natural, fornecidos por usuários, em scripts Python executáveis, facilitando tarefas de modelagem e consulta dentro do ambiente Revit para profissionais de arquitetura, engenharia e construção.

\noindent
\textbf{Estado da Técnica}

\pa No estado da técnica, são conhecidas soluções de inteligência artificial para geração automática de código a partir de linguagem natural, a exemplo dos documentos de patente US 11.960.867 B1 (Google) e CN 112947930 B (Nantong Univ.), que descrevem métodos genéricos de conversão de código. As soluções atualmente empregadas para automação de tarefas no software Revit englobam predominantemente a criação manual de scripts em Python, frequentemente utilizando a extensão pyRevit ou a plataforma de programação visual Dynamo. Outras abordagens incluem integrações mais simples com modelos de linguagem por meio de suas APIs ou métodos pontuais para geração de código, os quais, no entanto, apresentam limitações contextuais e de validação.

\pa Entretanto, não há registros de invenções voltadas à automação de software CAD/BIM (como o Autodesk Revit) por meio de comandos em língua natural, tampouco que empreguem uma arquitetura multi-agente cooperativa com modelos de linguagem natural integrados. No estado da técnica conhecido, não foram identificados sistemas que empreguem uma arquitetura multiagente orquestrada por LLMs, combinando mecanismos de cache semântico, Geração Aumentada por Recuperação (RAG - Retrieval-Augmented Generation), e um ciclo iterativo de validação de scripts diretamente integrado e contextualizado ao ambiente Revit.

\pa As soluções existentes exigem conhecimento de programação do usuário ou se limitam a converter código entre linguagens de programação. Nenhuma aborda a interpretação de comandos em português no contexto de projetos de construção civil (BIM) nem a geração de scripts específicos para a API do Revit. As soluções existentes tendem a ser mais genéricas ou focadas em formatos abertos (como IFC), carecendo da especificidade e da profundidade de integração com a API nativa do Revit e do fluxo autônomo de validação e correção proposto pela presente invenção.

\pa Não há, até a presente data, ensinamentos na literatura patenteária de um sistema ou método que combine múltiplos agentes de IA para automatizar comandos em um software de modelagem BIM como o Revit, representando assim uma lacuna técnica significativa no estado da arte.

\noindent
\textbf{Problema Técnico e Objetivos da Invenção}

\pa Diante do exposto, verifica-se que profissionais da área AEC (Arquitetura, Engenharia e Construção) sem conhecimento avançado de programação enfrentam barreiras para automatizar tarefas no Revit, sendo obrigados a recorrer a programadores ou limitar-se a funcionalidades manuais. O problema técnico central reside na elevada curva de aprendizado e na necessidade de conhecimento avançado em programação por parte de arquitetos, engenheiros e outros projetistas para automatizar tarefas repetitivas ou complexas.

\pa Dessa forma, a presente invenção tem por objetivo fornecer um sistema computacional que permita a geração automática de scripts na plataforma Revit a partir de comandos em língua portuguesa, eliminando a necessidade de o usuário conhecer linguagens de programação. A invenção visa reduzir essa barreira, permitindo que usuários descrevam suas necessidades em linguagem natural e recebam scripts Python válidos, contextuais e prontos para execução, otimizando o tempo de projeto e aumentando a eficiência ao delegar a geração de código a um sistema inteligente. Outro objetivo da presente invenção é empregar uma arquitetura multi-agente cooperativa com modelos de linguagem de grande porte (LLMs), de forma a interpretar comandos complexos em linguagem natural e traduzi-los em código Python válido para a API do Revit, aumentando a precisão e eficiência do processo.

\noindent
\textbf{Caracterização Geral da Solução}

\pa Em linhas gerais, a presente invenção consiste em um sistema multi-agente de software que orquestra diferentes módulos de inteligência artificial para gerar automaticamente scripts em Python executáveis no Autodesk Revit. O sistema é estruturado como um pipeline de quatro agentes principais coordenados por um agente central: OrchestratorAgent que gerencia o fluxo de informações e execução sequencial; CommandParserAgent que interpreta comandos em linguagem natural; DatabaseAgent que recupera exemplos relevantes através de busca semântica; e ScriptGeneratorAgent que gera e valida o código Python final utilizando modelos de linguagem de grande porte.

\pa Diferentemente das soluções conhecidas, que operam de forma isolada ou requerem intervenção manual, a arquitetura proposta combina múltiplos agentes inteligentes trabalhando em conjunto com técnicas avançadas de Geração Aumentada por Recuperação (RAG), validação iterativa e integração nativa com o ambiente PyRevit. Essa abordagem multi-agente é inédita no contexto de automação de ferramentas BIM, permitindo escalabilidade e melhor adaptação a comandos complexos, o que não foi identificado em nenhuma patente anterior conforme as buscas realizadas.

\noindent
\textbf{Breve Descrição dos Desenhos}

\noindent
A Figura 1 apresenta o diagrama da arquitetura multi-agente do sistema, ilustrando os quatro agentes principais e suas interações coordenadas.

\noindent
\textbf{Descrição Detalhada da Invenção}

\pa
A invenção consiste em um sistema computacional multiagente para geração automática de scripts Python para o software Revit 2024, operando por meio da extensão PyRevit. O sistema é estruturado como um pipeline de quatro agentes principais coordenados por um agente central:
OrchestratorAgent: Recebe o comando do usuário em linguagem natural e gerencia o fluxo de informações e a execução sequencial dos demais agentes, além de manter um histórico da conversação.
CommandParserAgent: Interpreta a intenção e os parâmetros do comando em linguagem natural, transformando-os em uma estrutura de dados formal denominada CommandSchema.
DatabaseAgent: Consulta um banco de dados vetorial (Qdrant) utilizando embeddings semânticos para recuperar exemplos de código Revit similares ao comando interpretado.
ScriptGeneratorAgent: Gera o código-fonte Python completo, compatível com IronPython 2.7 e PyRevit 4.8.16, utilizando o CommandSchema e os exemplos de código recuperados.
O script Python resultante é então retornado ao ambiente Revit, onde pode ser executado pelo PyRevit para realizar a ação desejada no modelo BIM. O sistema não executa automaticamente as ações no Revit durante a geração do script, apenas produz o script para execução posterior.

\pa % [005]
O sistema opera como um pipeline sequencial coordenado pelo OrchestratorAgent. A interação inicia-se com o usuário inserindo um comando em linguagem natural no ambiente Revit, por exemplo, através de uma interface fornecida por um botão personalizado no PyRevit. Este comando é então enviado a um pipeline externo implementado em Python.

\pa % [006] \textbf{6.1. Recebimento e Orquestração Inicial do Comando}
O OrchestratorAgent é o primeiro a receber o comando. Ele pode obter essa entrada de um arquivo (ex: input.txt) ou como um parâmetro de linha de comando. O agente registra o comando recebido em uma memória de conversação e inicia o log do processamento.

\pa % [007] \textbf{6.2. CommandParserAgent – Interpretação de Comandos}
O OrchestratorAgent aciona o CommandParserAgent, cuja responsabilidade é traduzir o comando em linguagem natural para uma estrutura formal, o CommandSchema. Este agente utiliza um Modelo de Linguagem de Larga Escala (LLM) para analisar o texto, identificar a intenção, o(s) objeto(s) e os parâmetros do comando.
A estrutura CommandSchema, definida utilizando Pydantic, contém campos como:
command\_type: Um tipo enumerado (Enum) que classifica a operação requisitada (ex: InserirFamilia, RemoverElemento, ModificarElemento, ListarElementos, ListarParametros, CriarElemento, Selecionar, Medir, GerarVista, Desconhecido). Essa classificação orienta a geração do script e a filtragem na busca de exemplos.
action: String com o verbo de ação principal (ex: "inserir", "remover").
elemento: O objeto principal do comando (ex: "tomada", "porta").
categoria: A categoria Revit do elemento, se inferida (ex: "Electrical Fixtures" para "tomada").
contexto: Informações contextuais da ação (ex: "parede selecionada", "todo o projeto").
parametros: Dicionário com parâmetros adicionais, como valores numéricos e unidades (ex: \{"altura": 0.3, "unidade": "metros"\}).
search\_queries: Lista de strings de consulta derivadas do comando para uso do DatabaseAgent (ex: ["inserir tomada parede", "colocar componente elétrico altura específica"]).
Para realizar a extração, o CommandParserAgent constrói um prompt para o LLM, contendo instruções de sistema e exemplos, visando obter uma saída em formato JSON que corresponda ao CommandSchema. A resposta JSON do LLM é então parseada e validada pela classe Pydantic.
Em caso de falhas no parsing (JSON malformado, falha na validação do esquema Pydantic), o agente implementa mecanismos de fallback. Pode tentar montar um CommandSchema parcial com command\_type definido como DESCONHECIDO, utilizando o texto original do comando nas search\_queries. Se totalmente inviável, retorna um CommandSchema genérico indicando falha na interpretação. O OrchestratorAgent utiliza um indicador booleano de sucesso retornado pelo parser para decidir se prossegue com o pipeline ou se aborta, gerando um script de erro.

\pa % [008] \textbf{6.3. DatabaseAgent – Recuperação de Exemplos (RAG)}
Com o CommandSchema validado, o OrchestratorAgent invoca o DatabaseAgent. Este agente realiza uma busca semântica em um banco de dados vetorial (Qdrant) para recuperar exemplos de código Revit relevantes. Esses exemplos são utilizados posteriormente pelo ScriptGeneratorAgent no processo de Geração Aumentada por Recuperação (RAG).
O banco de dados Qdrant contém uma coleção (ex: "scripts\_revit") de embeddings de scripts Revit. Os vetores possuem dimensionalidade combinada (ex: 3077 dimensões), representando embeddings do código Python, da documentação textual associada e metadados adicionais. Cada entrada no banco armazena, além do vetor, um payload com o código Python do script, sua docstring, nome do arquivo de origem e metadados como categoria Revit envolvida ("categoria"), tipo de script ("tipo\_script": inserção, modificação, etc.) e chamadas de API relevantes ("api\_calls").
O processo de busca do DatabaseAgent envolve:
Geração de Consultas de Busca: Extrai termos dos campos do CommandSchema ( action, elemento, search\_queries, etc.) para formar as queries textuais.
Geração de Embeddings da Consulta: Converte cada query textual em um vetor numérico (embedding) usando um modelo apropriado (ex: OpenAI text-embedding-ada-002), com a mesma dimensionalidade dos vetores no banco.
Busca Vetorial com Filtros: Consulta o Qdrant utilizando os embeddings das queries. A busca pode ser realizada em paralelo para múltiplas queries. Aplica-se um limiar de similaridade (ex: similarity\_threshold = 0.2) e, opcionalmente, filtros por metadados baseados no CommandSchema (ex: filtrar por categoria ou tipo\_script). Se a filtragem não retornar resultados, mecanismos de fallback são acionados, como a redução do limiar de similaridade (ex: fallback\_threshold = 0.1) ou a remoção de filtros. Existe também uma lógica para priorizar exemplos marcados como "gold\_standard" (alta qualidade).
Processamento dos Resultados: Os resultados da busca são combinados, deduplicados (baseado em um identificador único, como um hash do código), ordenados por score de similaridade (decrescente), e limitados a um número máximo (ex: max\_results = 5).
O DatabaseAgent retorna uma lista (potencialmente vazia) de exemplos de código, onde cada item contém o código e metadados relevantes. O OrchestratorAgent registra a quantidade de exemplos encontrados. Se nenhum exemplo for encontrado, um aviso é logado, mas o pipeline prossegue, pois o ScriptGeneratorAgent pode gerar código sem exemplos.

\pa % [009] \textbf{6.4. ScriptGeneratorAgent – Geração do Código Python}
Com o CommandSchema e os exemplos recuperados, o OrchestratorAgent aciona o ScriptGeneratorAgent. Este agente é responsável por gerar o script Python final, compatível com IronPython 2.7 e PyRevit, que executa a tarefa solicitada.
A geração do script envolve a construção de um prompt detalhado para um LLM, que inclui:
Template de Sistema (System Prompt): Contém instruções fixas e regras obrigatórias para a geração do código. Essas regras especificam, por exemplo:O script deve ser autocontido.
Deve iniciar com cabeçalho padrão (\# -\*- coding: utf-8 -\*-, \_\_title\_\_, \_\_doc\_\_).
Ordem específica para importações (clr, referências RevitAPI, PyRevit, outros).
Definição de variáveis globais como doc e uidoc.
Uso de transações PyRevit (with revit.Transaction("Nome"): ...) para modificações no modelo.
Compatibilidade com IronPython 2.7 (evitar f-strings, type hints).
Uso de forms.alert (PyRevit) para interação com o usuário.
Uso de um utilitário U (de utils.compat\_revit) para conversão de unidades, evitando UnitUtils direto da API Revit.
Evitar o módulo RevitServices.
A resposta do LLM deve ser apenas o código-fonte, sem explicações ou formatação Markdown.
O script deve sempre conter uma função main() e uma chamada a ela (if \_\_name\_\_ == "\_\_main\_\_": main()).
Conteúdo do Usuário (User Prompt): Construído dinamicamente, inclui:Exemplos de código relevantes: Se o DatabaseAgent retornou exemplos, eles são formatados (ex: numerados, em blocos de código Markdown) e incluídos. Exemplos longos podem ser truncados (ex: primeiras 30 linhas). Se nenhum exemplo foi encontrado, uma mensagem padrão informa o LLM para usar seu conhecimento da API Revit.
Resumo do comando interpretado: Uma representação textual compacta do CommandSchema (ex: "Tipo: InserirFamilia | Ação: inserir | Elem.: tomada...") é fornecida para que o LLM saiba exatamente o que foi compreendido da solicitação do usuário.
O ScriptGeneratorAgent envia essas mensagens (System e User) ao LLM e recebe uma string como resposta, que é o código gerado.
Após receber a resposta do LLM, o agente realiza um processo de validação e pós-processamento em um loop de tentativas (ex: max\_retries = 3):
Validação Sintática: Tenta fazer o parse do código usando ast.parse. Se ocorrer SyntaxError, a tentativa falha e uma nova é iniciada.
Presença da função main(): Verifica se o texto "def main(" está presente. Se ausente, a tentativa falha.
Script não vazio/trivial: Conta o número de linhas significativas (excluindo vazias, comentários, imports). Se abaixo de um limiar (ex: 5 linhas), considera-se o script vazio e a tentativa falha.
Remoção de formatação indesejada: Remove marcações Markdown (\`\`\`python ... \`\`\`) do código bruto retornado pelo LLM usando uma função como \_strip\_markdown.
Inserção de Esqueleto Padrão (Skeleton): Se o código gerado não começar com o cabeçalho \# -\*- coding: utf-8 -\*- (indicando que o LLM pode ter retornado apenas o corpo da função), o agente injeta o código gerado dentro de um esqueleto padrão (\_SKELETON). Esse esqueleto contém toda a estrutura básica necessária (encoding, imports, doc, uidoc, main() vazia, chamada ao main), garantindo a conformidade estrutural. O código do LLM é inserido no local apropriado dentro da função main() do esqueleto, com indentação correta.
Garantir import de utilitário de unidades: Verifica se a importação do utilitário U (from utils.compat\_revit import U) está presente, caso o código gerado o utilize. Se ausente, a linha de import é inserida na posição correta (após outros imports do Revit API).
Verificação de Sintaxe Final: Após as correções, uma nova verificação sintática (ast.parse) é realizada, capturando também IndentationError.
Se todas as verificações passarem, o código é considerado válido e retornado ao OrchestratorAgent. Se uma tentativa falhar, o agente registra o erro e inicia uma nova tentativa, até o limite de max\_retries. Se todas as tentativas falharem, uma exceção ScriptGenerationError é lançada, que será capturada pelo OrchestratorAgent.

\pa % [010] \textbf{6.5. Orquestração Final e Entrega do Resultado}
O OrchestratorAgent recebe o script final (ou a notificação de erro) do ScriptGeneratorAgent.
Em caso de sucesso: O OrchestratorAgent salva o script\_content em um arquivo de saída (ex: output.txt ou caminho especificado), utilizando codificação UTF-8. Ele registra uma mensagem de sucesso na memória de conversação e nos logs, incluindo o tempo total de processamento, e retorna o caminho do arquivo de saída como indicação de sucesso.
Em caso de falha (seja no parsing, na geração, ou uma exceção inesperada): O OrchestratorAgent registra a falha na memória e nos logs. Ele então gera e salva um script de erro no caminho de saída. Esse script de erro é um arquivo Python que, quando executado, não modifica o modelo Revit, mas exibe ou loga uma mensagem indicando que a geração falhou e o motivo. O OrchestratorAgent retorna uma mensagem de erro.
O OrchestratorAgent também mantém uma ConversationMemory, que armazena um histórico das interações (comandos do usuário, mensagens do sistema, etapas, erros) com timestamps e dados opcionais. Essa memória tem um limite de entradas e pode ser usada para depuração ou futuras funcionalidades de conversação multi-turno (atualmente, cada comando é tratado isoladamente).

\pa % [011] \textbf{6.6. Execução no Revit (Fora do Pipeline de Geração)}
Após o pipeline gerar e salvar o script (seja de sucesso ou de erro), o ambiente PyRevit no Revit é responsável por carregar e executar o arquivo Python resultante. Esta etapa efetua de fato as ações no modelo Revit. Um botão personalizado no PyRevit pode automatizar o disparo do pipeline externo e a subsequente execução do script gerado.

\pa % [012] \textbf{6.7. Fluxo no Ponto de Entrada (main.py) e Integração PyRevit}
O main.py serve como ponto de entrada para o pipeline externo. Ele é responsável por:
Carregar configurações de variáveis de ambiente (ex: chaves de API para LLMs, modelos a serem usados, caminhos Qdrant) de um arquivo .env.
Configurar o sistema de logging, incluindo um arquivo de log detalhado (ex: logs/pipeline\_full\_YYYYMMDD\_HHMMSS.log) que registra todas as mensagens, incluindo nível DEBUG.
Processar argumentos de linha de comando (ex: --command, --input-file, --output, --debug, --llm para seleção do provedor de LLM).
Inicializar as instâncias dos agentes (CommandParserAgent, DatabaseAgent, ScriptGeneratorAgent), injetando o cliente LLM configurado (ex: Anthropic Claude, OpenAI GPT, Google Gemini) e outras dependências.
Inicializar o OrchestratorAgent, passando as instâncias dos outros agentes.
Ler o comando do usuário (do arquivo de entrada ou argumento CLI) e chamar o método execute\_command do OrchestratorAgent, fornecendo o comando e o caminho do arquivo de saída.
Verificar o resultado retornado pelo OrchestratorAgent (caminho do arquivo em caso de sucesso, mensagem de erro em caso de falha) e finalizar com um código de saída apropriado (0 para sucesso, 1 para erro).
A integração com o PyRevit é feita através de um script associado a um botão na interface do Revit. Esse script PyRevit:
Coleta o comando em linguagem natural do usuário e a escolha do LLM através de diálogos forms.
Salva o comando em um arquivo input.txt.
Executa o main.py como um processo externo (subprocess.Popen), passando os caminhos de entrada/saída e o provedor de LLM como argumentos. Um timeout é definido para essa execução externa (ex: 300 segundos).
Aguarda o término do processo externo e coleta seu código de saída, stdout e stderr.
Se o processo externo indicou sucesso e o arquivo output.txt foi gerado e não está vazio, o script PyRevit lê o conteúdo do script Python gerado.
Realiza pequenas adaptações de compatibilidade no código lido (ex: ajustar sintaxe de except Exception as e: para except Exception, e:, garantir declaração de encoding).
Executa o script Python adaptado dentro do contexto do PyRevit usando exec(), em um escopo global que inclui referências como revit, doc, uidoc, forms.
Captura exceções durante a execução no Revit e as reporta ao usuário.
Fornece feedback ao usuário em várias etapas através de notificações (toasts) e alertas.
Esta arquitetura de execução externa para o pipeline principal e interna para a execução do script gerado permite o uso de bibliotecas Python modernas no pipeline (que rodam em CPython) enquanto o script final é compatível com o ambiente IronPython 2.7 do PyRevit.

\pa % [013] \textbf{6.8. Tratamento de Erros Comuns na Geração}
O sistema é projetado para lidar ou mitigar erros comuns:
Erros de Sintaxe Python: O ScriptGeneratorAgent detecta muitos via ast.parse. Erros de sintaxe específicos do Python 2.7 não pegos podem falhar no runtime do PyRevit.
Código Incompleto ou Vazio: Checagens por presença de main() e número mínimo de linhas significativas visam evitar isso.
Importações Ausentes: A importação de U é garantida. Outras podem causar NameError no runtime.
Uso Incorreto da API Revit: A qualidade dos exemplos recuperados (RAG) e o conhecimento do LLM são cruciais aqui. Erros lógicos (ex: "faces mal colocadas", uso de método inadequado para inserir família) não são detectados estaticamente, mas apenas no runtime ou por inspeção do resultado no modelo.
Exceções de Runtime no Revit: O sistema não executa o código durante a geração, então exceções como acesso a elementos inexistentes ou falta de seleção prévia só aparecem quando o script é rodado no Revit. O botão PyRevit tenta capturar e reportar essas exceções ao usuário.
Interpretação Ambígua: Se o CommandParserAgent interpretar mal, o script gerado pode não atender à intenção original.

\noindent
\textbf{Exemplos de concretizações da invenção}

\pa % [014]
As seções anteriores, em particular a "Descrição da Invenção" (itens 6.1 a 6.8), já detalham as concretizações da invenção, descrevendo o funcionamento de cada agente e do sistema como um todo. A interação entre os agentes, a estrutura de dados `CommandSchema`, os mecanismos de busca e filtragem no `DatabaseAgent`, as regras e o processo de validação no `ScriptGeneratorAgent`, e a coordenação pelo `OrchestratorAgent` constituem a forma como a invenção é concretizada. Um exemplo prático de comando do usuário, como "Inserir uma tomada a 0.3m do piso na parede selecionada", percorreria todo o pipeline descrito, resultando em um script Python específico para essa tarefa.

\pa % [015]
A forma preferida de concretizar a invenção envolve a utilização de um modelo de linguagem de larga escala (LLM) como o Anthropic Claude ou similar para as tarefas de interpretação de comando e geração de código, devido à sua capacidade de processar linguagem natural e seguir instruções complexas. A utilização do banco de dados vetorial Qdrant para a recuperação de exemplos (RAG) é também uma característica preferencial, pois aumenta a relevância e a precisão dos scripts gerados ao fornecer contexto específico do Revit ao LLM. A integração com o ambiente Revit através de um botão PyRevit que dispara um processo Python externo para a geração do script e depois executa o resultado internamente é a modalidade de uso preferida, balanceando poder de processamento externo com execução integrada.

\pa Testes comparativos demonstram que a abordagem multiagente com RAG e validação iterativa produz scripts mais robustos e alinhados com as intenções do usuário do que abordagens mais simples que dependem unicamente do conhecimento base do LLM sem contexto adicional ou validação rigorosa. Por exemplo, a capacidade do ScriptGeneratorAgent de injetar um esqueleto de código padrão e garantir importações essenciais corrige falhas comuns de LLMs, enquanto a busca semântica do DatabaseAgent provê exemplos que guiam o LLM a usar construções de API Revit mais adequadas.

\pa Portanto, a presente invenção provê uma solução técnica inédita ao viabilizar que usuários comuniquem-se com o Autodesk Revit em linguagem natural, delegando a execução das tarefas a um conjunto coordenado de agentes de IA, superando as limitações das ferramentas tradicionais de script ou macros. O sistema representa um avanço significativo na automação de plataformas BIM, democratizando o acesso a funcionalidades avançadas para profissionais sem conhecimento em programação e estabelecendo um novo paradigma de interação homem-máquina em ambientes de modelagem tridimensional.

\label{fim_descriptivo}
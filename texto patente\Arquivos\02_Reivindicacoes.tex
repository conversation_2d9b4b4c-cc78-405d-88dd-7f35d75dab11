\setcounter{page}{1}
\chead{\thepage\ / \pageref{fim_reinv}}
\cfoot{}
\begin{center}
\section*{REIVINDICAÇÕES}
\end{center}

\begin{enumerate}[wide, labelindent=0pt]
    \item Sistema computacional multi-agente para geração automática de scripts em ambiente Autodesk Revit, \textbf{caracterizado por} compreender:
    (a) um agente de interpretação de linguagem natural (CommandParserAgent), configurado para receber comandos textuais em língua natural de um usuário e produzir, a partir destes, uma representação estruturada da tarefa a ser executada no Revit;
    (b) um agente de banco de dados (DatabaseAgent), configurado para recuperar exemplos de código relevantes de um banco de dados vetorial utilizando busca semântica baseada na representação estruturada;
    (c) um agente gerador de código (ScriptGeneratorAgent), integrado a um modelo de linguagem de ampla escala, configurado para gerar automaticamente código executável (script em linguagem Python) adequado à API do Revit, com base na representação estruturada e nos exemplos recuperados;
    (d) um agente coordenador (OrchestratorAgent), configurado para orquestrar o fluxo de dados e a execução sequencial dos agentes (a), (b) e (c);
    onde os agentes (a), (b), (c) e (d) comunicam-se entre si de forma cooperativa, de maneira que um comando em linguagem natural do usuário seja interpretado e convertido em um script de automação no Revit sem intervenção manual.

    \item Sistema computacional multi-agente, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que o agente de interpretação de linguagem natural (CommandParserAgent) utiliza um modelo de linguagem de larga escala para extrair intenção de comando, elementos, parâmetros e consultas de busca, produzindo uma estrutura de dados (CommandSchema) validada por esquemas Pydantic.

    \item Sistema computacional multi-agente, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que o agente de banco de dados (DatabaseAgent) utiliza um banco de dados vetorial (Qdrant) com embeddings semânticos de dimensionalidade combinada para código-fonte e documentação textual, aplicando filtros baseados em metadados e mecanismos de fallback para busca de exemplos relevantes.

    \item Sistema computacional multi-agente, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que o agente gerador de código (ScriptGeneratorAgent) implementa validação sintática, verificação estrutural, correção automática e inserção de esqueleto de código padrão, garantindo compatibilidade com IronPython 2.7 e PyRevit.

    \item Sistema computacional multi-agente, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que o agente coordenador (OrchestratorAgent) mantém histórico de conversação, gerencia tratamento de erros e produz scripts informativos em caso de falha no pipeline de geração.

    \item Processo implementado por computador para geração automatizada de scripts em ambiente Autodesk Revit, \textbf{caracterizado por} compreender as etapas de:
    (a) receber um comando em linguagem natural de um usuário;
    (b) interpretar o referido comando utilizando um primeiro modelo de linguagem de larga escala para extrair intenção, elementos e parâmetros, resultando em uma representação estruturada;
    (c) recuperar exemplos de código relevantes de um banco de dados vetorial utilizando busca semântica baseada na representação estruturada;
    (d) gerar um script Python utilizando um segundo modelo de linguagem de larga escala, recebendo como entrada a representação estruturada e os exemplos recuperados;
    (e) validar e pós-processar o script gerado através de verificações sintáticas, estruturais e aplicação de correções automáticas;
    (f) salvar o script validado em arquivo para execução no ambiente PyRevit do Autodesk Revit.

    \item Processo, de acordo com a reivindicação 6, \textbf{caracterizado adicionalmente pelo} fato de que a etapa de validação e pós-processamento (e) compreende um loop de múltiplas tentativas de geração com máximo de três iterações, aplicando verificações de sintaxe Python, presença de função main() e inserção de esqueleto de código quando necessário.

    \item Processo, de acordo com a reivindicação 6, \textbf{caracterizado adicionalmente pelo} fato de que a recuperação de exemplos (c) utiliza filtros baseados em metadados incluindo categoria do elemento Revit e tipo de operação, com mecanismos de fallback que relaxam restrições de busca quando nenhum resultado relevante é encontrado.

\end{enumerate}

\label{fim_reinv}

\setcounter{page}{1}
\chead{\thepage\ / \pageref{fim_reinv}}
\cfoot{}
\begin{center}
 \section*{REIVINDICAÇÕES}
\end{center}

\begin{enumerate}[wide, labelindent=0pt]
    \item Processo implementado por computador para geração automatizada de scripts em ambiente Autodesk Revit, \textbf{caracterizado por} compreender as etapas de:
    a.  Receber um comando em linguagem natural de um usuário;
    b.  Interpretar o referido comando em linguagem natural utilizando um primeiro modelo de linguagem de larga escala (LLM) para extrair uma intenção de comando e parâmetros associados, resultando em uma representação estruturada do comando;
    c.  <PERSON>cupera<PERSON>, a partir de um banco de dados vetorial, um ou mais exemplos de código relevantes para a referida representação estruturada do comando, onde os referidos exemplos de código são indexados por embeddings semânticos;
    d.  Gerar um script de automação em linguagem Python utilizando um segundo modelo de linguagem de larga escala (LLM), o qual recebe como entrada a referida representação estruturada do comando e os referidos exemplos de código recuperados, sendo o referido script de automação compatível com um ambiente de execução específico da plataforma BIM (IronPython 2.7 para PyRevit);
    e.  Validar e pós-processar o referido script de automação gerado, compreendendo verificações de sintaxe, presença de estruturas de código mandatórias, e aplicação de correções automáticas, incluindo a inserção de um esqueleto de código padrão e importações necessárias caso estejam ausentes; e
    f.  Salvar o referido script de automação validado em um arquivo para posterior execução na referida plataforma BIM.

    \item Sistema computacional multi-agente para geração automática de scripts em ambiente Autodesk Revit, \textbf{caracterizado por} compreender:
    a.  Um CommandParserAgent, configurado para receber um comando em linguagem natural e, utilizando um primeiro modelo de linguagem de larga escala (LLM), gerar uma representação estruturada do comando (CommandSchema) contendo a intenção e parâmetros extraídos;
    b.  Um DatabaseAgent, configurado para receber a referida representação estruturada do comando e, utilizando embeddings semânticos, realizar uma busca em um banco de dados vetorial (Qdrant) para recuperar exemplos de código relevantes;
    c.  Um ScriptGeneratorAgent, configurado para receber a referida representação estruturada do comando e os referidos exemplos de código recuperados e, utilizando um segundo modelo de linguagem de larga escala (LLM), gerar um script de automação em linguagem Python, e subsequentemente validar e pós-processar o referido script gerado, aplicando verificações sintáticas e estruturais, e correções automáticas; e
    d.  Um OrchestratorAgent, configurado para coordenar o fluxo de dados e a execução sequencial do CommandParserAgent, DatabaseAgent e ScriptGeneratorAgent, gerenciar um histórico de conversação, e salvar o script de automação final ou um script de erro.

    \item O processo, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que a etapa de validação e pós-processamento (1.e) compreende um loop de múltiplas tentativas de geração caso o script inicialmente gerado não atenda aos critérios de validação.

    \item O sistema, de acordo com a reivindicação 2, \textbf{caracterizado adicionalmente pelo} fato de o OrchestratorAgent manter uma memória de conversação, registrando os comandos do usuário e as etapas do sistema, e pela capacidade de gerar um script de erro informativo caso qualquer etapa do pipeline falhe.

    \item O processo, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pelo} fato de que a recuperação de exemplos de código (1.c) utiliza filtros baseados em metadados associados aos exemplos no banco de dados vetorial, os referidos metadados incluindo categoria do elemento Revit e tipo de operação do script, e emprega mecanismos de fallback que relaxam as restrições de busca caso nenhum resultado relevante seja inicialmente encontrado.

    \item O sistema, de acordo com a reivindicação 2, \textbf{caracterizado adicionalmente pelo} fato de que o ScriptGeneratorAgent utiliza um template de sistema (System Prompt) contendo regras de formatação e compatibilidade específicas para o ambiente de execução (IronPython 2.7/PyRevit) ao instruir o segundo LLM, e um template de usuário (User Prompt) que inclui os exemplos de código recuperados e o comando interpretado.

    \item O processo, de acordo com a reivindicação 1, \textbf{caracterizado adicionalmente pela} integração com um ambiente BIM (Revit via PyRevit) através de um script de interface que: coleta o comando em linguagem natural do usuário, dispara a execução do processo de geração de script como um processo externo, e, em caso de sucesso na geração, carrega e executa o script de automação gerado dentro do ambiente BIM.

    \item O sistema, de acordo com a reivindicação 2, \textbf{caracterizado adicionalmente pelo} fato de o DatabaseAgent utilizar embeddings de dimensionalidade combinada para código-fonte e documentação textual para indexar e buscar os exemplos de código.

\end{enumerate}

\label{fim_reinv}
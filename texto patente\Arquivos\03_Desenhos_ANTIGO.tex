\setcounter{page}{1}
\chead{\thepage\ / \pageref{fim_desenhos}}
\cfoot{}
\begin{center}
\section*{DESENHOS}
\end{center}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.9\textwidth]{Figuras/Diagrama1.png}
    \caption[]{Arquitetura Multiagente da Solução} % O [] evita que "Figura X:" seja impresso antes
    % Ou, se o objetivo é apenas ter um label para \ref e não mostrar texto de caption:
    % \caption[]{\label{fig:arquitetura_multiagente}} %  Não ideal, mas funciona para \ref
    % Uma opção mais limpa para só ter label sem texto visível de caption (precisa de ajustes ou pacotes):
    % \phantomcaption % (requer pacote \usepackage{caption}, \usepackage{subcaption})
    \label{fig:arquitetura_multiagente}
\end{figure}

% Adicione mais blocos \begin{figure}...\end{figure} para cada um dos seus outros desenhos
% Exemplo para uma segunda figura:
% \begin{figure}[H]
%     \centering
%     \includegraphics[width=0.9\textwidth]{NomeDaSuaSegundaFigura.png}
%     \caption{Figura 2}
%     \label{fig:SegundaFigura}
% \end{figure}

% Continue para todas as 6 figuras listadas na seção 8 do seu rascunho.

\label{fim_desenhos}
\setcounter{page}{1}
\chead{\thepage\ / \pageref{fim_resumo}}
\cfoot{}

\section*{\textcolor{white}{Uma linha aqui em branco} \\ RESUMO \bigskip\\
SISTEMA MULTI-AGENTE PARA GERAÇÃO AUTOMÁTICA DE SCRIPTS EM PLATAFORMAS DE MODELAGEM DE INFORMAÇÃO DA CONSTRUÇÃO (BIM)}

A presente invenção refere-se a um sistema computacional multi-agente destinado à automação de tarefas no software Autodesk Revit. O sistema interpreta comandos em linguagem natural (em português) fornecidos pelo usuário e gera automaticamente scripts em Python que executam as ações solicitadas no Revit, sem necessidade de programação manual. A invenção é caracterizada pela coordenação de múltiplos agentes inteligentes que colaboram entre si: um CommandParserAgent interpreta semanticamente o comando do usuário utilizando modelos de linguagem de grande porte, um DatabaseAgent recupera exemplos relevantes através de busca semântica em banco de dados vetorial, um ScriptGeneratorAgent gera e valida o código Python utilizando técnicas de Geração Aumentada por Recuperação (RAG), e um OrchestratorAgent coordena todo o processo. Essa arquitetura cooperativa multi-agente inovadora permite que profissionais de arquitetura, engenharia e construção automatizem processos no Revit de forma simples e eficaz, superando limitações das técnicas atuais que exigem conhecimento avançado em programação.

\bigskip

\noindent
\textbf{Referências:} AUTOMAÇÃO, INTELIGÊNCIA ARTIFICIAL, BIM, MULTI-AGENTE, PROCESSAMENTO DE LINGUAGEM NATURAL

\label{fim_resumo}

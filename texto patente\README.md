# Patente: Sistema Multi-Agente para Geração Automática de Scripts Revit

## Estrutura do Documento

Este documento de patente segue os padrões do INPI (Instituto Nacional da Propriedade Industrial) para pedidos de patente brasileiros.

### Arquivos Principais

- `patente.tex` - Arquivo principal que compila todos os componentes
- `Arquivos/01_Relatorio_Descritivo_ANTIGO.tex` - Relatório Descritivo completo (modificado para padrões INPI)
- `Arquivos/02_Reivindicacoes_ANTIGO.tex` - Reivindicações seguindo padrão BR10 (aprimoradas)
- `Arquivos/03_Desenhos.tex` - Seção de desenhos/figuras
- `Arquivos/04_Resumo_ANTIGO.tex` - Resumo com palavras-chave (otimizado para INPI)

### Arquivos de Backup

- `Arquivos/01_Relatorio_Descritivo.tex` - V<PERSON><PERSON> simplificada (backup)
- `Arquivos/02_Reivindicacoes.tex` - <PERSON><PERSON><PERSON> b<PERSON> (backup)
- `Arquivos/04_Resumo.tex` - <PERSON>ersão inicial (backup)

### Características Técnicas Destacadas

1. **Campo da Invenção**: Sistema multi-agente para automação BIM
2. **Estado da Técnica**: Referências a patentes US 11.960.867 B1 e CN 112947930 B
3. **Problema Técnico**: Barreira de programação para profissionais AEC
4. **Solução Inovadora**: Arquitetura multi-agente cooperativa
5. **Diferencial**: Primeiro sistema multi-agente específico para Revit

### Agentes do Sistema

1. **OrchestratorAgent** - Coordenação geral
2. **CommandParserAgent** - Interpretação de linguagem natural
3. **DatabaseAgent** - Busca semântica (RAG)
4. **ScriptGeneratorAgent** - Geração e validação de código

### Compilação

Para compilar o documento:
```bash
pdflatex patente.tex
```

### Padrões Seguidos

- Numeração de parágrafos com `\pa` ([001], [002], etc.)
- Linguagem formal e impessoal
- Estrutura conforme diretrizes INPI
- Reivindicações com "caracterizado por"
- Resumo com palavras-chave
- Referências a anterioridade

% Preamble para Patente INPI - Sistema Multi-Agente Revit
\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{graphicx}
\usepackage{float} % Para a opção [H] em figuras
\usepackage{fancyhdr}
\pagestyle{fancy}
\usepackage{lastpage}
\usepackage{enumitem} % Para as reivindicações
\usepackage{xcolor} % Para formatações especiais se necessário
% Comentado para compatibilidade - usar com XeLaTeX/LuaLaTeX se disponível
% \usepackage{fontspec}
% \setmainfont{Arial}

% Definições de layout de página do seu template
\hoffset=-1.0in
\oddsidemargin=1.0in
\marginparwidth=0pt
\marginparsep=0pt
\textwidth=6.5in
\voffset=-1.25in
\topmargin=6pt
\headheight=66pt
\headsep=0.65in
\textheight=9.25in
\renewcommand{\headrulewidth}{0pt}

% Definições de espaçamento e parágrafo do seu template
\setlength{\parindent}{4em} % Ou ajuste conforme o padrão do INPI
\setlength{\parskip}{1em}   % Ou ajuste conforme o padrão do INPI
\renewcommand{\baselinestretch}{1.5} % Ou ajuste conforme o padrão do INPI

% Definição do comando \pa (deve estar em config/comandos.def ou aqui)
\newcounter{para}
\setcounter{para}{0}
\usepackage{ifthen} % Necessário para o \ifthenelse
\newcommand{\pa}{%
  \noindent
  \addtocounter{para}{1}%
  \ifthenelse{\value{para}<10}
    {\textbf{[00\thepara]}}
    {\ifthenelse{\value{para}<100}
      {\textbf{[0\thepara]}}
      {\ifthenelse{\value{para}<1000}
        {\textbf{[\thepara]}}
        {} % Pode adicionar mais níveis se precisar de mais de 999 parágrafos
      }
    }
  \hspace{12pt}% Se o INPI não quiser espaço extra, remova ou ajuste
}

% Ajuste de títulos de seção (do seu template)
\usepackage{titlesec}
\titleformat{\section}{\centering\normalfont\fontsize{12}{15}\bfseries}{\thesection}{1em}{}
\titlespacing*{\section}{0cm}{0cm}{1.25cm}

\usepackage[portuguese]{babel}
\usepackage{indentfirst} % Para indentar o primeiro parágrafo após um título
\hyphenpenalty=5000 % Para reduzir hifenização excessiva

\begin{document}

\input{Arquivos/01_Relatorio_Descritivo_ANTIGO}
\input{Arquivos/02_Reivindicacoes_ANTIGO}
\input{Arquivos/03_Desenhos}
\input{Arquivos/04_Resumo_ANTIGO}

\end{document}
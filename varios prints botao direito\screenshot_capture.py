#!/usr/bin/env python3
"""
Script para captura de screenshots com botão lateral do mouse
- Botão lateral esquerdo: captura screenshot
- Botão lateral direito: para o script
- Imagens salvas em formato PNG na pasta 'screenshots'
"""

import os
import time
from datetime import datetime
from pynput import mouse
from PIL import ImageGrab
import threading

class ScreenshotCapture:
    def __init__(self):
        # Cria pasta para salvar screenshots se não existir
        self.screenshot_dir = "screenshots"
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
            print(f"📁 Pasta '{self.screenshot_dir}' criada!")
        
        # Contador para numerar as imagens
        self.counter = 1
        self.is_running = True
        
        print("🖱️  Screenshot Capture iniciado!")
        print("📋 Instruções:")
        print("   • Botão lateral ESQUERDO: captura screenshot")
        print("   • Botão lateral DIREITO: para o script")
        print("   • Imagens salvas em: ./screenshots/")
        print("   • Pressione Ctrl+C para sair")
        print("-" * 50)

    def capture_screenshot(self):
        """Captura screenshot da tela inteira"""
        try:
            # Captura a tela inteira
            screenshot = ImageGrab.grab()
            
            # Gera nome do arquivo com timestamp e contador
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{self.counter:03d}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            
            # Salva a imagem
            screenshot.save(filepath, "PNG")
            
            print(f"📸 Screenshot #{self.counter} salvo: {filename}")
            self.counter += 1
            
        except Exception as e:
            print(f"❌ Erro ao capturar screenshot: {e}")

    def on_mouse_click(self, x, y, button, pressed):
        """Detecta cliques do mouse"""
        if not pressed:  # Só processa quando o botão é solto
            return
        
        # Botão lateral esquerdo (X1) - captura screenshot
        if button == mouse.Button.x1:
            print("🖱️  Botão lateral esquerdo detectado - capturando...")
            self.capture_screenshot()
        
        # Botão lateral direito (X2) - para o script
        elif button == mouse.Button.x2:
            print("🛑 Botão lateral direito detectado - parando script...")
            self.is_running = False
            return False  # Para o listener

    def start_listening(self):
        """Inicia o listener do mouse"""
        try:
            with mouse.Listener(on_click=self.on_mouse_click) as listener:
                print("👂 Escutando cliques do mouse...")
                listener.join()
        except KeyboardInterrupt:
            print("\n🛑 Script interrompido pelo usuário (Ctrl+C)")
        except Exception as e:
            print(f"❌ Erro no listener: {e}")
        finally:
            print(f"📊 Total de screenshots capturados: {self.counter - 1}")
            print("👋 Script finalizado!")

def main():
    """Função principal"""
    print("🚀 Iniciando Screenshot Capture...")
    
    # Verifica se as bibliotecas necessárias estão instaladas
    try:
        import pynput
        from PIL import ImageGrab
    except ImportError as e:
        print("❌ Erro: Biblioteca não encontrada!")
        print("📦 Instale as dependências com:")
        print("   pip install pynput pillow")
        return
    
    # Cria e inicia o capturador
    capturer = ScreenshotCapture()
    capturer.start_listening()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script de teste para demonstrar a captura de screenshots
"""

import os
from datetime import datetime
from PIL import ImageGrab

def testar_captura():
    """Testa a captura de screenshot"""
    print("🧪 Testando captura de screenshot...")
    
    # Cria pasta de teste
    test_dir = "teste_screenshots"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"📁 Pasta '{test_dir}' criada!")
    
    try:
        # Captura screenshot
        print("📸 Capturando tela...")
        screenshot = ImageGrab.grab()
        
        # Gera nome do arquivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"teste_{timestamp}.png"
        filepath = os.path.join(test_dir, filename)
        
        # Salva a imagem
        screenshot.save(filepath, "PNG")
        
        print(f"✅ Screenshot salvo: {filename}")
        print(f"📁 Localização: {os.path.abspath(filepath)}")
        
        # Mostra informações da imagem
        width, height = screenshot.size
        print(f"📐 Dimensões: {width}x{height} pixels")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando teste de screenshot...")
    print("-" * 40)
    
    sucesso = testar_captura()
    
    print("-" * 40)
    if sucesso:
        print("🎉 Teste concluído com sucesso!")
        print("💡 Agora você pode usar o script principal:")
        print("   python screenshot_capture.py")
    else:
        print("❌ Teste falhou. Verifique as dependências.")
